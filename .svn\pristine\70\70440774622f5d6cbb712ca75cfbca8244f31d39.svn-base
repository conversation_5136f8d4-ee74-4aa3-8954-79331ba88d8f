package com.carefirst.qadb.dao;

import java.sql.SQLException;
import java.util.List;

import net.sf.jasperreports.engine.JRDataSource;

import com.carefirst.audit.model.Associate;
import com.carefirst.audit.model.OperationalUnit;



public interface AssociateDAO {
	
	public List<Associate> getJobTitles() throws SQLException;
	
	public List<Associate> getLocations() throws SQLException;
	
	public Associate saveUpdate<PERSON><PERSON><PERSON>te(Associate associateDetails) throws Exception;

	public List<Associate> getAssociateSearch<PERSON><PERSON><PERSON><PERSON>(Associate associateSearchTO) throws SQLException;

	public List<OperationalUnit> getWorkUnits() throws SQLException;

	public Associate getAssociate(Associate associateSearchTO) throws SQLException;

	public Associate deleteAs<PERSON>ciate(Associate associateDetails) throws SQLException;

	public JRDataSource getAssociatesListReport(Associate associateDetails) throws SQLException;

	
	}
