package com.carefirst.qadb.controller;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JREmptyDataSource;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JRPrintElement;
import net.sf.jasperreports.engine.JRPrintPage;
import net.sf.jasperreports.engine.JRPrintText;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.design.JasperDesign;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.xml.JRXmlLoader;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;

import org.apache.http.HttpResponse;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.carefirst.audit.model.AdjustmentsReport;
import com.carefirst.audit.model.Associate;
import com.carefirst.audit.model.AuditAssessment;
import com.carefirst.audit.model.AuditDetails;
import com.carefirst.audit.model.AuditSearch;
import com.carefirst.audit.model.EditCodeReport;
import com.carefirst.audit.model.ErrorDetails;
import com.carefirst.audit.model.InSampleOutOfSampleReport;
import com.carefirst.audit.model.OperationalUnit;
import com.carefirst.audit.model.OtherReports;
import com.carefirst.audit.model.PerformanceGroupErrorSheet;
import com.carefirst.audit.model.ReportModel;
import com.carefirst.audit.model.ScoresAndTrends;
import com.carefirst.qadb.constant.QADBConstants;
import com.carefirst.qadb.dao.AssociateDAO;
import com.carefirst.qadb.dao.AuditDetailsDAO;
import com.carefirst.qadb.dao.ClaimsAuditAssessmentReportDAO;
import com.carefirst.qadb.dao.ClaimsAuditAssessmentReportDAOImpl;
import com.carefirst.qadb.dao.EditCodeReportsDAO;
import com.carefirst.qadb.dao.ErrorDetailsDAO;
import com.carefirst.qadb.dao.InSampleOutOfSampleReportsDAO;
import com.carefirst.qadb.dao.OperationalUnitDAO;
import com.carefirst.qadb.dao.PerformanceGroupErrorSheetDAO;
import com.carefirst.qadb.dao.ScoresReportsDAO;
import com.carefirst.qadb.dao.ScoresReportsDAOImpl;
import com.carefirst.qadb.dao.UserReportsDAO;
import com.carefirst.qadb.service.AuthorizationService;

@Controller
public class ReportsController {

	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);
	
	private static final String CURRENT_PAGE_NUMBER = "${CURRENT_PAGE_NUMBER}";
	private static final String TOTAL_PAGE_NUMBER = "${TOTAL_PAGE_NUMBER}";

	@Autowired  
	AuthorizationService authorizationService;
	
	@Autowired
	AssociateDAO associateDAO;
	
	@Autowired
	UserReportsDAO userReportsDAO;

	@Autowired
	ScoresReportsDAO scoresReportsDAO;
	
	@Autowired
	PerformanceGroupErrorSheetDAO performanceGroupErrorSheetDAO;
	
	@Autowired  
	AuditDetailsDAO auditDetailsDao;
	
	@Autowired
	ErrorDetailsDAO errorDetailsDao;
	
	@Autowired
	OperationalUnitDAO operationalUnitDAO; //for manager list
	
	@Autowired
	ClaimsAuditAssessmentReportDAO claimsAuditAssessmentReportDAO;
	
	@Autowired
	EditCodeReportsDAO editCodeReportsDAO;
	
	@Autowired
	InSampleOutOfSampleReportsDAO inSampleOutOfSampleReportsDAO;
	
	// Scores & Trends
	// Scores
	@RequestMapping(value = "/scores", method = RequestMethod.GET)
	public ModelAndView Scores(HttpServletRequest request) throws Exception,SQLException {
		
		logger.debug("*** Entry Scores method ***");
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersReports("reports_score_and_trends", request);
		List<Associate> claimProcessors = scoresReportsDAO.getClaimProcessors("ASSOCIATE","");
	    mv.addObject("claimProcessors", claimProcessors);
	    
	    String userGrp = authorizationService.getUserGroup(request);
		
	    List<AuditDetails> priPGAList = auditDetailsDao.getPriPGA(userGrp);
	    mv.addObject("priPGALists", priPGAList); 
	    
	    logger.debug("*** Exit Scores method ***");
		return mv;
	}
	
	/**
	 * Scores pdf report generation
	 * 
	 * @param modelAndView
	 * @return
	 */
	@RequestMapping(value = "/ScoresReport", method = RequestMethod.GET)
	public ModelAndView scoresReportPDF(ModelAndView modelAndView,@ModelAttribute("scoresForm") ScoresAndTrends scores,
			HttpSession session)
			throws JRException {
		logger.debug("*** Entry scoresReport method ***");
		logger.debug("Scores "+scores.getAuditType());
		
		String userId = (String) session.getAttribute(QADBConstants.USERNAME);
		logger.debug("user id " + userId);

		JRDataSource datasource = null;
		JRDataSource outSampleDatasource = null;
		JRDataSource inSampleDatasource = null;
		String type = "";
		try {  
			outSampleDatasource = scoresReportsDAO.getScoresReport(scores);
			type = ScoresReportsDAOImpl.type;

			logger.debug(" IS start "+type);
			scores.setSample("in");
			inSampleDatasource = scoresReportsDAO.getScoresReport(scores);

			if(type.equalsIgnoreCase("is")){
				datasource = inSampleDatasource;
			}
			else{
				datasource = outSampleDatasource;
			}
			
		} catch (SQLException e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		Map<String, Object> parameterMap = new HashMap<String, Object>();
		parameterMap.put("datasource", datasource);
		parameterMap.put("JasperCustomSubReportDatasource1", inSampleDatasource);
		parameterMap.put("JasperCustomSubReportDatasource2", outSampleDatasource);
		if((scores.getSubject()).equalsIgnoreCase("ASSOCIATE")){
			logger.debug("Associate report ");
			if(type.equalsIgnoreCase("is")){
				modelAndView = new ModelAndView("associateScoresIn", parameterMap); //single pdf
				}
			else{
				modelAndView = new ModelAndView("associateScores", parameterMap);
			}
		}
		if((scores.getSubject()).equalsIgnoreCase("SUPERVISOR")){
			logger.debug("Supervisor report ");
			if(type.equalsIgnoreCase("is")){
				modelAndView = new ModelAndView("supervisorScoresIn", parameterMap); //single pdf
				}
			else{
				modelAndView = new ModelAndView("supervisorScores", parameterMap);
			}
		}
		if((scores.getSubject()).equalsIgnoreCase("MANAGER")){
			logger.debug("Manager report ");
			if(type.equalsIgnoreCase("is")){
				modelAndView = new ModelAndView("managerScoresIn", parameterMap);
				}
			else{
				modelAndView = new ModelAndView("managerScores", parameterMap);
			}
		}
		if((scores.getSubject()).equalsIgnoreCase("DIRECTOR")){
			logger.debug("Director report ");
			if(type.equalsIgnoreCase("is")){
				modelAndView = new ModelAndView("directorScoresIn", parameterMap);
				}
			else{
				modelAndView = new ModelAndView("directorScores", parameterMap);
			}
		}
		if((scores.getSubject()).equalsIgnoreCase("DIVISION")){
			logger.debug("Division report ");
			Map<String, Object> parameterMap2 = new HashMap<String, Object>();
			parameterMap2.put("datasource", datasource);
			modelAndView = new ModelAndView("divisionScores", parameterMap2);
			}
		if((scores.getSubject()).equalsIgnoreCase("LOB")||(scores.getSubject()).equalsIgnoreCase("PPG")||(scores.getSubject()).equalsIgnoreCase("SPG")){
			logger.debug("LOB report ");
			Map<String, Object> parameterMap3 = new HashMap<String, Object>();
			parameterMap3.put("datasource", datasource);
			if((scores.getSubject()).equalsIgnoreCase("LOB")){
				modelAndView = new ModelAndView("lobScores", parameterMap3);
			}
			if((scores.getSubject()).equalsIgnoreCase("PPG")||(scores.getSubject()).equalsIgnoreCase("SPG")){
				modelAndView = new ModelAndView("pgScores", parameterMap3);
			}
		}
		// Return the View and the Model combined
		logger.debug("*** Exit scoresReport method ***");
		return modelAndView;
	}
	
	
	//Trends
	
	@RequestMapping(value = "/TrendsReport", method = RequestMethod.GET)
	public ModelAndView trendsReportPDF(ModelAndView modelAndView,@ModelAttribute("scoresForm") ScoresAndTrends trends,
			HttpSession session)
			throws JRException {
		logger.debug("*** Entry trendsReportPDF method ***");
		logger.debug("Trends "+trends.getAuditType());
		
		String userId = (String) session.getAttribute(QADBConstants.USERNAME);
		logger.debug("user id " + userId);

		JRDataSource datasource = null;
		JRDataSource outSampleDatasource = null;
		JRDataSource inSampleDatasource = null;
		String type = "";
		try {  
			outSampleDatasource = scoresReportsDAO.getTrendsReport(trends);
			type = ScoresReportsDAOImpl.type;
			logger.debug(" IS start ");
			trends.setSample("in");
			inSampleDatasource = scoresReportsDAO.getTrendsReport(trends);
			logger.debug("Type "+type);
			if(type.equalsIgnoreCase("is")){
				datasource = inSampleDatasource;
			}
			else{
				datasource = outSampleDatasource;
			}
			/*datasource = scoresReportsDAO.getTrendsReport(trends);
			outSampleDatasource = datasource;
			logger.debug(" IS start ");
			trends.setSample("in");
			inSampleDatasource = scoresReportsDAO.getTrendsReport(trends);*/
		} catch (SQLException e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		Map<String, Object> parameterMap = new HashMap<String, Object>();
		parameterMap.put("datasource", datasource);
		parameterMap.put("JasperCustomSubReportDatasource1", inSampleDatasource);
		parameterMap.put("JasperCustomSubReportDatasource2", outSampleDatasource);
		if((trends.getSubject()).equalsIgnoreCase("ASSOCIATE")||(trends.getSubject()).equalsIgnoreCase("SUPERVISOR")){
			logger.debug("Associate report gen ");
			if(type.equalsIgnoreCase("is")){
				modelAndView = new ModelAndView("associateTrendsIn", parameterMap); //Single PDF
				}
			else{
				modelAndView = new ModelAndView("associateTrends", parameterMap);
			}
		}
		if((trends.getSubject()).equalsIgnoreCase("DIRECTOR")||(trends.getSubject()).equalsIgnoreCase("MANAGER")||(trends.getSubject()).equalsIgnoreCase("DIVISION")||(trends.getSubject()).equalsIgnoreCase("LOB")
				||(trends.getSubject()).equalsIgnoreCase("PPG")||(trends.getSubject()).equalsIgnoreCase("SPG")){
			logger.debug("Division report gen ");
			Map<String, Object> parameterMap2 = new HashMap<String, Object>();
			parameterMap2.put("datasource", datasource);
			modelAndView = new ModelAndView("divisionTrends", parameterMap2);
		}
		
		// Return the View and the Model combined
		logger.debug("*** Exit scoresReport method ***");
		return modelAndView;
	}
	
	
	@RequestMapping(value = "/Monetary_Error_Sheet", method = RequestMethod.GET)
	public ModelAndView monetaryReportPDF(ModelAndView modelAndView,@ModelAttribute("scoresForm") ScoresAndTrends monetory,
			HttpSession session)
			throws JRException {
		logger.debug("*** Entry MonetaryReport method ***");
		logger.debug("MonetaryReport "+monetory.getAuditType());
		String userId = (String) session.getAttribute(QADBConstants.USERNAME);
		logger.debug(" us id " + userId);

		JRDataSource datasource = null;
		try {  
			datasource = scoresReportsDAO.getMonetoryReport(monetory);
		} catch (SQLException e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		Map<String, Object> parameterMap = new HashMap<String, Object>();
		parameterMap.put("datasource", datasource);
		if((monetory.getSubject()).equalsIgnoreCase("SUPERVISOR")||(monetory.getSubject()).equalsIgnoreCase("MANAGER")){
			logger.debug("Manager/SupervisorMonetary report ");
			modelAndView = new ModelAndView("ManagerSupervisorMonetary", parameterMap);
			}
		if((monetory.getSubject()).equalsIgnoreCase("DIVISION")){
			logger.debug("Division Monetary report ");
			modelAndView = new ModelAndView("DivisionMonetary", parameterMap);
			}
		// Return the View and the Model combined
		logger.debug("*** Exit monetory method ***");
		return modelAndView;
	}
	
	@RequestMapping(value = "/MonetaryErrorSheet", method = RequestMethod.GET)
	public ModelAndView monetaryReportXls(ModelAndView modelAndView,@ModelAttribute("scoresForm") ScoresAndTrends monetory,
			HttpSession session)
			throws JRException {
		logger.debug("*** Entry MonetaryReport xls method ***");
		logger.debug("MonetaryReport "+monetory.getAuditType());
		String userId = (String) session.getAttribute(QADBConstants.USERNAME);
		logger.debug(" us id " + userId);

		JRDataSource datasource = null;
		try {  
			datasource = scoresReportsDAO.getMonetoryReport(monetory);
		} catch (SQLException e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		Map<String, Object> parameterMap = new HashMap<String, Object>();
		parameterMap.put("datasource", datasource);
		modelAndView = new ModelAndView("ManagerMonetaryXls", parameterMap);
		
		// Return the View and the Model combined
		logger.debug("*** Exit monetory xls method ***");
		return modelAndView;
	}
	// User Reports

	@RequestMapping(value = "/userReports", method = RequestMethod.GET)
	public ModelAndView userReports(HttpServletRequest request) throws Exception,SQLException {
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersReports("reports_user_reports", request);
		 
		   //String userGrp = "('SAMMD','CD','ALL')";
		   
		   String userGrp = authorizationService.getUserGroup(request);

	 	   //String b[] = {"Contractor","PeopleSoft_ELM_User","onestop_users","qadb-samd-admin_users"};
	 	   String b[] = {request.getHeader("iv-groups")};
	 	   /*if(!(((Arrays.toString(b)).replace("[", "")).replace("]", "")).equals("null")){
	 			int last = b.length - 1;
	 			if(((b[last].toString()).contains("qadb-superadmin_users"))){
	 				userGrp = "('SAMMD','CD','ALL')";
	 			}
	 			if(((b[last].toString()).contains("qadb-samd-auditor_users"))||((b[last].toString()).contains("qadb-samd-admin_users"))){
	 				userGrp = "('SAMMD','ALL')";
	 			}
	 			if(((b[last].toString()).contains("qadb-cd-auditor_users"))||((b[last].toString()).contains("qadb-cd-admin_users"))){
	 				userGrp = "('CD','ALL')";
	 			}
	 		}*/
			/*if((!(((Arrays.toString(b)).replace("[", "")).replace("]", "")).equals("null"))||(b.length != 0)){
	 			if(((Arrays.asList(b)).contains("qadb-superadmin_users"))){
	 				userGrp = "('SAMMD','CD','ALL')";
	 			}
	 			if(((Arrays.asList(b)).contains("qadb-samd-auditor_users"))||((Arrays.asList(b)).contains("qadb-samd-admin_users"))||((Arrays.asList(b)).contains("qadb-samd-readonly_users"))){
	 				userGrp = "('SAMMD','ALL')";
	 			}
	 			if(((Arrays.asList(b)).contains("qadb-cd-auditor_users"))||((Arrays.asList(b)).contains("qadb-cd-admin_users"))||((Arrays.asList(b)).contains("qadb-cd-readonly_users"))){
	 				userGrp = "('CD','ALL')";
	 			}
	 		}*/
	 	   
	 	 logger.debug("User Grp "+b + " - "+userGrp);
	 	 
		 List<AuditDetails> auditorsList = auditDetailsDao.getAuditors(userGrp);
		 mv.addObject("auditorsList", auditorsList);
		
	    return mv;
	}
	
	/**
	 * Adjustment Required pdf report generation
	 * 
	 * @param modelAndView
	 * @param status
	 * @return
	 */
	@RequestMapping(value = "/AdjustmentsRequiredReport", method = RequestMethod.GET)
	public ModelAndView adjumentRequiredReportPDF(ModelAndView modelAndView,@ModelAttribute("adjReqForm") AdjustmentsReport adjustments,
			HttpSession session) throws Exception{
		logger.debug("*** Entry adjumentRequiredReportPDF method ***"+adjustments.getAuditFromDate());

		String userId = (String) session.getAttribute(QADBConstants.USERNAME);
		logger.debug("user id " + userId);

		JRDataSource datasource = null;
		try {
			datasource = userReportsDAO.getAdjustmentsRequiredReport(adjustments);
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		}
		Map<String, Object> parameterMap = new HashMap<String, Object>();
		parameterMap.put("datasource", datasource);
		modelAndView = new ModelAndView("AdjustmentsRequired", parameterMap);
		logger.debug("*** Exit adjumentRequiredReportPDF method ***");
		return modelAndView;
	}
	
	
	/**
	 * Error Codes pdf report generation
	 * 
	 * @param modelAndView
	 * @param status
	 * @return
	 */
	@RequestMapping(value = "/ListOfErrorCodes", method = RequestMethod.GET, params = { "status" })
	public ModelAndView errorCodesReportPDF(ModelAndView modelAndView,
			@RequestParam(value = "status") String status, HttpSession session) throws Exception{
		logger.debug("*** Entry errorCode method ***" + status);
		OtherReports errorCodeReport = new OtherReports();
		String userId = (String) session.getAttribute(QADBConstants.USERNAME);
		logger.debug("user id " + userId);
		errorCodeReport.setUserId(userId);
		errorCodeReport.setReportType("ERR_CODES");
		errorCodeReport.setStatus(status);

		JRDataSource datasource = null;
		try {
			datasource = userReportsDAO.getOtherReport(errorCodeReport);
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		}
		Map<String, Object> parameterMap = new HashMap<String, Object>();
		parameterMap.put("datasource", datasource);
		if(status.equalsIgnoreCase("All")){
			modelAndView = new ModelAndView("errorCodesReportAll", parameterMap);
		}else{
			modelAndView = new ModelAndView("errorCodesReport", parameterMap);
		}
		
		logger.debug("*** Exit errorCode method ***");
		return modelAndView;
	}

	/**
	 * Current Processors pdf report generation
	 * 
	 * @param modelAndView
	 * @param status
	 * @return
	 */
	@RequestMapping(value = "/ListOfCurrentProcessors", method = RequestMethod.GET, params = { "status" })
	public ModelAndView currentProcessorsReportPDF(ModelAndView modelAndView,
			@RequestParam(value = "status") String status, HttpSession session) throws Exception {
		logger.debug("*** Entry currentProcessors method ***" + status);
		OtherReports currentProcessorsReport = new OtherReports();
		String userId = (String) session.getAttribute(QADBConstants.USERNAME);
		logger.debug("user id " + userId);
		currentProcessorsReport.setUserId(userId);
		currentProcessorsReport.setReportType("CURR_PROCESSORS");
		currentProcessorsReport.setStatus(status);

		JRDataSource datasource = null;
		try {
			datasource = userReportsDAO
					.getOtherReport(currentProcessorsReport);
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		}
		Map<String, Object> parameterMap = new HashMap<String, Object>();
		parameterMap.put("datasource", datasource);
		if(status.equalsIgnoreCase("All")){
			modelAndView = new ModelAndView("currentProcessorsReportAll", parameterMap);
		}else{
			modelAndView = new ModelAndView("currentProcessorsReport", parameterMap);
		}
		logger.debug("*** Exit currentProcessors method ***");
		return modelAndView;
	}

	/**
	 * Current Processors pdf report generation
	 * 
	 * @param modelAndView
	 * @param status
	 * @return
	 */
	@RequestMapping(value = "/ListOfSupervisors", method = RequestMethod.GET, params = { "status" })
	public ModelAndView supervisorsReportPDF(ModelAndView modelAndView,
			@RequestParam(value = "status") String status, HttpSession session) throws Exception {
		logger.debug("*** Entry supervisors method ***" + status);
		OtherReports supervisorsReport = new OtherReports();
		String userId = (String) session.getAttribute(QADBConstants.USERNAME);
		logger.debug(" us id " + userId);
		supervisorsReport.setUserId(userId);
		supervisorsReport.setReportType("SUPERVISORS");
		supervisorsReport.setStatus(status);

		JRDataSource datasource = null;
		try {
			datasource = userReportsDAO.getOtherReport(supervisorsReport);
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		}
		Map<String, Object> parameterMap = new HashMap<String, Object>();
		parameterMap.put("datasource", datasource);
		if(status.equalsIgnoreCase("All")){
			modelAndView = new ModelAndView("supervisorsReportAll", parameterMap);
		}else{
			modelAndView = new ModelAndView("supervisorsReport", parameterMap);
		}
		logger.debug("*** Exit supervisors method ***");
		return modelAndView;
	}


	// Performance Error Sheet
	@RequestMapping(value = "/performanceReport", method = RequestMethod.GET)
	public ModelAndView performanceReports(HttpServletRequest request) throws Exception,SQLException{
	 	   
	 	   //String userGrp = "('SAMMD','CD','ALL')";
		   	String userGrp = authorizationService.getUserGroup(request);

		
	 	   //String b[] = {"Contractor","PeopleSoft_ELM_User","onestop_users","qadb-samd-admin_users"};
	 	   String b[] = {request.getHeader("iv-groups")};
	 	   /*if((!(((Arrays.toString(b)).replace("[", "")).replace("]", "")).equals("null"))||(b.length != 0)){
	 			if(((Arrays.asList(b)).contains("qadb-superadmin_users"))){
	 				userGrp = "('SAMMD','CD','ALL')";
	 			}
	 			if(((Arrays.asList(b)).contains("qadb-samd-auditor_users"))||((Arrays.asList(b)).contains("qadb-samd-admin_users"))||((Arrays.asList(b)).contains("qadb-samd-readonly_users"))){
	 				userGrp = "('SAMMD','ALL')";
	 			}
	 			if(((Arrays.asList(b)).contains("qadb-cd-auditor_users"))||((Arrays.asList(b)).contains("qadb-cd-admin_users"))||((Arrays.asList(b)).contains("qadb-cd-readonly_users"))){
	 				userGrp = "('CD','ALL')";
	 			}
	 		}*/
	 	   
	 	   logger.debug("User Grp "+b + " - "+userGrp);
	 	  ModelAndView mv = null;
	 	  mv = authorizationService.AuthorizeUsersReports("reports_performance_error_sheet", request);
		 
		List<AuditDetails> priPGAList = auditDetailsDao.getPriPGA(userGrp);
	    mv.addObject("priPGAList", priPGAList);
	     
	    List<AuditDetails> secPGAList = auditDetailsDao.getSecPGAList(userGrp);
	    mv.addObject("secPGAList", secPGAList);
		
	    return mv;
	}
	
	@RequestMapping(value = "/PerformanceGroupMonthlyErrorSheet", method = RequestMethod.GET)
	public ModelAndView PerformanceGroupMonthlyErrorSheetPDF(ModelAndView modelAndView,@ModelAttribute("pgErrorSheetForm") PerformanceGroupErrorSheet pgErrorSheet,
			HttpSession session)
			throws Exception,JRException {
		logger.debug("*** Entry PerformanceGroupMonthlyErrorSheet method ***");
		logger.debug("PerformanceGroupMonthlyErrorSheet "+pgErrorSheet.getMonth());
		String userId = (String) session.getAttribute(QADBConstants.USERNAME);
		logger.debug("user id " + userId);

		JRDataSource datasource = null;
		try {  
			datasource = performanceGroupErrorSheetDAO.getPGErrorSheet(pgErrorSheet);
		} catch (SQLException e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		Map<String, Object> parameterMap = new HashMap<String, Object>();
		parameterMap.put("datasource", datasource);
		modelAndView = new ModelAndView("PerfGrpErrSheet", parameterMap);
		// Return the View and the Model combined
		logger.debug("*** Exit monetory method ***");
		return modelAndView;
	}

	// Claims audit assessment report

	@RequestMapping(value = "/claimsAuditAssessmentReport", method = RequestMethod.GET)
	public ModelAndView claimsAuditAssessmentReport(HttpServletRequest request) throws Exception,SQLException {
		
		ModelAndView mv = null;
	 	mv = authorizationService.AuthorizeUsersReports("reports_claims_audit_assessment", request);
		
		 //String userGrp = "('SAMMD','CD','ALL')";
		   String userGrp = authorizationService.getUserGroup(request);

		 
	 	   //String b[] = {"Contractor","PeopleSoft_ELM_User","onestop_users","qadb-samd-admin_users"};
	 	   String b[] = {request.getHeader("iv-groups")};
	 	   /*if((!(((Arrays.toString(b)).replace("[", "")).replace("]", "")).equals("null"))||(b.length != 0)){
	 			if(((Arrays.asList(b)).contains("qadb-superadmin_users"))){
	 				userGrp = "('SAMMD','CD','ALL')";
	 			}
	 			if(((Arrays.asList(b)).contains("qadb-samd-auditor_users"))||((Arrays.asList(b)).contains("qadb-samd-admin_users"))||((Arrays.asList(b)).contains("qadb-samd-readonly_users"))){
	 				userGrp = "('SAMMD','ALL')";
	 			}
	 			if(((Arrays.asList(b)).contains("qadb-cd-auditor_users"))||((Arrays.asList(b)).contains("qadb-cd-admin_users"))||((Arrays.asList(b)).contains("qadb-cd-readonly_users"))){
	 				userGrp = "('CD','ALL')";
	 			}
	 		}*/
	 	   
	 	   logger.debug("UserGrp "+b + " - "+userGrp);
		
		List<Associate> claimProcessors = scoresReportsDAO.getClaimProcessors("ASSOCIATE","");
	    mv.addObject("claimProcessors", claimProcessors);
	    
	    List<Associate> facetsIds = scoresReportsDAO.getFacetsIds();
	    mv.addObject("facetsIds", facetsIds);
	    
	    List<AuditDetails> auditorsList = auditDetailsDao.getAuditors(userGrp);
	    mv.addObject("auditorsList", auditorsList);
	    
		return mv;
	}
	
	//changes 06/302020
		/*@RequestMapping(value = "/changeClaims_Audit_Assessment_Report", method = RequestMethod.GET)
		public ModelAndView SampleHtmlToPdfReport (ModelAndView modelAndView,
		HttpSession session) throws Exception {
			JRDataSource datasource = new JREmptyDataSource();
			Map<String, Object> parameterMap = new HashMap<String, Object>();
			logger.debug("*** Entry claimsAuditAssessmentReporttPDF method ***");
			parameterMap.put("htmlCode", "<title>Sample of html based report</title>\n"
					+ "<body><h1>This is sample pdf</h1></body>");
		parameterMap.put("datasource", datasource);
		logger.debug("Map Created");
		modelAndView = new ModelAndView("claimsAuditAssesments", parameterMap); //single pdf

		return modelAndView;
		}	*/
		
		/**
		 * EDIT code pdf report generation
		 * 
		 * @param modelAndView
		 * @param editCodeForm
		 * @return
		 */
	@RequestMapping(value = "/Claims_Audit_Assessment_Report", method = RequestMethod.GET)
	public ModelAndView claimsAuditAssessmentReporttPDF(ModelAndView modelAndView,@ModelAttribute("auditAssessmentForm") AuditSearch auditAssessmentForm,
			HttpSession session,HttpServletResponse response) throws Exception {
		logger.debug("*** Entry claimsAuditAssessmentReporttPDF method ***"+auditAssessmentForm.getAssociateName());
		/*logger.debug("Input parameters :: Name: "+ auditAssessmentForm.getAssociateName()+" Employee Number : "+auditAssessmentForm.getEmpNo()+
				" Processed Date From :"+auditAssessmentForm.getProcessedDateFrom()+" Processed Date To : "+auditAssessmentForm.getProcessedDateTo()+
				" DCN : "+auditAssessmentForm.getDcn()+" Auditor Name : "+auditAssessmentForm.getAuditorName()+
				" Audit Date From : "+auditAssessmentForm.getAuditDateFrom()+"Audit Date To : "+auditAssessmentForm.getAuditDateTo());*/
		//JRDataSource datasource = new JREmptyDataSource();
		//Map<String, Object> parameterMap = new HashMap<String, Object>();
		logger.debug("*** Entry claimsAuditAssessmentReporttPDF method ***");
		/*parameterMap.put("htmlCode", "<title>Sample of html based report</title>\n"
				+ "<body><h1>This is sample pdf</h1></body>");
		parameterMap.put("datasource", datasource);
		logger.debug("Map Created");
		modelAndView = new ModelAndView("claimsAuditAssesments", parameterMap);
		logger.debug("Model and view fetched : Now returning");*///single pdf
		
		
		try {
			//JRDataSource datasource= claimsAuditAssessmentReportDAO.getClaimsAuditAssesmentReport(auditAssessmentForm);
            InputStream jrxmlInput = this.getClass().getClassLoader().getResourceAsStream("auddata.jrxml");
            JasperDesign design = JRXmlLoader.load(jrxmlInput);
            JasperReport jasperReport = JasperCompileManager.compileReport(design);
            logger.debug("Report compiled "+jasperReport.getAllBands()[0].getHeight());
            
            jrxmlInput = this.getClass().getClassLoader().getResourceAsStream("auddataoos.jrxml");
            design = JRXmlLoader.load(jrxmlInput);
            JasperReport jasperReportOos = JasperCompileManager.compileReport(design);
            
            Map<String, Object> params = new HashMap<String, Object>();
            
            logger.debug(" Fetching OOS >>> start ");
			//auditAssessmentForm.setType("in");
            List<AuditAssessment> auditAssessmentOutSample=claimsAuditAssessmentReportDAO.getClaimsAuditAssessmentList(auditAssessmentForm);
            logger.debug("Fetching IS now >>> Type changed");
            auditAssessmentForm.setType("in");
            List<AuditAssessment> auditAssessmentInSample=claimsAuditAssessmentReportDAO.getClaimsAuditAssessmentList(auditAssessmentForm);
            
           //logger.debug("HTML LIST Value  : "+(HTMLCodeUtils.convertHTMLToString(claimsAuditAssessmentReportDAO.getClaimsAuditAssessmentList(auditAssessmentForm)).get(0)));
            List<String> auditListInSample= HTMLCodeUtils.convertHTMLToString(auditAssessmentInSample);
            params.put("htmlCode", "html1");
            params.put("htmlValue", HTMLCodeUtils.part3);
            
            List<String> auditListOutSample= HTMLCodeUtilsOOS.convertHTMLToString(auditAssessmentOutSample);
            params.put("htmlOosValue",HTMLCodeUtilsOOS.part3);
            
            JasperPrint jasperPrint=null;
            JasperPrint jasperPrintOos=null;
            List<JasperPrint> jPrint=new ArrayList<JasperPrint>();
            List<ReportModel> reportModelList =new ArrayList<ReportModel>();
            ReportModel reportModel=null;
            if(null!=auditListInSample && !auditListInSample.isEmpty()){
            for(String audit:auditListInSample){
            	reportModel=new ReportModel();
                reportModel.setHtmlRow(audit);
                reportModel.setAssociateName(auditAssessmentInSample.get(0).getAssociateName());
                reportModel.setSupervisor(auditAssessmentInSample.get(0).getSupervisor());
                reportModel.setEmpNO(auditAssessmentInSample.get(0).getEmpNO());
                reportModelList.add(reportModel);
            }
            JRDataSource ds = new JRBeanCollectionDataSource(reportModelList);
            jasperPrint = JasperFillManager.fillReport(jasperReport, params, ds);
            jPrint.add(jasperPrint);
            }
            
            List<ReportModel> reportModelListOos =new ArrayList<ReportModel>();
            if(null!=auditListOutSample && !auditListOutSample.isEmpty()){
            for(String audit:auditListOutSample){
            	reportModel=new ReportModel();
                reportModel.setHtmlOosRow(audit);
                reportModel.setAssociateName(auditAssessmentOutSample.get(0).getAssociateName());
                reportModel.setSupervisor(auditAssessmentOutSample.get(0).getSupervisor());
                reportModel.setEmpNO(auditAssessmentOutSample.get(0).getEmpNO());
                reportModelListOos.add(reportModel);
            }
            JRDataSource dsOos = new JRBeanCollectionDataSource(reportModelListOos);
            jasperPrintOos = JasperFillManager.fillReport(jasperReportOos, params, dsOos);
            jPrint.add(jasperPrintOos);
            }
            logger.debug("Values for Insample and Outsample put in Report Model");
            
            
            
            
            
            
          //First loop on all reports to get total page number
            int totPageNumber=0;
            for (JasperPrint jp : jPrint) {
                totPageNumber += jp.getPages().size();
            }

            //Second loop all reports to replace our markers with current and total number
            int currentPage = 1;
            for (JasperPrint jp : jPrint) {
                List<JRPrintPage> pages = jp.getPages();
                //Loop all pages of report
                for (JRPrintPage jpp : pages) {
                    List<JRPrintElement> elements = jpp.getElements();
                    //Loop all elements on page
                    for (JRPrintElement jpe : elements) {
                        //Check if text element
                        if (jpe instanceof JRPrintText){
                            JRPrintText jpt = (JRPrintText) jpe;
                            //Check if current page marker
                            if (CURRENT_PAGE_NUMBER.equals(jpt.getValue())){
                                jpt.setText("Page " + currentPage + " of"); //Replace marker
                                continue;
                            }
                            //Check if totale page marker
                            if (TOTAL_PAGE_NUMBER.equals(jpt.getValue())){
                                jpt.setText(" " + totPageNumber); //Replace marker
                            }
                        }
                    }
                    currentPage++;
                }
            }
            
            JRPdfExporter pdfExporter = new JRPdfExporter();
            ByteArrayOutputStream pdfReportStream = new ByteArrayOutputStream();
            //pdfExporter.setExporterInput(new SimpleExporterInput(jasperPrint));
            logger.debug("Setting Input to pdfExporter");
            pdfExporter.setExporterInput(SimpleExporterInput.getInstance(jPrint));
            logger.debug("Setting output to pdfExporter");
            pdfExporter.setExporterOutput(new SimpleOutputStreamExporterOutput(pdfReportStream));
            pdfExporter.exportReport();
            logger.debug("pdfReportStream.size() " + pdfReportStream.size());
            response.setContentType("application/pdf");
            response.setHeader("Content-Length", String.valueOf(pdfReportStream.size()));
            response.addHeader("Content-Disposition", "inline; filename=jasper.pdf;");
            
            OutputStream responseOutputStream = response.getOutputStream();
            responseOutputStream.write(pdfReportStream.toByteArray());
            //JasperRunManager.runReportToPdfStream(jrxmlInput, responseOutputStream, params, new JREmptyDataSource());
            responseOutputStream.flush();
            responseOutputStream.close();
            pdfReportStream.close();
            logger.debug("Completed Successfully: ");
        } catch (Exception e) {
            logger.debug("Error: ", e);
        }
		//return null;		
		
		/*String userId = (String) session.getAttribute(QADBConstants.USERNAME);
		logger.debug("user id " + userId);

		JRDataSource datasource = null;
		JRDataSource outSampleDatasource = null;
		JRDataSource inSampleDatasource = null;
		String sampleType = "";
		try {
			datasource = claimsAuditAssessmentReportDAO.getClaimsAuditAssesmentReport(auditAssessmentForm);
			outSampleDatasource = datasource;
			logger.debug(" IS start " + auditAssessmentForm.getType());
			auditAssessmentForm.setType("in");
			inSampleDatasource = claimsAuditAssessmentReportDAO.getClaimsAuditAssesmentReport(auditAssessmentForm);
			logger.debug("Set Type is already in getting outsample");
			outSampleDatasource = claimsAuditAssessmentReportDAO.getClaimsAuditAssesmentReport(auditAssessmentForm);
			logger.debug("setting type to in again geeting insample datasource");
			auditAssessmentForm.setType("in");
			inSampleDatasource = claimsAuditAssessmentReportDAO.getClaimsAuditAssesmentReport(auditAssessmentForm);

			sampleType = ClaimsAuditAssessmentReportDAOImpl.type;
			logger.debug(" IS start "+sampleType);
			if(sampleType.equalsIgnoreCase("is")){
				logger.debug("Insample");
				datasource = inSampleDatasource;
			}
			else{
				datasource = outSampleDatasource;
			}
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		}
		Map<String, Object> parameterMap = new HashMap<String, Object>();
		parameterMap.put("datasource", datasource);
		parameterMap.put("JasperCustomSubReportDatasource1", inSampleDatasource);
		parameterMap.put("JasperCustomSubReportDatasource2", outSampleDatasource);
		if(sampleType.equalsIgnoreCase("oos")){
			logger.debug("oos assesment report only");
			modelAndView = new ModelAndView("claimsAuditAssesmentsOos", parameterMap); //single pdf
			}
		else{
			modelAndView = new ModelAndView("claimsAuditAssesments", parameterMap);
		}*/
		logger.debug("*** Exit AuditAssesmentReport method ***");
		//return modelAndView;
		return null;
	}
		

	// Edit code Report

	@RequestMapping(value = "/editCodeReport", method = RequestMethod.GET)
	public ModelAndView editCodeReport(HttpServletRequest request) throws Exception,SQLException{
		   
		   //String userGrp = "('SAMMD','CD','ALL')";
		   
		   String userGrp = authorizationService.getUserGroup(request);
		   
		  // String b[] = {"Contractor","PeopleSoft_ELM_User","onestop_users","qadb-cd-admin_users"};
		   String b[] = {request.getHeader("iv-groups")};
		   /*if((!(((Arrays.toString(b)).replace("[", "")).replace("]", "")).equals("null"))||(b.length != 0)){
	 			if(((Arrays.asList(b)).contains("qadb-superadmin_users"))){
	 				userGrp = "('SAMMD','CD','ALL')";
	 			}
	 			if(((Arrays.asList(b)).contains("qadb-samd-auditor_users"))||((Arrays.asList(b)).contains("qadb-samd-admin_users"))||((Arrays.asList(b)).contains("qadb-samd-readonly_users"))){
	 				userGrp = "('SAMMD','ALL')";
	 			}
	 			if(((Arrays.asList(b)).contains("qadb-cd-auditor_users"))||((Arrays.asList(b)).contains("qadb-cd-admin_users"))||((Arrays.asList(b)).contains("qadb-cd-readonly_users"))){
	 				userGrp = "('CD','ALL')";
	 			}
	 		}*/
		   
		   logger.debug("Grp Gp "+b + " "+userGrp);
		ModelAndView mv = null;
	 	mv = authorizationService.AuthorizeUsersReports("reports_edit_code", request);
		
		List<ErrorDetails> errorCodeList = errorDetailsDao.getErrorCodes("add",userGrp);
        mv.addObject("errorCodes", errorCodeList);
        
        List<OperationalUnit> managers = operationalUnitDAO.getManagers();
        mv.addObject("managers", managers);
        
		return mv;
	}
	
	
	/**
	 * EDIT code pdf report generation
	 * 
	 * @param modelAndView
	 * @param editCodeForm
	 * @return
	 */
	@RequestMapping(value = "/EDIT_Code_Report", method = RequestMethod.GET)
	public ModelAndView editCodeReportPDF(ModelAndView modelAndView,@ModelAttribute("editCodeForm") EditCodeReport editCodeForm,
			HttpSession session) throws Exception {
		logger.debug("*** Entry editCodeReportPDF method ***"+editCodeForm.getFromDate());

		String userId = (String) session.getAttribute(QADBConstants.USERNAME);
		logger.debug("user id " + userId);

		JRDataSource datasource = null;
		try {
			datasource = editCodeReportsDAO.getEditCodeReport(editCodeForm);
		} catch (SQLException e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		Map<String, Object> parameterMap = new HashMap<String, Object>();
		parameterMap.put("datasource", datasource);
		modelAndView = new ModelAndView("EDITcodeReport", parameterMap);
		logger.debug("*** Exit editCodeReportPDF method ***");
		return modelAndView;
	}
	
	// In Sample - Out Of Sample Report

		@RequestMapping(value = "/inSampleOutOfSampleReport", method = RequestMethod.GET)
		public ModelAndView inSampleOutOfSampleReport() {
			return new ModelAndView("reports_InSample_OutSample_report");
		}
	
		/**
		 * In Sample - Out Of Sample report generation
		 * 
		 * @param modelAndView
		 * @param isOosReportForm
		 * @return
		 */
		@RequestMapping(value = "/InSample_OutOfSample_Report", method = RequestMethod.GET)
		public ModelAndView isOosReportPDF(ModelAndView modelAndView,@ModelAttribute("isOosReportForm") InSampleOutOfSampleReport isOosReportForm,
				HttpSession session,HttpServletRequest request) throws Exception {
			logger.debug("*** Entry isOosReportPDF method ***"+isOosReportForm.getMonth());

			String userId = (String) session.getAttribute(QADBConstants.USERNAME);
			logger.debug("user id " + userId);
			
			String b[] = {request.getHeader("iv-groups")};
			if(!(((Arrays.toString(b)).replace("[", "")).replace("]", "")).equals("null")){
				if(((Arrays.asList(b)).contains("qadb-samd-auditor_users"))||((Arrays.asList(b)).contains("qadb-cd-auditor_users"))){
					isOosReportForm.setReportType("AUDITOR");
					String auditorName = request.getHeader("givenname")+ " "+ request.getHeader("sn");
					isOosReportForm.setAuditorName(auditorName);
				}
				if(((Arrays.asList(b)).contains("qadb-samd-admin_users"))||((Arrays.asList(b)).contains("qadb-cd-admin_users"))||((Arrays.asList(b)).contains("qadb-superadmin_users"))){
					isOosReportForm.setReportType("ADMIN");
				}
			}
			
			JRDataSource datasource = null;
			try {
				datasource = inSampleOutOfSampleReportsDAO.getInSampleOutOfSampleReport(isOosReportForm);
			} catch (SQLException e) {
				logger.debug("Exception generated"+e.getMessage());
			}
			Map<String, Object> parameterMap = new HashMap<String, Object>();
			parameterMap.put("datasource", datasource);
			//modelAndView = new ModelAndView("isOosAuditorReport", parameterMap);

			if((isOosReportForm.getReportType()).equalsIgnoreCase("AUDITOR")){
				modelAndView = new ModelAndView("isOosAuditorReport", parameterMap);
			}
			if((isOosReportForm.getReportType()).equalsIgnoreCase("ADMIN")){
				modelAndView = new ModelAndView("isOosAdminReport", parameterMap);
			}
			logger.debug("*** Exit isOosReportPDF method ***");
			return modelAndView;
		}
		
		
	/**
     * Method to fill the Name drop down
     * @param request
     * @return
	 * @throws SQLException 
     */
    @RequestMapping(value = "/nameDropDown", method = RequestMethod.GET)
  	public ModelAndView nameDropDown(HttpServletRequest request) throws Exception,SQLException {
    	logger.debug("***nameDropDown method start***");
    	//String userGrp = "('SAMMD','CD','ALL')";
    	
	   	String userGrp = authorizationService.getUserGroup(request);

    	
    	//String b[] = {"Contractor","PeopleSoft_ELM_User","onestop_users","qadb-cd-admin_users"};
 	   String b[] = {request.getHeader("iv-groups")};
 	   /*if((!(((Arrays.toString(b)).replace("[", "")).replace("]", "")).equals("null"))||(b.length != 0)){
			if(((Arrays.asList(b)).contains("qadb-superadmin_users"))){
				userGrp = "('SAMMD','CD','ALL')";
			}
			if(((Arrays.asList(b)).contains("qadb-samd-auditor_users"))||((Arrays.asList(b)).contains("qadb-samd-admin_users"))||((Arrays.asList(b)).contains("qadb-samd-readonly_users"))){
				userGrp = "('SAMMD','ALL')";
			}
			if(((Arrays.asList(b)).contains("qadb-cd-auditor_users"))||((Arrays.asList(b)).contains("qadb-cd-admin_users"))||((Arrays.asList(b)).contains("qadb-cd-readonly_users"))){
				userGrp = "('CD','ALL')";
			}
		}*/
 	   
 	   	logger.debug("UserGrp "+b + " - "+userGrp);
 	  
    	
    	String subject= request.getParameter("subject");
    	ModelAndView mv = new ModelAndView("reports_nameDropDown_Div");
		
		 List<Associate> claimProcessors = scoresReportsDAO.getClaimProcessors(subject,userGrp);
	     mv.addObject("claimProcessors", claimProcessors);
		
		return mv;
    }
}
