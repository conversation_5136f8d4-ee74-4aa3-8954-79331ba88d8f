package com.carefirst.audit.model;

public class ErrorCodes {

	private String errorCode;
	private String errorName;
	private String errorDescription;
	private String grpSammd;
	private String grpCd;
	private String userGrp;
	private String effectiveStartDate;
	private String effectiveEndDate;
	private String disableStartDate;
	private String disableEndDate;
	private String errorStatus;
	private String status;
	private String userid;
	private String userActyp;
	private String count;
	private String sucessCode;
	private String successMsg;
	
	public String getDisableStartDate() {
		return disableStartDate;
	}
	public void setDisableStartDate(String disableStartDate) {
		this.disableStartDate = disableStartDate;
	}
	public String getDisableEndDate() {
		return disableEndDate;
	}
	public void setDisableEndDate(String disableEndDate) {
		this.disableEndDate = disableEndDate;
	}
	public String getGrpSammd() {
		return grpSammd;
	}
	public void setGrpSammd(String grpSammd) {
		this.grpSammd = grpSammd;
	}
	public String getGrpCd() {
		return grpCd;
	}
	public void setGrpCd(String grpCd) {
		this.grpCd = grpCd;
	}
	public String getUserGrp() {
		return userGrp;
	}
	public void setUserGrp(String userGrp) {
		this.userGrp = userGrp;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getCount() {
		return count;
	}
	public void setCount(String count) {
		this.count = count;
	}
	public String getSucessCode() {
		return sucessCode;
	}
	public void setSucessCode(String sucessCode) {
		this.sucessCode = sucessCode;
	}
	public String getSuccessMsg() {
		return successMsg;
	}
	public void setSuccessMsg(String sucessMsg) {
		this.successMsg = successMsg;
	}
	public String getUserid() {
		return userid;
	}
	public void setUserid(String userid) {
		this.userid = userid;
	}
	public String getUserActyp() {
		return userActyp;
	}
	public void setUserActyp(String userActyp) {
		this.userActyp = userActyp;
	}
	public String getErrorStatus() {
		return errorStatus;
	}
	public void setErrorStatus(String errorStatus) {
		this.errorStatus = errorStatus;
	}
	public String getErrorCode() {
		return errorCode;
	}
	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}
	public String getErrorName() {
		return errorName;
	}
	public void setErrorName(String errorName) {
		this.errorName = errorName;
	}
	public String getErrorDescription() {
		return errorDescription;
	}
	public void setErrorDescription(String errorDescription) {
		this.errorDescription = errorDescription;
	}
	public String getEffectiveStartDate() {
		return effectiveStartDate;
	}
	public void setEffectiveStartDate(String effectiveStartDate) {
		this.effectiveStartDate = effectiveStartDate;
	}
	public String getEffectiveEndDate() {
		return effectiveEndDate;
	}
	public void setEffectiveEndDate(String effectiveEndDate) {
		this.effectiveEndDate = effectiveEndDate;
	}
	
	
}
