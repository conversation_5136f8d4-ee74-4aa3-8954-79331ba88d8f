package com.carefirst.qadb.dao;

import java.sql.SQLException;
import java.util.List;

import com.carefirst.audit.model.RootCause;

import net.sf.jasperreports.engine.JRDataSource;

public interface RootCauseDAO {

	public RootCause saveRootCause(RootCause rootCause) throws SQLException;

	public List<RootCause> searchRootCause(RootCause rootCauseSearch) throws SQLException;

	public RootCause getRootCause(RootCause rootCauseTO) throws SQLException;
	
	public JRDataSource searchRootCauseList(RootCause rootCause) throws SQLException;

}
