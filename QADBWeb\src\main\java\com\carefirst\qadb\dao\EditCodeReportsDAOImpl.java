package com.carefirst.qadb.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.sql.DataSource;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import oracle.jdbc.OracleTypes;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import com.carefirst.audit.model.EditCodeReport;
import com.carefirst.qadb.constant.QADBConstants;

public class EditCodeReportsDAOImpl implements EditCodeReportsDAO {
	
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);
	
	@Autowired
	DataSource dataSource;

	@Override
	public JRDataSource getEditCodeReport(EditCodeReport editCodeForm)
			throws SQLException {
		
		logger.debug("***getEditCodeReport entry***");
		String getEditCodeReport = QADBConstants.REPORTS_EDIT_CODE_REPORT_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getEditCodeReport);
		List<EditCodeReport> editCodeList = new ArrayList<EditCodeReport>();
		
		try {
			
			callableStatment.setNull(1, java.sql.Types.VARCHAR);
			callableStatment.setString(2, editCodeForm.getErrorCodeId());
			callableStatment.setInt(3, Integer.parseInt(editCodeForm.getUnitManagerName()));
			callableStatment.setString(4, editCodeForm.getFromDate());
			callableStatment.setString(5, editCodeForm.getToDate());
			callableStatment.setNull(6, java.sql.Types.VARCHAR);
		
			callableStatment.registerOutParameter(7, OracleTypes.VARCHAR);

			callableStatment.registerOutParameter(8, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(9, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(10, OracleTypes.VARCHAR);
			
			callableStatment.executeUpdate();
			
			
			ResultSet  rs =   (ResultSet) callableStatment.getObject(8);
			
			while(rs.next()){
				logger.debug("***Entry"+(rs.getString("AUDIT_ID"))); 
				
				EditCodeReport editCodeEO = new EditCodeReport();
				 
				editCodeEO.setErrorCodeId(editCodeForm.getErrorCodeId());
				editCodeEO.setUnitManagerName(callableStatment.getString(7));
				
				if(null != (rs.getString("EDIT/ODL/PEND_CODE"))){
					editCodeEO.setEditCode((rs.getString("EDIT/ODL/PEND_CODE")).toString());
				}
				else{
					editCodeEO.setEditCode("");
				}
				if(null != (rs.getString("ASSOCIATE"))){
					editCodeEO.setAssociateName((rs.getString("ASSOCIATE")).toString());
				}
				else{
					editCodeEO.setAssociateName("");
				}
				if(null != (rs.getString("DCN"))){
					editCodeEO.setDcn((rs.getString("DCN")).toString());
				}
				else{
					editCodeEO.setDcn("");
				}
				if(null != (rs.getString("PROCESSED_DATE"))){
					editCodeEO.setProcessedDate((rs.getString("PROCESSED_DATE")).toString());
				}
				else{
					editCodeEO.setProcessedDate("");
				}
				if(null != (rs.getString("REASON_FOR_ERROR"))){
					editCodeEO.setErrorReason((rs.getString("REASON_FOR_ERROR")).toString());
				}
				else{
					editCodeEO.setErrorReason("");
				}
				
				editCodeList.add(editCodeEO);
				logger.debug("***Exit");
			}
			
			
			rs.close();
			
			callableStatment.close();
			 
			conn.close();
			
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		
		JRDataSource ds = new JRBeanCollectionDataSource(editCodeList);	
		
		logger.debug("***getEditCodeReport entry***");
		// Return the wrapped collection
		return ds;
		
	}
	
}
