<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@page import="com.carefirst.audit.model.AuditSearch"%>
<head>
<link href="webResources/css/qadb.css" rel="stylesheet">

<script type="text/javascript" src="webResources/js/metro.min.js"></script>
<!-- Local JavaScript -->

<link href="webResources/css/iconFont.css" rel="stylesheet">
<link href="webResources/css/docs.css" rel="stylesheet">
<link href="webResources/js/prettify/prettify.css" rel="stylesheet">

<!-- Load JavaScript Libraries -->
<script type="text/javascript" src="webResources/js/datep.js"></script>
<script src="webResources/js/jquery/jquery.min.js"></script>
<script src="webResources/js/jquery/jquery.widget.min.js"></script>


<script type="text/javascript" src="webResources/js/jquery.min.js"></script>
<script type="text/javascript" src="webResources/js/jquery-ui.js"></script> 
<script type="text/javascript" src="webResources/js/qadb.js"></script>

<script src="webResources/js/jquery/jquery.dataTables.js"></script>
<link href="webResources/css/qadb.css" rel="stylesheet">
<link href="webResources/css/jquery-ui.css" rel="stylesheet">
<style>
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
	margin-right: auto;
	height: 275%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}
.hidden
        {
            display: none;
        }
.ui-widget {
	font-family: Verdana,Arial,sans-serif;
	font-size: 1.1em;
}
.ui-widget .ui-widget {
	font-size: 1em;
}
.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
	font-family: Verdana,Arial,sans-serif;
	font-size: 1em;
}
.ui-widget-content {
	border: 1px solid #aaaaaa;
	background: #ffffff url() 50% 50% repeat-x;
	color: #222222;
}
.ui-widget-content a {
	color: #222222;
}
/* .ui-widget-header {
	border: 1px solid #aaaaaa;
	background:#2573ab  url(images/ui-bg_highlight-soft_75_cccccc_1x100.png) 50% 50% repeat-x;
	color: #e6f5fc;
	font-weight: bold;
} */
.ui-widget-header a {
	color: #222222;
}
.ui-datepicker-trigger{
background-image: url("webResources/images/Forms/Icn_Date_Picker.png");
height: 36px;
width: 36px;
background-color:white;
}
.ui-icon-circle-triangle-e{
background-image: url("webResources//Navbar/Icn_Right_Arrow.png");
}
.ui-icon-circle-triangle-w{
background-image: url("webResources/images/skip_backward.png");
}
.headerA {
	background: url('webResources/images/Forms/Icn_Accordion_Collapse.png')
		no-repeat;
	background-position: right 0px;
	cursor: pointer;
}

.collapsed .headerA {
	background-image:
		url('webResources/images/Forms/Icn_Accordion_Expand.png');
}

.contentA {
	height: auto;
	min-height: 100px;
	overflow: hidden;
	transition: all 0.3s linear;
	-webkit-transition: all 0.3s linear;
	-moz-transition: all 0.3s linear;
	-ms-transition: all 0.3s linear;
	-o-transition: all 0.3s linear;
	background-color: #fafafa;
}

.collapsed .contentA {
	min-height: 0px;
	height: 0px;
}        
</style>



<title><spring:message code="audit.search.title" /></title>
</head>
<body >
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container2">

		<!-- Sidebar Start-->
		<div id="left-sidebar">
			<h2 style="color: white ;padding-top: 32px;"><spring:message code="audit.search.leftNav.heading" /></h2>
			<h3 style="color: white"><spring:message code="audit.search.leftNav.text" /></h3>

			<div class="grid fluid">
				<div class="row">
					<div class="span4">

						<nav class="sidebar light">
							<ul>



								<li><a href="searchBasic"><img alt="BasicSearch"
										src="webResources/images/Tabs/Search/Icn_Basic_Search.png">&nbsp;&nbsp;&nbsp;&nbsp;
										<spring:message code="audit.search.leftNav.heading1" /></a></li>

								<li class="active"><a href="searchAdv"><img
										alt="AdvanceSearch"
										src="webResources/images/Tabs/Search/Icn_Advanced_Search.png">
										<spring:message code="audit.search.leftNav.heading2" /></a></li>


							</ul>
						</nav>

					</div>


				</div>
			</div>
		</div>
		<!-- Sidebar End-->


		<div id="content-container" style="padding-left: 40px">
		<form:form id="searchAdvForm" name="searchAdvForm"  method="GET" commandName="searchAdvForm" action="searchAdvRes" style="width:700px">   
			<div style="padding: 10px 0px 0px 0px;">
				<span class="bread1"><spring:message code="audit.new.bread1" /> &gt; </span><span class="bread2">
					<spring:message code="audit.search.leftNav.heading" /></span>
			</div>
			<br>
			<div class="panelContainer">
				<div id="dcnDetails" class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
			 			<span style="color: #0070c0; font-size: 16px"><spring:message code="audit.new.associate" /></span>
			 		</div>
				<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
				<table class="tableForm" >
					<tr>
						<td style="padding-right: 66px;"><spring:message code="audit.search.associate.name" /></td>
						<td><div class="input-control select" >
							 <select id="associateName" name="associateName" style="width: 250px;">
							 		<option selected value=""><spring:message
																code="audit.search.select.associate" /></option>
									<c:forEach items="${claimProcessors}" var="claimProcessors">
													<option value="${claimProcessors.associateId}"
														 ${claimProcessors.associateId== advSearch.associateName ? 'selected="selected"' : ''}>${claimProcessors.associateName}</option>
									</c:forEach>
								</select>
							</div>
						</td>
					</tr>
					<tr>
						<td><spring:message code="audit.search.employee" /></td>
						<td><div class="input-control select" >
							 <select id="empNo" name="empNo" style="width: 200px;">
							 		<option selected value=""><spring:message
																code="audit.search.select.employee" /></option>
									<c:forEach items="${facetsIds}" var="facetsIds">
													<option value="${facetsIds.facetsId}"
														 ${facetsIds.facetsId== advSearch.empNo ? 'selected="selected"' : ''}>${facetsIds.facetsId}</option>
									</c:forEach>
								</select>
							</div>
						</td>
					</tr>
					
				</table>
			</div>
			</div>	
			</div>
			
			<div class="panelContainer">
				<div id="dcnDetails" class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
					 <span style="color: #0070c0; font-size: 16px"><spring:message code="audit.search.results.processed.date" /></span>
					 </div>
				<div class="contentA" style="padding-top: 10px;padding-left: 7px;">	 
						<table class="tableForm">
							<tr>
								<td><spring:message code="audit.search.processed.date.from" /></td>
								<td><div class="input-control text" >
			                                 <input id="datepick1" size="50" name="processedDateFrom" value="${advSearch.processedDateFrom}"/>
											
			                                </div>
								</td>
							</tr>
							<tr>
								<td><spring:message code="audit.search.processed.date.to" /></td>
								<td><div class="input-control text" >
			                                 <input id="datepick2" size="50" name="processedDateTo" value="${advSearch.processedDateTo}"/>
											
			                                </div>
								</td>
							</tr>
			
						</table>
				</div>
				</div>
				</div>
				<div class="panelContainer">
				<div id="dcnDetails" class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
			 			<span style="color: #0070c0; font-size: 16px"><spring:message code="audit.new.claim.dcn" /></span>
					</div>
					<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
							<table class="tableForm">
								<tr>
									<td><spring:message code="audit.new.claim.dcn" /></td>
									<td><div class="input-control text">
											<input type="text"  name="dcn" maxlength="12" value="${advSearch.dcn}"/>
										</div>
									</td>
								</tr>
				
				
							</table>
					</div>
					</div>
					</div>
					
					<div class="panelContainer">
				<div id="dcnDetails" class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
						<span style="color: #0070c0; font-size: 16px"><spring:message code="audit.search.results.auditor" /></span>
					</div>
					<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
						<table class="tableForm">
							<tr>
								<td><spring:message code="audit.search.auditor.name" /></td>
								<td><div class="input-control select">
									
										<select name="auditorName">
											<option selected value=""><spring:message
																code="audit.search.select.auditor" /></option>
											<c:forEach items="${auditorsList}" var="auditorsList">
												<option value="${auditorsList.auditorName}"
																	${auditorsList.auditorName == advSearch.auditorName ? 'selected="selected"' : ''}>${auditorsList.auditorName}</option>
											</c:forEach>
										</select>
									</div>
								
								</td>
							</tr>
							<tr>
								<td><span style="color: #0070c0; font-size: 16px"><spring:message code="audit.search.results.audit.date" /></span></td>
							</tr>
							<tr>
								<td><spring:message code="audit.search.processed.date.from" /></td>
								<td><div class="input-control text" >
			                                 <input id="datepick3" size="50"  name="auditDateFrom" value="${advSearch.auditDateFrom}"/>
											
			                                </div>
								</td>
							</tr>
							<tr>
								<td><spring:message code="audit.search.processed.date.to" /></td>
								<td><div class="input-control text" >
			                                 <input id="datepick4" size="50" name="auditDateTo" value="${advSearch.auditDateTo}"/>
											
			                                </div>
								</td>
							</tr>
						</table>
					</div>
					</div>
					</div>
					<div class="panelContainer">
				<div id="dcnDetails" class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 16px">
								<spring:message code="audit.search.miscellaneous.filters" /></span>
							</div>
						<div class="contentA" style="padding-top: 10px;padding-left: 7px;">	
							<table class="tableForm">
								<tr>
									<td style="padding-right:115px"><spring:message code="audit.new.process.type" /></td>
									<td>
										<div class="input-control select">
											<%-- <form:select path="processType" items="${processTypes}"
															style="width: 200px; font-size: 14px; border-color: #919191" /> --%>
															
											<select name="processType"
															style="width: 200px; font-size: 14px; border-color: #919191">
															<option  selected value=""><spring:message code="audit.new.select.process.type" /></option>
															<c:forEach items="${processTypes}" var="processTypes">
																<option value="${processTypes.processTypId}"
																	${processTypes.processTypId == advSearch.processType ? 'selected="selected"' : ''}>${processTypes.processtype}</option>
															</c:forEach>
														</select>
										</div>
									</td>
								</tr>
								<tr>
									<td><spring:message code="audit.new.error.code" /></td>
									<td>
										<div class="input-control select">
											<%-- <form:select path="errorCode" items="${errorCodes}"
															style="width: 200px; font-size: 14px; border-color: #919191" /> --%>
											
											<select name="errorCode"  style="width: 200px; font-size: 14px; border-color: #919191" >
											<option  selected value=""><spring:message code="audit.search.select.error.code" /></option>
														<optgroup label="Code      --- Name">
														<c:forEach items="${errorCodes}" var="errorCodes">
															<option value="${errorCodes.errorCodeId}" 
																${errorCodes.errorCodeId == advSearch.errorCode ? 'selected="selected"' : ''}>${errorCodes.errorCodeId}. ${errorCodes.errorCode}</option>
														</c:forEach>
														</optgroup>
														</select>
											
											
										</div>
									</td>
								</tr>
								<tr>
									<td><spring:message code="audit.new.monetary.penalty.interest.type" />
									</td>
									<td>
										<div class="input-control select">
											<%-- <form:select path="piType" items="${piTypesL}"
															style="width: 200px; font-size: 14px; border-color: #919191" /> --%>
															
											<select  name="piType" style="width: 200px; font-size: 14px; border-color: #919191">
																<option  selected value=""><spring:message code="audit.search.select.pi.type" /></option>
											<c:forEach items="${piTypesL}" var="piTypesL">
												<option value="${piTypesL.piTypeId}"
												 ${piTypesL.piTypeId== advSearch.piType ? 'selected="selected"' : ''}>${piTypesL.penaltyInterestType}</option> 
											</c:forEach>
											
											</select>	
										</div>
									</td>
								</tr>
								<tr>
									<td><spring:message code="audit.new.primary.pga.special.audits" />
									</td>
									<td>
										<div class="input-control select">
											<%-- <form:select path="priPGA" items="${priPGALists}"
															style="width: 200px; font-size: 14px; border-color: #919191" /> --%>
											<select id="priPGA" name="priPGA" onchange="getSecPGADropdown();"
															style="width: 330px; font-size: 14px; border-color: #919191">
															<option  selected value=""><spring:message code="audit.new.select.primary.pga" /></option>
															<c:forEach items="${priPGALists}" var="priPGAList">
																<option value="${priPGAList.pPgaId}"
																	 ${priPGAList.pPgaId== advSearch.priPGA ? 'selected="selected"' : ''}>${priPGAList.priPGA}</option>
															</c:forEach>
														</select>			
											
										</div>
									</td>
								</tr>
								<tr>
									<td><spring:message code="audit.new.secondary.pga.special.audits" />
									</td>
									<td>
										<div class="input-control select" id="secPGA" >
											<jsp:include page="../jsp/audit_general_secPGADropDown_Div.jsp"></jsp:include>
												<%-- <form:select path="secPGA" items="${secPGALists}"
															style="width: 200px; font-size: 14px; border-color: #919191" /> --%>
															
												<%-- <select name="secPGA"
															style="width: 300px; font-size: 14px; border-color: #919191">
															<option  selected value=""><spring:message code="audit.new.select.secondary.pga" /></option>
															<c:forEach items="${secPGALists}" var="secPGAList">
																<option value="${secPGAList.sPgaId}"
																	 ${secPGAList.sPgaId== advSearch.secPGA ? 'selected="selected"' : ''}>${secPGAList.secPGA}</option>
															</c:forEach>
														</select> --%>
												
											
										</div>
									</td>
								</tr>
								<tr>
									<td><spring:message code="audit.new.claim.member.id" /></td>
									<td>
										<div class="input-control text">
											<input type="text" placeholder="Member ID" name="memberID" value="${advSearch.memberID}" />
										</div>
									</td>
								</tr>
								<tr>
									<td width="30%" ><spring:message
										code="reports.scores.reportType.e2e"></spring:message></td>
									<td width="70%">
									<div class="input-control select">
										<select name="e2e">
											<option  selected value=""><spring:message code="audit.search.select.e2e.type" /></option>
											<!-- <option value="ALL">All</option>
											<option value="Y">E2E</option>
											<option value="N">Non-E2E</option> -->
											<c:forEach items="${e2e}" var="e2e">
												<option value="${e2e.key}"
														 ${e2e.key == advSearch.e2e ? 'selected="selected"' : ''}>${e2e.value}</option>
											</c:forEach>
										</select>
									</div>
								</td>
								</tr>
								<tr>
									<td width="30%" ><spring:message
										code="audit.search.sample"></spring:message></td>
									<td width="70%">
									<div class="input-control select">
										<select name="oos">
											<option  selected value=""><spring:message code="audit.search.select.sample.type" /></option>
											<c:forEach items="${samples}" var="sample">
												<option value="${sample.key}"
														 ${sample.key == advSearch.oos ? 'selected="selected"' : ''}>${sample.value}</option>
											</c:forEach>
										</select>
									</div>
								</td>
								</tr>
								<tr>
									<td width="30%" ><spring:message
										code="audit.new.platform"></spring:message></td>
									<td width="70%">
									<div class="input-control select">
										<select name="platform">
											<option  selected value=""><spring:message code="audit.search.select.platform.type" /></option>
											<c:forEach items="${platforms}" var="platforms">
												<option value="${platforms.key}"
														 ${platforms.key == advSearch.platform ? 'selected="selected"' : ''}>${platforms.value}</option>
											</c:forEach>
										</select>
									</div>
								</td>
								</tr>
							</table>
					</div>
					</div>
					</div>
			<div style="width: 750px">
				<div style="float: right">
					<button class="button inverse"><spring:message code="audit.search.reset" /></button>
					<button style="background-color: #298fd8"
						class="button default"><spring:message code="audit.search.search" /></button>
				</div>
			</div>
			</form:form>
			<br>
			<c:if test="${advSearchResult!=null }">
			
			 <span style="color: #0070c0;font-size:16px"><spring:message code="audit.search.results" /></span>
			<div class="line-separator"></div>
			
			<!-- <div id="welcomeDiv" style="display:none;"> -->
			
			 <table class="table striped hovered dataTable" id="searchDatatable"
					 width="700px">
					<thead>
						<tr style="height:30px" >

							<td class="text-left" font-size="25px"><spring:message code="audit.search.results.audit.id" /></td>
							<td class="text-left" font-size="25px"  style="padding-left:5px"><spring:message code="audit.new.claim.dcn" /></td>
							<td class="text-left" font-size="25px"><spring:message code="audit.new.associate" /></td>
							<td class="text-left" font-size="25px"><spring:message code="audit.search.results.processed.date" /></td>
							<td class="text-left" font-size="25px"><spring:message code="audit.search.results.auditor" /></td>
							<td class="text-left" font-size="25px"><spring:message code="audit.search.results.audit.date" /></td>


						</tr>
					</thead>

					<tbody>
				
					<c:forEach items="${advSearchResult}"
							var="rs">
						<tr style="height: 30px">
							<td>${rs.auditId}</td>
							<td style="padding-left:5px">${rs.dcn}</td>
							<td>${rs.associateName}</td>
							<td>${rs.processedDateFrom}</td>
							<td>${rs.auditorName}</td>
							<td>${rs.auditDateFrom}</td>
						</tr>
						</c:forEach>
					
					</tbody>


				</table>
				</c:if>

		
			<script>
				$(function() {
					$('#searchDatatable').dataTable({
										
										
					"columns" : [
					
					{
           
            sClass: "hidden"
        }, 
					{
					"render": function(data, type, full, meta) {
					/* return '<a href="'+full[0]+'"><u>'+data+'<u></a>'; */
					return '<a href="editAuditGeneral?id='+full[0]+'&dcnNo='+data+'"><u>'+data+'<u></a>';
                   
            }
            }, {}, {}, {}, {} ]
									});
				});
			</script>
		
		
		<script type="text/javascript">
		$(function() {
		$('.headerA').click(function() {
			$(this).closest('.containerA').toggleClass('collapsed');
		});

	});
		
		
</script>

<script type="text/javascript">
		
	$("#datepick1").datepicker({
	showOn:"button",
    onSelect: function(selected) {
      $("#datepick2").datepicker("option","minDate", selected)
    },
	});
	$("#datepick2").datepicker({
	showOn:"button",
    onSelect: function(selected) {
      $("#datepick1").datepicker("option","maxDate", selected)
    },
	});
	
	$("#datepick3").datepicker({
	showOn:"button",
    onSelect: function(selected) {
      $("#datepick4").datepicker("option","minDate", selected)
    },
	});
	$("#datepick4").datepicker({
	showOn:"button",
    onSelect: function(selected) {
      $("#datepick3").datepicker("option","maxDate", selected)
    },
	});
	jQuery(function($){
		$("#datepick1").mask("99/99/9999",{placeholder:"MM/DD/YYYY"});
		$("#datepick2").mask("99/99/9999",{placeholder:"MM/DD/YYYY"});
		$("#datepick3").mask("99/99/9999",{placeholder:"MM/DD/YYYY"});
		$("#datepick4").mask("99/99/9999",{placeholder:"MM/DD/YYYY"});
	});	
		</script>	
			
			<script type="text/javascript">
				function getSecPGADropdown(val) {
					if (val == null) {
						var priId = document.getElementById("priPGA").value;
					} 
					var url = "secPGADropDown";
					console.log("inptA--->" + priId);
					$.ajax({
						type : "GET",
						url : url,
						dataType : "html",
						data : {
							'priId' : priId
						},
						success : function(response) {
							$("#secPGA").html(response);
						},
						error : function() {

						}

					});
				}
			</script>
			
		</div>

	</div>

	<jsp:include page="footer.jsp"></jsp:include>

</body>

</html>
