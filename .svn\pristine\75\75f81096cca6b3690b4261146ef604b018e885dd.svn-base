<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="tree-template" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="template.type" value="columnar"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<style name="Title" forecolor="#000000" fontName="Times New Roman" fontSize="50" isBold="false" pdfFontName="Times-Bold"/>
	<style name="SubTitle" forecolor="#666666" fontName="Times New Roman" fontSize="18" isBold="false" pdfFontName="Times-Roman"/>
	<style name="Column header" forecolor="#666666" fontName="Times New Roman" fontSize="14" isBold="true" pdfFontName="Times-Roman"/>
	<style name="Detail" mode="Transparent" fontName="Times New Roman" pdfFontName="Times-Roman"/>
	<style name="Row" mode="Transparent" fontName="Times New Roman" pdfFontName="Times-Roman">
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{REPORT_COUNT}%2 == 0]]></conditionExpression>
			<style mode="Opaque" backcolor="#F0EFEF"/>
		</conditionalStyle>
	</style>
	<field name="id" class="java.lang.Long"/>
	<field name="name" class="java.lang.String"/>
	<field name="price" class="java.lang.Double"/>
	<field name="description" class="java.lang.String"/>
	<group name="Group1">
		<groupExpression><![CDATA[(int)($V{REPORT_COUNT}/15)]]></groupExpression>
		<groupHeader>
			<band height="31">
				<frame>
					<reportElement mode="Opaque" x="0" y="7" width="555" height="24" forecolor="#B89F7D" backcolor="#9DB1B8"/>
				</frame>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="6"/>
		</groupFooter>
	</group>
	<group name="Group2">
		<groupExpression><![CDATA[(int)($V{REPORT_COUNT}/5)]]></groupExpression>
		<groupHeader>
			<band height="24"/>
		</groupHeader>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="132" splitType="Stretch">
			<image>
				<reportElement x="2" y="0" width="118" height="132"/>
				<imageExpression class="java.lang.String"><![CDATA["tree1.png"]]></imageExpression>
			</image>
			<staticText>
				<reportElement style="Title" x="120" y="10" width="435" height="93"/>
				<textElement>
					<font size="80" isBold="false"/>
				</textElement>
				<text><![CDATA[Tree Co.]]></text>
			</staticText>
			<staticText>
				<reportElement style="SubTitle" x="336" y="103" width="217" height="29"/>
				<textElement>
					<font size="22" isBold="false"/>
				</textElement>
				<text><![CDATA[A Demo Page]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="91" splitType="Stretch">
			<image>
				<reportElement x="2" y="0" width="56" height="80"/>
				<imageExpression class="java.lang.String"><![CDATA["tree2.png"]]></imageExpression>
			</image>
			<line>
				<reportElement positionType="FixRelativeToBottom" x="2" y="90" width="551" height="1"/>
			</line>
			<textField>
				<reportElement x="179" y="20" width="100" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="280" y="20" width="100" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Double"><![CDATA[$F{price}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="380" y="20" width="150" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.String"><![CDATA[$F{description}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="79" y="20" width="100" height="20"/>
				<textElement/>
				<textFieldExpression class="java.lang.Long"><![CDATA[$F{id}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="7" splitType="Stretch">
			<line>
				<reportElement positionType="FixRelativeToBottom" x="0" y="3" width="555" height="1"/>
				<graphicElement>
					<pen lineWidth="0.5" lineColor="#999999"/>
				</graphicElement>
			</line>
		</band>
	</columnFooter>
	<pageFooter>
		<band height="13" splitType="Stretch">
			<frame>
				<reportElement mode="Opaque" x="2" y="0" width="555" height="13" forecolor="#D0B48E" backcolor="#9DB1B8"/>
				<textField evaluationTime="Report">
					<reportElement style="Column header" x="513" y="-1" width="40" height="13" forecolor="#FFFFFF"/>
					<textElement verticalAlignment="Middle">
						<font size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[" " + $V{PAGE_NUMBER}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement style="Column header" x="433" y="-1" width="80" height="13" forecolor="#FFFFFF"/>
					<textElement textAlignment="Right" verticalAlignment="Middle">
						<font size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA["Page "+$V{PAGE_NUMBER}+" of"]]></textFieldExpression>
				</textField>
				<textField pattern="EEEEE dd MMMMM yyyy">
					<reportElement style="Column header" x="2" y="0" width="197" height="13" forecolor="#FFFFFF"/>
					<textElement verticalAlignment="Middle">
						<font size="10" isBold="false"/>
					</textElement>
					<textFieldExpression class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
				</textField>
			</frame>
		</band>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
