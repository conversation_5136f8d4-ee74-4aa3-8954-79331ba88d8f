package com.carefirst.audit.model;


public class OperationalUnit {
	
	private String userId;
	private String userActyp;
	private String searchType;
	private String pagetype;
	
	private String unitId;
	
	private String unitName;
	private String status;
	
	private String effectiveDateStart;
	private String effectiveDateEnd;
	
	private String operationId;
	private String operation;
	private String divisionId;
	private String division;
	private String directorId;
	private String director;
	private String managerId;
	private String manager;
	private String supervisorId;
	private String supervisor;
	
	private String locationId;
	private String location;
	
	private String assoId;
	private String assoStartDate;
	private String assoEndDate;
	private String assoName;
	
	
	private String successMsg;
	private String sucessCode;
	
	 
	
	public String getAssoId() {
		return assoId;
	}
	public void setAssoId(String assoId) {
		this.assoId = assoId;
	}
	public String getAssoStartDate() {
		return assoStartDate;
	}
	public void setAssoStartDate(String assoStartDate) {
		this.assoStartDate = assoStartDate;
	}
	public String getAssoEndDate() {
		return assoEndDate;
	}
	public void setAssoEndDate(String assoEndDate) {
		this.assoEndDate = assoEndDate;
	}
	public String getAssoName() {
		return assoName;
	}
	public void setAssoName(String assoName) {
		this.assoName = assoName;
	}
	public String getPagetype() {
		return pagetype;
	}
	public void setPagetype(String pagetype) {
		this.pagetype = pagetype;
	}
	public String getSearchType() {
		return searchType;
	}
	public void setSearchType(String searchType) {
		this.searchType = searchType;
	}
	public String getSuccessMsg() {
		return successMsg;
	}
	public void setSuccessMsg(String successMsg) {
		this.successMsg = successMsg;
	}
	public String getSucessCode() {
		return sucessCode;
	}
	public void setSucessCode(String sucessCode) {
		this.sucessCode = sucessCode;
	}
	public String getUserActyp() {
		return userActyp;
	}
	public void setUserActyp(String userActyp) {
		this.userActyp = userActyp;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getUnitId() {
		return unitId;
	}
	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}
	
	public String getUnitName() {
		return unitName;
	}
	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getEffectiveDateStart() {
		return effectiveDateStart;
	}
	public void setEffectiveDateStart(String effectiveDateStart) {
		this.effectiveDateStart = effectiveDateStart;
	}
	public String getEffectiveDateEnd() {
		return effectiveDateEnd;
	}
	public void setEffectiveDateEnd(String effectiveDateEnd) {
		this.effectiveDateEnd = effectiveDateEnd;
	}
	public String getOperationId() {
		return operationId;
	}
	public void setOperationId(String operationId) {
		this.operationId = operationId;
	}
	public String getOperation() {
		return operation;
	}
	public void setOperation(String operation) {
		this.operation = operation;
	}
	public String getDivisionId() {
		return divisionId;
	}
	public void setDivisionId(String divisionId) {
		this.divisionId = divisionId;
	}
	public String getDivision() {
		return division;
	}
	public void setDivision(String division) {
		this.division = division;
	}
	public String getDirectorId() {
		return directorId;
	}
	public void setDirectorId(String directorId) {
		this.directorId = directorId;
	}
	public String getDirector() {
		return director;
	}
	public void setDirector(String director) {
		this.director = director;
	}
	public String getManagerId() {
		return managerId;
	}
	public void setManagerId(String managerId) {
		this.managerId = managerId;
	}
	public String getManager() {
		return manager;
	}
	public void setManager(String manager) {
		this.manager = manager;
	}
	public String getSupervisorId() {
		return supervisorId;
	}
	public void setSupervisorId(String supervisorId) {
		this.supervisorId = supervisorId;
	}
	public String getSupervisor() {
		return supervisor;
	}
	public void setSupervisor(String supervisor) {
		this.supervisor = supervisor;
	}
	public String getLocationId() {
		return locationId;
	}
	public void setLocationId(String locationId) {
		this.locationId = locationId;
	}
	public String getLocation() {
		return location;
	}
	public void setLocation(String location) {
		this.location = location;
	}
	
	
}
