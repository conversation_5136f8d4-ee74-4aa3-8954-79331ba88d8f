<!DOCTYPE HTML><%@page language="java" contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<html>	
										<c:choose>
											<c:when test="${calculatedPi!=null }">
											<input type="hidden" name="piCalculated" id="piCalculated" value="${calculatedPi}"/>
												<%-- <form:input path="pi" value="${calculatedPi}" disabled="disabled" style="border:0px" /> --%>
												<input type="text" id="pi" name="pi" value="${calculatedPi}" disabled="disabled" style="border:0px" />
											</c:when>
											<c:when test="${piCalculated!=null }">
											<input type="hidden" name="piCalculated" id="piCalculated" value="${piCalculated}"/>
												<%-- <form:input path="pi" value="${calculatedPi}" disabled="disabled" style="border:0px" /> --%>
												<input type="text" id="pi" name="pi" value="${piCalculated}" disabled="disabled" style="border:0px" />
											</c:when>
											<c:when test="${ePiChk != null}">
												<%-- <form:input path="pi" value="${ePi}" disabled="disabled" style="border:0px" /> --%>
												<input type="text" id="pi" name="pi" value="${ePi}" disabled="disabled" style="border:0px" />
											</c:when>
											<c:otherwise>
												<c:out value="${calculatedPi}"></c:out>
												<input style="border: none; width: 320px" type="text" name="pi" placeholder="Calculate PI" disabled="disabled" id="input">
											</c:otherwise>
										</c:choose>
										<c:choose>
											<c:when test="${ePiChk != null}">
												<input style="width: 80px; float: right; height: 25px" type="button" value="Calculate" onclick="div_show();" id="calPiButton"></input>
											</c:when>
											<c:when test="${piCalculated!=null }">
												<input style="width: 80px; float: right; height: 25px" type="button" value="Calculate" onclick="div_show();" id="calPiButton"></input>
											</c:when>
											<c:otherwise>
												<input style="width: 80px; float: right; height: 25px" type="button" value="Calculate" onclick="div_show();" disabled="disabled" id="calPiButton"></input>
											</c:otherwise>
										</c:choose>
</html>						