<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="scriptlet" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802"  whenResourceMissingType="Empty" isSummaryWithPageHeaderAndFooter="true" isSummaryNewPage="true" >
	<property name="com.jasperassistant.designer.Grid" value="false"/>
	<property name="com.jasperassistant.designer.SnapToGrid" value="false"/>
	<property name="com.jasperassistant.designer.GridWidth" value="12"/>
	<property name="com.jasperassistant.designer.GridHeight" value="12"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="net.sf.jasperreports.awt.ignore.missing.font" value="true"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="month" class="java.lang.String">
		<fieldDescription><![CDATA[month]]></fieldDescription>
	</field>
	<field name="associateName" class="java.lang.String">
		<fieldDescription><![CDATA[associateName]]></fieldDescription>
	</field>
	<field name="empNO" class="java.lang.String">
		<fieldDescription><![CDATA[empNO]]></fieldDescription>
	</field>
	<field name="supervisor" class="java.lang.String">
		<fieldDescription><![CDATA[supervisor]]></fieldDescription>
	</field>
	
	<field name="isSno" class="java.lang.String">
		<fieldDescription><![CDATA[isSno]]></fieldDescription>
	</field>
	<field name="isDcn" class="java.lang.String">
		<fieldDescription><![CDATA[isDcn]]></fieldDescription>
	</field>
	<field name="isMemId" class="java.lang.String">
		<fieldDescription><![CDATA[isMemId]]></fieldDescription>
	</field>
	<field name="isPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isPaid]]></fieldDescription>
	</field>
	<field name="isUnderPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isUnderPaid]]></fieldDescription>
	</field>
	<field name="isOverPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isOverPaid]]></fieldDescription>
	</field>
	<field name="isHighDoller" class="java.lang.String">
		<fieldDescription><![CDATA[isHighDoller]]></fieldDescription>
	</field>
	<field name="isAuditType" class="java.lang.String">
		<fieldDescription><![CDATA[isAuditType]]></fieldDescription>
	</field>
	<field name="isMockAudit" class="java.lang.String">
		<fieldDescription><![CDATA[isMockAudit]]></fieldDescription>
	</field>
	<field name="reportType" class="java.lang.String">
		<fieldDescription><![CDATA[reportType]]></fieldDescription>
	</field>
	<field name="isAdjRequired" class="java.lang.String">
		<fieldDescription><![CDATA[isAdjRequired]]></fieldDescription>
	</field>
	<field name="isProcessOn" class="java.lang.String">
		<fieldDescription><![CDATA[isProcessOn]]></fieldDescription>
	</field>
	<field name="isErrorCode" class="java.lang.String">
		<fieldDescription><![CDATA[isErrorCode]]></fieldDescription>
	</field>
	<field name="isMonetary" class="java.lang.String">
		<fieldDescription><![CDATA[isMonetary]]></fieldDescription>
	</field>
	<field name="isProcedural" class="java.lang.String">
		<fieldDescription><![CDATA[isProcedural]]></fieldDescription>
	</field>
	<field name="isErrorType" class="java.lang.String">
		<fieldDescription><![CDATA[isErrorType]]></fieldDescription>
	</field>
	<field name="isExplanation" class="java.lang.String">
		<fieldDescription><![CDATA[isExplanation]]></fieldDescription>
	</field>
	<field name="isFyi" class="java.lang.String">
		<fieldDescription><![CDATA[isFyi]]></fieldDescription>
	</field>
	<field name="isSop" class="java.lang.String">
		<fieldDescription><![CDATA[isSop]]></fieldDescription>
	</field>
	<field name="isPriPerfGroup" class="java.lang.String">
		<fieldDescription><![CDATA[isPriPerfGroup]]></fieldDescription>
	</field>
	<field name="isSecPerfGroup" class="java.lang.String">
		<fieldDescription><![CDATA[isSecPerfGroup]]></fieldDescription>
	</field>
	<field name="isAuditor" class="java.lang.String">
		<fieldDescription><![CDATA[isAuditor]]></fieldDescription>
	</field>
	<field name="isAuditDate" class="java.lang.String">
		<fieldDescription><![CDATA[isAuditDate]]></fieldDescription>
	</field>
	
	<field name="isTotalClaims" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalClaims]]></fieldDescription>
	</field>
	<field name="isProcClaims" class="java.lang.String">
		<fieldDescription><![CDATA[isProcClaims]]></fieldDescription>
	</field>
	<field name="isMonClaims" class="java.lang.String">
		<fieldDescription><![CDATA[isMonClaims]]></fieldDescription>
	</field>
	<field name="isTotalPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalPaid]]></fieldDescription>
	</field>
	<field name="isTotalOvPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalOvPaid]]></fieldDescription>
	</field>
	<field name="isTotalUnPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalUnPaid]]></fieldDescription>
	</field>
	
	<group name="dummy"> 		
	<groupExpression><![CDATA["dummy"]]></groupExpression> 
		<groupHeader>
			
		</groupHeader>
	</group>
	
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="154" splitType="Stretch">
			<rectangle>
				<reportElement x="4" y="37" width="326" height="18" forecolor="#0A0909" backcolor="#0A0A0A"  />
			</rectangle>
			<staticText>
				<reportElement x="41" y="37" width="220" height="24" forecolor="#FFFFFF" backcolor="#2DE339"  />
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[IN-SAMPLE AUDITS]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="4" y="63" width="88" height="30"  />
				<box topPadding="4" leftPadding="8">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement>
					<font fontName="SansSerif" size="14"/>
				</textElement>
				<text><![CDATA[Associate : ]]></text>
			</staticText>
			<staticText>
				<reportElement x="311" y="80" width="75" height="14"  />
				<textElement>
					<font fontName="Haettenschweiler" size="12" />
				</textElement>
				<text><![CDATA[Supervisor   :]]></text>
			</staticText>
			<rectangle>
				<reportElement x="1" y="100" width="800" height="4" backcolor="#080707"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="117" y="118" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[High Dollar]]></text>
			</staticText>
			<staticText>
				<reportElement x="-12" y="118" width="131" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Membership #]]></text>
			</staticText>
			<staticText>
				<reportElement x="169" y="134" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Out-Of-Sample]]></text>
			</staticText>
			<staticText>
				<reportElement x="342" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Monetary]]></text>
			</staticText>
			<staticText>
				<reportElement x="392" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Procedural]]></text>
			</staticText>
			<staticText>
				<reportElement x="228" y="134" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Processed On]]></text>
			</staticText>
			<staticText>
				<reportElement x="67" y="134" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Under Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="66" y="118" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Over Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="169" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Audit Type]]></text>
			</staticText>
			<staticText>
				<reportElement x="169" y="118" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Mock Audit]]></text>
			</staticText>
			<staticText>
				<reportElement x="279" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Error]]></text>
			</staticText>
			<staticText>
				<reportElement x="-18" y="103" width="131" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[DCN]]></text>
			</staticText>
			<staticText>
				<reportElement x="713" y="103" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Auditor]]></text>
			</staticText>
			<staticText>
				<reportElement x="630" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[SOP Reference]]></text>
			</staticText>
			<staticText>
				<reportElement x="228" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Adj. Required]]></text>
			</staticText>
			<staticText>
				<reportElement x="622" y="134" width="116" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Performance Group]]></text>
			</staticText>
			<staticText>
				<reportElement x="68" y="103" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="516" y="103" width="114" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Explanation]]></text>
			</staticText>
			<staticText>
				<reportElement x="713" y="134" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Audit Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="431" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Error]]></text>
			</staticText>
			<rectangle>
				<reportElement x="1" y="150" width="800" height="4" backcolor="#080707"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement x="92" y="68" width="206" height="21"  />
				<textElement>
					<font size="14" fontName="Arial" isUnderline="true" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{associateName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="4" y="2" width="797" height="30"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="28"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{month}+" Daily Claims Audit Assessment"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="367" y="65" width="100" height="13"  />
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empNO}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="367" y="83" width="100" height="13"  />
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{supervisor}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="311" y="63" width="75" height="14"  />
				<textElement>
					<font fontName="Haettenschweiler" size="12" />
				</textElement>
				<text><![CDATA[Employee # :]]></text>
			</staticText>
			<staticText>
				<reportElement x="302" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Codes]]></text>
			</staticText>
			<staticText>
				<reportElement x="451" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Type]]></text>
			</staticText>
			<staticText>
				<reportElement x="516" y="135" width="114" height="16"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[FYI]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band/>
	</columnHeader>
	<detail>
		<band height="60" splitType="Stretch">
			<image>
				<reportElement x="154" y="13" width="16" height="15"  >
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<imageExpression><![CDATA[$F{isHighDoller}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="202" y="13" width="16" height="15"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<imageExpression><![CDATA[$F{isMockAudit}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="203" y="28" width="16" height="15"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<imageExpression><![CDATA[$F{reportType}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="265" y="2" width="16" height="15"  />
				<imageExpression><![CDATA[$F{isAdjRequired}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="377" y="2" width="16" height="15"  />
				<imageExpression><![CDATA[$F{isMonetary}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="429" y="2" width="16" height="15"  />
				<imageExpression><![CDATA[$F{isProcedural}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<line>
				<reportElement positionType="Float" x="0" y="1" width="800" height="1"  />
				<graphicElement>
					<pen lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement x="1" y="2" width="20" height="22"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isSno}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="11" y="2" width="74" height="18"  />
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isDcn}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="11" y="15" width="74" height="18"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isMemId}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="82" y="2" width="63" height="12"  />
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isPaid} != null && $F{isPaid}.length() > 0 ? Double.valueOf($F{isPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="82" y="15" width="63" height="12"  />
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isOverPaid} != null && $F{isOverPaid}.length() > 0 ? Double.valueOf($F{isOverPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="82" y="29" width="63" height="12"  />
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isUnderPaid} != null && $F{isUnderPaid}.length() > 0 ? Double.valueOf($F{isUnderPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="188" y="2" width="50" height="12"  />
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isAuditType}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="248" y="29" width="51" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isProcessOn}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="461" y="2" width="51" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isErrorType}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement  x="520" y="2" width="110" height="40"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isExplanation}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="520" y="42" width="110" height="17" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isFyi}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="640" y="29" width="92" height="14" positionType="Float" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isPriPerfGroup}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="640" y="44" width="92" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isSecPerfGroup}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="721" y="2" width="78" height="12"  />
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isAuditor}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="640" y="2" width="81" height="26" />
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isSop}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="316" y="2" width="31" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isErrorCode}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="732" y="29" width="51" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isAuditDate}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		
	</pageFooter>
	
	<summary>
		<band height="249" splitType="Stretch">
			<textField>
				<reportElement x="6" y="5" width="415" height="12"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Summary for 'Associate'= "+$F{associateName}+" ("+$F{isTotalClaims}+" claims)"]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="6" y="20" width="158" height="48"  />
			</rectangle>
			<rectangle>
				<reportElement x="112" y="20" width="52" height="16" backcolor="#CCCCCC"  />
			</rectangle>
			<staticText>
				<reportElement x="9" y="21" width="74" height="15"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Claims]]></text>
			</staticText>
			<staticText>
				<reportElement x="9" y="37" width="133" height="15"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Procedural Error Claims]]></text>
			</staticText>
			<staticText>
				<reportElement x="9" y="53" width="133" height="15"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Monetary Error Claims]]></text>
			</staticText>
			<rectangle>
				<reportElement x="176" y="20" width="153" height="48"  />
			</rectangle>
			<staticText>
				<reportElement x="178" y="37" width="91" height="15"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Over Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="178" y="21" width="74" height="15"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="178" y="53" width="91" height="15"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Under Paid]]></text>
			</staticText>
			<rectangle>
				<reportElement x="6" y="78" width="785" height="23"  />
			</rectangle>
			<rectangle>
				<reportElement x="6" y="101" width="785" height="23"  />
			</rectangle>
			<rectangle>
				<reportElement x="6" y="124" width="785" height="23"  />
			</rectangle>
			<staticText>
				<reportElement x="42" y="83" width="750" height="15"  />
				<textElement>
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[I certify that I have completed this audit in accordance with the standards and criteria appointed by the Quality Assurance Department.]]></text>
			</staticText>
			<staticText>
				<reportElement x="7" y="155" width="748" height="24"  />
				<textElement>
					<font fontName="Haettenschweiler" size="12"/>
				</textElement>
				<text><![CDATA[Please forward any dispute to this audit within 48 hours of the date listed on the bottom of the sheet. 
]]></text>
			</staticText>
			<rectangle>
				<reportElement x="112" y="36" width="52" height="16" backcolor="#CCCCCC"  />
			</rectangle>
			<rectangle>
				<reportElement x="112" y="52" width="52" height="16" backcolor="#CCCCCC"  />
			</rectangle>
			<rectangle>
				<reportElement x="254" y="20" width="76" height="16" backcolor="#CCCCCC"  />
			</rectangle>
			<rectangle>
				<reportElement x="254" y="36" width="76" height="16" backcolor="#CCCCCC"  />
			</rectangle>
			<rectangle>
				<reportElement x="254" y="52" width="76" height="16" backcolor="#CCCCCC"  />
			</rectangle>
			<staticText>
				<reportElement x="420" y="172" width="224" height="30"  />
				<textElement>
					<font fontName="Haettenschweiler" size="12"/>
				</textElement>
				<text><![CDATA[ ADJUSTED ?: €YES     €NO    ]]></text>
			</staticText>
			<rectangle>
				<reportElement x="125" y="186" width="157" height="1" forecolor="#FFFFFF" backcolor="#0A0A0A"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.25" lineStyle="Solid" lineColor="#0A0A0A"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="322" y="186" width="96" height="1" forecolor="#FFFFFF" backcolor="#0A0A0A"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.25" lineStyle="Solid" lineColor="#0A0A0A"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="7" y="172" width="748" height="18"  />
				<textElement>
					<font fontName="Haettenschweiler" size="12"/>
				</textElement>
				<text><![CDATA[Processor Signature :                                                                                                   Date :                                              ]]></text>
			</staticText>
			<textField>
				<reportElement x="113" y="23" width="51" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isTotalClaims}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="113" y="40" width="51" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isProcClaims}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="113" y="55" width="51" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isMonClaims}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="254" y="23" width="76" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isTotalPaid} != null && $F{isTotalPaid}.length() > 0 ? Double.valueOf($F{isTotalPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="254" y="40" width="76" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isTotalOvPaid} != null && $F{isTotalOvPaid}.length() > 0 ? Double.valueOf($F{isTotalOvPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="253" y="55" width="76" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isTotalUnPaid} != null && $F{isTotalUnPaid}.length() > 0 ? Double.valueOf($F{isTotalUnPaid}) : 0))]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
