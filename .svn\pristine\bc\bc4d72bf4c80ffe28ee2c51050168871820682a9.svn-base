package com.carefirst.qadb.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.sql.DataSource;

import oracle.jdbc.OracleTypes;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

import com.carefirst.audit.model.AdjustmentsReport;
import com.carefirst.audit.model.Associate;
import com.carefirst.audit.model.AuditAssessment;
import com.carefirst.audit.model.ErrorCodes;
import com.carefirst.audit.model.OtherReports;
import com.carefirst.qadb.constant.QADBConstants;

public class UserReportsDAOImpl implements UserReportsDAO {
	
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);
	
	@Autowired
	DataSource dataSource;
	
	@Override
	public JRDataSource getOtherReport(OtherReports otherReport) throws SQLException {
		// TODO Auto-generated method stub
		
		logger.debug("stutsTo " + otherReport.getStatus());
		String getOtherReport = QADBConstants.REPORTS_OTHER_REPORT_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getOtherReport);
		
		JRDataSource ds = null;
		
		List<ErrorCodes> errorCodesReportsRS = null;
		List<Associate> associateRS = null;
		
		if((otherReport.getReportType()).equalsIgnoreCase("ERR_CODES")){
			errorCodesReportsRS = new ArrayList<ErrorCodes>();
		}
		else if((otherReport.getReportType()).equalsIgnoreCase("CURR_PROCESSORS") || (otherReport.getReportType()).equalsIgnoreCase("SUPERVISORS")){
			associateRS = new ArrayList<Associate>();
		}
		
		try {
			
			callableStatment.setString(1, otherReport.getUserId());
			callableStatment.setString(2, otherReport.getReportType());
			callableStatment.setString(3, otherReport.getStatus());
			
			callableStatment.registerOutParameter(4, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(5, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(6, OracleTypes.VARCHAR);
			
			callableStatment.executeUpdate();
			ResultSet  rs =   (ResultSet) callableStatment.getObject(4);			
			logger.debug("Row count" +rs.getRow());
			
			
			if((otherReport.getReportType()).equalsIgnoreCase("ERR_CODES")){
				
				while (rs.next()) {
					ErrorCodes errorCodeEO = new ErrorCodes();
					
					errorCodeEO.setErrorCode((rs.getString("ERROR_ID")).toString());
					errorCodeEO.setErrorName((rs.getString("ERROR_NAME")).toString());
					errorCodeEO.setErrorDescription((rs.getString("ERROR_DESC")).toString());
					errorCodeEO.setErrorStatus((rs.getString("STATUS")).toString());
					errorCodeEO.setStatus(otherReport.getStatus());
					errorCodeEO.setCount((rs.getString("TOTAL_ROW_COUNT")).toString());
					errorCodesReportsRS.add(errorCodeEO);
				}	
				
			}
			else if((otherReport.getReportType()).equalsIgnoreCase("CURR_PROCESSORS") || (otherReport.getReportType()).equalsIgnoreCase("SUPERVISORS")){
				
				while (rs.next()) {
					Associate associateEO = new Associate();
					
					associateEO.setAssociateId((rs.getString("ASSOCIATE_ID")).toString());
					associateEO.setAssociateName((rs.getString("ASSOCIATE_NAME")).toString());
					associateEO.setJobTitles((rs.getString("JOB_TITLE_NAME")).toString());
					if(null != (rs.getString("ASSOCIATE_FACETS_ID"))){
						associateEO.setFacetsId((rs.getString("ASSOCIATE_FACETS_ID")).toString());
					}
					else{
						associateEO.setFacetsId("");
					}
					associateEO.setAuditingStatus((rs.getString("STATUS")).toString());
					associateEO.setStatus(otherReport.getStatus());
					associateEO.setCount((rs.getString("TOTAL_ROW_COUNT")).toString());
					associateRS.add(associateEO);
					
				}	
				
			}
			
			rs.close();
			
			callableStatment.close();
			 
			conn.close();
			
		} catch (Exception e) {
			logger.debug("Exception generated "+e.getMessage());
		}
		
		if((otherReport.getReportType()).equalsIgnoreCase("ERR_CODES")){
			 ds = new JRBeanCollectionDataSource(errorCodesReportsRS);
		}
		else if((otherReport.getReportType()).equalsIgnoreCase("CURR_PROCESSORS") || (otherReport.getReportType()).equalsIgnoreCase("SUPERVISORS")){
			 ds = new JRBeanCollectionDataSource(associateRS);
		}
		
		
		// Return the wrapped collection
		
		return ds;
		
	}

	
	@Override
	public JRDataSource getAdjustmentsRequiredReport(AdjustmentsReport adjustments) throws SQLException {
		
		logger.debug("***getAdjustmentsRequiredReport entry***");
		String getAdjustmentsReport = QADBConstants.REPORTS_ADJUSTMENTS_REQUIRED_REPORT_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getAdjustmentsReport);
		List<AuditAssessment> adjustmentRequiredList = new ArrayList<AuditAssessment>();
		
		try {
			
			callableStatment.setNull(1, java.sql.Types.VARCHAR);
			callableStatment.setString(2, adjustments.getAuditorName());
			callableStatment.setString(3, adjustments.getAuditFromDate());
			callableStatment.setString(4, adjustments.getAuditToDate());
			callableStatment.setNull(5, java.sql.Types.VARCHAR);
		
			callableStatment.registerOutParameter(6, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(7, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(8, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(9, OracleTypes.VARCHAR);
			
			callableStatment.executeUpdate();
			
			
			ResultSet  rs =   (ResultSet) callableStatment.getObject(7);
			
			logger.debug("rs== " +rs.getFetchSize());
			
			while(rs.next()){
				logger.debug("***Entry"+(rs.getString("AUDIT_ID"))); 
				AuditAssessment adjustmentEO = new AuditAssessment();
				
				adjustmentEO.setTimeframe((adjustments.getAuditFromDate())+" To "+(adjustments.getAuditToDate()));
				
				adjustmentEO.setIsAuditor(callableStatment.getString(6));
				adjustmentEO.setIsSno(rs.getString("SNO"));
				adjustmentEO.setIsAuditDate((rs.getString("AUDIT_DATE")).toString());
				adjustmentEO.setIsDcn(rs.getString("DCN"));
				adjustmentEO.setIsMemId(rs.getString("MEMBERSHIP#"));
				adjustmentEO.setEmpNO(rs.getString("EMPLOYEE#"));
				adjustmentEO.setAssociateName((rs.getString("ASSOCIATE")).toString());
				adjustmentEO.setSupervisor((rs.getString("SUPERVISOR")).toString());
				adjustmentEO.setIsProcessOn((rs.getString("PROCESSED_ON")).toString());
				if(null != (rs.getString("ADJUSTMENT_REQUIRED"))){
					adjustmentEO.setIsAdjRequired((rs.getString("ADJUSTMENT_REQUIRED")).toString());
				}
				else{
					adjustmentEO.setIsAdjRequired("");
				}
				if(null != (rs.getString("ADJUSTMENT_MADE"))){
					adjustmentEO.setIsAdjustmentsMade((rs.getString("ADJUSTMENT_MADE")).toString());
				}
				else{
					adjustmentEO.setIsAdjustmentsMade("");
				}
				if(null != (rs.getString("ERROR_TYPE"))){
					adjustmentEO.setIsErrorType((rs.getString("ERROR_TYPE")).toString());
				}
				else{
					adjustmentEO.setIsErrorType("");
				}
				if(null != (rs.getString("ERROR_CODES"))){
					adjustmentEO.setIsErrorCode((rs.getString("ERROR_CODES")).toString());
				}
				else{
					adjustmentEO.setIsErrorCode("");
				}
				if(null != (rs.getString("MONETARY_FLG"))){
					adjustmentEO.setIsMonetary((rs.getString("MONETARY_FLG")).toString());
				}
				else{
					adjustmentEO.setIsMonetary("");
				}
				if(null != (rs.getString("PROCEDURAL_FLG"))){
					adjustmentEO.setIsProcedural((rs.getString("PROCEDURAL_FLG")).toString());
				}
				else{
					adjustmentEO.setIsProcedural("");
				}
				if(null != (rs.getString("ERROR_EXPLANATION"))){
					adjustmentEO.setIsExplanation((rs.getString("ERROR_EXPLANATION")).toString());
				}
				else{
					adjustmentEO.setIsExplanation("");
				}
				
				adjustmentRequiredList.add(adjustmentEO);
			}
			
			
			rs.close();
			
			callableStatment.close();
			 
			conn.close();
			
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		
		JRDataSource ds = new JRBeanCollectionDataSource(adjustmentRequiredList);	
		
		logger.debug("***getAdjustmentsRequiredReport exit***");

		// Return the wrapped collection
		return ds;
	
	}
 
}
