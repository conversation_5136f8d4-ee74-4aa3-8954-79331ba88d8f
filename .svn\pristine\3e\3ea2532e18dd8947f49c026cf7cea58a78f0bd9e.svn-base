package com.carefirst.qadb.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.sql.DataSource;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import oracle.jdbc.OracleTypes;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import com.carefirst.audit.model.AuditAssessment;
import com.carefirst.audit.model.AuditSearch;
import com.carefirst.qadb.constant.QADBConstants;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.xml.DomDriver;

public class ClaimsAuditAssessmentReportDAOImpl implements ClaimsAuditAssessmentReportDAO {
	
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);
	public static String type = "" ;
	
	@Autowired
	DataSource dataSource;
	
	@Override
	public JRDataSource getClaimsAuditAssesmentReport(
			AuditSearch auditAssessmentForm) throws SQLException {
		
		logger.debug("Calling getClaimsAuditAssesmentReport Impl " + auditAssessmentForm.getDcn());
		String getClaimsAssesmentReport = QADBConstants.CLAIMS_AUDIT_ASSESMENT_REPORT_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getClaimsAssesmentReport);
		List<AuditAssessment> claimsAuditAssessmentList = new ArrayList<AuditAssessment>();
		boolean flag = false;
		
		try {
			
			if(null!=auditAssessmentForm.getAssociateName()){
				if((auditAssessmentForm.getAssociateName()).equalsIgnoreCase("")){
					callableStatment.setNull(1, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(1, auditAssessmentForm.getAssociateName());
				}
			}else{
				callableStatment.setNull(1, java.sql.Types.VARCHAR);
			}
			
			if(null!=auditAssessmentForm.getEmpNo()){
				if((auditAssessmentForm.getEmpNo()).equalsIgnoreCase("")){
					callableStatment.setNull(2, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(2, auditAssessmentForm.getEmpNo());
				}
			}else{
				callableStatment.setNull(2, java.sql.Types.VARCHAR);
			}
			
			if(null!=auditAssessmentForm.getProcessedDateFrom()){
				if((auditAssessmentForm.getProcessedDateFrom()).equalsIgnoreCase("")){
					callableStatment.setNull(3, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(3, auditAssessmentForm.getProcessedDateFrom());
				}
			}else{
				callableStatment.setNull(3, java.sql.Types.VARCHAR);
			}
			
			if(null!=auditAssessmentForm.getProcessedDateTo()){
				if((auditAssessmentForm.getProcessedDateTo()).equalsIgnoreCase("")){
					callableStatment.setNull(4, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(4,auditAssessmentForm.getProcessedDateTo());
				}
			}else{
				callableStatment.setNull(4, java.sql.Types.VARCHAR);
			}
			
			if(null!=auditAssessmentForm.getDcn()){
				if((auditAssessmentForm.getDcn()).equalsIgnoreCase("")){
					callableStatment.setNull(5, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(5, auditAssessmentForm.getDcn());
				}
			}else{
				callableStatment.setNull(5, java.sql.Types.VARCHAR);
			}
			
			if(null!=auditAssessmentForm.getAuditorName()){
				if((auditAssessmentForm.getAuditorName()).equalsIgnoreCase("")){
					callableStatment.setNull(6, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(6, auditAssessmentForm.getAuditorName());
				}
			}else{
				callableStatment.setNull(6, java.sql.Types.VARCHAR);
			}
			
			if(null!=auditAssessmentForm.getAuditDateFrom()){
				if((auditAssessmentForm.getAuditDateFrom()).equalsIgnoreCase("")){
					callableStatment.setNull(7, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(7,auditAssessmentForm.getAuditDateFrom());
				}
			}else{
				callableStatment.setNull(7, java.sql.Types.VARCHAR);
			}
			
			if(null!=auditAssessmentForm.getAuditDateTo()){
				if((auditAssessmentForm.getAuditDateTo()).equalsIgnoreCase("")){
					callableStatment.setNull(8, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(8, auditAssessmentForm.getAuditDateTo());
				}
			}else{
				callableStatment.setNull(8, java.sql.Types.VARCHAR);
			}
			
			callableStatment.registerOutParameter(9, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(10, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(11, OracleTypes.VARCHAR);
			/*set out parameters from 12 to 17*/
			 for(int i=12 ; i < 18 ; i++)
			    {
			            callableStatment.registerOutParameter(i, OracleTypes.CURSOR);
			    }
			
			callableStatment.registerOutParameter(18, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(19, OracleTypes.VARCHAR);
			
			callableStatment.executeUpdate();
			
			logger.debug("type "+auditAssessmentForm.getType());
			
				if(null!= (auditAssessmentForm.getType()) && (auditAssessmentForm.getType()).equalsIgnoreCase("in")){
					
					/*IN-Sample RS*/
					
					ResultSet  rs4 =   (ResultSet) callableStatment.getObject(15);			
					logger.debug("Row count IS " +rs4.getRow());
					
					while (rs4.next()) {
						
						AuditAssessment auditAssessmentIs4 = new AuditAssessment();
						
						logger.debug("Assesment RS4 "+rs4.getString("DCN_NBR")+ " memb "+rs4.getString("SNO")+ "total " +rs4.getString("TOTAL_PAID"));
						
						if(null != (callableStatment.getString(9))){
							auditAssessmentIs4.setAssociateName(callableStatment.getString(9));
						}
						else{
							auditAssessmentIs4.setAssociateName("");
						}
						if(null != (callableStatment.getString(10))){
							auditAssessmentIs4.setEmpNO(callableStatment.getString(10));
						}
						else{
							auditAssessmentIs4.setEmpNO("");
						}
						if(null != (callableStatment.getString(11))){
							auditAssessmentIs4.setSupervisor(callableStatment.getString(11));
						}
						else{
							auditAssessmentIs4.setSupervisor("");
						}
						
						//IS counts
						
						auditAssessmentIs4.setIsSno((rs4.getString("SNO")));
						auditAssessmentIs4.setIsDcn((rs4.getString("DCN_NBR")));
						auditAssessmentIs4.setIsMemId((rs4.getString("MEMB_ID")));
						auditAssessmentIs4.setIsPaid((rs4.getString("TOTAL_PAID")));
						logger.info("Total"+rs4.getString("TOTAL_PAID"));
						auditAssessmentIs4.setIsOverPaid((rs4.getString("OVER_PAID")));
						logger.info("Total Over Paid"+rs4.getString("OVER_PAID"));
						auditAssessmentIs4.setIsUnderPaid((rs4.getString("UNDER_PAID")));
						logger.info("Total Under Paid"+rs4.getString("UNDER_PAID"));
						auditAssessmentIs4.setIsHighDoller((rs4.getString("HIGH_DOLLAR_FLG")));
						auditAssessmentIs4.setIsAuditType((rs4.getString("AUDIT_TYPE")));
						auditAssessmentIs4.setIsMockAudit((rs4.getString("MOCK_FLG")));
						//auditAssessmentIs.setReportType((rs2.getString("OOS_FLG")));
						type= "is";
						if(null != (rs4.getString("REQUIRED_FLG"))){
							auditAssessmentIs4.setIsAdjRequired((rs4.getString("REQUIRED_FLG")).toString());
						}
						else{
							auditAssessmentIs4.setIsAdjRequired("");
						}
						auditAssessmentIs4.setIsProcessOn((rs4.getString("PROCESS_DT")).toString());
						
						if(null != (rs4.getString("ERROR_ID"))){
							auditAssessmentIs4.setIsErrorCode((rs4.getString("ERROR_ID")).toString());
						}
						else{
							auditAssessmentIs4.setIsErrorCode("");
						}
						if(null != (rs4.getString("MONETARY_FLG"))){
							auditAssessmentIs4.setIsMonetary((rs4.getString("MONETARY_FLG")).toString());
						}
						else{
							auditAssessmentIs4.setIsMonetary("");
						}
						if(null != (rs4.getString("PROCEDURAL_FLG"))){
							auditAssessmentIs4.setIsProcedural((rs4.getString("PROCEDURAL_FLG")).toString());
						}
						else{
							auditAssessmentIs4.setIsProcedural("");
						}
						if(null != (rs4.getString("ROOT_CAUSE"))){
							auditAssessmentIs4.setIsErrorType((rs4.getString("ROOT_CAUSE")).toString());
						}
						else{
							auditAssessmentIs4.setIsErrorType("");
						}
						if(null != (rs4.getString("ERROR_REASON_DESC"))){
							auditAssessmentIs4.setIsExplanation((rs4.getString("ERROR_REASON_DESC")).toString());
							logger.debug("verbiage issue get reason for error===debug on 08/08/2018"+rs4.getString("ERROR_REASON_DESC"));
						}
						else{
							auditAssessmentIs4.setIsExplanation("");
						}
						if(null != (rs4.getString("FYI"))){
							auditAssessmentIs4.setIsFyi("FYI - "+(rs4.getString("FYI")).toString());
						}
						else{
							auditAssessmentIs4.setIsFyi("");
						}
						if(null != (rs4.getString("SOP_NEWS_FLASH_REFNC"))){
							auditAssessmentIs4.setIsSop((rs4.getString("SOP_NEWS_FLASH_REFNC")).toString());
						}
						else{
							auditAssessmentIs4.setIsSop("");
						}
						
						if(null != (rs4.getString("PRIMARY_PERF_GROUP_ID"))){
							auditAssessmentIs4.setIsPriPerfGroup("P-"+((rs4.getString("PRIMARY_PERF_GROUP_ID")).toString()));
						}
						else{
							auditAssessmentIs4.setIsPriPerfGroup("");
						}
						if(null != (rs4.getString("SECONDARY_PERF_GROUP_ID"))){
							auditAssessmentIs4.setIsSecPerfGroup("S-"+((rs4.getString("SECONDARY_PERF_GROUP_ID")).toString()));
						}
						else{
							auditAssessmentIs4.setIsSecPerfGroup("");
						}

						auditAssessmentIs4.setIsAuditor((rs4.getString("USER_ID")));
						auditAssessmentIs4.setIsAuditDate((rs4.getString("AUDIT_DT")));
						
						flag = true;
						
						claimsAuditAssessmentList.add(auditAssessmentIs4);
					}	
					
					rs4.close(); 
					
					logger.debug("IS flag "+flag);
					//if rs4 is empty do not iterate rs5 and rs6
					if(flag){
						
						AuditAssessment auditAssessmentIs5 = new AuditAssessment();
						
						ResultSet  rs5 =   (ResultSet) callableStatment.getObject(16);			
						
						while (rs5.next()) {
							
							logger.debug("CA claims "+rs5.getString("TOTAL_CLAIMS")+" "+rs5.getString("TOTAL_PAID"));
							
							if(null != (callableStatment.getString(9))){
								auditAssessmentIs5.setAssociateName(callableStatment.getString(9));
							}
							else{
								auditAssessmentIs5.setAssociateName("");
							}
							if(null != (callableStatment.getString(10))){
								auditAssessmentIs5.setEmpNO(callableStatment.getString(10));
							}
							else{
								auditAssessmentIs5.setEmpNO("");
							}
							if(null != (callableStatment.getString(11))){
								auditAssessmentIs5.setSupervisor(callableStatment.getString(11));
							}
							else{
								auditAssessmentIs5.setSupervisor("");
							}
							
							if((null == (rs5.getString("TOTAL_CLAIMS")))||((rs5.getString("TOTAL_CLAIMS")).equalsIgnoreCase(""))){
								auditAssessmentIs5.setIsTotalClaims("0");
							}
							else{
								auditAssessmentIs5.setIsTotalClaims((rs5.getString("TOTAL_CLAIMS")));
							}
							
							auditAssessmentIs5.setIsTotalPaid((rs5.getString("TOTAL_PAID")));
							auditAssessmentIs5.setIsTotalOvPaid((rs5.getString("TOTAL_OVER_PAID")));
							auditAssessmentIs5.setIsTotalUnPaid((rs5.getString("TOTAL_UNDER_PAID")));

							claimsAuditAssessmentList.add(auditAssessmentIs5);
						}
						
						rs5.close();
						
						
						//IS error claim details RS
						
						ResultSet  rs6 =   (ResultSet) callableStatment.getObject(17);			
						
						while (rs6.next()) {
							
							if((null == (rs6.getString("PROCEDURAL_ERROR_CLAIMS")))||((rs6.getString("PROCEDURAL_ERROR_CLAIMS")).equalsIgnoreCase(""))){
								auditAssessmentIs5.setIsProcClaims("0");
							}
							else{
								auditAssessmentIs5.setIsProcClaims((rs6.getString("PROCEDURAL_ERROR_CLAIMS")).toString());
							}
							
							if((null == (rs6.getString("MONETARY_ERROR_CLAIMS")))||((rs6.getString("MONETARY_ERROR_CLAIMS")).equalsIgnoreCase(""))){
								auditAssessmentIs5.setIsMonClaims("0");
							}
							else{
								auditAssessmentIs5.setIsMonClaims((rs6.getString("MONETARY_ERROR_CLAIMS")).toString());
							}
							claimsAuditAssessmentList.add(auditAssessmentIs5);
						}
						rs6.close();
					}
					
	}else{
			
			/*Out-of-Sample RS*/
			ResultSet  rs1 =   (ResultSet) callableStatment.getObject(12);			
			logger.debug("Row count OOS " +rs1.getRow());
			
			while (rs1.next()) {
				
				AuditAssessment auditAssessmentOos1 = new AuditAssessment();
				
				logger.debug("Assesment RS "+rs1.getString("DCN_NBR")+ " memb "+rs1.getString("SNO")+ "total " +rs1.getString("TOTAL_PAID"));
				
				if(null != (callableStatment.getString(9))){
					auditAssessmentOos1.setAssociateName(callableStatment.getString(9));
				}
				else{
					auditAssessmentOos1.setAssociateName("");
				}
				if(null != (callableStatment.getString(10))){
					auditAssessmentOos1.setEmpNO(callableStatment.getString(10));
				}
				else{
					auditAssessmentOos1.setEmpNO("");
				}
				if(null != (callableStatment.getString(11))){
					auditAssessmentOos1.setSupervisor(callableStatment.getString(11));
				}
				else{
					auditAssessmentOos1.setSupervisor("");
				}
				logger.debug("Asso det " +callableStatment.getString(9)+" "+callableStatment.getString(10)+" "+callableStatment.getString(11));
				
				//OOS
				auditAssessmentOos1.setOosSno((rs1.getString("SNO")));
				auditAssessmentOos1.setOosDcn((rs1.getString("DCN_NBR")));
				auditAssessmentOos1.setOosMemId((rs1.getString("MEMB_ID")));
				auditAssessmentOos1.setOosPaid((rs1.getString("TOTAL_PAID")));
				auditAssessmentOos1.setOosOverPaid((rs1.getString("OVER_PAID")));
				auditAssessmentOos1.setOosUnderPaid((rs1.getString("UNDER_PAID")));
				auditAssessmentOos1.setOosHighDoller((rs1.getString("HIGH_DOLLAR_FLG")));
				auditAssessmentOos1.setOosAuditType((rs1.getString("AUDIT_TYPE")));
				auditAssessmentOos1.setOosMockAudit((rs1.getString("MOCK_FLG")));
				auditAssessmentOos1.setReportType((rs1.getString("OOS_FLG")));
				type= "oos";
				if(null != (rs1.getString("REQUIRED_FLG"))){
					auditAssessmentOos1.setOosAdjRequired((rs1.getString("REQUIRED_FLG")).toString());
				}
				else{
					auditAssessmentOos1.setOosAdjRequired("");
				}
				auditAssessmentOos1.setOosProcessOn((rs1.getString("PROCESS_DT")));

				if(null != (rs1.getString("ERROR_ID"))){
					auditAssessmentOos1.setOosErrorCode((rs1.getString("ERROR_ID")).toString());
				}
				else{
					auditAssessmentOos1.setOosErrorCode("");
				}
				if(null != (rs1.getString("MONETARY_FLG"))){
					auditAssessmentOos1.setOosMonetary((rs1.getString("MONETARY_FLG")).toString());
				}
				else{
					auditAssessmentOos1.setOosMonetary("");
				}
				if(null != (rs1.getString("PROCEDURAL_FLG"))){
					auditAssessmentOos1.setOosProcedural((rs1.getString("PROCEDURAL_FLG")).toString());
				}
				else{
					auditAssessmentOos1.setOosProcedural("");
				}
				if(null != (rs1.getString("ROOT_CAUSE"))){
					auditAssessmentOos1.setOosErrorType((rs1.getString("ROOT_CAUSE")).toString());
				}
				else{
					auditAssessmentOos1.setOosErrorType("");
				}
				if(null != (rs1.getString("ERROR_REASON_DESC"))){
					auditAssessmentOos1.setOosExplanation((rs1.getString("ERROR_REASON_DESC")).toString());
					logger.info("verbiage issue get reason for error====debug on 08/08/2018"+rs1.getString("ERROR_REASON_DESC"));
				}
				else{
					auditAssessmentOos1.setOosExplanation("");
				}
				if(null != (rs1.getString("FYI"))){
					auditAssessmentOos1.setOosFyi("FYI - "+(rs1.getString("FYI")).toString());
				}
				else{
					auditAssessmentOos1.setOosFyi("");
				}
				if(null != (rs1.getString("SOP_NEWS_FLASH_REFNC"))){
					auditAssessmentOos1.setOosSop((rs1.getString("SOP_NEWS_FLASH_REFNC")).toString());
				}
				else{
					auditAssessmentOos1.setOosSop("");
				}
				
				if(null != (rs1.getString("PRIMARY_PERF_GROUP_ID"))){
					auditAssessmentOos1.setOosPriPerfGroup("P-"+((rs1.getString("PRIMARY_PERF_GROUP_ID")).toString()));
				}
				else{
					auditAssessmentOos1.setOosPriPerfGroup("");
				}
				if(null != (rs1.getString("SECONDARY_PERF_GROUP_ID"))){
					auditAssessmentOos1.setOosSecPerfGroup("S-"+((rs1.getString("SECONDARY_PERF_GROUP_ID")).toString()));
				}
				else{
					auditAssessmentOos1.setOosSecPerfGroup("");
				}
				
				auditAssessmentOos1.setOosAuditor((rs1.getString("USER_ID")).toString());
				auditAssessmentOos1.setOosAuditDate((rs1.getString("AUDIT_DT")).toString());
				
				flag = true;
				
				claimsAuditAssessmentList.add(auditAssessmentOos1);
			}	
			
			rs1.close();
			
			logger.debug("OOS flag "+flag);
			if(flag){
				//RS2 OOS Claim paid details
				
				AuditAssessment auditAssessmentOos2 = new AuditAssessment();
				
				ResultSet  rs2 =   (ResultSet) callableStatment.getObject(13);			
				
				while (rs2.next()) { 
					
					logger.debug("CA2 claims "+rs2.getString("TOTAL_CLAIMS")+" "+rs2.getString("TOTAL_PAID")+" "+rs2.getString("TOTAL_OVER_PAID")+" "+rs2.getString("TOTAL_UNDER_PAID"));
					
					if(null != (callableStatment.getString(9))){
						auditAssessmentOos2.setAssociateName(callableStatment.getString(9));
					}
					else{
						auditAssessmentOos2.setAssociateName("");
					}
					if(null != (callableStatment.getString(10))){
						auditAssessmentOos2.setEmpNO(callableStatment.getString(10));
					}
					else{
						auditAssessmentOos2.setEmpNO("");
					}
					if(null != (callableStatment.getString(11))){
						auditAssessmentOos2.setSupervisor(callableStatment.getString(11));
					}
					else{
						auditAssessmentOos2.setSupervisor("");
					}
					
					if((null == (rs2.getString("TOTAL_CLAIMS")))||((rs2.getString("TOTAL_CLAIMS")).equalsIgnoreCase(""))){
						auditAssessmentOos2.setOosTotalClaims("0");
					}
					else{
						auditAssessmentOos2.setOosTotalClaims((rs2.getString("TOTAL_CLAIMS")));

					}
					auditAssessmentOos2.setOosTotalPaid((rs2.getString("TOTAL_PAID")));
					auditAssessmentOos2.setOosTotalOvPaid((rs2.getString("TOTAL_OVER_PAID")));
					auditAssessmentOos2.setOosTotalUnPaid((rs2.getString("TOTAL_UNDER_PAID")));
					
					claimsAuditAssessmentList.add(auditAssessmentOos2);
					
				}
				
				rs2.close();
				
				//OOS error claim details RS
				
				ResultSet  rs3 =   (ResultSet) callableStatment.getObject(14);			
				
				while (rs3.next()) {
					
					logger.debug("OOS R3");
					if((null == (rs3.getString("PROCEDURAL_ERROR_CLAIMS")))||((rs3.getString("PROCEDURAL_ERROR_CLAIMS")).equalsIgnoreCase(""))){
						auditAssessmentOos2.setOosProcClaims("0");
					}
					else{
						auditAssessmentOos2.setOosProcClaims((rs3.getString("PROCEDURAL_ERROR_CLAIMS")));

					}
					if((null == (rs3.getString("MONETARY_ERROR_CLAIMS")))||((rs3.getString("MONETARY_ERROR_CLAIMS")).equalsIgnoreCase(""))){
						auditAssessmentOos2.setOosMonClaims("0");
					}
					else{
						auditAssessmentOos2.setOosMonClaims((rs3.getString("MONETARY_ERROR_CLAIMS")));

					}
					
					claimsAuditAssessmentList.add(auditAssessmentOos2);
				}
				
				rs3.close();
				
			 }
			
		  }
				
			callableStatment.close();
			 
			conn.close();
			
		} catch (Exception e) {
			logger.debug("Exception generated "+e.getMessage());
		}
		
		logger.debug(claimsAuditAssessmentList.size());
		for(AuditAssessment a:claimsAuditAssessmentList){
			logger.debug("List Value "+objectToXML(a));
		}
		
		
		JRDataSource ds = new JRBeanCollectionDataSource(claimsAuditAssessmentList);	
		
		// Return the wrapped collection
		return ds;
	}
	public static String objectToXML(Object obj) {
		XStream xstream = new XStream(new DomDriver());
		return xstream.toXML(obj);
	}
	@Override
	public List<AuditAssessment> getClaimsAuditAssessmentList(AuditSearch auditAssessmentForm) throws SQLException {
		logger.debug("Calling getClaimsAuditAssesmentReport Impl " + auditAssessmentForm.getDcn());
		String getClaimsAssesmentReport = QADBConstants.CLAIMS_AUDIT_ASSESMENT_REPORT_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getClaimsAssesmentReport);
		List<AuditAssessment> claimsAuditAssessmentList = new ArrayList<AuditAssessment>();
		boolean flag = false;
		
		try {
			
			if(null!=auditAssessmentForm.getAssociateName()){
				if((auditAssessmentForm.getAssociateName()).equalsIgnoreCase("")){
					callableStatment.setNull(1, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(1, auditAssessmentForm.getAssociateName());
				}
			}else{
				callableStatment.setNull(1, java.sql.Types.VARCHAR);
			}
			
			if(null!=auditAssessmentForm.getEmpNo()){
				if((auditAssessmentForm.getEmpNo()).equalsIgnoreCase("")){
					callableStatment.setNull(2, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(2, auditAssessmentForm.getEmpNo());
				}
			}else{
				callableStatment.setNull(2, java.sql.Types.VARCHAR);
			}
			
			if(null!=auditAssessmentForm.getProcessedDateFrom()){
				if((auditAssessmentForm.getProcessedDateFrom()).equalsIgnoreCase("")){
					callableStatment.setNull(3, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(3, auditAssessmentForm.getProcessedDateFrom());
				}
			}else{
				callableStatment.setNull(3, java.sql.Types.VARCHAR);
			}
			
			if(null!=auditAssessmentForm.getProcessedDateTo()){
				if((auditAssessmentForm.getProcessedDateTo()).equalsIgnoreCase("")){
					callableStatment.setNull(4, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(4,auditAssessmentForm.getProcessedDateTo());
				}
			}else{
				callableStatment.setNull(4, java.sql.Types.VARCHAR);
			}
			
			if(null!=auditAssessmentForm.getDcn()){
				if((auditAssessmentForm.getDcn()).equalsIgnoreCase("")){
					callableStatment.setNull(5, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(5, auditAssessmentForm.getDcn());
				}
			}else{
				callableStatment.setNull(5, java.sql.Types.VARCHAR);
			}
			
			if(null!=auditAssessmentForm.getAuditorName()){
				if((auditAssessmentForm.getAuditorName()).equalsIgnoreCase("")){
					callableStatment.setNull(6, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(6, auditAssessmentForm.getAuditorName());
				}
			}else{
				callableStatment.setNull(6, java.sql.Types.VARCHAR);
			}
			
			if(null!=auditAssessmentForm.getAuditDateFrom()){
				if((auditAssessmentForm.getAuditDateFrom()).equalsIgnoreCase("")){
					callableStatment.setNull(7, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(7,auditAssessmentForm.getAuditDateFrom());
				}
			}else{
				callableStatment.setNull(7, java.sql.Types.VARCHAR);
			}
			
			if(null!=auditAssessmentForm.getAuditDateTo()){
				if((auditAssessmentForm.getAuditDateTo()).equalsIgnoreCase("")){
					callableStatment.setNull(8, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(8, auditAssessmentForm.getAuditDateTo());
				}
			}else{
				callableStatment.setNull(8, java.sql.Types.VARCHAR);
			}
			
			callableStatment.registerOutParameter(9, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(10, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(11, OracleTypes.VARCHAR);
			/*set out parameters from 12 to 17*/
			 for(int i=12 ; i < 18 ; i++)
			    {
			            callableStatment.registerOutParameter(i, OracleTypes.CURSOR);
			    }
			
			callableStatment.registerOutParameter(18, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(19, OracleTypes.VARCHAR);
			
			callableStatment.executeUpdate();
			
			logger.debug("Claims type "+auditAssessmentForm.getType());
			
				if(null!= (auditAssessmentForm.getType()) && (auditAssessmentForm.getType()).equalsIgnoreCase("in")){
					
					/*IN-Sample RS*/
					
					ResultSet  rs4 =   (ResultSet) callableStatment.getObject(15);			
					logger.debug("Row count IS " +rs4.getRow());
					
					while (rs4.next()) {
						
						AuditAssessment auditAssessmentIs4 = new AuditAssessment();
						
						logger.debug("Assesment RS4 "+rs4.getString("DCN_NBR")+ " memb "+rs4.getString("SNO")+ "total " +rs4.getString("TOTAL_PAID"));
						
						if(null != (callableStatment.getString(9))){
							auditAssessmentIs4.setAssociateName(callableStatment.getString(9));
						}
						else{
							auditAssessmentIs4.setAssociateName("");
						}
						if(null != (callableStatment.getString(10))){
							auditAssessmentIs4.setEmpNO(callableStatment.getString(10));
						}
						else{
							auditAssessmentIs4.setEmpNO("");
						}
						if(null != (callableStatment.getString(11))){
							auditAssessmentIs4.setSupervisor(callableStatment.getString(11));
						}
						else{
							auditAssessmentIs4.setSupervisor("");
						}
						
						//IS counts
						
						auditAssessmentIs4.setIsSno((rs4.getString("SNO")));
						auditAssessmentIs4.setIsDcn((rs4.getString("DCN_NBR")));
						auditAssessmentIs4.setIsMemId((rs4.getString("MEMB_ID")));
						
						if(null != (rs4.getString("PLATFORM"))){
							auditAssessmentIs4.setIsPlatForm((rs4.getString("PLATFORM")));
						}
						else{
							auditAssessmentIs4.setIsPlatForm("");
						}
						
						
						auditAssessmentIs4.setIsPaid((rs4.getString("TOTAL_PAID")));
						logger.info("Total"+rs4.getString("TOTAL_PAID"));
						auditAssessmentIs4.setIsOverPaid((rs4.getString("OVER_PAID")));
						logger.info("Total Over Paid"+rs4.getString("OVER_PAID"));
						auditAssessmentIs4.setIsUnderPaid((rs4.getString("UNDER_PAID")));
						logger.info("Total Under Paid"+rs4.getString("UNDER_PAID"));
						auditAssessmentIs4.setIsHighDoller((rs4.getString("HIGH_DOLLAR_FLG")));
						auditAssessmentIs4.setIsAuditType((rs4.getString("AUDIT_TYPE")));
						auditAssessmentIs4.setIsMockAudit((rs4.getString("MOCK_FLG")));
						//auditAssessmentIs.setReportType((rs2.getString("OOS_FLG")));
						type= "is";
						if(null != (rs4.getString("REQUIRED_FLG"))){
							auditAssessmentIs4.setIsAdjRequired((rs4.getString("REQUIRED_FLG")).toString());
						}
						else{
							auditAssessmentIs4.setIsAdjRequired("");
						}
						auditAssessmentIs4.setIsProcessOn((rs4.getString("PROCESS_DT")).toString());
						
						if(null != (rs4.getString("ERROR_ID"))){
							auditAssessmentIs4.setIsErrorCode((rs4.getString("ERROR_ID")).toString());
						}
						else{
							auditAssessmentIs4.setIsErrorCode("");
						}
						if(null != (rs4.getString("MONETARY_FLG"))){
							auditAssessmentIs4.setIsMonetary((rs4.getString("MONETARY_FLG")).toString());
						}
						else{
							auditAssessmentIs4.setIsMonetary("");
						}
						if(null != (rs4.getString("PROCEDURAL_FLG"))){
							auditAssessmentIs4.setIsProcedural((rs4.getString("PROCEDURAL_FLG")).toString());
						}
						else{
							auditAssessmentIs4.setIsProcedural("");
						}
						if(null != (rs4.getString("ROOT_CAUSE"))){
							auditAssessmentIs4.setIsErrorType((rs4.getString("ROOT_CAUSE")).toString());
						}
						else{
							auditAssessmentIs4.setIsErrorType("");
						}
						if(null != (rs4.getString("ERROR_REASON_DESC"))){
							auditAssessmentIs4.setIsExplanation((rs4.getString("ERROR_REASON_DESC")).toString());
							logger.debug("verbiage issue get reason for error===debug on 08/08/2018"+rs4.getString("ERROR_REASON_DESC"));
						}
						else{
							auditAssessmentIs4.setIsExplanation("");
						}
						if(null != (rs4.getString("FYI"))){
							auditAssessmentIs4.setIsFyi("FYI - "+(rs4.getString("FYI")).toString());
						}
						else{
							auditAssessmentIs4.setIsFyi("");
						}
						if(null != (rs4.getString("SOP_NEWS_FLASH_REFNC"))){
							auditAssessmentIs4.setIsSop((rs4.getString("SOP_NEWS_FLASH_REFNC")).toString());
						}
						else{
							auditAssessmentIs4.setIsSop("");
						}
						
						if(null != (rs4.getString("PRIMARY_PERF_GROUP_ID"))){
							auditAssessmentIs4.setIsPriPerfGroup("P-"+((rs4.getString("PRIMARY_PERF_GROUP_ID")).toString()));
						}
						else{
							auditAssessmentIs4.setIsPriPerfGroup("");
						}
						if(null != (rs4.getString("SECONDARY_PERF_GROUP_ID"))){
							auditAssessmentIs4.setIsSecPerfGroup("S-"+((rs4.getString("SECONDARY_PERF_GROUP_ID")).toString()));
						}
						else{
							auditAssessmentIs4.setIsSecPerfGroup("");
						}

						auditAssessmentIs4.setIsAuditor((rs4.getString("USER_ID")));
						auditAssessmentIs4.setIsAuditDate((rs4.getString("AUDIT_DT")));
						
						flag = true;
						
						claimsAuditAssessmentList.add(auditAssessmentIs4);
					}	
					
					rs4.close(); 
					
					logger.debug("IS flag "+flag);
					//if rs4 is empty do not iterate rs5 and rs6
					if(flag){
						
						AuditAssessment auditAssessmentIs5 = new AuditAssessment();
						
						ResultSet  rs5 =   (ResultSet) callableStatment.getObject(16);			
						
						while (rs5.next()) {
							
							logger.debug("CA claims "+rs5.getString("TOTAL_CLAIMS")+" "+rs5.getString("TOTAL_PAID"));
							
							if(null != (callableStatment.getString(9))){
								auditAssessmentIs5.setAssociateName(callableStatment.getString(9));
							}
							else{
								auditAssessmentIs5.setAssociateName("");
							}
							if(null != (callableStatment.getString(10))){
								auditAssessmentIs5.setEmpNO(callableStatment.getString(10));
							}
							else{
								auditAssessmentIs5.setEmpNO("");
							}
							if(null != (callableStatment.getString(11))){
								auditAssessmentIs5.setSupervisor(callableStatment.getString(11));
							}
							else{
								auditAssessmentIs5.setSupervisor("");
							}
							
							if((null == (rs5.getString("TOTAL_CLAIMS")))||((rs5.getString("TOTAL_CLAIMS")).equalsIgnoreCase(""))){
								auditAssessmentIs5.setIsTotalClaims("0");
							}
							else{
								auditAssessmentIs5.setIsTotalClaims((rs5.getString("TOTAL_CLAIMS")));
							}
							
							auditAssessmentIs5.setIsTotalPaid((rs5.getString("TOTAL_PAID")));
							auditAssessmentIs5.setIsTotalOvPaid((rs5.getString("TOTAL_OVER_PAID")));
							auditAssessmentIs5.setIsTotalUnPaid((rs5.getString("TOTAL_UNDER_PAID")));

							claimsAuditAssessmentList.add(auditAssessmentIs5);
						}
						
						rs5.close();
						
						
						//IS error claim details RS
						
						ResultSet  rs6 =   (ResultSet) callableStatment.getObject(17);			
						
						while (rs6.next()) {
							
							if((null == (rs6.getString("PROCEDURAL_ERROR_CLAIMS")))||((rs6.getString("PROCEDURAL_ERROR_CLAIMS")).equalsIgnoreCase(""))){
								auditAssessmentIs5.setIsProcClaims("0");
							}
							else{
								auditAssessmentIs5.setIsProcClaims((rs6.getString("PROCEDURAL_ERROR_CLAIMS")).toString());
							}
							
							if((null == (rs6.getString("MONETARY_ERROR_CLAIMS")))||((rs6.getString("MONETARY_ERROR_CLAIMS")).equalsIgnoreCase(""))){
								auditAssessmentIs5.setIsMonClaims("0");
							}
							else{
								auditAssessmentIs5.setIsMonClaims((rs6.getString("MONETARY_ERROR_CLAIMS")).toString());
							}
							claimsAuditAssessmentList.add(auditAssessmentIs5);
						}
						rs6.close();
					}
					
	}else{
			
			/*Out-of-Sample RS*/
			ResultSet  rs1 =   (ResultSet) callableStatment.getObject(12);			
			logger.debug("Row count OOS " +rs1.getRow());
			
			while (rs1.next()) {
				
				AuditAssessment auditAssessmentOos1 = new AuditAssessment();
				
				logger.debug("Assesment RS "+rs1.getString("DCN_NBR")+ " memb "+rs1.getString("SNO")+ "total " +rs1.getString("TOTAL_PAID"));
				
				if(null != (callableStatment.getString(9))){
					auditAssessmentOos1.setAssociateName(callableStatment.getString(9));
				}
				else{
					auditAssessmentOos1.setAssociateName("");
				}
				if(null != (callableStatment.getString(10))){
					auditAssessmentOos1.setEmpNO(callableStatment.getString(10));
				}
				else{
					auditAssessmentOos1.setEmpNO("");
				}
				if(null != (callableStatment.getString(11))){
					auditAssessmentOos1.setSupervisor(callableStatment.getString(11));
				}
				else{
					auditAssessmentOos1.setSupervisor("");
				}
				logger.debug("Asso det " +callableStatment.getString(9)+" "+callableStatment.getString(10)+" "+callableStatment.getString(11));
				
				//OOS
				auditAssessmentOos1.setOosSno((rs1.getString("SNO")));
				auditAssessmentOos1.setOosDcn((rs1.getString("DCN_NBR")));
				auditAssessmentOos1.setOosMemId((rs1.getString("MEMB_ID")));
							
				if(null != (rs1.getString("PLATFORM"))){
					auditAssessmentOos1.setOosPlatForm((rs1.getString("PLATFORM")));
				}
				else{
					auditAssessmentOos1.setOosPlatForm("");
				}
				
				
				auditAssessmentOos1.setOosPaid((rs1.getString("TOTAL_PAID")));
				auditAssessmentOos1.setOosOverPaid((rs1.getString("OVER_PAID")));
				auditAssessmentOos1.setOosUnderPaid((rs1.getString("UNDER_PAID")));
				auditAssessmentOos1.setOosHighDoller((rs1.getString("HIGH_DOLLAR_FLG")));
				auditAssessmentOos1.setOosAuditType((rs1.getString("AUDIT_TYPE")));
				auditAssessmentOos1.setOosMockAudit((rs1.getString("MOCK_FLG")));
				auditAssessmentOos1.setReportType((rs1.getString("OOS_FLG")));
				type= "oos";
				if(null != (rs1.getString("REQUIRED_FLG"))){
					auditAssessmentOos1.setOosAdjRequired((rs1.getString("REQUIRED_FLG")).toString());
				}
				else{
					auditAssessmentOos1.setOosAdjRequired("");
				}
				auditAssessmentOos1.setOosProcessOn((rs1.getString("PROCESS_DT")));
				
				if(null != (rs1.getString("ERROR_ID"))){
					auditAssessmentOos1.setOosErrorCode((rs1.getString("ERROR_ID")).toString());
				}
				else{
					auditAssessmentOos1.setOosErrorCode("");
				}
				if(null != (rs1.getString("MONETARY_FLG"))){
					auditAssessmentOos1.setOosMonetary((rs1.getString("MONETARY_FLG")).toString());
				}
				else{
					auditAssessmentOos1.setOosMonetary("");
				}
				if(null != (rs1.getString("PROCEDURAL_FLG"))){
					auditAssessmentOos1.setOosProcedural((rs1.getString("PROCEDURAL_FLG")).toString());
				}
				else{
					auditAssessmentOos1.setOosProcedural("");
				}
				if(null != (rs1.getString("ROOT_CAUSE"))){
					auditAssessmentOos1.setOosErrorType((rs1.getString("ROOT_CAUSE")).toString());
				}
				else{
					auditAssessmentOos1.setOosErrorType("");
				}
				if(null != (rs1.getString("ERROR_REASON_DESC"))){
					auditAssessmentOos1.setOosExplanation((rs1.getString("ERROR_REASON_DESC")).toString());
					logger.info("verbiage issue get reason for error====debug on 08/08/2018"+rs1.getString("ERROR_REASON_DESC"));
				}
				else{
					auditAssessmentOos1.setOosExplanation("");
				}
				if(null != (rs1.getString("FYI"))){
					auditAssessmentOos1.setOosFyi("FYI - "+(rs1.getString("FYI")).toString());
				}
				else{
					auditAssessmentOos1.setOosFyi("");
				}
				if(null != (rs1.getString("SOP_NEWS_FLASH_REFNC"))){
					auditAssessmentOos1.setOosSop((rs1.getString("SOP_NEWS_FLASH_REFNC")).toString());
				}
				else{
					auditAssessmentOos1.setOosSop("");
				}
				
				if(null != (rs1.getString("PRIMARY_PERF_GROUP_ID"))){
					auditAssessmentOos1.setOosPriPerfGroup("P-"+((rs1.getString("PRIMARY_PERF_GROUP_ID")).toString()));
				}
				else{
					auditAssessmentOos1.setOosPriPerfGroup("");
				}
				if(null != (rs1.getString("SECONDARY_PERF_GROUP_ID"))){
					auditAssessmentOos1.setOosSecPerfGroup("S-"+((rs1.getString("SECONDARY_PERF_GROUP_ID")).toString()));
				}
				else{
					auditAssessmentOos1.setOosSecPerfGroup("");
				}
				
				auditAssessmentOos1.setOosAuditor((rs1.getString("USER_ID")).toString());
				auditAssessmentOos1.setOosAuditDate((rs1.getString("AUDIT_DT")).toString());
				
				flag = true;
				
				claimsAuditAssessmentList.add(auditAssessmentOos1);
			}	
			
			rs1.close();
			
			logger.debug("OOS flag "+flag);
			if(flag){
				//RS2 OOS Claim paid details
				
				AuditAssessment auditAssessmentOos2 = new AuditAssessment();
				
				ResultSet  rs2 =   (ResultSet) callableStatment.getObject(13);			
				
				while (rs2.next()) { 
					
					logger.debug("CA2 claims "+rs2.getString("TOTAL_CLAIMS")+" "+rs2.getString("TOTAL_PAID")+" "+rs2.getString("TOTAL_OVER_PAID")+" "+rs2.getString("TOTAL_UNDER_PAID"));
					
					if(null != (callableStatment.getString(9))){
						auditAssessmentOos2.setAssociateName(callableStatment.getString(9));
					}
					else{
						auditAssessmentOos2.setAssociateName("");
					}
					if(null != (callableStatment.getString(10))){
						auditAssessmentOos2.setEmpNO(callableStatment.getString(10));
					}
					else{
						auditAssessmentOos2.setEmpNO("");
					}
					if(null != (callableStatment.getString(11))){
						auditAssessmentOos2.setSupervisor(callableStatment.getString(11));
					}
					else{
						auditAssessmentOos2.setSupervisor("");
					}
					
					if((null == (rs2.getString("TOTAL_CLAIMS")))||((rs2.getString("TOTAL_CLAIMS")).equalsIgnoreCase(""))){
						auditAssessmentOos2.setOosTotalClaims("0");
					}
					else{
						auditAssessmentOos2.setOosTotalClaims((rs2.getString("TOTAL_CLAIMS")));

					}
					auditAssessmentOos2.setOosTotalPaid((rs2.getString("TOTAL_PAID")));
					auditAssessmentOos2.setOosTotalOvPaid((rs2.getString("TOTAL_OVER_PAID")));
					auditAssessmentOos2.setOosTotalUnPaid((rs2.getString("TOTAL_UNDER_PAID")));
					
					claimsAuditAssessmentList.add(auditAssessmentOos2);
					
				}
				
				rs2.close();
				
				//OOS error claim details RS
				
				ResultSet  rs3 =   (ResultSet) callableStatment.getObject(14);			
				
				while (rs3.next()) {
					
					logger.debug("OOS R3");
					if((null == (rs3.getString("PROCEDURAL_ERROR_CLAIMS")))||((rs3.getString("PROCEDURAL_ERROR_CLAIMS")).equalsIgnoreCase(""))){
						auditAssessmentOos2.setOosProcClaims("0");
					}
					else{
						auditAssessmentOos2.setOosProcClaims((rs3.getString("PROCEDURAL_ERROR_CLAIMS")));

					}
					if((null == (rs3.getString("MONETARY_ERROR_CLAIMS")))||((rs3.getString("MONETARY_ERROR_CLAIMS")).equalsIgnoreCase(""))){
						auditAssessmentOos2.setOosMonClaims("0");
					}
					else{
						auditAssessmentOos2.setOosMonClaims((rs3.getString("MONETARY_ERROR_CLAIMS")));

					}
					
					claimsAuditAssessmentList.add(auditAssessmentOos2);
				}
				
				rs3.close();
				
			 }
			
		  }
				
			callableStatment.close();
			 
			conn.close();
			
		} catch (Exception e) {
			logger.debug("Exception generated "+e.getMessage());
		}
		
		logger.debug(claimsAuditAssessmentList.size());
		for(AuditAssessment a:claimsAuditAssessmentList){
			logger.debug("List Value "+objectToXML(a));
		}
		
		
		//JRDataSource ds = new JRBeanCollectionDataSource(claimsAuditAssessmentList);	
		
		// Return the wrapped collection
		return claimsAuditAssessmentList;
	}
	
	 
}
