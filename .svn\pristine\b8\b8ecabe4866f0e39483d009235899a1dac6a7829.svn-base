package com.carefirst.qadb.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import javax.sql.DataSource;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import oracle.jdbc.OracleTypes;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import com.carefirst.audit.model.AuditCounts;
import com.carefirst.audit.model.AuditSave;
import com.carefirst.audit.model.AuditSearch;
import com.carefirst.audit.model.Products;
import com.carefirst.audit.model.Statistics;
import com.carefirst.qadb.constant.QADBConstants;


public class AuditSearchDAOImpl implements AuditSearchDAO{
	
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);
	
	@Autowired
	DataSource dataSource;   
	 
	
	@Override
	public List<AuditSearch> getSearchResults(AuditSearch searchTO) throws SQLException {
		
		logger.debug("*** Entry getSearchResults method ***");
		logger.debug("Search DCN " +searchTO.getDcn());
		logger.debug("Search Asso " +searchTO.getAssociateName());
		logger.debug("Search Asso Id " +searchTO.getEmpNo());
		String searchAuditSp = QADBConstants.AUDIT_SEARCH_SP; 
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(searchAuditSp);
		
		List<AuditSearch> auditSearchEO = new ArrayList<AuditSearch>();
		
		try {
				callableStatment.setString(1,searchTO.getSearchType());
				logger.debug("**Search Type**"+searchTO.getSearchType());
				callableStatment.setNull(2, java.sql.Types.VARCHAR);
				callableStatment.setNull(3, java.sql.Types.INTEGER);
				
				if(null!=searchTO.getAssociateName()){
					if((searchTO.getAssociateName()).equalsIgnoreCase("")){
						callableStatment.setNull(4, java.sql.Types.VARCHAR);
					}else{
						callableStatment.setString(4, searchTO.getAssociateName());
					}
				}else{
					callableStatment.setNull(4, java.sql.Types.VARCHAR);
				}
				
				if(null!=searchTO.getEmpNo()){
					if((searchTO.getEmpNo()).equalsIgnoreCase("")){
						callableStatment.setNull(5, java.sql.Types.VARCHAR);
						
					}else{
						callableStatment.setString(5, searchTO.getEmpNo());
					}
				}else{
					callableStatment.setNull(5, java.sql.Types.VARCHAR);
				}
				
				if(null!=searchTO.getDcn()){
					if((searchTO.getDcn()).equalsIgnoreCase("")){
						callableStatment.setNull(6, java.sql.Types.VARCHAR);
					}else{
						callableStatment.setString(6, searchTO.getDcn());
					}
				}else{
					callableStatment.setNull(6, java.sql.Types.VARCHAR);
				}
				
				if(null!=searchTO.getProcessedDateFrom()){
					if((searchTO.getProcessedDateFrom()).equalsIgnoreCase("")){
						callableStatment.setNull(7, java.sql.Types.DATE);
					}else{
						callableStatment.setString(7, searchTO.getProcessedDateFrom());
					}
				}else{
					callableStatment.setNull(7, java.sql.Types.DATE);
				}
				
				if(null!=searchTO.getProcessedDateTo()){
					if((searchTO.getProcessedDateTo()).equalsIgnoreCase("")){
						callableStatment.setNull(8, java.sql.Types.DATE);
					}else{
						callableStatment.setString(8,searchTO.getProcessedDateTo());
					}
				}else{
					callableStatment.setNull(8, java.sql.Types.DATE);
				}
				
				if(null!=searchTO.getAuditorName()){
					if((searchTO.getAuditorName()).equalsIgnoreCase("")){
						callableStatment.setNull(9, java.sql.Types.VARCHAR);
					}else{
						callableStatment.setString(9, searchTO.getAuditorName());
					}
				}else{
					callableStatment.setNull(9, java.sql.Types.VARCHAR);
				}
				
				if(null!=searchTO.getAuditDateFrom()){
					if((searchTO.getAuditDateFrom()).equalsIgnoreCase("")){
						callableStatment.setNull(10, java.sql.Types.DATE);
					}else{
						callableStatment.setString(10,searchTO.getAuditDateFrom());
					}
				}else{
					callableStatment.setNull(10, java.sql.Types.DATE);
				}
				
				
				if(null!=searchTO.getAuditDateTo()){
					if((searchTO.getAuditDateTo()).equalsIgnoreCase("")){
						callableStatment.setNull(11, java.sql.Types.DATE);
					}else{
						callableStatment.setString(11, searchTO.getAuditDateTo());
					}
				}else{
					callableStatment.setNull(11, java.sql.Types.DATE);
				}
				
				if(null!=searchTO.getProcessType()){
					if((searchTO.getProcessType()).equalsIgnoreCase("")){
						callableStatment.setNull(12, java.sql.Types.INTEGER);
						
					}else{
						callableStatment.setInt(12, Integer.parseInt(searchTO.getProcessType()));
					}
				}else{
					callableStatment.setNull(12, java.sql.Types.INTEGER);
				}
				
				if(null!=searchTO.getErrorCode()){
					if((searchTO.getErrorCode()).equalsIgnoreCase("")){
						callableStatment.setNull(13, java.sql.Types.VARCHAR);
						
					}else{
						callableStatment.setString(13, searchTO.getErrorCode());
					}
				}else{
					callableStatment.setNull(13, java.sql.Types.VARCHAR);
				}
				
				if(null!=searchTO.getPiType()){
					if((searchTO.getPiType()).equalsIgnoreCase("")){
						callableStatment.setNull(14, java.sql.Types.INTEGER);
					}else{
						callableStatment.setInt(14, Integer.parseInt(searchTO.getPiType()));
					}
				}else{
					callableStatment.setNull(14, java.sql.Types.INTEGER);
				}
				
				if(null!=searchTO.getPriPGA()){
					if((searchTO.getPriPGA()).equalsIgnoreCase("")){
						callableStatment.setNull(15, java.sql.Types.INTEGER);
						
					}else{
						callableStatment.setInt(15, Integer.parseInt(searchTO.getPriPGA()));
					}
				}else{
					callableStatment.setNull(15, java.sql.Types.INTEGER);
				}
				
				if(null!=searchTO.getSecPGA()){
					if((searchTO.getSecPGA()).equalsIgnoreCase("")){
						callableStatment.setNull(16, java.sql.Types.INTEGER);
						
					}else{
						callableStatment.setInt(16, Integer.parseInt(searchTO.getSecPGA()));
					}
				}else{
					callableStatment.setNull(16, java.sql.Types.INTEGER);
				}
				
				if(null!=searchTO.getMemberID()){
					if((searchTO.getMemberID()).equalsIgnoreCase("")){
						callableStatment.setNull(17, java.sql.Types.VARCHAR);
					}else{
						callableStatment.setString(17, searchTO.getMemberID());
					}
				}else{
					callableStatment.setNull(17, java.sql.Types.VARCHAR);
				}
				
				callableStatment.setNull(18, java.sql.Types.VARCHAR);
				String userGrp = searchTO.getUserGroup();
				logger.debug("Search grp "+searchTO.getUserGroup());
				if((null != userGrp)&&(userGrp.equalsIgnoreCase("('SAMMD','CD','ALL')"))){
					userGrp = "ALL";
				}
				else if((null != userGrp)&&(userGrp.equalsIgnoreCase("('SAMMD','ALL')"))){
					userGrp = "SAMMD";
				}
				else if((null != userGrp)&&(userGrp.equalsIgnoreCase("('CD','ALL')"))){
					userGrp = "CD";
				}
				
				if(null == userGrp){
					callableStatment.setNull(19, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(19,userGrp);//SAMMD, CD, ALL
				}
				
				logger.debug("e2e "+searchTO.getE2e());
				if(null!=searchTO.getE2e()){
					if((searchTO.getE2e()).equalsIgnoreCase("")){
						callableStatment.setNull(20, java.sql.Types.VARCHAR);
						
					}else{
						callableStatment.setString(20, searchTO.getE2e());
					}
				}else{
					callableStatment.setNull(20, java.sql.Types.VARCHAR);
				}
				
				logger.debug("oos "+searchTO.getOos());
				if(null!=searchTO.getOos()){
					if((searchTO.getOos()).equalsIgnoreCase("")){
						callableStatment.setNull(21, java.sql.Types.VARCHAR);
						
					}else{
						callableStatment.setString(21, searchTO.getOos());
					}
				}else{
					callableStatment.setNull(21, java.sql.Types.VARCHAR);
				}
				logger.debug("**Input exe completed**");
				if (searchTO.getPlatform() != null && !searchTO.getPlatform().isEmpty())
				{
					callableStatment.setString(22, searchTO.getPlatform());
				} else {
					callableStatment.setNull(22, java.sql.Types.VARCHAR);
				}
				//Out
				callableStatment.registerOutParameter(23, OracleTypes.CURSOR);
				callableStatment.registerOutParameter(24, OracleTypes.CURSOR);
				callableStatment.registerOutParameter(25, OracleTypes.CURSOR);
				callableStatment.registerOutParameter(26, OracleTypes.VARCHAR);
				callableStatment.registerOutParameter(27, OracleTypes.VARCHAR);
				callableStatment.registerOutParameter(28, OracleTypes.VARCHAR);
				callableStatment.registerOutParameter(29, OracleTypes.VARCHAR);
				
				logger.debug("Execute proc "+callableStatment.execute());
				logger.debug("Status message = "+callableStatment.getString(28));
				callableStatment.executeUpdate();
				
				ResultSet  rs =   (ResultSet) callableStatment.getObject(23);			
				logger.debug("Row counts " +rs.getRow());
				
				while (rs.next()) {
					AuditSearch auditSearchEOs = new AuditSearch();
					auditSearchEOs.setAuditId((rs.getString("AUDIT_ID")).toString());
					auditSearchEOs.setDcn((rs.getString("DCN_NBR")).toString());
					auditSearchEOs.setEmpNo((rs.getString("ASSOCIATE_ID")).toString());
					auditSearchEOs.setAssociateName((rs.getString("ASSOCIATE_NAME")).toString());
					if(null!=rs.getString("PROCESS_DT")){
						auditSearchEOs.setProcessedDateFrom((rs.getString("PROCESS_DT")).toString());
					}
					auditSearchEOs.setAuditorName((rs.getString("AUDITOR")).toString());
					auditSearchEOs.setAuditDateFrom((rs.getString("AUDIT_DT")).toString());
					
					auditSearchEO.add(auditSearchEOs);
				}	
				 rs.close();
			} catch (Exception e) {
				e.printStackTrace();
				logger.debug("Exception generated "+e.getMessage());
			}finally{
				callableStatment.close();
				conn.close();
			}
			logger.debug("*** Exit getSearchResults method ***");
			
		
		
		return auditSearchEO;
		
	}


	@Override
	public AuditSave getEditAudit(AuditSearch auditSearchTO) {
		
		
		String editAuditSp = QADBConstants.AUDIT_SEARCH_SP; 
		
		logger.debug("After edit SP");
		
		DecimalFormat df1 = new DecimalFormat("0.00");
		
		//List<AuditSave> auditSaveEO = new ArrayList<AuditSave>();
		AuditSave auditSaveEOs = new AuditSave();
		
		try {
			
			Connection conn = dataSource.getConnection();
			CallableStatement callableStatment = conn.prepareCall(editAuditSp);
			
			if((auditSearchTO.getPageType()).equals("GNRL")){
				
				callableStatment.setString(1,auditSearchTO.getSearchType());
				callableStatment.setString(2,auditSearchTO.getPageType());
				callableStatment.setInt(3,Integer.parseInt(auditSearchTO.getAuditId()) );
				
				callableStatment.setString(4, null);
				callableStatment.setInt(5, Types.INTEGER);
				callableStatment.setString(6, auditSearchTO.getDcn());
				callableStatment.setDate(7,null);
				callableStatment.setDate(8,null);
				callableStatment.setString(9, null);
				
				callableStatment.setDate(10,null);
				callableStatment.setDate(11,null);
				callableStatment.setInt(12,Types.INTEGER );
				callableStatment.setInt(13,Types.INTEGER );
				callableStatment.setInt(14,Types.INTEGER );
				callableStatment.setInt(15,Types.INTEGER );
				callableStatment.setInt(16,Types.INTEGER );
				
			    callableStatment.setNull(17, java.sql.Types.VARCHAR);
			    callableStatment.setNull(18, java.sql.Types.VARCHAR);
			    callableStatment.setNull(19, java.sql.Types.VARCHAR);
			    callableStatment.setNull(20, java.sql.Types.VARCHAR);
			    callableStatment.setNull(21, java.sql.Types.VARCHAR);
				if (auditSearchTO.getPlatform() != null && !auditSearchTO.getPlatform().isEmpty())
				{
					callableStatment.setString(22, auditSearchTO.getPlatform());
				} else {
					callableStatment.setNull(22, java.sql.Types.VARCHAR);
				}
			    
				logger.debug("EditGen all done");
				
				
				//Out
				callableStatment.registerOutParameter(23, OracleTypes.CURSOR);
				callableStatment.registerOutParameter(24, OracleTypes.CURSOR);
				callableStatment.registerOutParameter(25, OracleTypes.CURSOR);
				callableStatment.registerOutParameter(26, OracleTypes.VARCHAR);
				callableStatment.registerOutParameter(27, OracleTypes.VARCHAR);
				callableStatment.registerOutParameter(28, OracleTypes.VARCHAR);
				callableStatment.registerOutParameter(29, OracleTypes.VARCHAR);
				logger.debug("EditGen all done2");
				
				callableStatment.executeUpdate();
				logger.debug("EditGen EXEC complte");
				
				ResultSet  rs =   (ResultSet) callableStatment.getObject(23);
				//ResultSet rs = callableStatment.executeQuery();
				
				logger.debug("RS "+rs);
				logger.debug("Row count" +rs.getRow());
				
				while (rs.next()) {
					
					logger.debug("EO "+rs.getString("DCN_NBR")+" Cid "+rs.getString("CLAIM_TYPE_ID")+" lob "+rs.getString("LOB_VAL")+" Mid "+rs.getString("MEMB_ID")+" PD "+rs.getString("PROCESS_DT")+" PaidD "+rs.getString("PAID_DT")+" TC "+rs.getString("TOTAL_CHARGE")+" OOS "+rs.getString("OOS_FLG")+" PG2 "+rs.getString("SECONDARY_PERF_GROUP_ID"));
					
					//logger.debug("prcDt"+new SimpleDateFormat("MM-dd-yy").format((rs.getString("PROCESS_DT"))));
					
					auditSaveEOs.setDcn(rs.getString("DCN_NBR"));
					auditSaveEOs.setClaimType(rs.getString("CLAIM_TYPE_ID"));
					auditSaveEOs.setLob(rs.getString("LOB_VAL"));
					auditSaveEOs.setMemberId(rs.getString("MEMB_ID"));
					auditSaveEOs.setPlatform(rs.getString("PLATFORM"));
					//auditSaveEOs.setProcessDate(rs.getDate("PROCESS_DT"));
					auditSaveEOs.setProcessDate(new SimpleDateFormat("MM-dd-yy").format((rs.getDate("PROCESS_DT"))));
					if(null != (rs.getDate("PAID_DT"))){
						auditSaveEOs.setPaidDate(new SimpleDateFormat("MM-dd-yy").format((rs.getDate("PAID_DT"))));
					}else{
						auditSaveEOs.setPaidDate("");
					}
					//auditSaveEOs.setPaidDate(new SimpleDateFormat("MM-dd-yy").format((rs.getDate("PAID_DT"))));
					auditSaveEOs.setTotalCharge(df1.format(Double.parseDouble(rs.getString("TOTAL_CHARGE"))));
					auditSaveEOs.setPaid(df1.format(Double.parseDouble(rs.getString("TOTAL_PAID"))));
					
					auditSaveEOs.setMonetaryError(rs.getString("MONETARY_ERROR_FLG"));
					auditSaveEOs.setAmountp(rs.getString("MONETARY_ERROR_AMOUNT"));
					auditSaveEOs.setTheoreticalPaid(rs.getString("MONETARY_ERROR_PAID"));
					auditSaveEOs.setPiChk(rs.getString("PENALTY_INTEREST_FLG"));
					auditSaveEOs.setPenaltyInterestType(rs.getString("PI_TYPE_ID"));
					auditSaveEOs.setPi(rs.getString("PENALTY_INTEREST_AMOUNT"));
					auditSaveEOs.setJurisdiction(rs.getString("JURISDICTION"));
					
					auditSaveEOs.setAuditType(rs.getString("AUDIT_TYPE_ID"));
					auditSaveEOs.setPriPGA(rs.getString("PRIMARY_PERF_GROUP_ID"));
					auditSaveEOs.setSecPGA(rs.getString("SECONDARY_PERF_GROUP_ID"));
					auditSaveEOs.setMock(rs.getString("MOCK_FLG"));
					auditSaveEOs.setOos(rs.getString("OOS_FLG"));
					auditSaveEOs.setE2e(rs.getString("E2E_FLG"));
					auditSaveEOs.setRiskAccount(rs.getString("RISK_ACCOUNT_FLG"));
					auditSaveEOs.setProcesstype(rs.getString("PROCESS_TYPE_ID"));
					auditSaveEOs.setPlatform(rs.getString("PLATFORM"));
					auditSaveEOs.setAssociateFacetsId(rs.getString("ASSOCIATE_FACETS_ID"));
					auditSaveEOs.setFyi(rs.getString("FYI"));
					
					logger.debug("sub g "+rs.getString("SUBSCRIBER_CK"));
					auditSaveEOs.setSubscriberCK(rs.getString("SUBSCRIBER_CK"));
					auditSaveEOs.setAccountID(rs.getString("ACCOUNT_ID"));
					auditSaveEOs.setAccountName(rs.getString("ACCOUNT_NAME"));
					auditSaveEOs.setGroup(rs.getString("GROUP_CODE"));
					auditSaveEOs.setClaimCurrentStatus(rs.getString("CLAIM_CURRENT_STUS"));
					auditSaveEOs.setProductID(rs.getString("PRODUCT_ID"));
					logger.debug("Adj code "+rs.getString("ADJUSTMENT_REASON_CODE"));
					auditSaveEOs.setAdjustmentReasoncode(rs.getString("ADJUSTMENT_REASON_CODE"));
					logger.debug("fyi "+rs.getString("FYI"));
					
				}	
				rs.close();
				
				
				ResultSet  rs2 =   (ResultSet) callableStatment.getObject(24);
				//ResultSet rs = callableStatment.executeQuery();
				
				
				logger.debug("RS "+rs2);
				logger.debug("Row count" +rs2.getRow());
				
				List<Products> productsEO  = new ArrayList<Products>();
				while (rs2.next()) {
				
					Products pr = new Products();
					logger.debug("RS "+rs2.getString("BSBS_CODE"));
					pr.setBSBSCode(rs2.getString("BSBS_CODE"));
					pr.setGroupID(rs2.getString("GROUP_ID"));
					pr.setProductLine(rs2.getString("PRODUCT_LINE"));
					pr.setProductDescription(rs2.getString("PRODUCT_DESCRIPTION"));
					productsEO.add(pr);
				}
				
				rs2.close();
				
				auditSaveEOs.setProductInfo(productsEO);
				
				ResultSet  rsHigh =   (ResultSet) callableStatment.getObject(25);
				logger.debug("RS "+rsHigh);
				logger.debug("Row count" +rsHigh.getRow());
				
				
				while (rsHigh.next()) {
					//High Dollar
					logger.debug("get Ex "+rsHigh.getString("EXAMINER_NAME"));
					auditSaveEOs.setClaimAdjFlag(rsHigh.getString("CLAIM_ADJUSTMENT_FLG"));
					auditSaveEOs.setServiceAdjFlag(rsHigh.getString("SERVICE_ADJUSTMENT_FLG"));
					auditSaveEOs.setExaminerName(rsHigh.getString("EXAMINER_NAME"));
					auditSaveEOs.setQAName(rsHigh.getString("QA_ANALYST_NAME"));
					auditSaveEOs.setPatientName(rsHigh.getString("PATIENT_NAME"));
					auditSaveEOs.setServiceDates((rsHigh.getString("SERVICE_DATES")));
					auditSaveEOs.setTypeOfService(rsHigh.getString("TYPE_OF_SERVICE"));
					auditSaveEOs.setDiagnosis(rsHigh.getString("DIAGNOSIS"));
					
					if(null!=rsHigh.getString("SURGERY_DATE")){
						logger.debug("CLAIM_ADJUSTMENT_FLG"+new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("SURGERY_DATE"))));
						auditSaveEOs.setSurgeryDOS(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("SURGERY_DATE"))));
					}else {
						auditSaveEOs.setSurgeryDOS("");
					}
					
					if(null!=rsHigh.getString("SURGERY_DESC")){
						auditSaveEOs.setSurgery(rsHigh.getString("SURGERY_DESC"));
					}else {
						auditSaveEOs.setSurgery("");
					}
					
					if(null!=rsHigh.getString("AUTH_FILE_REFERENCE")){
						auditSaveEOs.setFileReferenced(rsHigh.getString("AUTH_FILE_REFERENCE"));
					}else {
						auditSaveEOs.setFileReferenced("");
					}
					
					logger.debug("interest R "+rsHigh.getString("INTEREST_PAID"));
					auditSaveEOs.setInterestPaid(rsHigh.getString("INTEREST_PAID"));
					auditSaveEOs.setProviderName(rsHigh.getString("PROVIDER_NAME"));
					auditSaveEOs.setProviderNumber(rsHigh.getString("PROVIDER_ID"));
					auditSaveEOs.setPayee(rsHigh.getString("PAYEE"));
					
					if(null!=rsHigh.getString("NOTES")){
						auditSaveEOs.setNotes(rsHigh.getString("NOTES"));
					}else {
						auditSaveEOs.setNotes("");
					}
					
					if(null!=rsHigh.getString("QA_AUDITOR_SIGN_DATE")){
						auditSaveEOs.setAudSignDate(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("QA_AUDITOR_SIGN_DATE"))));
					}else {
						auditSaveEOs.setAudSignDate("");
					}
					
					if(null!=rsHigh.getString("VP_SIGN_DATE")){
						auditSaveEOs.setVpSignDate(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("VP_SIGN_DATE"))));
					}else {
						auditSaveEOs.setVpSignDate("");
					}
					
					if(null!=rsHigh.getString("FORWARDED_TO_DATE")){
						auditSaveEOs.setForwrdToDate(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("FORWARDED_TO_DATE"))));
					}else {
						auditSaveEOs.setForwrdToDate("");
					}
					
					if(null!=rsHigh.getString("RECEIVED_FROM_DATE")){
						auditSaveEOs.setRcvedFrmDate(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("RECEIVED_FROM_DATE"))));
					}else {
						auditSaveEOs.setRcvedFrmDate("");
					}
					if(null!=rsHigh.getString("RELEASED_BY_DATE")){
						auditSaveEOs.setReleasedByDate(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("RELEASED_BY_DATE"))));
					}else {
						auditSaveEOs.setReleasedByDate("");
					}
				}
				rsHigh.close();
				
				auditSaveEOs.setLastSavedBy(callableStatment.getString(26));
				auditSaveEOs.setLastSavedOn(callableStatment.getString(27));
				auditSaveEOs.setSucessCode(callableStatment.getString(28));
				auditSaveEOs.setSuccessMsg(callableStatment.getString(29));
				
				
				logger.debug("Edit savedBy msg "+callableStatment.getString(26));
				logger.debug("Edit savedON  "+callableStatment.getString(27));
				
				 callableStatment.close();
				 
				 conn.close();
			
			}
			else if((auditSearchTO.getPageType()).equals("ERR")){
				
				logger.debug("**EditErrDetails**");
				callableStatment.setString(1,auditSearchTO.getSearchType());
				callableStatment.setString(2,auditSearchTO.getPageType());
				callableStatment.setInt(3,Integer.parseInt(auditSearchTO.getAuditId()) );
				callableStatment.setString(4, null);
				callableStatment.setNull(5, java.sql.Types.INTEGER);
				callableStatment.setString(6, auditSearchTO.getDcn());
				callableStatment.setDate(7,null);
				callableStatment.setDate(8,null);
				callableStatment.setString(9, null);
				
				callableStatment.setDate(10,null);
				callableStatment.setDate(11,null);
				callableStatment.setNull(12,java.sql.Types.INTEGER );
				callableStatment.setNull(13,java.sql.Types.INTEGER );
				callableStatment.setNull(14,java.sql.Types.INTEGER );
				callableStatment.setNull(15,java.sql.Types.INTEGER );
				callableStatment.setNull(16,java.sql.Types.INTEGER );
				callableStatment.setNull(17, java.sql.Types.VARCHAR);
			    callableStatment.setNull(18, java.sql.Types.VARCHAR);
			    callableStatment.setNull(19, java.sql.Types.VARCHAR);
			    callableStatment.setNull(20, java.sql.Types.VARCHAR);
			    callableStatment.setNull(21, java.sql.Types.VARCHAR);
				if (auditSearchTO.getPlatform() != null && !auditSearchTO.getPlatform().isEmpty())
				{
					callableStatment.setString(22, auditSearchTO.getPlatform());
				} else {
					callableStatment.setNull(22, java.sql.Types.VARCHAR);
				}
				logger.debug("**EditErr all completed**");
				//Out
				callableStatment.registerOutParameter(23, OracleTypes.CURSOR);
				callableStatment.registerOutParameter(24, OracleTypes.CURSOR);
				callableStatment.registerOutParameter(25, OracleTypes.CURSOR);
				callableStatment.registerOutParameter(26, OracleTypes.VARCHAR);
				callableStatment.registerOutParameter(27, OracleTypes.VARCHAR);
				callableStatment.registerOutParameter(28, OracleTypes.VARCHAR);
				callableStatment.registerOutParameter(29, OracleTypes.VARCHAR);
				
				logger.debug("**EditErr all Out param completed**");
				
				callableStatment.executeUpdate();
				logger.debug("**EditErr Exec completed**");
				
				ResultSet  rs =   (ResultSet) callableStatment.getObject(23);
				
				logger.debug("RS "+rs);
				logger.debug("Row count" +rs.getRow());
				
				while (rs.next()) {
					
					logger.debug("ErrTy "+rs.getString("ERROR_TYP")+" Sop "+rs.getString("SOP_NEWS_FLASH_REFNC")+" Rea "+rs.getString("ERROR_REASON_DESC")+" Comm "+rs.getString("COMMENTS_TEXT"));
					logger.debug("Req "+rs.getString("REQUIRED_FLG")+" compl "+rs.getString("COMPLETED_FLG")+" AdjD "+rs.getString("ADJUSTED_DATE")+" App "+rs.getString("APPEAL_FLG"));
					
					
					
					/*auditSaveEOs.set(rs.getString("DCN_NBR"));
					auditSaveEOs.set(rs.getString("DCN_NBR"));
					auditSaveEOs.set(rs.getString("DCN_NBR"));
					auditSaveEOs.set(rs.getString("DCN_NBR"));
					auditSaveEOs.set(rs.getString("DCN_NBR"));
					auditSaveEOs.set(rs.getString("DCN_NBR"));*/
					
					//auditSaveEOs.setErrorCode(rs.getString("ERROR_TYP"));
					
					auditSaveEOs.setErrorType(rs.getString("ERROR_TYP"));
					auditSaveEOs.setSop(rs.getString("SOP_NEWS_FLASH_REFNC"));
					auditSaveEOs.setReason(rs.getString("ERROR_REASON_DESC"));
					auditSaveEOs.setComments(rs.getString("COMMENTS_TEXT"));
					auditSaveEOs.setRequired(rs.getString("REQUIRED_FLG"));
					auditSaveEOs.setCompleted(rs.getString("COMPLETED_FLG"));
					if(null != (rs.getDate("ADJUSTED_DATE"))){
						auditSaveEOs.setDateAdj(new SimpleDateFormat("MM/dd/yyyy").format((rs.getDate("ADJUSTED_DATE"))));
					}
					auditSaveEOs.setAppeal(rs.getString("APPEAL_FLG"));
					
				}	
				rs.close();
				
				auditSaveEOs.setLastSavedBy(callableStatment.getString(26));
				auditSaveEOs.setLastSavedOn(callableStatment.getString(27));
				auditSaveEOs.setSucessCode(callableStatment.getString(28));
				auditSaveEOs.setSuccessMsg(callableStatment.getString(29));
				
				logger.debug("EditErr savedBy msg "+callableStatment.getString(26));
				logger.debug("EditErr savedON  "+callableStatment.getString(27));
				
				callableStatment.close();
				 
				conn.close();
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.debug("*** Exception ***"+e.getMessage());
		}
		return   auditSaveEOs;
		
	}


	public List<Statistics> getStatastics(AuditSearch auditSearchTO) throws SQLException {
		
		logger.debug("*** Entry getStatastics method ***");
		logger.debug("Search DCN " +auditSearchTO.getDcn());
		logger.debug("Search Asso " +auditSearchTO.getAssociateName());
		logger.debug("Search Asso Id " +auditSearchTO.getEmpNo());
		String auditStatSp = QADBConstants.AUDIT_SEARCH_SP; 
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(auditStatSp);
		
		List<Statistics> auditEditStatEOs = new ArrayList<Statistics>();
		
		try {
			
			logger.debug("**EditStats Start**)");
			callableStatment.setString(1,auditSearchTO.getSearchType());
			callableStatment.setString(2,auditSearchTO.getPageType());
			callableStatment.setInt(3,Integer.parseInt(auditSearchTO.getAuditId()) );
			callableStatment.setNull(4, java.sql.Types.VARCHAR);
			callableStatment.setNull(5, java.sql.Types.INTEGER);
			callableStatment.setString(6, auditSearchTO.getDcn());
			callableStatment.setNull(7, java.sql.Types.DATE);
			callableStatment.setNull(8, java.sql.Types.DATE);
			callableStatment.setNull(9, java.sql.Types.VARCHAR);
			
			callableStatment.setNull(10, java.sql.Types.DATE);
			callableStatment.setNull(11, java.sql.Types.DATE);
			callableStatment.setNull(12, java.sql.Types.INTEGER);
			callableStatment.setNull(13, java.sql.Types.INTEGER);
			callableStatment.setNull(14, java.sql.Types.INTEGER);
			callableStatment.setNull(15, java.sql.Types.INTEGER);
			callableStatment.setNull(16, java.sql.Types.INTEGER);
			
		    callableStatment.setNull(17, java.sql.Types.VARCHAR);
		    callableStatment.setNull(18, java.sql.Types.VARCHAR);
		    callableStatment.setNull(19, java.sql.Types.VARCHAR);
		    callableStatment.setNull(20, java.sql.Types.VARCHAR);
		    callableStatment.setNull(21, java.sql.Types.VARCHAR);
			if (auditSearchTO.getPlatform() != null && !auditSearchTO.getPlatform().isEmpty())
			{
				callableStatment.setString(22, auditSearchTO.getPlatform());
			} else {
				callableStatment.setNull(22, java.sql.Types.VARCHAR);
			}
			logger.debug("**EditStat all completed**");
			
			//Out
			callableStatment.registerOutParameter(23, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(24, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(25, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(26, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(27, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(28, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(29, OracleTypes.VARCHAR);

			logger.debug("**EditStat all out param completed**");
			
			logger.debug("Execute proc "+callableStatment.execute());
			logger.debug("Status message = "+callableStatment.getString(28));
			callableStatment.executeUpdate();
			
			ResultSet  rs =   (ResultSet) callableStatment.getObject(23);			
			logger.debug("Row counts " +rs.getRow());
			
			while (rs.next()) {
				Statistics stat = new Statistics();
				
				stat.setSavedBy((rs.getString("SAVED_BY")).toString());
				stat.setSavedDate((rs.getString("AUDIT_DATE")).toString());
				stat.setSavedTime((rs.getString("AUDIT_TIME")).toString());
				
				auditEditStatEOs.add(stat);
			}	
			rs.close();
			
		} catch (Exception e) {
			logger.debug("Exception generated "+e.getMessage());
		}finally{
			callableStatment.close();
			conn.close();
		}
		logger.debug("*** Exit getStatastics method ***");
		
		return auditEditStatEOs;
	}


	@Override
	public AuditCounts getAuditCounts(AuditCounts auditCountsTO) throws SQLException {
		logger.debug("*** Entry getAuditCounts method ***");
		logger.debug("audit count id  " +auditCountsTO.getFacetsId());
		
		String auditCountSp = QADBConstants.AUDIT_COUNT_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(auditCountSp);
		
		AuditCounts auditCountsEO = new AuditCounts();
		
		try {

			callableStatment.setString(1,auditCountsTO.getFacetsId());
			callableStatment.setString(2,auditCountsTO.getInpProccessDate());

			//Out
			callableStatment.registerOutParameter(3, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(4, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(5, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(6, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(7, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(8, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(9, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(10, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(11, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(12, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(13, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(14, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(15, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(16, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(17, OracleTypes.VARCHAR);
			
			logger.debug("Execute proc "+callableStatment.execute());
			
			callableStatment.executeUpdate();
			
			logger.debug("Status code = "+callableStatment.getString(16));
			logger.debug("Status message = "+callableStatment.getString(17));
			
			auditCountsEO.setInsCount(callableStatment.getString(3));
			auditCountsEO.setInsNonMockCount(callableStatment.getString(4));
			auditCountsEO.setInsMockCount(callableStatment.getString(5));
			auditCountsEO.setInsPrePayCount(callableStatment.getString(6));
			auditCountsEO.setInsPostPayCount(callableStatment.getString(7));
			
			auditCountsEO.setOosCount(callableStatment.getString(8));
			auditCountsEO.setOosNonMockCount(callableStatment.getString(9));
			auditCountsEO.setOosMockCount(callableStatment.getString(10));
			auditCountsEO.setOosPrePayCount(callableStatment.getString(11));
			auditCountsEO.setOosPostPayCount(callableStatment.getString(12));
			
			auditCountsEO.setTotalAuditCount(callableStatment.getString(13));
			
			auditCountsEO.setProcessDate(callableStatment.getString(14));
			auditCountsEO.setAssociateName(callableStatment.getString(15));
			auditCountsEO.setStatusCode(callableStatment.getString(16));
			auditCountsEO.setStatusMsg(callableStatment.getString(17));
			
		} catch (Exception e) {
			logger.debug("Exception generated "+e.getMessage());
		}finally{
			callableStatment.close();
			conn.close();
		}
		logger.debug("*** Exit getAuditCounts method ***");
		
		return auditCountsEO;
	}


	@Override
	public JRDataSource getHighDollarReport(String auditId) throws SQLException {
		
		logger.debug("*** Entry getHighDollarReport method ***");
		String highDollerReportSP = QADBConstants.AUDIT_SEARCH_SP; 
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(highDollerReportSP);
		List<AuditSave> highDollarEOs = new ArrayList<AuditSave>();
		
		try {
			
			callableStatment.setString(1,"EDIT_AUDIT");
			callableStatment.setString(2,"GNRL");
			callableStatment.setInt(3,Integer.parseInt(auditId));

			callableStatment.setNull(4, java.sql.Types.VARCHAR);
			callableStatment.setNull(5, java.sql.Types.INTEGER);
			callableStatment.setNull(6, java.sql.Types.VARCHAR);
			callableStatment.setNull(7, java.sql.Types.DATE);
			callableStatment.setNull(8, java.sql.Types.DATE);
			callableStatment.setNull(9, java.sql.Types.VARCHAR);
			
			callableStatment.setNull(10,java.sql.Types.DATE);
			callableStatment.setNull(11,java.sql.Types.DATE);
			callableStatment.setNull(12,java.sql.Types.INTEGER);
			callableStatment.setNull(13,java.sql.Types.INTEGER);
			callableStatment.setNull(14,java.sql.Types.INTEGER);
			callableStatment.setNull(15,java.sql.Types.INTEGER);
			callableStatment.setNull(16,java.sql.Types.INTEGER);
			
		    callableStatment.setNull(17,java.sql.Types.VARCHAR);
		    callableStatment.setNull(18, java.sql.Types.VARCHAR);
		    callableStatment.setNull(19, java.sql.Types.VARCHAR);
			logger.debug("EditGen all done");
			
			//Out
			callableStatment.registerOutParameter(20, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(21, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(22, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(23, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(24, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(25, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(26, OracleTypes.VARCHAR);
			logger.debug("EditGen all done2");
			
			callableStatment.executeUpdate();
			logger.debug("EditGen EXEC complte");
			
			ResultSet  rsHigh =   (ResultSet) callableStatment.getObject(22);
			logger.debug("RS "+rsHigh);
			logger.debug("Row count" +rsHigh.getRow());
			
			AuditSave highDollarEO = new AuditSave();

			while (rsHigh.next()) {
				//High Dollar
				logger.debug("Adj "+rsHigh.getString("PROCESS_DATE"));
				highDollarEO.setClaimAdjFlag(rsHigh.getString("CLAIM_ADJUSTMENT_FLG"));
				highDollarEO.setServiceAdjFlag(rsHigh.getString("SERVICE_ADJUSTMENT_FLG"));
				highDollarEO.setDcn(rsHigh.getString("DCN_NBR"));
				highDollarEO.setExaminerName(rsHigh.getString("EXAMINER_NAME"));
				highDollarEO.setQAName(rsHigh.getString("QA_ANALYST_NAME"));
				highDollarEO.setProcessDate(new SimpleDateFormat("MM/dd/yy").format((rsHigh.getDate("PROCESS_DATE").getTime())));
				highDollarEO.setMemberId(rsHigh.getString("MEMB_ID"));
				highDollarEO.setPatientName(rsHigh.getString("PATIENT_NAME"));
				highDollarEO.setServiceDates((rsHigh.getString("SERVICE_DATES"))); 
				highDollarEO.setTypeOfService(rsHigh.getString("TYPE_OF_SERVICE"));
				highDollarEO.setDiagnosis(rsHigh.getString("DIAGNOSIS"));
				/*logger.debug("CLAIM_ADJUSTMENT_FLG"+new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("SURGERY_DATE"))));
				highDollarEO.setSurgeryDOS(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("SURGERY_DATE"))));
				highDollarEO.setSurgery(rsHigh.getString("SURGERY_DESC"));
				highDollarEO.setFileReferenced(rsHigh.getString("AUTH_FILE_REFERENCE"));
				highDollarEO.setTotalCharge(rsHigh.getString("TOTAL_CHARGE"));
				highDollarEO.setPaid(rsHigh.getString("TOTAL_PAID"));
				highDollarEO.setProviderName(rsHigh.getString("PROVIDER_NAME"));
				highDollarEO.setProviderNumber(rsHigh.getString("PROVIDER_ID"));
				highDollarEO.setPayee(rsHigh.getString("PAYEE"));
				highDollarEO.setNotes(rsHigh.getString("NOTES"));
				highDollarEO.setAudSignDate(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("QA_AUDITOR_SIGN_DATE"))));
				highDollarEO.setVpSignDate(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("VP_SIGN_DATE"))));
				highDollarEO.setForwrdToDate(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("FORWARDED_TO_DATE"))));
				highDollarEO.setRcvedFrmDate(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("RECEIVED_FROM_DATE"))));
				highDollarEO.setReleasedByDate(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("RELEASED_BY_DATE"))));*/
				
				if(null!=rsHigh.getString("SURGERY_DATE")){
					logger.debug("CLAIM_ADJUSTMENT_FLG"+new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("SURGERY_DATE"))));
					highDollarEO.setSurgeryDOS(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("SURGERY_DATE"))));
				}else {
					highDollarEO.setSurgeryDOS("");
				}
				
				if(null!=rsHigh.getString("SURGERY_DESC")){
					highDollarEO.setSurgery(rsHigh.getString("SURGERY_DESC"));
				}else {
					highDollarEO.setSurgery("");
				}
				if(null!=rsHigh.getString("AUTH_FILE_REFERENCE")){
					highDollarEO.setFileReferenced(rsHigh.getString("AUTH_FILE_REFERENCE"));
				}else {
					highDollarEO.setFileReferenced("");
				}
				//auditSaveEOs.setSurgeryDOS(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("SURGERY_DATE"))));
				//auditSaveEOs.setSurgery(rsHigh.getString("SURGERY_DESC"));
				//auditSaveEOs.setFileReferenced(rsHigh.getString("AUTH_FILE_REFERENCE"));
				highDollarEO.setTotalCharge(rsHigh.getString("TOTAL_CHARGE"));
				highDollarEO.setPaid(rsHigh.getString("TOTAL_PAID"));
				highDollarEO.setInterestPaid(rsHigh.getString("INTEREST_PAID"));
				highDollarEO.setProviderName(rsHigh.getString("PROVIDER_NAME"));
				highDollarEO.setProviderNumber(rsHigh.getString("PROVIDER_ID"));
				highDollarEO.setPayee(rsHigh.getString("PAYEE"));
				
				if(null!=rsHigh.getString("NOTES")){
					highDollarEO.setNotes(rsHigh.getString("NOTES"));
				}else {
					highDollarEO.setNotes("");
				}
				//auditSaveEOs.setNotes(rsHigh.getString("NOTES"));
				
				highDollarEO.setAudSignDate(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("QA_AUDITOR_SIGN_DATE"))));
				
				if(null!=rsHigh.getString("VP_SIGN_DATE")){
					highDollarEO.setVpSignDate(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("VP_SIGN_DATE"))));
				}else {
					highDollarEO.setVpSignDate("");
				}
				//auditSaveEOs.setVpSignDate(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("VP_SIGN_DATE"))));
				
				highDollarEO.setForwrdToDate(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("FORWARDED_TO_DATE"))));
				
				if(null!=rsHigh.getString("RECEIVED_FROM_DATE")){
					highDollarEO.setRcvedFrmDate(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("RECEIVED_FROM_DATE"))));
					}else {
						highDollarEO.setRcvedFrmDate("");
					}
				if(null!=rsHigh.getString("RELEASED_BY_DATE")){
					highDollarEO.setReleasedByDate(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("RELEASED_BY_DATE"))));
					}else {
						highDollarEO.setReleasedByDate("");
					}
				//auditSaveEOs.setRcvedFrmDate(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("RECEIVED_FROM_DATE"))));
				//auditSaveEOs.setReleasedByDate(new SimpleDateFormat("MM/dd/yyyy").format((rsHigh.getDate("RELEASED_BY_DATE"))));
			
				
			}
			rsHigh.close();
			
			highDollarEOs.add(highDollarEO);
			callableStatment.close();
			 
			conn.close();
			logger.debug("*** Exit getHighDollarReport method ***");
		} catch (Exception e) {
			e.getStackTrace();
			logger.debug("Exception generated"+e.getMessage());
		}
		
		JRDataSource ds = new JRBeanCollectionDataSource(highDollarEOs);	
		
		return ds;
		
	}
}
