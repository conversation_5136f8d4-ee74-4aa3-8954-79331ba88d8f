
<!DOCTYPE html>
<%@page language="java"
	contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>

<html style="background-color: #dddfe1;">
<head>

<style>
.container2 {
	margin-left: auto;
    margin-right: auto;
	height: 350%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}
</style>
<link href="webResources/css/qadb.css" rel="stylesheet">
<link href="webResources/css/jquery-ui.css" rel="stylesheet">

<!-- <script  type="text/javascript" src="webResources/js/jquery-ui.js"></script> -->

<title><spring:message code="QADB.title" /></title>
</head>
<body class="metro">




	<jsp:include page="../jsp/header.jsp"></jsp:include>



	<div class="container2">

	
	<div id="left-sidebar">
        <jsp:include page="../jsp/audit_leftNav.jsp"></jsp:include>
    </div>
    <div id="content-container">
    	<div id = "aud" >
			<jsp:include page="../jsp/audit_general.jsp"></jsp:include>
		</div>    
		<c:if test="${edit != null}">
			<div id="stat" style="width:645px">
				<jsp:include page="../jsp/audit_statistics_tab.jsp"></jsp:include>       
   	 		</div>
    	</c:if>    
    </div>
    <%-- <c:if test="${edit != null}">
		<div id="content-container" style="width:645px">
			<jsp:include page="../jsp/audit_statistics_tab.jsp"></jsp:include>       
   	 	</div>
    </c:if> --%>
	</div>

	<jsp:include page="../jsp/footer.jsp"></jsp:include>

<script type="text/javascript">
$('#statsLi').removeClass('active');
$('#errorleftLi').removeClass('active');
$('#auditLeftLi').addClass('active');


$('#stat').hide();
$('#stats').click(function() {
	console.log("in stats show");
    $('#aud').hide();
    $('#stat').show();
    $('#auditLeftLi').removeClass('active');
    $('#errorleftLi').removeClass('active');
	$('#statsLi').addClass('active');
});
</script>

</body>

</html>
