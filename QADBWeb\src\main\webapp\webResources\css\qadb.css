html {
	background-color:#dddfe1; /* change */
  	font-size: 62.5%;/* change */
   /* change */
  
}
body {
  font-size: 1.4rem;
background-color:#dddfe1;/* change */
  
}
html,
body {
   
  padding: 0;
  margin: 0;
  height: 100%;
}
html,
body,
* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
nav,
section {
  display: block;
}
audio,
canvas,
video {
  display: inline-block;
}
audio:not([controls]) {
  display: none;
}
a:hover,
a:active,
.tile:active {
  outline: 0;
}
sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}
img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
  border: 0;
  -ms-interpolation-mode: bicubic;
}
#map_canvas img,
.google-maps img {
  max-width: none;
}
button,
input,
select,
textarea {
  margin: 0;
  font-size: 100%;
  vertical-align: middle;
}
button,
input {
  line-height: normal;
}
button::-moz-focus-inner,
input::-moz-focus-inner {
  padding: 0;
  border: 0;
}
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer;
}
label,
select,
button,
input[type="button"],
input[type="reset"],
input[type="submit"],
input[type="radio"],
input[type="checkbox"] {
  cursor: pointer;
}
input[type="search"] {
  -webkit-appearance: textfield;
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  -ms-box-sizing: content-box;
  -o-box-sizing: content-box;
  box-sizing: content-box;
}
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button {
  -webkit-appearance: none;
}
textarea {
  overflow: auto;
  vertical-align: top;
}
input[type=text]::-ms-clear,
input[type=email]::-ms-clear,
input[type=url]::-ms-clear,
input[type=tel]::-ms-clear,
input[type=number]::-ms-clear,
input[type=time]::-ms-clear {
  display: none;
}
input[type=password]::-ms-reveal {
  display: none;
}
* {
  border-collapse: collapse;
}
a {
  text-decoration: none;
}
@media print {
  * {
    text-shadow: none !important;
    color: #000 !important;
    background: transparent !important;
    box-shadow: none !important;
  }
  a,
  a:visited {
    text-decoration: underline;
  }
  a[href]:after {
    content: " (" attr(href) ")";
  }
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  .ir a:after,
  a[href^="javascript:"]:after,
  a[href^="#"]:after {
    content: "";
  }
  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }
  thead {
    display: table-header-group;
  }
  tr,
  img {
    page-break-inside: avoid;
  }
  img {
    max-width: 100% !important;
  }
  p,
  h2,
  h3 {
 
    orphans: 3;
    widows: 3;
  }
  h2,
  h3 {
    page-break-after: avoid;
  }
}
.metro .box-sizing-border {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}
.metro .box-sizing-content {
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  -ms-box-sizing: content-box;
  -o-box-sizing: content-box;
  box-sizing: content-box;
}
.metro .text-left {
  text-align: left;
}
.metro .text-right {
  text-align: right;
}
.metro .text-center {
  text-align: center;
}
.metro .text-justify {
  text-align: justify;
}
.metro .text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.metro .text-bold {
  font-weight: bold;
}
.metro .text-normal {
  font-weight: normal;
}
.metro .text-italic {
  font-style: italic;
}
.metro .no-border {
  border: 0 !important;
}
.metro .border {
  border: 1px #eaeaea solid;
}
.metro .border-left {
  border-left: 1px #eaeaea solid;
}
.metro .border-right {
  border-right: 1px #eaeaea solid;
}
.metro .border-top {
  border-top: 1px #eaeaea solid;
}
.metro .border-bottom {
  border-bottom: 1px #eaeaea solid;
}
.metro .collapse-separate {
  border-collapse: separate;
}
.metro .collapse-collapse {
  border-collapse: collapse;
}
.metro .pos-abs {
  position: absolute;
}
.metro .pos-rel {
  position: relative;
}
.metro .pos-fix {
  position: fixed;
}
.metro .place-left {
  float: left;
}
.metro .place-right {
  float: right;
}
.metro .place-top-left {
  position: absolute;
  top: 0;
  left: 0;
}
.metro .place-top-right {
  position: absolute;
  top: 0;
  right: 0;
}
.metro .place-bottom-left {
  position: absolute;
  bottom: 0;
  left: 0;
}
.metro .place-bottom-right {
  position: absolute;
  bottom: 0;
  right: 0;
}
.metro .on-left {
  margin-right: 5px;
}
.metro .on-right {
  margin-left: 5px;
}
.metro .on-left-more {
  margin-right: 10px;
}
.metro .on-right-more {
  margin-left: 10px;
}
.metro .padding5 {
  padding: 5px;
}
.metro .padding10 {
  padding: 10px;
}
.metro .padding15 {
  padding: 15px;
}
.metro .padding20 {
  padding: 20px;
}
.metro .margin5 {
  margin: 5px;
}
.metro .margin10 {
  margin: 10px;
}
.metro .margin15 {
  margin: 15px;
}
.metro .margin20 {
  margin: 20px;
}
.metro .nlm {
  margin-left: 0 !important;
}
.metro .nrm {
  margin-right: 0 !important;
}
.metro .ntm {
  margin-top: 0 !important;
}
.metro .nbm {
  margin-bottom: 0 !important;
}
.metro .nlp {
  padding-left: 0 !important;
}
.metro .nrp {
  padding-right: 0 !important;
}
.metro .ntp {
  padding-top: 0 !important;
}
.metro .nbp {
  padding-bottom: 0 !important;
}
.metro .no-margin {
  margin: 0  !important;
}
.metro .no-padding {
  padding: 0  !important;
}
.metro .no-float {
  float: none !important;
}
.metro .block {
  display: block !important;
  float: none !important;
}
.metro .inline-block {
  display: inline-block !important;
}
.metro .hide,
.metro .no-display {
  display: none !important;
}
.metro .no-visible {
  visibility: hidden !important;
}
.metro .visible {
  visibility: visible !important;
}
.metro .scroll-y,
.metro .scroll-vertical {
  overflow-y: scroll;
}
.metro .scroll-x,
.metro .scroll-horizontal {
  overflow-x: scroll;
}
.metro .no-overflow {
  overflow: hidden;
}
.metro .overflow {
  overflow: scroll;
}
.metro .one-column {
  -moz-columns: 1;
  -webkit-columns: 1;
  -o-columns: 1;
  -ms-columns: 1;
  columns: 1;
  -moz-column-gap: 20px;
  -webkit-column-gap: 20px;
  -o-column-gap: 20px;
  -ms-column-gap: 20px;
  column-gap: 20px;
}
.metro .two-columns {
  -moz-columns: 2;
  -webkit-columns: 2;
  -o-columns: 2;
  -ms-columns: 2;
  columns: 2;
  -moz-column-gap: 20px;
  -webkit-column-gap: 20px;
  -o-column-gap: 20px;
  -ms-column-gap: 20px;
  column-gap: 20px;
}
.metro .three-columns {
  -moz-columns: 3;
  -webkit-columns: 3;
  -o-columns: 3;
  -ms-columns: 3;
  columns: 3;
  -moz-column-gap: 20px;
  -webkit-column-gap: 20px;
  -o-column-gap: 20px;
  -ms-column-gap: 20px;
  column-gap: 20px;
}
.metro .four-columns {
  -moz-columns: 4;
  -webkit-columns: 4;
  -o-columns: 4;
  -ms-columns: 4;
  columns: 4;
  -moz-column-gap: 20px;
  -webkit-column-gap: 20px;
  -o-column-gap: 20px;
  -ms-column-gap: 20px;
  column-gap: 20px;
}
.metro .five-columns {
  -moz-columns: 5;
  -webkit-columns: 5;
  -o-columns: 5;
  -ms-columns: 5;
  columns: 5;
  -moz-column-gap: 20px;
  -webkit-column-gap: 20px;
  -o-column-gap: 20px;
  -ms-column-gap: 20px;
  column-gap: 20px;
}
.metro .square5 {
  width: 5px;
  height: 5px;
}
.metro .square10 {
  width: 10px;
  height: 10px;
}
.metro .square15 {
  width: 15px;
  height: 15px;
}
.metro .square20 {
  width: 20px;
  height: 20px;
}
.metro .shadow {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3), 0 3px 8px rgba(0, 0, 0, 0.2);
}
.metro .text-shadow {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4);
}
.metro .opacity {
  opacity: .9;
}
.metro .half-opacity {
  opacity: .5;
}
.metro .hi-opacity {
  opacity: .2;
}
.metro .clearfix:before,
.metro .clearfix:after {
  display: table;
  content: "";
}
.metro .clearfix:after {
  clear: both;
}
.metro #element .selected {
  border: 4px #4390df solid;
}
.metro #element .selected:after {
  position: absolute;
  display: block;
  border-top: 28px solid #4390df;
  border-left: 28px solid transparent;
  right: 0;
  content: "";
  top: 0;
  z-index: 101;
}
.metro #element .selected:before {
  position: absolute;
  display: block;
  content: "\e003";
  color: #fff;
  right: 0;
  font-family: 'metroSysIcons';
  font-size: 9pt;
  font-weight: normal;
  z-index: 102;
  top: 0;
}
.metro .rotate45 {
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}
.metro .rotate90 {
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);
}
.metro .rotate135 {
  -webkit-transform: rotate(135deg);
  -moz-transform: rotate(135deg);
  -ms-transform: rotate(135deg);
  -o-transform: rotate(135deg);
  transform: rotate(135deg);
}
.metro .rotate180 {
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.metro .rotate225 {
  -webkit-transform: rotate(225deg);
  -moz-transform: rotate(225deg);
  -ms-transform: rotate(225deg);
  -o-transform: rotate(225deg);
  transform: rotate(225deg);
}
.metro .rotate270 {
  -webkit-transform: rotate(270deg);
  -moz-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  -o-transform: rotate(270deg);
  transform: rotate(270deg);
}
.metro .rotate360 {
  -webkit-transform: rotate(360deg);
  -moz-transform: rotate(360deg);
  -ms-transform: rotate(360deg);
  -o-transform: rotate(360deg);
  transform: rotate(360deg);
}
.metro .rotateX45 {
  -webkit-transform: rotateX(45deg);
  -moz-transform: rotateX(45deg);
  -ms-transform: rotateX(45deg);
  -o-transform: rotateX(45deg);
  transform: rotateX(45deg);
}
.metro .rotateX90 {
  -webkit-transform: rotateX(90deg);
  -moz-transform: rotateX(90deg);
  -ms-transform: rotateX(90deg);
  -o-transform: rotateX(90deg);
  transform: rotateX(90deg);
}
.metro .rotateX135 {
  -webkit-transform: rotateX(135deg);
  -moz-transform: rotateX(135deg);
  -ms-transform: rotateX(135deg);
  -o-transform: rotateX(135deg);
  transform: rotateX(135deg);
}
.metro .rotateX180 {
  -webkit-transform: rotateX(180deg);
  -moz-transform: rotateX(180deg);
  -ms-transform: rotateX(180deg);
  -o-transform: rotateX(180deg);
  transform: rotateX(180deg);
}
.metro .rotateX225 {
  -webkit-transform: rotateX(225deg);
  -moz-transform: rotateX(225deg);
  -ms-transform: rotateX(225deg);
  -o-transform: rotateX(225deg);
  transform: rotateX(225deg);
}
.metro .rotateX270 {
  -webkit-transform: rotateX(270deg);
  -moz-transform: rotateX(270deg);
  -ms-transform: rotateX(270deg);
  -o-transform: rotateX(270deg);
  transform: rotateX(270deg);
}
.metro .rotateX360 {
  -webkit-transform: rotateX(360deg);
  -moz-transform: rotateX(360deg);
  -ms-transform: rotateX(360deg);
  -o-transform: rotateX(360deg);
  transform: rotateX(360deg);
}
.metro .rotateY45 {
  -webkit-transform: rotateY(45deg);
  -moz-transform: rotateY(45deg);
  -ms-transform: rotateY(45deg);
  -o-transform: rotateY(45deg);
  transform: rotateY(45deg);
}
.metro .rotateY90 {
  -webkit-transform: rotateY(90deg);
  -moz-transform: rotateY(90deg);
  -ms-transform: rotateY(90deg);
  -o-transform: rotateY(90deg);
  transform: rotateY(90deg);
}
.metro .rotateY135 {
  -webkit-transform: rotateY(135deg);
  -moz-transform: rotateY(135deg);
  -ms-transform: rotateY(135deg);
  -o-transform: rotateY(135deg);
  transform: rotateY(135deg);
}
.metro .rotateY180 {
  -webkit-transform: rotateY(180deg);
  -moz-transform: rotateY(180deg);
  -ms-transform: rotateY(180deg);
  -o-transform: rotateY(180deg);
  transform: rotateY(180deg);
}
.metro .rotateY225 {
  -webkit-transform: rotateY(225deg);
  -moz-transform: rotateY(225deg);
  -ms-transform: rotateY(225deg);
  -o-transform: rotateY(225deg);
  transform: rotateY(225deg);
}
.metro .rotateY270 {
  -webkit-transform: rotateY(270deg);
  -moz-transform: rotateY(270deg);
  -ms-transform: rotateY(270deg);
  -o-transform: rotateY(270deg);
  transform: rotateY(270deg);
}
.metro .rotateY360 {
  -webkit-transform: rotateY(360deg);
  -moz-transform: rotateY(360deg);
  -ms-transform: rotateY(360deg);
  -o-transform: rotateY(360deg);
  transform: rotateY(360deg);
}
@font-face {
  font-family: "PT Serif Caption";
  font-style: normal;
  font-weight: 400;
  src: local("Cambria"), local("PT Serif Caption"), local("PTSerif-Caption"), url(https://themes.googleusercontent.com/static/fonts/ptserifcaption/v6/7xkFOeTxxO1GMC1suOUYWWhBabBbEjGd1iRmpyoZukE.woff) format('woff');
}
@font-face {
  font-family: "Open Sans Light";
  font-style: normal;
  font-weight: 300;
  src: local("Segoe UI Light"), local("Open Sans Light"), local("OpenSans-Light"), url(https://themes.googleusercontent.com/static/fonts/opensans/v8/DXI1ORHCpsQm3Vp6mXoaTZ1r3JsPcQLi8jytr04NNhU.woff) format('woff');
}
@font-face {
  font-family: "Open Sans";
  font-style: normal;
  font-weight: 400;
  src: local("Segoe UI"), local("Open Sans"), local("OpenSans"), url(https://themes.googleusercontent.com/static/fonts/opensans/v8/K88pR3goAWT7BTt32Z01mz8E0i7KZn-EPnyo3HZu7kw.woff) format('woff');
}
@font-face {
  font-family: "Open Sans Bold";
  font-style: normal;
  font-weight: 700;
  src: local("Segoe UI Bold"), local("Open Sans Bold"), local("OpenSans-Bold"), url(https://themes.googleusercontent.com/static/fonts/opensans/v8/k3k702ZOKiLJc3WVjuplzJ1r3JsPcQLi8jytr04NNhU.woff) format('woff');
}
.metro .text-rest-state {
  color: #000000;
}
.metro .text-rest2-state {
  color: #000000;
}
.metro .text-hover-state {
  color: rgba(0, 0, 0, 0.8);
}
.metro .text-pressed-state {
  color: rgba(0, 0, 0, 0.4);
}
.metro #font .light {
  font-weight: 300;
}
.metro #font .normal {
  font-weight: normal;
  font-style: normal;
}
.metro #font .bold {
  font-weight: bold;
}
.metro #font .italic {
  font-style: italic;
}
.metro #font .segoe {
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
}
.metro #font .segoe-bold {
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
}
.metro #font .segoe-light {
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
}
.metro #font .cambria {
  font-family: 'Cambria_', 'PT Serif Caption', Verdana, Arial, Helvetica, sans-serif;
}
.metro #font .mono {
  font-family: 'Courier New', monospace;
}
.metro #font .serif {
  font-family: Georgia, "Times New Roman", Times, serif;
}
.metro #font .sans {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}
.metro .header,
.metro .subheader {
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-weight: 300;
  color: #000000;
  letter-spacing: 0.00em;
}
.metro .header {
  font-size: 4.8rem;
  line-height: 4.8rem;
}
.metro .subheader {
  font-size: 2.5rem;
  line-height: 2.5rem;
  letter-spacing: 0.01em;
}
.metro .subheader-secondary {
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-weight: 300;
  letter-spacing: 0.00em;
  font-size: 2.5rem;
  line-height: 2.5rem;
  letter-spacing: 0.01em;
  color: #000000;
  font-size: 2.2rem;
  line-height: 2.2rem;
}
.metro .item-title {
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  color: #000000;
  font-size: 1.6rem;
  line-height: 1.6rem;
  letter-spacing: 0.02em;
}
.metro .item-title-secondary {
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  font-size: 1.6rem;
  line-height: 1.6rem;
  letter-spacing: 0.02em;
  color: #000000;
  font-size: 1.4rem;
  line-height: 1.4rem;
}
.metro .navigation {
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  font-weight: normal;
  font-style: normal;
  color: #000000;
  font-size: 1.2rem;
  line-height: 1.2rem;
  letter-spacing: 0.02em;
}
.metro .body-text {
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  font-weight: normal;
  font-style: normal;
  color: #000000;
  font-size: 11pt;
  line-height: 15pt;
  letter-spacing: 0.02em;
}
.metro .code-text {
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  font-weight: normal;
  font-style: normal;
  color: #000000;
  font-size: 11pt;
  line-height: 15pt;
  letter-spacing: 0.02em;
  font-family: 'Courier New', monospace;
}
.metro .tertiary-text {
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  color: #000000;
  font-size: 1.3rem;
  line-height: 1.5rem;
  font-weight: normal !important;
}
.metro .tertiary-text-secondary {
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  font-size: 1.3rem;
  line-height: 1.5rem;
  font-weight: normal !important;
  color: #000000;
}
.metro .readable-text {
  font-family: 'Cambria_', 'PT Serif Caption', Verdana, Arial, Helvetica, sans-serif;
  font-weight: normal;
  font-style: normal;
  font-size: 1.8rem;
  line-height: 2rem;
}
.metro .menu-title {
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-weight: 300;
  color: #000000;
  letter-spacing: 0.00em;
  font-size: 2.5rem;
  line-height: 2.5rem;
  letter-spacing: 0.01em;
  font-size: 4rem;
}
.metro .menu-item {
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-weight: 300;
  letter-spacing: 0.00em;
  font-size: 2.5rem;
  line-height: 2.5rem;
  letter-spacing: 0.01em;
  color: #000000;
  line-height: 2.2rem;
  font-size: 2.2rem;
}
.metro .menu-item-secondary {
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  color: #000000;
  font-size: 1.6rem;
  line-height: 1.6rem;
  letter-spacing: 0.02em;
  font-weight: normal;
  font-style: normal;
  line-height: 1.4rem;
}
.metro h1,
.metro h2,
.metro h3,
.metro h4,
.metro h5,
.metro h6 {
  padding: 0;
  margin: 10px 0;
}
.metro h1 {
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-weight: 300;
  color: #000000;
  letter-spacing: 0.00em;
  font-size: 4.8rem;
  line-height: 4.8rem;
}
.metro h2 {
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-weight: 300;
  color: #000000;
  letter-spacing: 0.00em;
  font-size: 2.5rem;
  line-height: 2.5rem;
  letter-spacing: 0.01em;
}
.metro h3 {
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-weight: 300;
  letter-spacing: 0.00em;
  font-size: 2.5rem;
  line-height: 2.5rem;
  letter-spacing: 0.01em;
  color: #000000;
  font-size: 2.2rem;
  line-height: 2.2rem;
  font-weight: bold;
}
.metro h4 {
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  color: #000000;
  font-size: 1.6rem;
  line-height: 1.6rem;
  letter-spacing: 0.02em;
}
.metro h5 {
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  font-size: 1.6rem;
  line-height: 1.6rem;
  letter-spacing: 0.02em;
  color: #000000;
  font-size: 1.4rem;
  line-height: 1.4rem;
}
.metro h6 {
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  font-weight: normal;
  font-style: normal;
  color: #000000;
  font-size: 11pt;
  line-height: 15pt;
  letter-spacing: 0.02em;
  font-weight: bold;
  font-size: 9pt;
}
.metro h1 small {
  font-size: 19.25pt;
}
.metro h2 small {
  font-size: 13.75pt;
}
.metro h3 small {
  font-size: 11pt;
}
.metro h4 small {
  font-size: 11pt;
}
.metro a,
.metro .link {
  color: #2e92cf;
}
.metro a:hover,
.metro .link:hover {
  color: rgba(45, 173, 237, 0.8);
}
.metro a:focus,
.metro .link:focus,
.metro a:active,
.metro .link:active {
  color: rgba(45, 173, 237, 0.6);
}
.metro div,
.metro span,
.metro p {
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  font-weight: normal;
  font-style: normal;
}
.metro p {
  margin: 0 0 7pt;
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  font-weight: normal;
  font-style: normal;
  color: #000000;
  font-size: 11pt;
  line-height: 15pt;
  letter-spacing: 0.02em;
}
.metro strong {
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
}
.metro .text-muted {
  color: #999999 !important;
}
.metro .text-info {
  color: #1ba1e2 !important;
}
.metro .text-alert {
  color: #e51400 !important;
}
.metro .text-warning {
  color: #fa6800 !important;
}
.metro .text-success {
  color: #60a917 !important;
}
.metro abbr {
  text-decoration: none;
  border-bottom: 1px #999999 dotted;
  cursor: help;
}
.metro address {
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  font-weight: normal;
  font-style: normal;
  margin-bottom: 7pt;
}
.metro blockquote {
  margin: 0;
  padding: 0;
  border-left: 4px #999999 solid;
  padding-left: 10px;
}
.metro blockquote small {
  color: #999999;
}
.metro blockquote small:before {
  content: "\2014 \00A0";
}
.metro blockquote.place-right {
  border: 0;
  border-right: 4px #999999 solid;
  padding-right: 10px;
  text-align: right;
}
.metro blockquote.place-right small:before {
  content: "";
}
.metro blockquote.place-right small:after {
  content: " \00A0 \2014";
}

.metro dl dt,
.metro dl dd {
  line-height: 20px;
}
.metro dl dt {
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
}
.metro dl dd {
  margin-left: 15px;
}
.metro dl.horizontal dt {
  float: left;
  width: 160px;
  overflow: hidden;
  clear: left;
  text-align: right;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.metro dl.horizontal dd {
  margin-left: 180px;
}
.metro .prettyprint * {
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  font-weight: normal;
  font-style: normal;
  color: #000000;
  font-size: 11pt;
  line-height: 15pt;
  letter-spacing: 0.02em;
  font-family: 'Courier New', monospace;
  font-size: .9em !important;
  line-height: 10pt;
}
/* Container background */
.metro .container {

  margin: 0 auto;
}

.metro .page {
  width: 100%;
  min-height: 100%;
  margin: 0;
  padding: 0;
}
.metro .page:before,
.metro .page:after {
  display: table;
  content: "";
}
.metro .page:after {
  clear: both;
}
.metro .page .page-header .page-header-content {
  min-height: auto;
}
.metro .page .page-header .page-header-content:before,
.metro .page .page-header .page-header-content:after {
  display: table;
  content: "";
}
.metro .page .page-header .page-header-content:after {
  clear: both;
}
.metro .page .page-header .page-header-content .branding {
  display: inline-block;
  width: auto;
  padding: 10px 0;
  margin-right: 20px;
}
.metro .page .page-header .page-header-content .branding .brand-title {
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-weight: 300;
  color: #000000;
  letter-spacing: 0.00em;
  font-size: 2.5rem;
  line-height: 2.5rem;
  letter-spacing: 0.01em;
  display: block;
}
.metro .page .page-header .page-header-content .branding .brand-slogan {
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  font-size: 1.6rem;
  line-height: 1.6rem;
  letter-spacing: 0.02em;
  color: #000000;
  font-size: 1.4rem;
  line-height: 1.4rem;
  font-weight: normal;
  font-style: normal;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  display: block;
}
.metro .page .page-header .page-header-content .branding .brand-title,
.metro .page .page-header .page-header-content .branding .brand-slogan {
  margin: 0;
}
.metro .page .page-header .page-header-content .branding a + .brand-title,
.metro .page .page-header .page-header-content .branding a + .brand-slogan {
  text-decoration: none !important;
}
.metro .page .page-region .page-region-content {
  min-height: 500px;
}
.metro .page .page-region .page-region-content:before,
.metro .page .page-region .page-region-content:after {
  display: table;
  content: "";
}
.metro .page .page-region .page-region-content:after {
  clear: both;
}
.metro .page .page-footer .page-footer-content {
  height: 100px;
}
.metro .page .page-footer .page-footer-content:before,
.metro .page .page-footer .page-footer-content:after {
  display: table;
  content: "";
}
.metro .page .page-footer .page-footer-content:after {
  clear: both;
}
.metro .page.secondary .page-region-content,
.metro .page.secondary .page-header-content {
  padding-left: 80px;
}
.metro .app-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  min-height: 100px;
  background-color: #1d1d1d;
  z-index: 1060;
}
.metro .charms {
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  height: 100%;
  min-width: 200px;
  width: auto;
  z-index: 1060;
}
.metro .charms.place-left {
  left: 0;
  right: auto;
}
.metro .message-dialog {
  position: fixed;
  left: 0;
  right: 0;
  height: auto;
  min-height: 100px;
  top: 30%;
  padding: 10px 10px 0;
  z-index: 1050;
}
.metro .error-bar,
.metro .warning-bar,
.metro .info-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  padding: 10px 20px;
  color: #fff;
  min-height: 100px;
  z-index: 1050;
}
.metro .error-bar {
  background-color: #9a1616 !important;
}
.metro .warning-bar {
  background-color: #bf5a15 !important;
}
.metro .info-bar {
  background-color: #4390df !important;
}
.metro .span1,
.metro .size1 {
  width: 60px !important;
}
.metro .span2,
.metro .size2 {
  width: 140px !important;
}
.metro .span3,
.metro .size3 {
  width: 220px !important;
}
.metro .span4,
.metro .size4 {
  width: 300px !important;
}
.metro .span5,
.metro .size5 {
  width: 380px !important;
}
.metro .span6,
.metro .size6 {
  width: 460px !important;
}
.metro .span7,
.metro .size7 {
  width: 540px !important;
}
.metro .span8,
.metro .size8 {
  width: 620px !important;
}
.metro .span9,
.metro .size9 {
  width: 700px !important;
}
.metro .span10,
.metro .size10 {
  width: 780px !important;
}
.metro .span11,
.metro .size11 {
  width: 860px !important;
}
.metro .span12,
.metro .size12 {
  width: 940px !important;
}
.metro .offset1 {
  margin-left: 80px !important;
}
.metro .offset1:not(:first-child) {
  margin-left: 100px !important;
}
.metro .offset2 {
  margin-left: 160px !important;
}
.metro .offset2:not(:first-child) {
  margin-left: 180px !important;
}
.metro .offset3 {
  margin-left: 240px !important;
}
.metro .offset3:not(:first-child) {
  margin-left: 260px !important;
}
.metro .offset4 {
  margin-left: 320px !important;
}
.metro .offset4:not(:first-child) {
  margin-left: 340px !important;
}
.metro .offset5 {
  margin-left: 400px !important;
}
.metro .offset5:not(:first-child) {
  margin-left: 420px !important;
}
.metro .offset6 {
  margin-left: 480px !important;
}
.metro .offset6:not(:first-child) {
  margin-left: 500px !important;
}
.metro .offset7 {
  margin-left: 560px !important;
}
.metro .offset7:not(:first-child) {
  margin-left: 580px !important;
}
.metro .offset8 {
  margin-left: 640px !important;
}
.metro .offset8:not(:first-child) {
  margin-left: 660px !important;
}
.metro .offset9 {
  margin-left: 720px !important;
}
.metro .offset9:not(:first-child) {
  margin-left: 740px !important;
}
.metro .offset10 {
  margin-left: 800px !important;
}
.metro .offset10:not(:first-child) {
  margin-left: 820px !important;
}
.metro .offset11 {
  margin-left: 880px !important;
}
.metro .offset11:not(:first-child) {
  margin-left: 900px !important;
}
.metro .offset12 {
  margin-left: 960px !important;
}
.metro .offset12:not(:first-child) {
  margin-left: 980px !important;
}
.metro .grid {
  margin-bottom: 20px;
}
.metro .grid .row {
  display: block;
  margin-top: 20px;
}
.metro .grid .row:before,
.metro .grid .row:after {
  display: table;
  content: "";
}
.metro .grid .row:after {
  clear: both;
}
.metro .grid .row [class*="span"] {
  float: left;
  min-height: 1px;
  margin-left: 20px;
}
.metro .grid .row [class*="span"]:first-child {
  margin-left: 0;
}
.metro .grid.fluid {
  width: 100%;
}
.metro .grid.fluid .row .span1 {
  width: 6.382978% !important;
}
.metro .grid.fluid .row .span2 {
  width: 14.893615% !important;
}
.metro .grid.fluid .row .span3 {
  width: 23.404252% !important;
}
.metro .grid.fluid .row .span4 {
  width: 31.914889% !important;
}
.metro .grid.fluid .row .span5 {
  width: 40.425526% !important;
}
.metro .grid.fluid .row .span6 {
  width: 48.936163% !important;
}
.metro .grid.fluid .row .span7 {
  width: 57.4468% !important;
}
.metro .grid.fluid .row .span8 {
  width: 65.957437% !important;
}
.metro .grid.fluid .row .span9 {
  width: 74.468074% !important;
}
.metro .grid.fluid .row .span10 {
  width: 82.978711% !important;
}
.metro .grid.fluid .row .span11 {
  width: 91.489348% !important;
}
.metro .grid.fluid .row .span12 {
  width: 99.999985% !important;
}
.metro .grid.fluid .row [class*="span"] {
  margin-left: 2.127659%;
}
.metro .grid.fluid .row [class*="span"]:first-child {
  margin-left: 0;
}
.metro .grid.fluid .row .offset1 {
  margin-left: 8.510637% !important;
}
.metro .grid.fluid .row .offset1:not(:first-child) {
  margin-left: 10.638296% !important;
}
.metro .grid.fluid .row .offset2 {
  margin-left: 17.021274% !important;
}
.metro .grid.fluid .row .offset2:not(:first-child) {
  margin-left: 19.148933% !important;
}
.metro .grid.fluid .row .offset3 {
  margin-left: 25.531911% !important;
}
.metro .grid.fluid .row .offset3:not(:first-child) {
  margin-left: 27.65957% !important;
}
.metro .grid.fluid .row .offset4 {
  margin-left: 34.042548% !important;
}
.metro .grid.fluid .row .offset4:not(:first-child) {
  margin-left: 36.170207% !important;
}
.metro .grid.fluid .row .offset5 {
  margin-left: 42.553185% !important;
}
.metro .grid.fluid .row .offset5:not(:first-child) {
  margin-left: 44.680844% !important;
}
.metro .grid.fluid .row .offset6 {
  margin-left: 51.063822% !important;
}
.metro .grid.fluid .row .offset6:not(:first-child) {
  margin-left: 53.191481% !important;
}
.metro .grid.fluid .row .offset7 {
  margin-left: 59.574459% !important;
}
.metro .grid.fluid .row .offset7:not(:first-child) {
  margin-left: 61.702118% !important;
}
.metro .grid.fluid .row .offset8 {
  margin-left: 68.085096% !important;
}
.metro .grid.fluid .row .offset8:not(:first-child) {
  margin-left: 70.212755% !important;
}
.metro .grid.fluid .row .offset9 {
  margin-left: 76.595733% !important;
}
.metro .grid.fluid .row .offset9:not(:first-child) {
  margin-left: 78.723392% !important;
}
.metro .grid.fluid .row .offset10 {
  margin-left: 85.10637% !important;
}
.metro .grid.fluid .row .offset10:not(:first-child) {
  margin-left: 87.234029% !important;
}
.metro .grid.fluid .row .offset11 {
  margin-left: 93.617007% !important;
}
.metro .grid.fluid .row .offset11:not(:first-child) {
  margin-left: 95.744666% !important;
}
.metro .grid.fluid .row .offset12 {
  margin-left: 102.127644% !important;
}
.metro .grid.fluid .row .offset12:not(:first-child) {
  margin-left: 104.255303% !important;
}
.metro table {
  max-width: 100%;
 
  border-collapse: collapse;
  border-spacing: 0;
}
.metro .table {
  width: 100%;
  margin-bottom: 14pt;
}
.metro .table th,
/* Table border removed and padding decr */
.metro .table td {
  padding: 1px;
  font-size: 14px;
  line-height: 14pt;
  vertical-align: top;
  
}
.metro .table th[class*="span"],
.metro .table td[class*="span"] {
  display: table-cell !important;
  float: none !important;
  margin: 0;
}
.metro .table thead th {
  vertical-align: bottom;
}
.metro .table thead th,
.metro .table thead td {
  border-bottom: 1px solid #111111;
}
.metro .table tbody tr:last-child td {
  border-bottom: 0;
}
.metro .table tbody + tbody {
  border-top: 2px solid #eaeaea;
}
.metro .table tfoot th,
.metro .table tfoot td {
  border-top: 1px solid #111111;
}
.metro .table .table {
  background-color: #bfbfbf;
}
.metro .table.condensed th,
.metro .table.condensed td {
  padding: 4px 5px;
}
.metro .table.condensed th .input-control,
.metro .table.condensed td .input-control {
  margin: 0;
}
.metro .table.super-condensed th,
.metro .table.super-condensed td {
  padding: 2px;
}
.metro .table.super-condensed th .input-control,
.metro .table.super-condensed td .input-control {
  margin: 0;
}
.metro .table.striped tbody tr:nth-child(odd) {
  background: #eeeeee;
}
.metro .table.bordered {
  border: 1px #eaeaea solid;
  border-left: 0;
}
.metro .table.bordered td,
.metro .table.bordered th {
  border-left: 1px #eaeaea solid;
  border-bottom: 1px #eaeaea solid;
}
.metro .table.no-border td,
.metro .table.no-border th {
  border: 0;
}
.metro .table.border {
  border: 1px #eaeaea solid !important;
}
.metro .table.hovered tbody tr:hover {
  background-color: rgba(28, 183, 236, 0.1);
}
.metro .table tr.success {
  background-color: #60a917 !important;
}
.metro .table tr.error {
  background-color: #e51400 !important;
}
.metro .table tr.warning {
  background-color: #fa6800 !important;
}
.metro .table tr.info {
  background-color: #4390df !important;
}
.metro .table tr.info td,
.metro .table tr.warning td,
.metro .table tr.error td,
.metro .table tr.success td,
.metro .table tr.info td *,
.metro .table tr.warning td *,
.metro .table tr.error td *,
.metro .table tr.success td * {
  color: #ffffff !important;
}
.metro .table tr.selected {
  background-color: rgba(28, 183, 236, 0.1);
}
.metro .button {
  padding: 4px 12px;
  text-align: center;
  vertical-align: middle !important;
  background-color: #d9d9d9;
  border: 1px transparent solid;
  color: white;
  border-radius: 0;
  cursor: pointer;
  display: inline-block;
  outline: none;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-size: 14px;
  line-height: 16px;
  margin: auto;
}
.metro .button.default {
  background-color: #008287;
  color: #fff;
}
.metro .button:focus {
  outline: 0;
  border: 1px #353535 dotted;
}
.metro .button:disabled,
.metro .button.disabled {
  background-color: #eaeaea !important;
  color: #bebebe !important;
  cursor: not-allowed !important;
}
.metro .button * {
  color: inherit;
}
.metro .button *:hover {
  color: inherit;
}
.metro a.button:hover {
  color: inherit;
}
.metro a.button * {
  color: inherit;
}
.metro a.button *:hover {
  color: inherit;
}
.metro .button.primary {
  background-color: #16499a !important;
  color: #ffffff !important;
}
.metro .button.info {
  background-color: #4390df !important;
  color: #ffffff !important;
}
.metro .button.success {
  background-color: #60a917 !important;
  color: #ffffff !important;
}
.metro .button.warning {
  background-color: #fa6800 !important;
  color: #ffffff !important;
}
.metro .button.danger {
  background-color: #9a1616 !important;
  color: #ffffff !important;
}
/* Logout button */
.metro .button.inverse {
  background-color: gray !important;
  color: #ffffff !important;
  border-color: black;
}
.metro .button.link {
  background-color: transparent !important;
  color: #2e92cf !important;
}
.metro .button.link:hover {
  text-decoration: underline;
}
.metro .button.link:disabled,
.metro .button.link.disabled {
  background-color: transparent !important;
  color: #bebebe !important;
  cursor: not-allowed !important;
}
.metro .button.link [class*="icon-"] {
  text-decoration: none !important;
}
.metro button {
  padding: 4px 12px;
  text-align: center;
  vertical-align: middle !important;
  background-color: #d9d9d9;
  border: 1px transparent solid;
  color: #222222;
  border-radius: 0;
  cursor: pointer;
  display: inline-block;
  outline: none;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-size: 14px;
  line-height: 16px;
  margin: auto;
}
.metro button.default {
  background-color: #008287;
  color: #fff;
}
.metro button:focus {
  outline: 0;
  border: 1px #353535 dotted;
}
.metro button:disabled,
.metro button.disabled {
  background-color: #eaeaea !important;
  color: #bebebe !important;
  cursor: not-allowed !important;
}
.metro button * {
  color: inherit;
}
.metro button *:hover {
  color: inherit;
}
.metro button.primary {
  background-color: #16499a !important;
  color: #ffffff !important;
}
.metro button.info {
  background-color: #4390df !important;
  color: #ffffff !important;
}
.metro button.success {
  background-color: #60a917 !important;
  color: #ffffff !important;
}
.metro button.warning {
  background-color: #fa6800 !important;
  color: #ffffff !important;
}
.metro button.danger {
  background-color: #9a1616 !important;
  color: #ffffff !important;
}
.metro button.inverse {
  background-color: #1d1d1d !important;
  color: #ffffff !important;
}
.metro button.link {
  background-color: transparent !important;
  color: #2e92cf !important;
}
.metro button.link:hover {
  text-decoration: underline;
}
.metro button.link:disabled,
.metro button.link.disabled {
  background-color: transparent !important;
  color: #bebebe !important;
  cursor: not-allowed !important;
}
.metro button.link [class*="icon-"] {
  text-decoration: none !important;
}
.metro button:active {
  background-color: #1d1d1d !important;
  color: #ffffff !important;
  border-color: transparent;
}
.metro button.large {
  padding: 11px 19px;
  font-size: 17.5px;
}
.metro button.small {
  padding: 2px 10px;
  font-size: 11.9px;
}
.metro button.mini {
  padding: 0px 6px;
  font-size: 10.5px;
}
.metro .command-button {
  padding: 4px 12px;
  text-align: center;
  vertical-align: middle !important;
  background-color: #d9d9d9;
  border: 1px transparent solid;
  color: #222222;
  border-radius: 0;
  cursor: pointer;
  display: inline-block;
  outline: none;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-size: 14px;
  line-height: 16px;
  margin: auto;
  text-align: left;
  font-size: 14pt;
  width: auto;
}
.metro .command-button.default {
  background-color: #008287;
  color: #fff;
}
.metro .command-button:focus {
  outline: 0;
  border: 1px #353535 dotted;
}
.metro .command-button:disabled,
.metro .command-button.disabled {
  background-color: #eaeaea !important;
  color: #bebebe !important;
  cursor: not-allowed !important;
}
.metro .command-button * {
  color: inherit;
}
.metro .command-button *:hover {
  color: inherit;
}
.metro .command-button.primary {
  background-color: #16499a !important;
  color: #ffffff !important;
}
.metro .command-button.info {
  background-color: #4390df !important;
  color: #ffffff !important;
}
.metro .command-button.success {
  background-color: #60a917 !important;
  color: #ffffff !important;
}
.metro .command-button.warning {
  background-color: #fa6800 !important;
  color: #ffffff !important;
}
.metro .command-button.danger {
  background-color: #9a1616 !important;
  color: #ffffff !important;
}
.metro .command-button.inverse {
  background-color: #1d1d1d !important;
  color: #ffffff !important;
}
.metro .command-button.link {
  background-color: transparent !important;
  color: #2e92cf !important;
}
.metro .command-button.link:hover {
  text-decoration: underline;
}
.metro .command-button.link:disabled,
.metro .command-button.link.disabled {
  background-color: transparent !important;
  color: #bebebe !important;
  cursor: not-allowed !important;
}
.metro .command-button.link [class*="icon-"] {
  text-decoration: none !important;
}
.metro .command-button:active {
  background-color: #1d1d1d !important;
  color: #ffffff !important;
  border-color: transparent;
}
.metro .command-button.large {
  padding: 11px 19px;
  font-size: 17.5px;
}
.metro .command-button.small {
  padding: 2px 10px;
  font-size: 11.9px;
}
.metro .command-button.mini {
  padding: 0px 6px;
  font-size: 10.5px;
}
.metro .command-button small {
  display: block;
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  font-size: 1.3rem;
  line-height: 1.5rem;
  font-weight: normal !important;
  color: #000000;
  color: inherit;
}
.metro .command-button [class*="icon-"] {
  font-size: 1.7em;
  margin-top: 0;
}
.metro .command-button [class*="icon-"].on-left {
  float: left;
}
.metro .command-button [class*="icon-"].on-right {
  float: right;
}
.metro .command-button [class*="icon-"].on-left + small {
  margin-left: 40px;
}
.metro .command-button [class*="icon-"].on-right + small {
  margin-right: 40px;
}
.metro .toolbar {
  width: auto;
  margin-bottom: 10px;
}
.metro .toolbar:before,
.metro .toolbar:after {
  display: table;
  content: "";
}
.metro .toolbar:after {
  clear: both;
}
.metro .toolbar.transparent button,
.metro .toolbar .transparent button,
.metro .toolbar.transparent .button,
.metro .toolbar .transparent .button {
  background-color: transparent;
}
.metro .toolbar.transparent button:hover,
.metro .toolbar .transparent button:hover,
.metro .toolbar.transparent .button:hover,
.metro .toolbar .transparent .button:hover {
  background-color: #eeeeee;
  border: 1px #ffffff solid;
}
.metro .toolbar button,
.metro .toolbar .button {
  color: inherit;
  padding: 4px 8px;
  display: block;
  float: left;
  font-size: 12pt;
  line-height: 12pt;
  margin-right: 2px;
}
.metro .toolbar button:last-child,
.metro .toolbar .button:last-child {
  margin-right: 0;
}
.metro .toolbar.no-spaces button,
.metro .toolbar .toolbar-group.no-spaces button,
.metro .toolbar.no-spaces .button,
.metro .toolbar .toolbar-group.no-spaces .button {
  margin-right: 0;
}
.metro .toolbar.no-spaces button:active,
.metro .toolbar .toolbar-group.no-spaces button:active,
.metro .toolbar.no-spaces .button:active,
.metro .toolbar .toolbar-group.no-spaces .button:active {
  background-color: #1d1d1d;
  color: #ffffff;
  top: auto;
  left: auto;
}
.metro .toolbar .divider {
  display: block;
  float: left;
  height: 12px;
  width: 1px;
  margin: 8px 8px;
  background-color: #999999;
  vertical-align: middle !important;
}
.metro .toolbar .toolbar-group {
  padding: 0;
  margin: 0;
  display: inline-block;
  margin-right: 10px;
  color: inherit;
}
.metro .toolbar .toolbar-group:before,
.metro .toolbar .toolbar-group:after {
  display: table;
  content: "";
}
.metro .toolbar .toolbar-group:after {
  clear: both;
}
.metro .button-dropdown {
  position: relative;
  display: inline-block;
}
.metro .button-dropdown .dropdown-menu.place-right {
  right: 0;
  left: auto;
}
.metro button.dropdown-toggle,
.metro .button.dropdown-toggle {
  padding-right: 16px;
  border: 0;
}
.metro button.dropdown-toggle:before,
.metro .button.dropdown-toggle:before {
  position: absolute;
  font-family: metroSysIcons;
  font-size: 12px;
  right: 1px;
  z-index: 2;
}
.metro .image-button {
  padding: 4px 12px;
  text-align: center;
  vertical-align: middle !important;
  background-color: #d9d9d9;
  border: 1px transparent solid;
  color: #222222;
  border-radius: 0;
  cursor: pointer;
  display: inline-block;
  outline: none;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-size: 14px;
  line-height: 16px;
  margin: auto;
  position: relative;
  border: 0;
  padding-right: 45px;
}
.metro .image-button.default {
  background-color: #008287;
  color: #fff;
}
.metro .image-button:focus {
  outline: 0;
  border: 1px #353535 dotted;
}
.metro .image-button:disabled,
.metro .image-button.disabled {
  background-color: #eaeaea !important;
  color: #bebebe !important;
  cursor: not-allowed !important;
}
.metro .image-button * {
  color: inherit;
}
.metro .image-button *:hover {
  color: inherit;
}
.metro .image-button.primary {
  background-color: #16499a !important;
  color: #ffffff !important;
}
.metro .image-button.info {
  background-color: #4390df !important;
  color: #ffffff !important;
}
.metro .image-button.success {
  background-color: #60a917 !important;
  color: #ffffff !important;
}
.metro .image-button.warning {
  background-color: #fa6800 !important;
  color: #ffffff !important;
}
.metro .image-button.danger {
  background-color: #9a1616 !important;
  color: #ffffff !important;
}
.metro .image-button.inverse {
  background-color: #1d1d1d !important;
  color: #ffffff !important;
}
.metro .image-button.link {
  background-color: transparent !important;
  color: #2e92cf !important;
}
.metro .image-button.link:hover {
  text-decoration: underline;
}
.metro .image-button.link:disabled,
.metro .image-button.link.disabled {
  background-color: transparent !important;
  color: #bebebe !important;
  cursor: not-allowed !important;
}
.metro .image-button.link [class*="icon-"] {
  text-decoration: none !important;
}
.metro .image-button:active {
  background-color: #1d1d1d !important;
  color: #ffffff !important;
  border-color: transparent;
}
.metro .image-button.large {
  padding: 11px 19px;
  font-size: 17.5px;
}
.metro .image-button.small {
  padding: 2px 10px;
  font-size: 11.9px;
}
.metro .image-button.mini {
  padding: 0px 6px;
  font-size: 10.5px;
}
.metro .image-button img,
.metro .image-button:active img,
.metro .image-button [class*="icon-"] {
  display: block;
  position: absolute;
  float: right;
  right: 0;
  top: 0;
  padding: 5px;
  height: 100%;
  margin-left: 0px;
  box-sizing: border-box;
  border: 1px transparent solid;
  z-index: 2;
  text-align: center;
}
.metro .image-button [class*="icon-"] {
  vertical-align: middle !important;
  font-size: 12pt;
  line-height: 12pt;
  top: 0;
  right: 0;
}
.metro .image-button.image-left {
  padding-right: 12px;
  padding-left: 45px;
}
.metro .image-button.image-left img,
.metro .image-button.image-left:active img,
.metro .image-button.image-left [class*="icon-"] {
  right: auto;
  left: 0;
}
.metro .image-button:focus {
  border: 0;
}
.metro .shortcut {
  padding: 4px 12px;
  vertical-align: middle !important;
  background-color: #d9d9d9;
  border: 1px transparent solid;
  color: #222222;
  border-radius: 0;
  cursor: pointer;
  outline: none;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-size: 14px;
  line-height: 16px;
  margin: auto;
  height: 92px;
  width: 92px;
  text-align: center;
  font-size: 12pt;
  border: 0;
  border-bottom: 2px solid transparent;
  display: inline-block;
  position: relative;
}
.metro .shortcut.default {
  background-color: #008287;
  color: #fff;
}
.metro .shortcut:focus {
  outline: 0;
  border: 1px #353535 dotted;
}
.metro .shortcut:disabled,
.metro .shortcut.disabled {
  background-color: #eaeaea !important;
  color: #bebebe !important;
  cursor: not-allowed !important;
}
.metro .shortcut * {
  color: inherit;
}
.metro .shortcut *:hover {
  color: inherit;
}
.metro .shortcut.primary {
  background-color: #16499a !important;
  color: #ffffff !important;
}
.metro .shortcut.info {
  background-color: #4390df !important;
  color: #ffffff !important;
}
.metro .shortcut.success {
  background-color: #60a917 !important;
  color: #ffffff !important;
}
.metro .shortcut.warning {
  background-color: #fa6800 !important;
  color: #ffffff !important;
}
.metro .shortcut.danger {
  background-color: #9a1616 !important;
  color: #ffffff !important;
}
.metro .shortcut.inverse {
  background-color: #1d1d1d !important;
  color: #ffffff !important;
}
.metro .shortcut.link {
  background-color: transparent !important;
  color: #2e92cf !important;
}
.metro .shortcut.link:hover {
  text-decoration: underline;
}
.metro .shortcut.link:disabled,
.metro .shortcut.link.disabled {
  background-color: transparent !important;
  color: #bebebe !important;
  cursor: not-allowed !important;
}
.metro .shortcut.link [class*="icon-"] {
  text-decoration: none !important;
}
.metro .shortcut:active {
  background-color: #1d1d1d !important;
  color: #ffffff !important;
  border-color: transparent;
}
.metro .shortcut.large {
  padding: 11px 19px;
  font-size: 17.5px;
}
.metro .shortcut.small {
  padding: 2px 10px;
  font-size: 11.9px;
}
.metro .shortcut.mini {
  padding: 0px 6px;
  font-size: 10.5px;
}
.metro .shortcut [class*="icon-"] {
  display: block;
  margin: 10px;
  font-size: 1.5em;
}
.metro .shortcut small {
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  padding: 2px 4px;
  min-height: 10px;
  min-width: 10px;
  text-align: center;
  vertical-align: middle;
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
}
.metro .shortcut:hover {
  border-color: #e51400;
}
.metro .shortcut.link {
  border: 1px #2e92cf solid;
  text-decoration: none !important;
}
.metro .shortcut.link:hover {
  border-bottom-width: 2px;
}
.metro a.shortcut {
  padding: 12px 0;
}
.metro a.shortcut:hover {
  color: inherit;
}
.metro .button-set:before,
.metro .button-set:after {
  display: table;
  content: "";
}
.metro .button-set:after {
  clear: both;
}
.metro .button-set .button,
.metro .button-set button {
  display: block;
  float: left;
  margin: 0;
  margin-right: 2px;
}
.metro .button-set .button.active,
.metro .button-set button.active {
  background-color: #555555;
  color: #ffffff;
}
.metro .button-set.no-spaces button,
.metro .button-set.no-spaces .button {
  margin-right: 0 !important;
}
.metro .button-set.no-spaces button:active,
.metro .button-set.no-spaces .button:active {
  background-color: #1d1d1d;
  color: #ffffff;
  top: auto;
  left: auto;
}
.metro .button-set.transparent .button,
.metro .button-set.transparent button {
  background-color: transparent;
}
.metro .button-set.transparent .button.active,
.metro .button-set.transparent button.active {
  background-color: #555555;
  color: #ffffff;
}
.metro .button:active {
  background-color: #1d1d1d !important;
  color: #ffffff !important;
  border-color: transparent;
}
.metro .button.large,
.metro button.large {
  padding: 11px 19px;
  font-size: 17.5px;
}
.metro .button.small,
.metro button.small {
  padding: 2px 10px;
  font-size: 11.9px;
}
.metro .button.mini,
.metro button.mini {
  padding: 0px 6px;
  font-size: 10.5px;
}
.metro .breadcrumbs {
  margin: 0.2em;
}
.metro .breadcrumbs ul {
  margin: 0;
  padding: 0;
  list-style: none;
  overflow: hidden;
  width: 100%;
}
.metro .breadcrumbs ul li {
  float: left;
  margin: 0 .2em 0 1em;
}
.metro .breadcrumbs ul li:first-child {
  margin-left: 0 !important;
}
.metro .breadcrumbs ul li:first-child a:before {
  content: normal;
}
.metro .breadcrumbs ul li:last-child {
  background: none;
}
.metro .breadcrumbs ul li:last-child a {
  color: #1d1d1d;
}
.metro .breadcrumbs ul li:last-child:after,
.metro .breadcrumbs ul li:last-child:before {
  content: normal;
}
.metro .breadcrumbs ul a {
  background: #d9d9d9;
  padding: .3em 1em;
  float: left;
  text-decoration: none;
  color: #2e92cf;
  position: relative;
}
.metro .breadcrumbs ul a:hover {
  background: #1ba1e2;
  color: #ffffff;
}
.metro .breadcrumbs ul a:hover:before {
  border-color: #1ba1e2 #1ba1e2 #1ba1e2 transparent;
}
.metro .breadcrumbs ul a:hover:after {
  border-left-color: #1ba1e2;
}
.metro .breadcrumbs ul a:before {
  content: "";
  position: absolute;
  top: 50%;
  margin-top: -1.5em;
  border-width: 1.5em 0 1.5em 1em;
  border-style: solid;
  border-color: #d9d9d9 #d9d9d9 #d9d9d9 transparent;
  left: -1em;
  margin-left: 1px;
}
.metro .breadcrumbs ul a:after {
  content: "";
  position: absolute;
  top: 50%;
  margin-top: -1.5em;
  border-top: 1.5em solid transparent;
  border-bottom: 1.5em solid transparent;
  border-left: 1em solid #d9d9d9;
  right: -1em;
  margin-right: 1px;
}
.metro .breadcrumbs ul .active,
.metro .breadcrumbs ul .active:hover {
  background: none;
}
.metro .breadcrumbs ul .active a,
.metro .breadcrumbs ul .active:hover a {
  color: #1d1d1d;
}
.metro .breadcrumbs ul .active:hover a {
  color: #ffffff;
}
.metro .breadcrumbs ul .active:after,
.metro .breadcrumbs ul .active:before {
  content: normal;
}
.metro .breadcrumbs.small li {
  margin: 0 .2em 0 .8em;
}
.metro .breadcrumbs.small a {
  padding: .2em 1em;
  font-size: 11.9px;
}
.metro .breadcrumbs.mini li {
  margin: 0 .2em 0 .6em;
}
.metro .breadcrumbs.mini a {
  padding: .1em 1em;
  font-size: 10.5px;
}
/* Form width modified to 150 */
.metro form {
  margin: 0 0 14pt;
  width: 150%;
}
.metro fieldset {
  margin: 0;
  padding: 0;
  border: 0;
}
.metro legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 14pt;
  font-size: 16.5pt;
  line-height: 28pt;
  color: #333333;
  border: 0;
  border-bottom: 1px solid #e5e5e5;
}
.metro legend small {
  font-size: 10.5pt;
  color: #999999;
}
.metro label {
  display: block;
  margin: 5px 0;
}
.metro .input-control.checkbox,
.metro .input-control.radio,
.metro .input-control.switch,
.metro .input-control.text,
.metro .input-control.password,
.metro .input-control.select,
.metro .input-control.textarea {
  display: inline-block;
 /*  cursor: pointer; */
  position: relative;
  margin-bottom: 7pt;
}
.metro .input-control.checkbox input[type="checkbox"],
.metro .input-control.radio input[type="checkbox"],
.metro .input-control.checkbox input[type="radio"],
.metro .input-control.radio input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 1px;
  height: 1px;
}
.metro .input-control.checkbox input[type="checkbox"]:focus ~ .check,
.metro .input-control.radio input[type="checkbox"]:focus ~ .check,
.metro .input-control.checkbox input[type="radio"]:focus ~ .check,
.metro .input-control.radio input[type="radio"]:focus ~ .check {
  outline: 1px #999999 dotted !important;
}
.metro .input-control.checkbox .caption,
.metro .input-control.radio .caption {
  margin: 0 5px;
}
.metro .input-control.checkbox .check,
.metro .input-control.radio .check {
  width: 20px;
  height: 20px;
  background-color: #ffffff;
  border: 2px #919191 solid;
  padding: 0;
  margin: 0 5px 0 0;
  position: relative;
  display: inline-block;
  vertical-align: middle;
}
.metro .input-control.checkbox.text-left .check,
.metro .input-control.radio.text-left .check {
  margin: 0 0 0 5px;
}
.metro .input-control.checkbox:hover .check,
.metro .input-control.radio:hover .check {
  border-color: #919191;
}
.metro .input-control.checkbox .check:active,
.metro .input-control.radio .check:active {
  border-color: #1e1e1e;
}
.metro .input-control.checkbox .check:focus,
.metro .input-control.radio .check:focus {
  border-color: #919191;
}
.metro .input-control.checkbox .check:after,
.metro .input-control.radio .check:after {
  position: absolute;
  font-family: metroSysIcons;
  font-size: 9pt;
  line-height: 9pt;
}
.metro .input-control.checkbox input[type="checkbox"]:checked ~ .check:after,
.metro .input-control.radio input[type="checkbox"]:checked ~ .check:after,
.metro .input-control.checkbox input[type="radio"]:checked ~ .check:after,
.metro .input-control.radio input[type="radio"]:checked ~ .check:after {
  content: "\e003";
  margin: 2px;
  top: 0;
  color: #1d1d1d;
}
.metro .input-control.checkbox input[type="checkbox"]:disabled ~ .check,
.metro .input-control.radio input[type="checkbox"]:disabled ~ .check,
.metro .input-control.checkbox input[type="radio"]:disabled ~ .check,
.metro .input-control.radio input[type="radio"]:disabled ~ .check {
  border-color: #d9d9d9;
  background-color: #e6e6e6;
}
.metro .input-control.checkbox input[type="checkbox"]:disabled ~ .check:after,
.metro .input-control.radio input[type="checkbox"]:disabled ~ .check:after,
.metro .input-control.checkbox input[type="radio"]:disabled ~ .check:after,
.metro .input-control.radio input[type="radio"]:disabled ~ .check:after {
  color: #8a8a8a;
}
.metro .input-control.checkbox input[data-show="intermediate"] ~ .check:after,
.metro .input-control.radio input[data-show="intermediate"] ~ .check:after,
.metro .input-control.checkbox input[data-show="intermediate"]:checked ~ .check:after,
.metro .input-control.radio input[data-show="intermediate"]:checked ~ .check:after {
  display: block;
  content: "" !important;
  width: 10px;
  height: 10px;
  background-color: #0070c0;
  margin: 3px;
}
.metro .input-control.checkbox input[data-show="intermediate"]:disabled ~ .check:after,
.metro .input-control.radio input[data-show="intermediate"]:disabled ~ .check:after {
  background-color: #8a8a8a;
}
.metro .input-control.radio .check {
  border-radius: 50%;
}
.metro .input-control.radio.default-style input[type="radio"]:checked ~ .check:after {
  display: block;
  content: "" !important;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #0070c0;
  margin: 3px;
}
.metro .input-control.radio.default-style input[type="radio"]:checked:disabled ~ .check:after {
  background-color: #8a8a8a;
}
.metro .input-control.switch input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  width: 1px;
  height: 1px;
}
.metro .input-control.switch .caption {
  margin: 0 5px;
}
.metro .input-control.switch .check {
  position: relative;
  width: 45px;
  height: 18px;
  outline: 2px #a6a6a6 solid;
  border: 1px #fff solid;
  cursor: pointer;
  background: #A6A6A6;
  z-index: 1;
  display: inline-block;
  vertical-align: middle;
}
.metro .input-control.switch .check:after {
  position: absolute;
  left: -1px;
  top: -1px;
  display: block;
  content: "";
  width: 9px;
  height: 16px;
  outline: 2px #333 solid;
  border: 1px #333 solid;
  cursor: pointer;
  background: #333;
  z-index: 2;
}
.metro .input-control.switch input[type="checkbox"]:focus ~ .check,
.metro .input-control.switch input[type="radio"]:focus ~ .check {
  outline: 1px #999999 dotted !important;
}
.metro .input-control.switch input[type="checkbox"]:checked ~ .check {
  background: #008287;
}
.metro .input-control.switch input[type="checkbox"]:checked ~ .check:after {
  left: auto;
  right: -1px;
}
.metro .input-control.switch input[type="checkbox"]:disabled ~ .check {
  background-color: #e6e6e6;
  border-color: #ffffff;
}
.metro .input-control.switch input[type="checkbox"]:disabled ~ .check:after {
  background-color: #8a8a8a;
  outline-color: #8a8a8a;
  border-color: #8a8a8a;
}
.metro .input-control.text,
.metro .input-control.password,
.metro .input-control.file,
.metro .input-control.email,
.metro .input-control.tel,
.metro .input-control.number {
  display: inline-block;
  /* cursor: pointer; */
  position: relative;
  background-color: inherit;
  outline: 0;
  height: 34px;
  width: 100%;
}
.metro .input-control.text .btn-clear,
.metro .input-control.password .btn-clear,
.metro .input-control.file .btn-clear,
.metro .input-control.email .btn-clear,
.metro .input-control.tel .btn-clear,
.metro .input-control.number .btn-clear,
.metro .input-control.text .btn-reveal,
.metro .input-control.password .btn-reveal,
.metro .input-control.file .btn-reveal,
.metro .input-control.email .btn-reveal,
.metro .input-control.tel .btn-reveal,
.metro .input-control.number .btn-reveal,
.metro .input-control.text .btn-date,
.metro .input-control.password .btn-date,
.metro .input-control.file .btn-date,
.metro .input-control.email .btn-date,
.metro .input-control.tel .btn-date,
.metro .input-control.number .btn-date,
.metro .input-control.text .btn-file,
.metro .input-control.password .btn-file,
.metro .input-control.file .btn-file,
.metro .input-control.email .btn-file,
.metro .input-control.tel .btn-file,
.metro .input-control.number .btn-file,
.metro .input-control.text .btn-search,
.metro .input-control.password .btn-search,
.metro .input-control.file .btn-search,
.metro .input-control.email .btn-search,
.metro .input-control.tel .btn-search,
.metro .input-control.number .btn-search {
  background-color: #F0F0F0;
  position: absolute;
  top: 5px;
  z-index: 2;
  border: 0;
  right: 4px;
  width: 24px;
  height: 24px;
  min-height: 1px;
  min-width: 1px;
  padding: 0;
  margin: 0;
  display: none;
}
.metro .input-control.text .btn-clear:after,
.metro .input-control.password .btn-clear:after,
.metro .input-control.file .btn-clear:after,
.metro .input-control.email .btn-clear:after,
.metro .input-control.tel .btn-clear:after,
.metro .input-control.number .btn-clear:after,
.metro .input-control.text .btn-reveal:after,
.metro .input-control.password .btn-reveal:after,
.metro .input-control.file .btn-reveal:after,
.metro .input-control.email .btn-reveal:after,
.metro .input-control.tel .btn-reveal:after,
.metro .input-control.number .btn-reveal:after,
.metro .input-control.text .btn-date:after,
.metro .input-control.password .btn-date:after,
.metro .input-control.file .btn-date:after,
.metro .input-control.email .btn-date:after,
.metro .input-control.tel .btn-date:after,
.metro .input-control.number .btn-date:after,
.metro .input-control.text .btn-file:after,
.metro .input-control.password .btn-file:after,
.metro .input-control.file .btn-file:after,
.metro .input-control.email .btn-file:after,
.metro .input-control.tel .btn-file:after,
.metro .input-control.number .btn-file:after,
.metro .input-control.text .btn-search:after,
.metro .input-control.password .btn-search:after,
.metro .input-control.file .btn-search:after,
.metro .input-control.email .btn-search:after,
.metro .input-control.tel .btn-search:after,
.metro .input-control.number .btn-search:after {
  font-family: metroSysIcons;
  font-size: 1em;
}
.metro .input-control.text .btn-clear:not(:disabled):active,
.metro .input-control.password .btn-clear:not(:disabled):active,
.metro .input-control.file .btn-clear:not(:disabled):active,
.metro .input-control.email .btn-clear:not(:disabled):active,
.metro .input-control.tel .btn-clear:not(:disabled):active,
.metro .input-control.number .btn-clear:not(:disabled):active,
.metro .input-control.text .btn-reveal:not(:disabled):active,
.metro .input-control.password .btn-reveal:not(:disabled):active,
.metro .input-control.file .btn-reveal:not(:disabled):active,
.metro .input-control.email .btn-reveal:not(:disabled):active,
.metro .input-control.tel .btn-reveal:not(:disabled):active,
.metro .input-control.number .btn-reveal:not(:disabled):active,
.metro .input-control.text .btn-date:not(:disabled):active,
.metro .input-control.password .btn-date:not(:disabled):active,
.metro .input-control.file .btn-date:not(:disabled):active,
.metro .input-control.email .btn-date:not(:disabled):active,
.metro .input-control.tel .btn-date:not(:disabled):active,
.metro .input-control.number .btn-date:not(:disabled):active,
.metro .input-control.text .btn-file:not(:disabled):active,
.metro .input-control.password .btn-file:not(:disabled):active,
.metro .input-control.file .btn-file:not(:disabled):active,
.metro .input-control.email .btn-file:not(:disabled):active,
.metro .input-control.tel .btn-file:not(:disabled):active,
.metro .input-control.number .btn-file:not(:disabled):active,
.metro .input-control.text .btn-search:not(:disabled):active,
.metro .input-control.password .btn-search:not(:disabled):active,
.metro .input-control.file .btn-search:not(:disabled):active,
.metro .input-control.email .btn-search:not(:disabled):active,
.metro .input-control.tel .btn-search:not(:disabled):active,
.metro .input-control.number .btn-search:not(:disabled):active {
  background-color: #1d1d1d;
  color: #ffffff;
}
.metro .input-control.text .btn-clear:active,
.metro .input-control.password .btn-clear:active,
.metro .input-control.file .btn-clear:active,
.metro .input-control.email .btn-clear:active,
.metro .input-control.tel .btn-clear:active,
.metro .input-control.number .btn-clear:active,
.metro .input-control.text .btn-reveal:active,
.metro .input-control.password .btn-reveal:active,
.metro .input-control.file .btn-reveal:active,
.metro .input-control.email .btn-reveal:active,
.metro .input-control.tel .btn-reveal:active,
.metro .input-control.number .btn-reveal:active,
.metro .input-control.text .btn-date:active,
.metro .input-control.password .btn-date:active,
.metro .input-control.file .btn-date:active,
.metro .input-control.email .btn-date:active,
.metro .input-control.tel .btn-date:active,
.metro .input-control.number .btn-date:active,
.metro .input-control.text .btn-file:active,
.metro .input-control.password .btn-file:active,
.metro .input-control.file .btn-file:active,
.metro .input-control.email .btn-file:active,
.metro .input-control.tel .btn-file:active,
.metro .input-control.number .btn-file:active,
.metro .input-control.text .btn-search:active,
.metro .input-control.password .btn-search:active,
.metro .input-control.file .btn-search:active,
.metro .input-control.email .btn-search:active,
.metro .input-control.tel .btn-search:active,
.metro .input-control.number .btn-search:active {
  left: auto;
}
.metro .input-control.text .btn-clear:focus,
.metro .input-control.password .btn-clear:focus,
.metro .input-control.file .btn-clear:focus,
.metro .input-control.email .btn-clear:focus,
.metro .input-control.tel .btn-clear:focus,
.metro .input-control.number .btn-clear:focus,
.metro .input-control.text .btn-reveal:focus,
.metro .input-control.password .btn-reveal:focus,
.metro .input-control.file .btn-reveal:focus,
.metro .input-control.email .btn-reveal:focus,
.metro .input-control.tel .btn-reveal:focus,
.metro .input-control.number .btn-reveal:focus,
.metro .input-control.text .btn-date:focus,
.metro .input-control.password .btn-date:focus,
.metro .input-control.file .btn-date:focus,
.metro .input-control.email .btn-date:focus,
.metro .input-control.tel .btn-date:focus,
.metro .input-control.number .btn-date:focus,
.metro .input-control.text .btn-file:focus,
.metro .input-control.password .btn-file:focus,
.metro .input-control.file .btn-file:focus,
.metro .input-control.email .btn-file:focus,
.metro .input-control.tel .btn-file:focus,
.metro .input-control.number .btn-file:focus,
.metro .input-control.text .btn-search:focus,
.metro .input-control.password .btn-search:focus,
.metro .input-control.file .btn-search:focus,
.metro .input-control.email .btn-search:focus,
.metro .input-control.tel .btn-search:focus,
.metro .input-control.number .btn-search:focus {
  outline: 1px #ccc dotted ;
}
.metro .input-control.text input:focus ~ .btn-clear,
.metro .input-control.password input:focus ~ .btn-clear,
.metro .input-control.file input:focus ~ .btn-clear,
.metro .input-control.email input:focus ~ .btn-clear,
.metro .input-control.tel input:focus ~ .btn-clear,
.metro .input-control.number input:focus ~ .btn-clear,
.metro .input-control.text input:focus ~ .btn-reveal,
.metro .input-control.password input:focus ~ .btn-reveal,
.metro .input-control.file input:focus ~ .btn-reveal,
.metro .input-control.email input:focus ~ .btn-reveal,
.metro .input-control.tel input:focus ~ .btn-reveal,
.metro .input-control.number input:focus ~ .btn-reveal {
  display: block;
}
.metro .input-control.text input:not(:focus) ~ .btn-clear:active,
.metro .input-control.password input:not(:focus) ~ .btn-clear:active,
.metro .input-control.file input:not(:focus) ~ .btn-clear:active,
.metro .input-control.email input:not(:focus) ~ .btn-clear:active,
.metro .input-control.tel input:not(:focus) ~ .btn-clear:active,
.metro .input-control.number input:not(:focus) ~ .btn-clear:active,
.metro .input-control.text input:not(:focus) ~ .btn-reveal:active,
.metro .input-control.password input:not(:focus) ~ .btn-reveal:active,
.metro .input-control.file input:not(:focus) ~ .btn-reveal:active,
.metro .input-control.email input:not(:focus) ~ .btn-reveal:active,
.metro .input-control.tel input:not(:focus) ~ .btn-reveal:active,
.metro .input-control.number input:not(:focus) ~ .btn-reveal:active {
  display: block;
}
.metro .input-control.text .btn-clear:after,
.metro .input-control.password .btn-clear:after,
.metro .input-control.file .btn-clear:after,
.metro .input-control.email .btn-clear:after,
.metro .input-control.tel .btn-clear:after,
.metro .input-control.number .btn-clear:after {
  content: "\e00d";
}
.metro .input-control.text .btn-reveal:after,
.metro .input-control.password .btn-reveal:after,
.metro .input-control.file .btn-reveal:after,
.metro .input-control.email .btn-reveal:after,
.metro .input-control.tel .btn-reveal:after,
.metro .input-control.number .btn-reveal:after {
  content: "\e007";
}
.metro .input-control.text .btn-search:after,
.metro .input-control.password .btn-search:after,
.metro .input-control.file .btn-search:after,
.metro .input-control.email .btn-search:after,
.metro .input-control.tel .btn-search:after,
.metro .input-control.number .btn-search:after {
  content: "\e00e";
}
.metro .input-control.text .btn-date:after,
.metro .input-control.password .btn-date:after,
.metro .input-control.file .btn-date:after,
.metro .input-control.email .btn-date:after,
.metro .input-control.tel .btn-date:after,
.metro .input-control.number .btn-date:after {
  content: "\e013";
}
.metro .input-control.text .btn-file:after,
.metro .input-control.password .btn-file:after,
.metro .input-control.file .btn-file:after,
.metro .input-control.email .btn-file:after,
.metro .input-control.tel .btn-file:after,
.metro .input-control.number .btn-file:after {
  content: "\e008";
}
.metro .input-control.text .btn-search,
.metro .input-control.password .btn-search,
.metro .input-control.file .btn-search,
.metro .input-control.email .btn-search,
.metro .input-control.tel .btn-search,
.metro .input-control.number .btn-search {
  display: block;
}
.metro .input-control.text .btn-date,
.metro .input-control.password .btn-date,
.metro .input-control.file .btn-date,
.metro .input-control.email .btn-date,
.metro .input-control.tel .btn-date,
.metro .input-control.number .btn-date {
  display: block;
}
.metro .input-control.text .btn-file,
.metro .input-control.password .btn-file,
.metro .input-control.file .btn-file,
.metro .input-control.email .btn-file,
.metro .input-control.tel .btn-file,
.metro .input-control.number .btn-file {
  display: block;
}
.metro .input-control.text [class*="btn-"],
.metro .input-control.password [class*="btn-"],
.metro .input-control.file [class*="btn-"],
.metro .input-control.email [class*="btn-"],
.metro .input-control.tel [class*="btn-"],
.metro .input-control.number [class*="btn-"] {
  background-color: transparent;
}
.metro .input-control.text input::-ms-reveal,
.metro .input-control.password input::-ms-reveal,
.metro .input-control.file input::-ms-reveal,
.metro .input-control.email input::-ms-reveal,
.metro .input-control.tel input::-ms-reveal,
.metro .input-control.number input::-ms-reveal,
.metro .input-control.text input::-ms-clear,
.metro .input-control.password input::-ms-clear,
.metro .input-control.file input::-ms-clear,
.metro .input-control.email input::-ms-clear,
.metro .input-control.tel input::-ms-clear,
.metro .input-control.number input::-ms-clear {
  display: none;
}
.metro .input-control.text input,
.metro .input-control.password input,
.metro .input-control.file input,
.metro .input-control.email input,
.metro .input-control.tel input,
/* text input size */
.metro .input-control.number input {
  border: 1px #919191 solid;
  width: 200px;
  height: 30px;
  padding: 5px;
  z-index: 1;
  -webkit-appearance: none;
}
.metro .input-control.text input:focus,
.metro .input-control.password input:focus,
.metro .input-control.file input:focus,
.metro .input-control.email input:focus,
.metro .input-control.tel input:focus,
.metro .input-control.number input:focus {
  outline: 0;
  border-color: black;
}
.metro .input-control.text input:disabled,
.metro .input-control.password input:disabled,
.metro .input-control.file input:disabled,
.metro .input-control.email input:disabled,
.metro .input-control.tel input:disabled,
.metro .input-control.number input:disabled {
  padding-right: 5px;
}
.metro .input-control.text input:disabled ~ .btn-clear,
.metro .input-control.password input:disabled ~ .btn-clear,
.metro .input-control.file input:disabled ~ .btn-clear,
.metro .input-control.email input:disabled ~ .btn-clear,
.metro .input-control.tel input:disabled ~ .btn-clear,
.metro .input-control.number input:disabled ~ .btn-clear,
.metro .input-control.text input:disabled ~ .btn-reveal,
.metro .input-control.password input:disabled ~ .btn-reveal,
.metro .input-control.file input:disabled ~ .btn-reveal,
.metro .input-control.email input:disabled ~ .btn-reveal,
.metro .input-control.tel input:disabled ~ .btn-reveal,
.metro .input-control.number input:disabled ~ .btn-reveal {
  display: none;
}
.metro .input-control.text input::-moz-placeholder,
.metro .input-control.password input::-moz-placeholder,
.metro .input-control.file input::-moz-placeholder,
.metro .input-control.email input::-moz-placeholder,
.metro .input-control.tel input::-moz-placeholder,
.metro .input-control.number input::-moz-placeholder {
  color: #999999;
}
.metro .input-control.text input::-webkit-input-placeholder,
.metro .input-control.password input::-webkit-input-placeholder,
.metro .input-control.file input::-webkit-input-placeholder,
.metro .input-control.email input::-webkit-input-placeholder,
.metro .input-control.tel input::-webkit-input-placeholder,
.metro .input-control.number input::-webkit-input-placeholder {
  color: #999999;
}
.metro .input-control.select,
.metro .input-control.textarea {
  display: inline-block;
  /* cursor: pointer; */
  position: relative;
  width: 100%;
}
.metro .input-control.select select,
.metro .input-control.textarea select,
.metro .input-control.select textarea,
.metro .input-control.textarea textarea {
  border: 1px #919191 solid;
  width: 50%;
  padding: 5px;
}
.metro .input-control.select select:focus,
.metro .input-control.textarea select:focus,
.metro .input-control.select textarea:focus,
.metro .input-control.textarea textarea:focus {
  outline: 0;
  border-color: #919191;
}
.metro .input-control.select select:disabled,
.metro .input-control.textarea select:disabled,
.metro .input-control.select textarea:disabled,
.metro .input-control.textarea textarea:disabled {
  padding-right: 5px;
}
.metro .input-control.select textarea,
.metro .input-control.textarea textarea {
  min-height: 100px;
}
.metro .input-control.text.warning-state input,
.metro .input-control.password.warning-state input,
.metro .input-control.select.warning-state input,
.metro .input-control.textarea.warning-state input,
.metro .input-control.file.warning-state input,
.metro .input-control.email.warning-state input,
.metro .input-control.tel.warning-state input,
.metro .input-control.number.warning-state input,
.metro .input-control.text.warning-state select,
.metro .input-control.password.warning-state select,
.metro .input-control.select.warning-state select,
.metro .input-control.textarea.warning-state select,
.metro .input-control.file.warning-state select,
.metro .input-control.email.warning-state select,
.metro .input-control.tel.warning-state select,
.metro .input-control.number.warning-state select,
.metro .input-control.text.warning-state textarea,
.metro .input-control.password.warning-state textarea,
.metro .input-control.select.warning-state textarea,
.metro .input-control.textarea.warning-state textarea,
.metro .input-control.file.warning-state textarea,
.metro .input-control.email.warning-state textarea,
.metro .input-control.tel.warning-state textarea,
.metro .input-control.number.warning-state textarea {
  border-color: #fa6800;
}
.metro .input-control.text.error-state input,
.metro .input-control.password.error-state input,
.metro .input-control.select.error-state input,
.metro .input-control.textarea.error-state input,
.metro .input-control.file.error-state input,
.metro .input-control.email.error-state input,
.metro .input-control.tel.error-state input,
.metro .input-control.number.error-state input,
.metro .input-control.text.error-state select,
.metro .input-control.password.error-state select,
.metro .input-control.select.error-state select,
.metro .input-control.textarea.error-state select,
.metro .input-control.file.error-state select,
.metro .input-control.email.error-state select,
.metro .input-control.tel.error-state select,
.metro .input-control.number.error-state select,
.metro .input-control.text.error-state textarea,
.metro .input-control.password.error-state textarea,
.metro .input-control.select.error-state textarea,
.metro .input-control.textarea.error-state textarea,
.metro .input-control.file.error-state textarea,
.metro .input-control.email.error-state textarea,
.metro .input-control.tel.error-state textarea,
.metro .input-control.number.error-state textarea {
  border-color: #e51400;
}
.metro .input-control.text.info-state input,
.metro .input-control.password.info-state input,
.metro .input-control.select.info-state input,
.metro .input-control.textarea.info-state input,
.metro .input-control.file.info-state input,
.metro .input-control.email.info-state input,
.metro .input-control.tel.info-state input,
.metro .input-control.number.info-state input,
.metro .input-control.text.info-state select,
.metro .input-control.password.info-state select,
.metro .input-control.select.info-state select,
.metro .input-control.textarea.info-state select,
.metro .input-control.file.info-state select,
.metro .input-control.email.info-state select,
.metro .input-control.tel.info-state select,
.metro .input-control.number.info-state select,
.metro .input-control.text.info-state textarea,
.metro .input-control.password.info-state textarea,
.metro .input-control.select.info-state textarea,
.metro .input-control.textarea.info-state textarea,
.metro .input-control.file.info-state textarea,
.metro .input-control.email.info-state textarea,
.metro .input-control.tel.info-state textarea,
.metro .input-control.number.info-state textarea {
  border-color: #4390df;
}
.metro .input-control.text.success-state input,
.metro .input-control.password.success-state input,
.metro .input-control.select.success-state input,
.metro .input-control.textarea.success-state input,
.metro .input-control.file.success-state input,
.metro .input-control.email.success-state input,
.metro .input-control.tel.success-state input,
.metro .input-control.number.success-state input,
.metro .input-control.text.success-state select,
.metro .input-control.password.success-state select,
.metro .input-control.select.success-state select,
.metro .input-control.textarea.success-state select,
.metro .input-control.file.success-state select,
.metro .input-control.email.success-state select,
.metro .input-control.tel.success-state select,
.metro .input-control.number.success-state select,
.metro .input-control.text.success-state textarea,
.metro .input-control.password.success-state textarea,
.metro .input-control.select.success-state textarea,
.metro .input-control.textarea.success-state textarea,
.metro .input-control.file.success-state textarea,
.metro .input-control.email.success-state textarea,
.metro .input-control.tel.success-state textarea,
.metro .input-control.number.success-state textarea {
  border-color: #60a917;
}
.metro .input-control.text input[data-state="warning"],
.metro .input-control.password input[data-state="warning"],
.metro .input-control.select input[data-state="warning"],
.metro .input-control.textarea input[data-state="warning"],
.metro .input-control.file input[data-state="warning"],
.metro .input-control.email input[data-state="warning"],
.metro .input-control.tel input[data-state="warning"],
.metro .input-control.number input[data-state="warning"] {
  border-color: #fa6800;
}
.metro .input-control.text input[data-state="error"],
.metro .input-control.password input[data-state="error"],
.metro .input-control.select input[data-state="error"],
.metro .input-control.textarea input[data-state="error"],
.metro .input-control.file input[data-state="error"],
.metro .input-control.email input[data-state="error"],
.metro .input-control.tel input[data-state="error"],
.metro .input-control.number input[data-state="error"] {
  border-color: #e51400;
}
.metro .input-control.text input[data-state="info"],
.metro .input-control.password input[data-state="info"],
.metro .input-control.select input[data-state="info"],
.metro .input-control.textarea input[data-state="info"],
.metro .input-control.file input[data-state="info"],
.metro .input-control.email input[data-state="info"],
.metro .input-control.tel input[data-state="info"],
.metro .input-control.number input[data-state="info"] {
  border-color: #4390df;
}
.metro .input-control.text input[data-state="success"],
.metro .input-control.password input[data-state="success"],
.metro .input-control.select input[data-state="success"],
.metro .input-control.textarea input[data-state="success"],
.metro .input-control.file input[data-state="success"],
.metro .input-control.email input[data-state="success"],
.metro .input-control.tel input[data-state="success"],
.metro .input-control.number input[data-state="success"] {
  border-color: #60a917;
}
.metro .input-control.text[class*="span"],
.metro .input-control.password[class*="span"],
.metro .input-control.select[class*="span"],
.metro .input-control.textarea[class*="span"],
.metro .input-control.file[class*="span"],
.metro .input-control.email[class*="span"],
.metro .input-control.tel[class*="span"],
.metro .input-control.number[class*="span"] {
  margin-left: 0;
}
.metro .input-control.file {
  overflow: hidden;
}
.metro .input-control.file input[type="file"] {
  position: absolute;
  opacity: 0;
  width: 1px;
  height: 1px;
  bottom: 0;
  right: 0;
}
.metro input[type="button"],
.metro input[type="submit"],
.metro input[type="reset"] {
  padding: 4px 12px;
  text-align: center;
  vertical-align: middle !important;
  background-color: #d9d9d9;
  border: 1px transparent solid;
  color: #222222;
  border-radius: 0;
  cursor: pointer;
  display: inline-block;
  outline: none;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-size: 14px;
  line-height: 16px;
  margin: auto;
}
.metro input[type="button"].default,
.metro input[type="submit"].default,
.metro input[type="reset"].default {
  background-color: #008287;
  color: #fff;
}
.metro input[type="button"]:focus,
.metro input[type="submit"]:focus,
.metro input[type="reset"]:focus {
  outline: 0;
  border: 1px #353535 dotted;
}
.metro input[type="button"]:disabled,
.metro input[type="submit"]:disabled,
.metro input[type="reset"]:disabled,
.metro input[type="button"].disabled,
.metro input[type="submit"].disabled,
.metro input[type="reset"].disabled {
  background-color: #eaeaea !important;
  color: #bebebe !important;
  cursor: not-allowed !important;
}
.metro input[type="button"] *,
.metro input[type="submit"] *,
.metro input[type="reset"] * {
  color: inherit;
}
.metro input[type="button"] *:hover,
.metro input[type="submit"] *:hover,
.metro input[type="reset"] *:hover {
  color: inherit;
}
.metro input[type="button"].primary,
.metro input[type="submit"].primary,
.metro input[type="reset"].primary {
  background-color: #16499a !important;
  color: #ffffff !important;
}
.metro input[type="button"].info,
.metro input[type="submit"].info,
.metro input[type="reset"].info {
  background-color: #4390df !important;
  color: #ffffff !important;
}
.metro input[type="button"].success,
.metro input[type="submit"].success,
.metro input[type="reset"].success {
  background-color: #60a917 !important;
  color: #ffffff !important;
}
.metro input[type="button"].warning,
.metro input[type="submit"].warning,
.metro input[type="reset"].warning {
  background-color: #fa6800 !important;
  color: #ffffff !important;
}
.metro input[type="button"].danger,
.metro input[type="submit"].danger,
.metro input[type="reset"].danger {
  background-color: #9a1616 !important;
  color: #ffffff !important;
}
.metro input[type="button"].inverse,
.metro input[type="submit"].inverse,
.metro input[type="reset"].inverse {
  background-color: #1d1d1d !important;
  color: #ffffff !important;
}
.metro input[type="button"].link,
.metro input[type="submit"].link,
.metro input[type="reset"].link {
  background-color: transparent !important;
  color: #2e92cf !important;
}
.metro input[type="button"].link:hover,
.metro input[type="submit"].link:hover,
.metro input[type="reset"].link:hover {
  text-decoration: underline;
}
.metro input[type="button"].link:disabled,
.metro input[type="submit"].link:disabled,
.metro input[type="reset"].link:disabled,
.metro input[type="button"].link.disabled,
.metro input[type="submit"].link.disabled,
.metro input[type="reset"].link.disabled {
  background-color: transparent !important;
  color: #bebebe !important;
  cursor: not-allowed !important;
}
.metro input[type="button"].link [class*="icon-"],
.metro input[type="submit"].link [class*="icon-"],
.metro input[type="reset"].link [class*="icon-"] {
  text-decoration: none !important;
}
.metro input[type="button"]:active,
.metro input[type="submit"]:active,
.metro input[type="reset"]:active {
  background-color: #1d1d1d !important;
  color: #ffffff !important;
  border-color: transparent;
}
.metro input[type="button"].large,
.metro input[type="submit"].large,
.metro input[type="reset"].large {
  padding: 11px 19px;
  font-size: 17.5px;
}
.metro input[type="button"].small,
.metro input[type="submit"].small,
.metro input[type="reset"].small {
  padding: 2px 10px;
  font-size: 11.9px;
}
.metro input[type="button"].mini,
.metro input[type="submit"].mini,
.metro input[type="reset"].mini {
  padding: 0px 6px;
  font-size: 10.5px;
}
.metro input[type=number]::-webkit-inner-spin-button,
.metro input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.metro img {
  max-width: 100%;
}
.metro img.rounded,
.metro img.cycle,
.metro img.polaroid {
  background-color: #ffffff;
  border: 1px #eaeaea solid;
}
.metro img.rounded {
  border-radius: 10px;
}
.metro img.cycle {
  border-radius: 50%;
}
.metro img.polaroid {
  padding: 10px;
}
.metro .image-container {
  position: relative;
  padding: 0;
  background-color: #333333;
  width: 240px;
  overflow: hidden;
}
.metro .image-container.full-size {
  width: 100%;
}
.metro .image-container img {
  margin: 0;
  width: 100%;
  height: auto;
}
.metro .image-container > .overlay,
.metro .image-container > .overlay-fluid {
  position: absolute;
  width: 100%;
  height: 55px;
  overflow: hidden;
  background-color: #1e1e1e;
  padding: 5px 10px;
  opacity: .8;
  bottom: 0;
  font-size: 8pt;
  color: #fff;
}
.metro .image-container > .overlay-fluid {
  bottom: -55px;
  font-size: 8pt;
  color: #fff;
}
.metro .image-container:hover .overlay-fluid {
  -webkit-transform: translate(0, -55px);
  -ms-transform: translate(0, -55px);
  -o-transform: translate(0, -55px);
  -moz-transform: translate(0, -55px);
  transform: translate(0, -55px);
  -webkit-transition: all 0.3s ease;
  -moz-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  -ms-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.metro .image-container.selected {
  border: 4px #4390df solid;
}
.metro .image-container.selected:after {
  position: absolute;
  display: block;
  border-top: 28px solid #4390df;
  border-left: 28px solid transparent;
  right: 0;
  content: "";
  top: 0;
  z-index: 101;
}
.metro .image-container.selected:before {
  position: absolute;
  display: block;
  content: "\e003";
  color: #fff;
  right: 0;
  font-family: 'metroSysIcons';
  font-size: 9pt;
  font-weight: normal;
  z-index: 102;
  top: 0;
}
.metro #menu .init {
  margin: 0;
  padding: 0;
  list-style: none;
}
.metro #menu .item-vertical {
  display: block;
  margin: 10px 0;
  padding: 0;
  vertical-align: baseline;
  position: relative;
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
}
.metro #menu .item-horizontal {
  display: inline-block;
  margin: 0 15px;
  padding: 0;
  vertical-align: baseline;
  position: relative;
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
}
.metro .vertical-menu {
  margin: 0;
  padding: 0;
  list-style: none;
}
.metro .vertical-menu li {
  display: block;
  margin: 10px 0;
  padding: 0;
  vertical-align: baseline;
  position: relative;
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
}
.metro .vertical-menu li a {
  display: block;
  text-decoration: none;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-weight: 300;
  letter-spacing: 0.00em;
  font-size: 2.5rem;
  line-height: 2.5rem;
  letter-spacing: 0.01em;
  color: #000000;
  line-height: 2.2rem;
  font-size: 2.2rem;
  color: inherit;
}
.metro .vertical-menu li.title {
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-weight: 300;
  color: #000000;
  letter-spacing: 0.00em;
  font-size: 2.5rem;
  line-height: 2.5rem;
  letter-spacing: 0.01em;
  font-size: 4rem;
  margin-bottom: 20px;
}
.metro .vertical-menu.compact a {
  font-weight: normal;
  font-size: 10.5pt;
  line-height: 12pt;
  color: inherit;
}
.metro .vertical-menu.compact li.title {
  margin-bottom: 10px;
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  color: #000000;
  font-size: 1.6rem;
  line-height: 1.6rem;
  letter-spacing: 0.02em;
}
.metro .vertical-menu ul.secondary {
  margin: 0;
  padding: 0;
  list-style: none;
  margin-left: 15px;
}
.metro .vertical-menu ul.secondary li a {
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  color: #000000;
  font-size: 1.6rem;
  line-height: 1.6rem;
  letter-spacing: 0.02em;
  font-weight: normal;
  font-style: normal;
  line-height: 1.4rem;
  color: inherit;
}
.metro .horizontal-menu {
  margin: 0;
  padding: 0;
  list-style: none;
  padding: 0px;
}
.metro .horizontal-menu li {
  display: inline-block;
  margin: 0 15px;
  padding: 0;
  vertical-align: baseline;
  position: relative;
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
}
.metro .horizontal-menu li a {
  display: inline-block;
  text-decoration: none;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-weight: 300;
  letter-spacing: 0.00em;
  font-size: 2.5rem;
  line-height: 2.5rem;
  letter-spacing: 0.01em;
  color: #000000;
  line-height: 2.2rem;
  font-size: 2.2rem;
  color: inherit;
  padding: 5px;
}
.metro .horizontal-menu li a:hover {
  background: inherit;
}
.metro .horizontal-menu.compact li {
  margin-left: 5px;
}
.metro .horizontal-menu.compact a {
  font-weight: normal;
  font-size: 10.5pt;
  color: inherit;
}
.metro .horizontal-menu.compact .dropdown-toggle:after {
  bottom: 2px;
}
.metro .horizontal-menu li:first-child {
  margin-left: 0px;
}
.metro .horizontal-menu li:hover > .dropdown-menu[data-show="hover"] {
  display: block;
}
.metro .side-menu {
  margin: 0;
  padding: 0;
  list-style: none;
  position: relative;
  background: #ffffff;
  min-width: 200px;
  width: auto;
  border: 1px solid rgba(0, 0, 0, 0.2);
}
.metro .side-menu li {
  display: block;
  margin: 10px 0;
  padding: 0;
  vertical-align: baseline;
  position: relative;
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
}
.metro .side-menu li a {
  display: block;
  text-decoration: none;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-weight: 300;
  letter-spacing: 0.00em;
  font-size: 2.5rem;
  line-height: 2.5rem;
  letter-spacing: 0.01em;
  color: #000000;
  line-height: 2.2rem;
  font-size: 2.2rem;
  color: inherit;
}
.metro .side-menu li.title {
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-weight: 300;
  color: #000000;
  letter-spacing: 0.00em;
  font-size: 2.5rem;
  line-height: 2.5rem;
  letter-spacing: 0.01em;
  font-size: 4rem;
  margin-bottom: 20px;
}
.metro .side-menu.compact a {
  font-weight: normal;
  font-size: 10.5pt;
  line-height: 12pt;
  color: inherit;
}
.metro .side-menu.compact li.title {
  margin-bottom: 10px;
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  color: #000000;
  font-size: 1.6rem;
  line-height: 1.6rem;
  letter-spacing: 0.02em;
}
.metro .side-menu ul.secondary {
  margin: 0;
  padding: 0;
  list-style: none;
  margin-left: 15px;
}
.metro .side-menu ul.secondary li a {
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  color: #000000;
  font-size: 1.6rem;
  line-height: 1.6rem;
  letter-spacing: 0.02em;
  font-weight: normal;
  font-style: normal;
  line-height: 1.4rem;
  color: inherit;
}
.metro .side-menu li {
  margin: 5px 0 0;
  width: 100%;
}
.metro .side-menu li:last-child {
  margin-bottom: 5px;
}
.metro .side-menu a {
  padding: 8px 20px !important;
  color: inherit;
  font-size: 15px !important;
  line-height: 14px !important;
}
.metro .side-menu li:hover {
  background: #1ba1e2;
  color: #ffffff;
}
.metro .side-menu .divider {
  padding: 0;
  height: 1px;
  margin: 9px 1px;
  overflow: hidden;
  background-color: #e5e5e5;
}
.metro .side-menu .divider:hover {
  background: #e5e5e5;
}
.metro .side-menu .dropdown-menu {
  left: 100%;
  top: -10%;
}
.metro .side-menu.left .dropdown-menu {
  left: -100%;
}
.metro .side-menu > li:hover > .dropdown-menu[data-show="hover"] {
  display: block;
}
.metro .side-menu > li > a.dropdown-toggle:after {
  font-family: metroSysIcons;
  font-size: .8em;
  content: "\e156";
  left: 85%;
  top: .75em;
  -webkit-transform: rotate(0deg);
  -moz-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  -o-transform: rotate(0deg);
  transform: rotate(0deg);
}
.metro .side-menu.text-right li {
  text-align: right;
}
.metro .dropdown-menu {
  margin: 0;
  padding: 0;
  list-style: none;
  position: absolute;
  display: none;
  z-index: 1000;
  background: #ffffff;
  min-width: 220px;
  width: auto;
  border: 1px solid #dcddde;
}
.metro .dropdown-menu li {
  display: block;
  margin: 10px 0;
  padding: 0;
  vertical-align: baseline;
  position: relative;
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
}
.metro .dropdown-menu li a {
  display: block;
  text-decoration: none;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-weight: 300;
  letter-spacing: 0.00em;
  font-size: 2.5rem;
  line-height: 2.5rem;
  letter-spacing: 0.01em;
  color: #000000;
  line-height: 2.2rem;
  font-size: 2.2rem;
  color: inherit;
}
.metro .dropdown-menu li.title {
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-weight: 300;
  color: #000000;
  letter-spacing: 0.00em;
  font-size: 2.5rem;
  line-height: 2.5rem;
  letter-spacing: 0.01em;
  font-size: 4rem;
  margin-bottom: 20px;
}
.metro .dropdown-menu.compact a {
  font-weight: normal;
  font-size: 10.5pt;
  line-height: 12pt;
  color: inherit;
}
.metro .dropdown-menu.compact li.title {
  margin-bottom: 10px;
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  color: #000000;
  font-size: 1.6rem;
  line-height: 1.6rem;
  letter-spacing: 0.02em;
}
.metro .dropdown-menu ul.secondary {
  margin: 0;
  padding: 0;
  list-style: none;
  margin-left: 15px;
}
.metro .dropdown-menu ul.secondary li a {
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  color: #000000;
  font-size: 1.6rem;
  line-height: 1.6rem;
  letter-spacing: 0.02em;
  font-weight: normal;
  font-style: normal;
  line-height: 1.4rem;
  color: inherit;
}
.metro .dropdown-menu.shadow {
  box-shadow: 3px 3px 0 rgba(236, 236, 236, 0.7);
}
.metro .dropdown-menu li {
  margin: 5px 0 0;
  width: 100%;
  border: 1px transparent solid;
}
.metro .dropdown-menu li:last-child {
  margin-bottom: 5px;
}
.metro .dropdown-menu li.disabled,
.metro .dropdown-menu li.disabled:hover {
  cursor: default !important;
  background: transparent !important;
  color: #eeeeee;
}
.metro .dropdown-menu li.disabled a,
.metro .dropdown-menu li.disabled:hover a {
  color: inherit;
  cursor: inherit;
  background: inherit;
}
.metro .dropdown-menu a {
  padding: 8px 32px !important;
  color: inherit;
  font-size: 15px !important;
  line-height: 14px !important;
  cursor: pointer;
}
.metro .dropdown-menu li:hover {
  background: #edf4fc;
  border-color: #a8d2fd;
}
.metro .dropdown-menu li.checked a:before,
.metro .dropdown-menu li.checked-partial a:before,
.metro .dropdown-menu li.unchecked a:before {
  font-family: metroSysIcons;
  position: absolute;
  left: 10px;
  content: "\e004";
}
.metro .dropdown-menu li.checked-partial a:before {
  content: "\e005";
}
.metro .dropdown-menu li.unchecked a:before {
  content: "\e005";
}
.metro .dropdown-menu.place-right {
  right: -15px;
  left: auto;
}
.metro .dropdown-menu .divider {
  padding: 0;
  height: 1px;
  margin: 9px 1px;
  overflow: hidden;
  background-color: #e5e5e5;
  border-width: 0;
}
.metro .dropdown-menu .divider:hover {
  background: #e5e5e5;
}
.metro .dropdown-menu.open {
  display: block !important;
}
.metro .dropdown-menu .dropdown-menu {
  left: 100%;
  top: -10%;
}
.metro .dropdown-menu > li:hover > .dropdown-menu[data-show="hover"] {
  display: block;
}
.metro .dropdown-menu > li > a.dropdown-toggle:after {
  font-family: metroSysIcons;
  font-size: .7em;
  content: "\e001";
  left: 90%;
  top: 50%;
  margin-top: -1.4em;
  -webkit-transform: rotate(-90deg);
  -moz-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  -o-transform: rotate(-90deg);
  transform: rotate(-90deg);
}
.metro .dropdown-menu.drop-left {
  left: -100%;
}
.metro .dropdown-menu.drop-up {
  top: auto;
  bottom: 0;
}
.metro .dropdown-menu .menu-title {
  background-color: #f6f7f8;
  font-size: 12px;
  line-height: 14px;
  padding: 4px 8px;
  border: 0;
  border-bottom: 1px #dcddde solid;
  border-top: 1px #dcddde solid;
  color: #646464;
}
.metro .dropdown-menu .menu-title:first-child {
  margin: 0;
  border-top-width: 0;
}
.metro .dropdown-menu .menu-title:first-child:hover {
  border-top-width: 0;
}
.metro .dropdown-menu .menu-title:hover {
  background-color: #f6f7f8;
  cursor: default;
  border: 0;
  border-bottom: 1px #dcddde solid;
  border-top: 1px #dcddde solid;
}
.metro .dropdown-menu.inverse {
  border: 2px #3b3b3b solid;
}
.metro .dropdown-menu.inverse li {
  border: 0;
}
.metro .dropdown-menu.inverse li a {
  padding: 9px 32px !important;
}
.metro .dropdown-menu.inverse li:hover {
  border: 0;
  background-color: #e5e5e5;
}
.metro .dropdown-menu.inverse .menu-title:hover {
  background-color: #f6f7f8;
}
.metro .dropdown-menu.inverse .divider {
  margin-left: 0;
  margin-right: 0;
}
.metro .dropdown-menu.dark {
  background-color: #333333;
  border: 2px #333333 solid;
  color: #ffffff;
}
.metro .dropdown-menu.dark.opacity {
  background-color: rgba(61, 61, 61, 0.9);
}
.metro .dropdown-menu.dark li {
  border: 0;
  color: inherit;
}
.metro .dropdown-menu.dark li a {
  padding: 9px 32px !important;
  color: inherit;
}
.metro .dropdown-menu.dark li.disabled,
.metro .dropdown-menu.dark li.disabled:hover {
  cursor: default !important;
  background: transparent !important;
  color: #555555;
}
.metro .dropdown-menu.dark li.disabled a,
.metro .dropdown-menu.dark li.disabled:hover a {
  color: inherit;
  cursor: inherit;
  background: inherit;
}
.metro .dropdown-menu.dark li:hover {
  border: 0;
  background-color: #201a1a;
  color: #ffffff;
}
.metro .dropdown-menu.dark .menu-title {
  background-color: #3b3b3b;
}
.metro .dropdown-menu.dark .menu-title:hover {
  background-color: #3b3b3b;
}
.metro .dropdown-menu.dark .divider {
  margin-left: 0;
  margin-right: 0;
  background-color: #555555;
}
.metro .dropdown-menu.dark .divider:hover {
  background: #555555;
}
.metro .dropdown-toggle {
  padding-right: 20px !important;
}
.metro .dropdown-toggle:after {
  position: absolute;
  margin-left: .25em;
  bottom: 0;
  font-family: metroSysIcons;
  font-size: .7em;
  content: '\e001';
}
.metro .dropdown-toggle.no-marker:after {
  content: "";
}
.metro .dropdown-toggle.without-icon:after {
  content: "" !important;
}
.metro .contextmenu {
  z-index: 1000;
  position: absolute;
  background-color: #FFFFFF;
  border: 2.5px solid #2a2a2a;
  padding: 2px;
}
.metro .contextmenu .item:active {
  background-color: #000000;
  color: #ffffff;
}
.metro .contextmenu .item:hover {
  background-color: #dedede;
}
.metro .contextmenu .item {
  background-color: #FFFFFF;
  width: auto;
  padding: 12px;
  margin: -2px;
}
.metro .navigation-bar,
.metro .navbar {
  width: 100%;
  display: block;
  background-color: #1ba1e2;
  color: #ffffff;
  height: 4px;
}
.metro .navigation-bar .navigation-bar-content,
.metro .navbar .navigation-bar-content,
.metro .navigation-bar .navbar-content,
.metro .navbar .navbar-content {
  background-color: inherit;
  color: inherit;
  display: block;
}
.metro .navigation-bar .navigation-bar-content .element,
.metro .navbar .navigation-bar-content .element,
.metro .navigation-bar .navbar-content .element,
.metro .navbar .navbar-content .element,
.metro .navigation-bar .navigation-bar-content .element-divider,
.metro .navbar .navigation-bar-content .element-divider,
.metro .navigation-bar .navbar-content .element-divider,
.metro .navbar .navbar-content .element-divider {
  display: block;
  float: left;
  background-color: inherit;
  position: relative;
  height: 4px;
}
.metro .navigation-bar .navigation-bar-content .element.place-right,
.metro .navbar .navigation-bar-content .element.place-right,
.metro .navigation-bar .navbar-content .element.place-right,
.metro .navbar .navbar-content .element.place-right,
.metro .navigation-bar .navigation-bar-content .element-divider.place-right,
.metro .navbar .navigation-bar-content .element-divider.place-right,
.metro .navigation-bar .navbar-content .element-divider.place-right,
.metro .navbar .navbar-content .element-divider.place-right {
  float: right;
}
.metro .navigation-bar .navigation-bar-content .element,
.metro .navbar .navigation-bar-content .element,
.metro .navigation-bar .navbar-content .element,
.metro .navbar .navbar-content .element {
  padding: 15px;
  line-height: 15px;
  color: inherit;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-size: 14px;
  position: relative;
}
.metro .navigation-bar .navigation-bar-content .element:hover,
.metro .navbar .navigation-bar-content .element:hover,
.metro .navigation-bar .navbar-content .element:hover,
.metro .navbar .navbar-content .element:hover {
  background-color: #00aafd;
}
.metro .navigation-bar .navigation-bar-content .element > a.dropdown-toggle,
.metro .navbar .navigation-bar-content .element > a.dropdown-toggle,
.metro .navigation-bar .navbar-content .element > a.dropdown-toggle,
.metro .navbar .navbar-content .element > a.dropdown-toggle {
  color: inherit;
  position: relative;
  padding-right: 5px !important;
  margin-right: 5px;
}
.metro .navigation-bar .navigation-bar-content .element > a.dropdown-toggle:after,
.metro .navbar .navigation-bar-content .element > a.dropdown-toggle:after,
.metro .navigation-bar .navbar-content .element > a.dropdown-toggle:after,
.metro .navbar .navbar-content .element > a.dropdown-toggle:after {
  position: absolute;
  left: 100%;
  margin-left: 0;
}
.metro .navigation-bar .navigation-bar-content .element > .dropdown-menu,
.metro .navbar .navigation-bar-content .element > .dropdown-menu,
.metro .navigation-bar .navbar-content .element > .dropdown-menu,
.metro .navbar .navbar-content .element > .dropdown-menu {
  top: 100%;
  left: 0;
}
.metro .navigation-bar .navigation-bar-content .element > .dropdown-menu.place-right,
.metro .navbar .navigation-bar-content .element > .dropdown-menu.place-right,
.metro .navigation-bar .navbar-content .element > .dropdown-menu.place-right,
.metro .navbar .navbar-content .element > .dropdown-menu.place-right {
  right: 0;
  left: auto;
}
.metro .navigation-bar .navigation-bar-content .element-divider,
.metro .navbar .navigation-bar-content .element-divider,
.metro .navigation-bar .navbar-content .element-divider,
.metro .navbar .navbar-content .element-divider {
  padding: 15px 0;
  line-height: 15px;
  width: 1px !important;
  border-left: 1px #ccc solid;
  opacity: .3;
  position: relative;
}
.metro .navigation-bar .navigation-bar-content .element-divider:before,
.metro .navbar .navigation-bar-content .element-divider:before,
.metro .navigation-bar .navbar-content .element-divider:before,
.metro .navbar .navbar-content .element-divider:before {
  content: "|";
  visibility: hidden;
}
.metro .navigation-bar-content .brand,
.metro .navbar-content .brand {
  font-size: 18px;
}
.metro .navigation-bar-content form,
.metro .navbar-content form {
  margin: 0;
  padding-bottom: 0 !important;
  position: relative;
}
.metro .navigation-bar-content .element .input-control.text,
.metro .navbar-content .element .input-control.text,
.metro .navigation-bar-content .element .input-control.password,
.metro .navbar-content .element .input-control.password {
  width: auto;
  height: auto;
  position: relative;
  margin: 0;
  top: -6px;
}
.metro .navigation-bar-content .element .input-control.text [class*='btn-'],
.metro .navbar-content .element .input-control.text [class*='btn-'],
.metro .navigation-bar-content .element .input-control.password [class*='btn-'],
.metro .navbar-content .element .input-control.password [class*='btn-'] {
  top: 2px;
  right: 2px;
}
.metro .navigation-bar-content .element .input-control.text input[type='text'],
.metro .navbar-content .element .input-control.text input[type='text'],
.metro .navigation-bar-content .element .input-control.password input[type='text'],
.metro .navbar-content .element .input-control.password input[type='text'] {
  font-size: 12px;
}
.metro .navigation-bar-content .input-control.checkbox,
.metro .navbar-content .input-control.checkbox,
.metro .navigation-bar-content .input-control.radio,
.metro .navbar-content .input-control.radio {
  position: relative;
  margin: 0;
  top: -6px;
}
.metro .navigation-bar-content .button,
.metro .navbar-content .button,
.metro .navigation-bar-content .image-button,
.metro .navbar-content .image-button {
  background-color: transparent;
}
.metro .navigation-bar-content .button,
.metro .navbar-content .button {
  margin-top: -14px;
  background-color: inherit;
}
.metro .navigation-bar-content .image-button,
.metro .navbar-content .image-button {
  padding-right: 40px;
  padding-left: 5px;
  background-color: inherit;
}
.metro .navigation-bar-content .image-button:active,
.metro .navbar-content .image-button:active {
  left: auto;
  right: auto;
  top: auto;
}
.metro .navigation-bar-content .image-button img,
.metro .navbar-content .image-button img {
  width: 40px;
  height: 40px;
  margin-top: 2.5px;
}
.metro .navigation-bar-content .image-button.image-left,
.metro .navbar-content .image-button.image-left {
  padding-right: 5px;
  padding-left: 40px;
}
.metro .navigation-bar-content [class*="icon-"],
.metro .navbar-content [class*="icon-"] {
  font-size: 16px !important;
}
.metro .input-element,
.metro .element-input {
  padding-bottom: 0 !important;
}
.metro .input-element:hover,
.metro .element-input:hover {
  background-color: inherit !important;
}
.metro .navigation-bar .element-menu,
.metro .navbar .element-menu {
  margin: 0;
  padding: 0;
  list-style: none;
  color: inherit;
}
.metro .navigation-bar .element-menu > li,
.metro .navbar .element-menu > li {
  display: block;
  float: left;
  position: relative;
  color: inherit;
}
.metro .navigation-bar .element-menu > li > a,
.metro .navbar .element-menu > li > a {
  display: block;
  float: left;
  width: 100%;
  padding: 15px;
  line-height: 15px;
  color: inherit;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-size: 14px;
}
.metro .navigation-bar .element-menu > li > a.dropdown-toggle,
.metro .navbar .element-menu > li > a.dropdown-toggle {
  color: inherit;
  position: relative;
}
.metro .navigation-bar .element-menu > li > a.dropdown-toggle:after,
.metro .navbar .element-menu > li > a.dropdown-toggle:after {
  position: absolute;
  left: 95%;
  top: 50%;
  margin-left: -12px;
  margin-top: -5px;
}
.metro .navigation-bar .element-menu > li > .dropdown-menu,
.metro .navbar .element-menu > li > .dropdown-menu {
  top: 100%;
  left: 0;
}
.metro .navigation-bar .element-menu > li > .dropdown-menu.place-right,
.metro .navbar .element-menu > li > .dropdown-menu.place-right {
  right: 0;
  left: auto;
}
.metro .navigation-bar .element-menu > li:hover,
.metro .navbar .element-menu > li:hover {
  background-color: #00aafd;
}
.metro .navigation-bar .element-menu.drop-up li > .dropdown-menu,
.metro .navbar .element-menu.drop-up li > .dropdown-menu {
  top: auto;
  bottom: 45px;
}
.metro .navigation-bar .element-menu.drop-up li > .dropdown-menu .dropdown-menu,
.metro .navbar .element-menu.drop-up li > .dropdown-menu .dropdown-menu {
  bottom: 0;
}
.metro .navigation-bar .pull-menu,
.metro .navbar .pull-menu {
  display: none;
  float: right !important;
  color: #fff;
  cursor: pointer;
  font: 1.8em sans-serif;
  position: absolute;
  top: 0;
  right: 0;
  height: 45px;
  width: 40px;
  line-height: 20px;
  margin: 0;
}
.metro .navigation-bar .pull-menu:before,
.metro .navbar .pull-menu:before {
  content: "\2261";
  position: absolute;
  font-size: 20pt;
  top: 10px;
  left: 10px;
}
.metro .navigation-bar .pull-menu input[type="checkbox"],
.metro .navbar .pull-menu input[type="checkbox"] {
  width: 0;
  height: 0;
  opacity: 0;
}
.metro .navigation-bar-content:before,
.metro .navbar-content:before,
.metro .navigation-bar-content:after,
.metro .navbar-content:after {
  display: table;
  content: "";
}
.metro .navigation-bar-content:after,
.metro .navbar-content:after {
  clear: both;
}
.metro .navigation-bar.fixed-top,
.metro .navbar.fixed-top,
.metro .navigation-bar.fixed-bottom,
.metro .navbar.fixed-bottom {
  position: fixed;
  z-index: 1030;
  left: 0;
}
.metro .navigation-bar.fixed-top,
.metro .navbar.fixed-top {
  top: 0;
  bottom: auto;
}
.metro .navigation-bar.fixed-bottom,
.metro .navbar.fixed-bottom {
  bottom: 0;
  top: auto;
}
/* Navigation */
.metro .navigation-bar.dark {
  background-color:#0070c0;
}
.metro .navigation-bar.dark .element-menu > li > a {
  color: #ffffff;
}
.metro .navigation-bar.dark .element:hover,
.metro .navigation-bar.dark .element-menu > li:hover {
  background-color: #201a1a;
}
.metro .navigation-bar.light {
  background-color: #eeeeee;
  color: #555555;
}
.metro .navigation-bar.light .element:hover,
.metro .navigation-bar.light .element-menu > li:hover {
  background-color: #999999;
  color: #ffffff;
}
.metro .navigation-bar.light .pull-menu {
  color: #1d1d1d;
}
.metro .navigation-bar.white {
  background-color: #ffffff;
  color: #555555;
}
.metro .navigation-bar.white .element:hover,
.metro .navigation-bar.white .element-menu > li:hover {
  background-color: #00aff0;
  color: #ffffff;
}
.metro .navigation-bar.white .pull-menu {
  color: #1d1d1d;
}
.metro #tile .small-size {
  width: 80px;
  height: 80px;
  margin: 0 5px 5px 0;
}
.metro #tile .meduim-size {
  width: 120px;
  height: 120px;
  margin: 0 10px 10px 0;
}
.metro #tile .large-size {
  width: 150px;
  height: 150px;
  margin: 0 15px 15px 0;
}
.metro .tile-area {
  position: relative;
  padding: 120px 80px 0 80px;
  overflow: hidden;
}
.metro .tile-area .tile-area-title {
  position: fixed;
  top: 20px;
  left: 80px;
}
.metro .tile-area .user-id {
  position: fixed;
  right: 40px;
  top: 40px;
  display: inline-block;
  padding: 8px;
  background-color: transparent;
  width: auto;
  color: #ffffff;
  cursor: default;
}
.metro .tile-area .user-id .user-id-image {
  width: 40px;
  height: 40px;
  float: right;
  margin-left: 10px;
  color: inherit;
  background-color: #464646;
  text-align: center;
  vertical-align: middle;
}
.metro .tile-area .user-id .user-id-image img,
.metro .tile-area .user-id .user-id-image [class*="icon-"] {
  color: inherit;
  font-size: 16pt;
  line-height: 16pt;
  width: 100%;
  height: 100%;
  text-align: center;
  vertical-align: middle;
}
.metro .tile-area .user-id .user-id-image [class*="icon-"] {
  padding: 10px;
}
.metro .tile-area .user-id .user-id-name {
  float: right;
  text-align: right;
  margin: 0;
  padding: 0;
}
.metro .tile-area .user-id .user-id-name .first-name,
.metro .tile-area .user-id .user-id-name .last-name {
  display: block;
  color: inherit;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  line-height: 12pt;
}
.metro .tile-area .user-id .user-id-name .first-name {
  font-size: 20pt;
  line-height: 20pt;
}
.metro .tile-area .user-id .user-id-name .last-name {
  font-size: 12pt;
}
.metro .tile-area .user-id:hover {
  background-color: #404040;
}
.metro .tile-area-dark {
  min-width: 100%;
  height: 100%;
  background-color: #1d1d1d !important;
}
.metro .tile-area-dark .tile {
  outline-color: #373737 !important;
}
.metro .tile-area-dark:before,
.metro .tile-area-dark:after {
  display: table;
  content: "";
}
.metro .tile-area-dark:after {
  clear: both;
}
.metro .tile-area-darkBrown {
  min-width: 100%;
  height: 100%;
  background-color: #63362f !important;
}
.metro .tile-area-darkBrown .tile {
  outline-color: #86493f !important;
}
.metro .tile-area-darkBrown:before,
.metro .tile-area-darkBrown:after {
  display: table;
  content: "";
}
.metro .tile-area-darkBrown:after {
  clear: both;
}
.metro .tile-area-darkCrimson {
  min-width: 100%;
  height: 100%;
  background-color: #640024 !important;
}
.metro .tile-area-darkCrimson .tile {
  outline-color: #970036 !important;
}
.metro .tile-area-darkCrimson:before,
.metro .tile-area-darkCrimson:after {
  display: table;
  content: "";
}
.metro .tile-area-darkCrimson:after {
  clear: both;
}
.metro .tile-area-darkViolet {
  min-width: 100%;
  height: 100%;
  background-color: #57169a !important;
}
.metro .tile-area-darkViolet .tile {
  outline-color: #701cc7 !important;
}
.metro .tile-area-darkViolet:before,
.metro .tile-area-darkViolet:after {
  display: table;
  content: "";
}
.metro .tile-area-darkViolet:after {
  clear: both;
}
.metro .tile-area-darkMagenta {
  min-width: 100%;
  height: 100%;
  background-color: #81003c !important;
}
.metro .tile-area-darkMagenta .tile {
  outline-color: #b40054 !important;
}
.metro .tile-area-darkMagenta:before,
.metro .tile-area-darkMagenta:after {
  display: table;
  content: "";
}
.metro .tile-area-darkMagenta:after {
  clear: both;
}
.metro .tile-area-darkCyan {
  min-width: 100%;
  height: 100%;
  background-color: #1b6eae !important;
}
.metro .tile-area-darkCyan .tile {
  outline-color: #228ada !important;
}
.metro .tile-area-darkCyan:before,
.metro .tile-area-darkCyan:after {
  display: table;
  content: "";
}
.metro .tile-area-darkCyan:after {
  clear: both;
}
.metro .tile-area-darkCobalt {
  min-width: 100%;
  height: 100%;
  background-color: #00356a !important;
}
.metro .tile-area-darkCobalt .tile {
  outline-color: #004e9d !important;
}
.metro .tile-area-darkCobalt:before,
.metro .tile-area-darkCobalt:after {
  display: table;
  content: "";
}
.metro .tile-area-darkCobalt:after {
  clear: both;
}
.metro .tile-area-darkTeal {
  min-width: 100%;
  height: 100%;
  background-color: #004050 !important;
}
.metro .tile-area-darkTeal .tile {
  outline-color: #006983 !important;
}
.metro .tile-area-darkTeal:before,
.metro .tile-area-darkTeal:after {
  display: table;
  content: "";
}
.metro .tile-area-darkTeal:after {
  clear: both;
}
.metro .tile-area-darkEmerald {
  min-width: 100%;
  height: 100%;
  background-color: #003e00 !important;
}
.metro .tile-area-darkEmerald .tile {
  outline-color: #007100 !important;
}
.metro .tile-area-darkEmerald:before,
.metro .tile-area-darkEmerald:after {
  display: table;
  content: "";
}
.metro .tile-area-darkEmerald:after {
  clear: both;
}
.metro .tile-area-darkGreen {
  min-width: 100%;
  height: 100%;
  background-color: #128023 !important;
}
.metro .tile-area-darkGreen .tile {
  outline-color: #18ad2f !important;
}
.metro .tile-area-darkGreen:before,
.metro .tile-area-darkGreen:after {
  display: table;
  content: "";
}
.metro .tile-area-darkGreen:after {
  clear: both;
}
.metro .tile-area-darkOrange {
  min-width: 100%;
  height: 100%;
  background-color: #bf5a15 !important;
}
.metro .tile-area-darkOrange .tile {
  outline-color: #e77120 !important;
}
.metro .tile-area-darkOrange:before,
.metro .tile-area-darkOrange:after {
  display: table;
  content: "";
}
.metro .tile-area-darkOrange:after {
  clear: both;
}
.metro .tile-area-darkRed {
  min-width: 100%;
  height: 100%;
  background-color: #9a1616 !important;
}
.metro .tile-area-darkRed .tile {
  outline-color: #c71c1c !important;
}
.metro .tile-area-darkRed:before,
.metro .tile-area-darkRed:after {
  display: table;
  content: "";
}
.metro .tile-area-darkRed:after {
  clear: both;
}
.metro .tile-area-darkPink {
  min-width: 100%;
  height: 100%;
  background-color: #9a165a !important;
}
.metro .tile-area-darkPink .tile {
  outline-color: #c71c74 !important;
}
.metro .tile-area-darkPink:before,
.metro .tile-area-darkPink:after {
  display: table;
  content: "";
}
.metro .tile-area-darkPink:after {
  clear: both;
}
.metro .tile-area-darkIndigo {
  min-width: 100%;
  height: 100%;
  background-color: #4b0096 !important;
}
.metro .tile-area-darkIndigo .tile {
  outline-color: #6400c9 !important;
}
.metro .tile-area-darkIndigo:before,
.metro .tile-area-darkIndigo:after {
  display: table;
  content: "";
}
.metro .tile-area-darkIndigo:after {
  clear: both;
}
.metro .tile-area-darkBlue {
  min-width: 100%;
  height: 100%;
  background-color: #16499a !important;
}
.metro .tile-area-darkBlue .tile {
  outline-color: #1c5ec7 !important;
}
.metro .tile-area-darkBlue:before,
.metro .tile-area-darkBlue:after {
  display: table;
  content: "";
}
.metro .tile-area-darkBlue:after {
  clear: both;
}
.metro .tile-area-lightBlue {
  min-width: 100%;
  height: 100%;
  background-color: #4390df !important;
}
.metro .tile-area-lightBlue .tile {
  outline-color: #6faae6 !important;
}
.metro .tile-area-lightBlue:before,
.metro .tile-area-lightBlue:after {
  display: table;
  content: "";
}
.metro .tile-area-lightBlue:after {
  clear: both;
}
.metro .tile-area-lightTeal {
  min-width: 100%;
  height: 100%;
  background-color: #45fffd !important;
}
.metro .tile-area-lightTeal .tile {
  outline-color: #78fffd !important;
}
.metro .tile-area-lightTeal:before,
.metro .tile-area-lightTeal:after {
  display: table;
  content: "";
}
.metro .tile-area-lightTeal:after {
  clear: both;
}
.metro .tile-area-lightOlive {
  min-width: 100%;
  height: 100%;
  background-color: #78aa1c !important;
}
.metro .tile-area-lightOlive .tile {
  outline-color: #97d623 !important;
}
.metro .tile-area-lightOlive:before,
.metro .tile-area-lightOlive:after {
  display: table;
  content: "";
}
.metro .tile-area-lightOlive:after {
  clear: both;
}
.metro .tile-area-lightOrange {
  min-width: 100%;
  height: 100%;
  background-color: #c29008 !important;
}
.metro .tile-area-lightOrange .tile {
  outline-color: #f3b40a !important;
}
.metro .tile-area-lightOrange:before,
.metro .tile-area-lightOrange:after {
  display: table;
  content: "";
}
.metro .tile-area-lightOrange:after {
  clear: both;
}
.metro .tile-area-lightPink {
  min-width: 100%;
  height: 100%;
  background-color: #f472d0 !important;
}
.metro .tile-area-lightPink .tile {
  outline-color: #f8a1e0 !important;
}
.metro .tile-area-lightPink:before,
.metro .tile-area-lightPink:after {
  display: table;
  content: "";
}
.metro .tile-area-lightPink:after {
  clear: both;
}
.metro .tile-area-grayed {
  min-width: 100%;
  height: 100%;
  background-color: #585858 !important;
}
.metro .tile-area-grayed .tile {
  outline-color: #727272 !important;
}
.metro .tile-area-grayed:before,
.metro .tile-area-grayed:after {
  display: table;
  content: "";
}
.metro .tile-area-grayed:after {
  clear: both;
}
.metro .tile-group {
  margin-right: 46px;
  min-width: 130px;
  width: auto;
  float: left;
  display: block;
  padding-top: 40px;
  position: relative;
}
.metro .tile-group.one {
  width: 130px;
}
.metro .tile-group.two,
.metro .tile-group.double {
  width: 260px;
}
.metro .tile-group.three,
.metro .tile-group.triple {
  width: 390px;
}
.metro .tile-group.four,
.metro .tile-group.quadro {
  width: 520px;
}
.metro .tile-group.five {
  width: 650px;
}
.metro .tile-group.six {
  width: 780px;
}
.metro .tile-group.seven {
  width: 910px;
}
.metro .tile-group:first-child {
  margin-left: 80px;
}
.metro .tile-group {
  color: #ffffff;
}
.metro .tile-group .tile-group-title {
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-size: 18px;
  line-height: 20px;
  position: absolute;
  top: 10px;
  left: 0;
  color: inherit;
}
.metro .tile {
  position: relative;
  overflow: hidden;
  display: block;
  float: left;
  margin: 0 10px 10px 0;
  padding: 0;
  cursor: pointer;
  background-color: #eeeeee;
  box-shadow: inset 0px 0px 1px #2b7cb5;
}
/* Hover change */
.metro .tile:hover {
  outline: #025d9e solid 3px;
}
.metro .tile:active {
  outline: 0;
}
.metro .tile.no-outline {
  outline-color: transparent;
}

/* Changed */
.metro .tile {
  width: 140px;
  height: 90px;
  margin: 0 10px 10px 0;
  background-color:#62ade2;
  border: 1px #1d74b1;
}
.metro .tile.half {
  width: 55px;
  height: 55px;
}
.metro .tile.double {
  width: 250px;
}

/* Tiles Change */

.metro .tile.double-vertical {
	background-color:#2b7cb5;
  height: 195px;
  width: 155px;
}

.metro .tile.double-verticalContent1 {
	background-color:#62ade2;
  height: 40%;
  width: 155px;
  
}


/* Tiles New vertical */
.metro .tile.double-vertical2 {
  height: 195px;
  width: 430px;
  background-color:#2b7cb5;
   
  
}

.metro .tile.double-vertical2content1 {
background-color:#2d80bb;
  height: 78px;
  width: 430px;
 
}
.metro .tile.double-vertical2content2 {
background-color:#2d80bb;
  height: 45px;
  width: 430px;
 
  
}
.metro .tile.double-vertical2content3 {
  height: 78px;
  width: 430px;
}
/* Tiles Change vertical default n 2*/
.metro .tile.triple {
  width: 380px;
}
.metro .tile.triple-vertical {
  height: 380px;
}
.metro .tile.quadro {
  width: 510px;
}
.metro .tile.quadro-vertical {
  height: 510px;
}
.metro .tile.selected {
  border: 4px #4390df solid;
}
.metro .tile.selected:after {
  position: absolute;
  display: block;
  border-top: 28px solid #4390df;
  border-left: 28px solid transparent;
  right: 0;
  content: "";
  top: 0;
  z-index: 101;
}
.metro .tile.selected:before {
  position: absolute;
  display: block;
  content: "\e003";
  color: #fff;
  right: 0;
  font-family: 'metroSysIcons';
  font-size: 9pt;
  font-weight: normal;
  z-index: 102;
  top: 0;
}
.metro .tile .tile-content {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
}
.metro .tile .tile-content.padded {
  padding: 10px;
}
.metro .tile .tile-content.image {
  width: 100%;
  height: 100%;
  min-width: 100%;
  max-height: 100%;
  background-color: #2B7CB5;
  
}
.metro .tile .tile-content.icon [class*="icon-"],
.metro .tile .tile-content.icon img {
  line-height: 56px;
  height: 56px;
  width: 56px;
  font-size: 48px;
  color: #ffffff;
  text-align: center;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-top: -28px;
  margin-left: -28px;
}
.metro .tile .tile-content.image-set img {
  margin: 0;
  padding: 0;
  width: 25% !important;
  height: 50%;
  float: left;
  border: 1px #1e1e1e solid;
}
.metro .tile .tile-content.image-set img:first-child {
  width: 50% !important;
  float: left;
  height: 100%;
}
.metro .tile .tile-content {
  display: none;
}
.metro .tile .tile-content:first-child {
  display: block;
}
.metro .tile .brand,
.metro .tile .tile-status {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  min-height: 29px;
  margin: 0;
}
.metro .tile .brand:before,
.metro .tile .tile-status:before,
.metro .tile .brand:after,
.metro .tile .tile-status:after {
  display: table;
  content: "";
}
.metro .tile .brand:after,
.metro .tile .tile-status:after {
  clear: both;
}
.metro .tile .brand.opacity,
.metro .tile .tile-status.opacity {
  opacity: .7;
}
.metro .tile .brand .name,
.metro .tile .tile-status .name,
.metro .tile .brand .label,
.metro .tile .tile-status .label,
.metro .tile .brand .text,
.metro .tile .tile-status .text {
  display: block;
  font-size: 10pt;
  margin: 5px 20px 2px 5px;
  background: transparent;
  color: #ffffff;
}
.metro .tile .brand .text,
.metro .tile .tile-status .text {
  margin: 4px 5px;
  font-size: 8pt;
  line-height: 10pt;
}
.metro .tile .brand .badge,
.metro .tile .tile-status .badge {
  position: absolute;
  bottom: 60px;
  right: 10px;
  width: 29px;
  height: 29px;
  text-align: center;
  color: #ffffff;
  padding: 5px;
  margin: 0;
  vertical-align: middle !important;
  display: block;
  font-size: 10pt;
}
.metro .tile .brand .badge.activity,
.metro .tile .tile-status .badge.activity {
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGMSURBVDhPvZMtTwNBEIbv2mtScaICcQJRgSgJCQIEhqSiAlEHAlFRwU/ov0AgUEgUsrIkiJIgMOAQJFSQQAIJJBWIu95Hj2eGvXIpB3W8yWTn452Z3dld25pDmqZuFEWdcrm8jr6JK7Bt+wb9Ft85+vsXswBxHHdIfmFNi4TYG7InXAp6ss52kCTJIc6e6KzSVbrdYzrYDaSFXZU4uEQ8x3FW1ZpMJge5Tn3IdQ3kID5iw4zHTqIsUEP3TWCA7WhgDjRZg/eUFRCR3Fl3KYJjyfALIUU46jHcsSlQl8FdmQJnhrcQJFbJ6QZB0LDDMNyS4XBFo1Kp9Gw4/wi247GLHmvNuBaC47Y5gtzIQB1mBmMGdDSdTpfV+QdM8vfcsqkap6ClgQIQa+a4bXViPGRO5ILjuBqYAwk7yIfhXcNz9CljDFkkST6P4JGjnHA7d+gBxAY3tIve1Khljbi1beKvakHQp0uhfTrMjvOL9H3fX9FE8OM7yxAhdem4QWHZkSufSoTYaaVSkY9kYFmfXgyTciI3uacAAAAASUVORK5CYII%3D') 50% no-repeat;
}
.metro .tile .brand .badge.alert,
.metro .tile .tile-status .badge.alert {
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFeSURBVDhPpZMtT8RAEIbb7YoTJ04gkQgQuBNIEpB4LD8AwQ9AkCCQhGAvQSAuKHCIE0gEP+DEISAhQYK4pE0/eWa65a7lSvh4k8nsvDv77sxs67UhSZLNNE0LZ3uO/gLj/J+hAkVRWI1+geqMCuR5fkKZoyiKViX+DuQu094wy7KhEmEYrkAk0qt4Nk5R77GszQCuE8fxIXxY8ZJjgiBY8n3/UcTwlsQDNifGmF29AcBtITyGOyan47gXXFfW2g/q+yi+VeptJhVgR1KRHp4HZI+bzknQlhYcvpQZuHRF8xmnCDyLL8MZEI9o4YkW3h1VB+o73DJp3to08l7xsw9Lng5i1EiSSV/Pcbdwzfk8MLcNqjIyye1STnHD5joln7lYcGWtXaP8gYsFfeJyHvR9waExt3wKsV74L3Brn/geu3OUDqiL1T7nNoEK8mLi9RUoZYqlsv4pqtf459/oeR8seozS7mDHCwAAAABJRU5ErkJggg%3D%3D') 50% no-repeat;
}
.metro .tile .brand .badge.available,
.metro .tile .tile-status .badge.available {
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAIGNIUk0AAHolAACAgwAA+f8AAIDoAABSCAABFVgAADqXAAAXb9daH5AAAAKvSURBVHjahJA/bJR1HMY/31977x33r2LuClc1LYM9TSAUr5gqtkVJjAkSFxYHE3VgaWRw0cUwOagxMZLApoXFBIwuHVSoQYkVMBXUpqSkMW9jaS25plh7/3rv+3scTIwixs/8PHn+2Bk/SVtN2mqxacYOKw13KfNiXtlneihmDONXqs0VVs/VXP1UqJvnc8qBeZoWYWf9JHXVqWkj2EX55G76X86R4W40aDHNzMdzLBwJLLEWm6fTI+o0knvZ+dkgO/cDfGczTNpl5gjxePrpY0SPMKwKT1A5nCe7Y4ofDgQEv/Ghn2AqunZabUmR9Fb8gQoaUVIVFTSiokaV0qDu0T694Y+rGbWktnQ5+nHiuP+IjrFjR4cqevj9wBK8beO87t6jiyzbKJAiIEWSreQxjAm7QGyeAwzRzb39i/7WFbdV2bGs0nxvs7zjxtlOgRwZPP6v7R5PmhQPUOKEneFLd4UECfqs51WXU/opDL6wb/mdDfJkEfrXgUKk2UKbiM/5BoD76d7reujOANwgJH9H8p14PDnSzBGySZsSReecDIAIDxj/jxH/LcQtW7UJ0E8f69RwuP+0Ohwb1CnTS0CCW6zK3Wb9a4AnNcgWktRoYHdpYhgtWvypfRSARVv5yVXd2smGWuzTHo7qeRZZpk7zH00cRos2ITd5yT/HQY0gPKGW3u0YPvZ06HB77tO2hx5jN5HFTNk11lgHRIs2VW5Tp8kRf5g3eYUUSa5y/eKsfn7NTvlPaCjqelwDF3bx4ADAeXeJc1xijpCYmDJ9jKrCIe0H4IaF81/56VGDJTvtPwV1IFmhTO/4AOWDSQIAWmwiRIokADEx08xeXGD5hUjxQp0GnQCdOAKS1RnNP7tO7VDOMmO9bB8qUQRghVVCW7raUOPEvH45W7IidRoA/DEAmmk0pL+n6f4AAAAASUVORK5CYII%3D') 50% no-repeat;
}
.metro .tile .brand .badge.unavailable,
.metro .tile .tile-status .badge.unavailable {
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAIGNIUk0AAHolAACAgwAA+f8AAIDoAABSCAABFVgAADqXAAAXb9daH5AAAAKASURBVHjalJK9axxXFMV/772ZzOysVqvRDgtaSSwpJYFwY3ATEpIm5KNLawgp3Ljz/5E2bu20CYQUBoMNNnaRMkUKqYiQtIgdCQ0TaVc7M29n3nspzC7GMYYcuMWFe7jnHI4YjUY453DOYYyh0+l8opT63vO8L8MwbAshqKqq0lo/c849rqrquXMOIcSbGY1GWGsxxny0urr6MI7jH5RSAFhrAZBSLvc8z3+dTqf3lFL/SCnxAIwxwdra2tP19fXPAC4vL8myjKIoAIiiiF6vR7/fJ0mS75RSH19dXX0hpbwWx8fHrKys/JwkyV1rLYeHh5yenuKc420lzjm2trbY3d3F8zzyPH8ynU6/ERcXF3fiOP7D930ODg44OjoiDMOl7AWstZRlyXA4ZH9/H2MM4/H4K+l53n3f98myjJOTE4Ig+A95kUMURZydnXF+fo5SiiiKHkjf9z9f+AaWst+HRfKL2yiKbssgCNrOOWaz2Xs/vwulFLPZjLquCcPwDcM5x//B2/dyPp9XC3/WWoQQHyQbY2i32/i+T1VVTtZ1/QogSZJlGz/02VpLkiQAlGX5l2ya5mHTNPT7fba3tynLctnAd8llWTIYDNjY2MBaS1EUP0qt9YvJZPI7wM7ODsPhEK01WmuapqFpGrTWVFXFYDBgb28PIQTX19ev67r+TYzHY7TW3W63+zKO41sAaZqSZRk3NzcAtNtter0em5ubAEwmk7/zPP9USjkWaZoyn89xziWdTudRt9v9etGFuq4B8H1/aSXP89dFUdx1zp065xBpmlLXNUIIjDG0Wq1vPc+7H4bhnVarhRCCsiwpiuJPY8xPRVH8EgQBxhistfw7ABpxTL93U9x/AAAAAElFTkSuQmCC') 50% no-repeat;
}
.metro .tile .brand .badge.away,
.metro .tile .tile-status .badge.away {
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAIGNIUk0AAHolAACAgwAA+f8AAIDoAABSCAABFVgAADqXAAAXb9daH5AAAAJ2SURBVHjajJI7iFVnFIW//d9zz52ZO2fG14gzJBgbp5JYKPh+NqKxsxWMRZoBCxu1sAuBKFaClj5KDUQhRXybCIqICjqNYjFDhtExN45e7/uc8y+L/yJGp3A1+2fDWv/ea23zlQvIp0gpRgfrWbZRNrhP0cAOopEyGGSvWmQz15zq59SeuC5LsAis0MJ85SLKG8jXY3pXnKb8/X6iBAB8KLhuzZtQf/gbrWc/WTGetSgnAg9qlCiv/pNk1RYAqz3A6jeg/SyoxMtReRNKNsLAhj24gWW0726H+B3+9Rmyd3fPp5KyXMpf/SqNL5KelEIdH5Ke9Ejj8+SnjyrLWkolZbX7f/jZk5h/e3WN7197j0I/NnMMN3MYoiXgBv6/g+rQmUJDR/Ajv4BP0eylnU5u/pgK/Vj9Ee6/411y8gm5a4b1Qfwt9uYUrnoTXBGLvzvoFCXbDLDaVcjfd38WX0JBRCnUroRW/M1qRzRSxgPt55+NPRc8FJJgbtaB4rBz+phRxtcj//hylr5s4YDScvDVT0KfCw7yGpRGIYohfS2H3v4NoL6tYL3BbWwOsoHawY3y1tDJpp46p8pp5U2UrEcLD0BnCtT4bBIXyJ0J/Pwf0eAu8ELtiROO5uQtazy9LMAvPoKGDoU00n/CSr4K2RTkFfyCMRj+OWg2Ht9RNv27+X/PId8cVN+62/SvWAngqtehdq17yjmURlHfZjRvdxi98fyFr/21GWfT5ivnkQehRfSOnqV35S4KpW4w7ZB/1NNNMYf6wzukk3ulbBI1iIJkBBZX1Bn/gby621wyRrx0DcXhQGzPYOnEY/nmKbVeXLTicNcn+DAArZ4503S5ZjkAAAAASUVORK5CYII%3D') 50% no-repeat;
}
.metro .tile .brand .badge.busy,
.metro .tile .tile-status .badge.busy {
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAIGNIUk0AAHolAACAgwAA+f8AAIDoAABSCAABFVgAADqXAAAXb9daH5AAAAKNSURBVHjajJI9a1RBGIWfGeLdmPXuKkR0Q0RTmFsFUxgQNcaPRonpbAW1sAlY2IhFfoCIVSDaaVImFhYWmkTxAwJBVgttNqTYQFAjAWPi7t3svXeOxeC3hQdeZpiZ887DmTFuchIlCUoSTLOJ6erqV7F4QYXCaTo68hgDHz82WFmZsbXauKrVWYUhBjCNBsZNTaF6HdVqAT09tzlw4BJhyD8Vx1Au36dSuWyC4LPJMlpwDur1HH19jzh48DiAefUK8+QJVCrgHHR3o2PHUH8/HD16jkKhi7m5UwTBF9zdu6RzcxOJpFRSduOG1N4u5XJ+3LlTam2Vtm+XGxlR2mgokZTOzz90o6PgpqcPpRsbP83GSKWSFEXS/v2+okjq7JRA7vp1pZLSZlPJ5OQZqx07hrVtG+b1a+zNm7B7N4ShR/8u56CtDfbswYyNYZ8+hS1bMPv2XbUKw5MGMNPTsLEBhQJIfwco+SZJAo8f+7XOzj5LR0cegIUFb/715j/lnKerVKDZhFLJWlnrN9OU/1aW/Zha8+FDA4Dublhfh+8N/yVr4etXiCIIAvj0SZa1tRcAOnECtm6FWg2M+dtsDGxu8uMsYJaX31q7unpbcYyOHEFXrsDyMtTrv5NY683VKu7iRTQ4CBKqVm/h7twhnZ9/kEhK41ju2jWpWJTa2qRdu3zl81I+r2x4WNnamv8H5fKLZHQU48bHURwXdfjwM3p6egHs7CzMzPi0swyiCA0MoKEhj76wsOiePx/AmPfGTUwgQFI7UXSP3t5BcjmPvrnp37+19Wf65fJLlpbOK02XqNdpAaClBYJgVe/enWV9fciE4TB79x6iVPLGlRVMtfpGcTymxcUpUyr5nIBvAwDWIWcndiwtQAAAAABJRU5ErkJggg%3D%3D') 50% no-repeat;
}
.metro .tile .brand .badge.newMessage,
.metro .tile .tile-status .badge.newMessage {
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAC/SURBVDhP1ZE9DgIhFIQhobDYg1haWniMbSw9j0exsfMAeg9L7Sy2kPATnCFI2LgYtjJOMjx4vPkoED+X5OK934cQ+thpFOYvSqmdMMascVDOuQMcGn1GptNaL4W1dgBkMwOSw8jeBJszIKMwexFAN0A+wnQG0Lh4wv0EJIb5AO4fRX8MoDFAlZAyPJSztOSSfiYLAYeyxTcdURcIrqSUJ7iLA4UmAdQbgnqvhakqgEoQXQtTXwEtIuCa9n8pIV67VJf6AmhGmgAAAABJRU5ErkJggg%3D%3D') 50% no-repeat;
}
.metro .tile .brand .badge.paused,
.metro .tile .tile-status .badge.paused {
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAArSURBVDhPY/j9+7fDnz9//mPBCQxQgE8NE1QN2WDUgFEDQGDUgIE3gIEBAArtNKc4HT7sAAAAAElFTkSuQmCC') 50% no-repeat;
}
.metro .tile .brand .badge.playing,
.metro .tile .tile-status .badge.playing {
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEXSURBVDhPY4CBnz9/pvz+/dsFyiUaMEFpBiYmJhkgtf3v37/t////Z4GIEgZwA0CAkZGRBai5AmjIYSCtABXGC1AMQAIWf/78OQ/EEVA+ToDLAJBrBIDUcqBrZgNdwwMRxQQ4DYABoOYUoCGngYFsABVCAQQNgAINYCAf//XrVwGUDwfEGgDyEgfQkH5guGwGukoEKky8AUhA5sePH6DwAQOSDAC6YgIzM7MpJyfnHagQcQYAnfwGiD2BmguBhvyBCoMBMQbsYWFh0WVlZd0B5aMAnAYAbfzz79+/SqBmV6CtL6DCGACXAQ+ABliysbF1QPk4AYYBQI0rgH7VBWo+AxXCC+AGADV+AVKJQL9GAp0MYhMBGBgA8v5j1f90TA8AAAAASUVORK5CYII%3D') 50% no-repeat;
}
.metro .tile .brand .badge.error,
.metro .tile .tile-status .badge.error {
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFiSURBVDhPjVM7TsNQELRjy8ISBQeIREtBEYnQUXCINFTkCCBxgNwAJI5AaejSpaCAEqRINBTcIQ1SbD9/mHmfZP3iSIw0ytt9O7O7thMGHpqmGVZVNQnD8AwcMde27RL8rOt6nqbpjy7sA4RTpdQKv20fcbcuy/IOZrGVbIHLpz7RHr52TJCYukuMeU+6WDBjdxej4UyLubMbm0KdBDyTzHWEyY01UEVRnA4Q8IEdaZVAFEW3yD/g+IzzFc6VuTFAHAPXO7vLKQi5q+suuOD+X15yx4ToEXON1QB3B6ZkC3Qd+q8Kaxzbo0TMCTLPefPAfPS8nTeOtnk1YEfMsf11pIm+y/P8BLusmaCZrevsLE1QO3F51FzopJyCQil2pAnFoLLxI7X6z8SxkVjgeMn4H/jGQz3Ht/BrY2MC85nrsI/sjNpDKzMTSODzHPELQ9EY1H9ndFqCHxC/JEnyrgs1guAPTvwreuY0IiIAAAAASUVORK5CYII%3D') 50% no-repeat;
}
.metro .tile .brand .badge.attention,
.metro .tile .tile-status .badge.attention {
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEbSURBVDhPtZI9bsJAEIVZ7ANQ5gApEomChjoNBUUOkSJFivSUQE3JEThCCo4BkotcIVKKNEi2vP7hveVZrMFgKPJJo915szOzf51/Jc/zhbV2Jfc+kiR5QrLNsqzEMJJ8O0hcM1kWlWUZKtQOOo69ZGdpmn4ofB12QsI3k1BoRtP8F7Gell0GnT6rrpJ4HOfzUiU1ww7o9HepAGI2juNHyeegw7Ja3FRA9iW5jv9slSl0WqD2rEYjF7Hy68E7gCPNORpjpk44sg2CYAg969JTxVoywYIXmlyfAS77jRPDZ8PZN5j3KfiEYeh2yG07wQN5P4g/d9H9Hf5ZMkHM/QO5NbCzh6IoJgbVI/iNBdrALnY8An9X+w9rpLPbA/sADga+JgSiAAAAAElFTkSuQmCC') 50% no-repeat;
}
.metro .tile.parts > ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.metro .tile.parts > ul > li {
  display: block;
  float: left;
  height: 60px;
  width: 60px;
  margin: 0;
}
.metro .tile.parts > .part {
  display: block;
  float: left;
  height: 60px;
  width: 60px;
  margin: 0;
}
.metro .tile.half .tile-content.icon [class*="icon-"],
.metro .tile.half .tile-content.icon img {
  line-height: 37.33333333px;
  height: 37.33333333px;
  width: 37.33333333px;
  font-size: 32px;
  color: #ffffff;
  text-align: center;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-top: -18.66666667px;
  margin-left: -18.66666667px;
}
.metro .tile {
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
}
.metro .tile * {
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
}
.metro .tile.scale {
  -webkit-transform: scale(0.98);
  -moz-transform: scale(0.98);
  -ms-transform: scale(0.98);
  -o-transform: scale(0.98);
  transform: scale(0.98);
}
.metro [draggable] {
  cursor: default;
}
.metro .tile .tile-content.email {
  color: #ffffff;
  padding: 10px;
}
.metro .tile .tile-content.email .email-image {
  width: 64px;
  height: 64px;
  float: left;
}
.metro .tile .tile-content.email .email-image img {
  width: 100%;
  height: 100%;
}
.metro .tile .tile-content.email .email-data {
  display: block;
  color: inherit;
  margin-left: 70px;
  width: auto;
  overflow: hidden;
}
.metro .tile .tile-content.email .email-data .email-data-title,
.metro .tile .tile-content.email .email-data .email-data-subtitle,
.metro .tile .tile-content.email .email-data .email-data-text {
  color: inherit;
  display: block;
}
.metro .tile .tile-content.email .email-data .email-data-title {
  font-size: 14pt;
}
.metro .tile .tile-content.email .email-data .email-data-subtitle {
  font-size: 11pt;
}
.metro .tile .tile-content.email .email-data .email-data-text {
  font-size: 10pt;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.metro [class*=tile-transform-] {
  -webkit-transition: all 0.22s;
  transition: all 0.22s;
}
.metro .tile-transform-right {
  -webkit-transform-origin: left 50%;
  -moz-transform-origin: left 50%;
  -ms-transform-origin: left 50%;
  -o-transform-origin: left 50%;
  transform-origin: left 50%;
  -webkit-transform: perspective(500px) rotateY(0.138372rad);
  -moz-transform: perspective(500px) rotateY(0.138372rad);
  -ms-transform: perspective(500px) rotateY(0.138372rad);
  -o-transform: perspective(500px) rotateY(0.138372rad);
  transform: perspective(500px) rotateY(0.138372rad);
}
.metro .tile.double.tile-transform-right {
  -webkit-transform: perspective(500px) rotateY(0.069186rad);
  -moz-transform: perspective(500px) rotateY(0.069186rad);
  -ms-transform: perspective(500px) rotateY(0.069186rad);
  -o-transform: perspective(500px) rotateY(0.069186rad);
  transform: perspective(500px) rotateY(0.069186rad);
}
.metro .tile.triple.tile-transform-right {
  -webkit-transform: perspective(500px) rotateY(0.046124rad);
  -moz-transform: perspective(500px) rotateY(0.046124rad);
  -ms-transform: perspective(500px) rotateY(0.046124rad);
  -o-transform: perspective(500px) rotateY(0.046124rad);
  transform: perspective(500px) rotateY(0.046124rad);
}
.metro .tile.quadro.tile-transform-right {
  -webkit-transform: perspective(500px) rotateY(0.034593rad);
  -moz-transform: perspective(500px) rotateY(0.034593rad);
  -ms-transform: perspective(500px) rotateY(0.034593rad);
  -o-transform: perspective(500px) rotateY(0.034593rad);
  transform: perspective(500px) rotateY(0.034593rad);
}
.metro .tile.half.tile-transform-right {
  -webkit-transform: perspective(500px) rotateY(0.276744rad);
  -moz-transform: perspective(500px) rotateY(0.276744rad);
  -ms-transform: perspective(500px) rotateY(0.276744rad);
  -o-transform: perspective(500px) rotateY(0.276744rad);
  transform: perspective(500px) rotateY(0.276744rad);
}
.metro .tile-transform-left {
  -webkit-transform-origin: right 50%;
  -moz-transform-origin: right 50%;
  -ms-transform-origin: right 50%;
  -o-transform-origin: right 50%;
  transform-origin: right 50%;
  -webkit-transform: perspective(500px) rotateY(-0.138372rad);
  -moz-transform: perspective(500px) rotateY(-0.138372rad);
  -ms-transform: perspective(500px) rotateY(-0.138372rad);
  -o-transform: perspective(500px) rotateY(-0.138372rad);
  transform: perspective(500px) rotateY(-0.138372rad);
}
.metro .tile.double.tile-transform-left {
  -webkit-transform: perspective(500px) rotateY(-0.069186rad);
  -moz-transform: perspective(500px) rotateY(-0.069186rad);
  -ms-transform: perspective(500px) rotateY(-0.069186rad);
  -o-transform: perspective(500px) rotateY(-0.069186rad);
  transform: perspective(500px) rotateY(-0.069186rad);
}
.metro .tile.triple.tile-transform-left {
  -webkit-transform: perspective(500px) rotateY(-0.046124rad);
  -moz-transform: perspective(500px) rotateY(-0.046124rad);
  -ms-transform: perspective(500px) rotateY(-0.046124rad);
  -o-transform: perspective(500px) rotateY(-0.046124rad);
  transform: perspective(500px) rotateY(-0.046124rad);
}
.metro .tile.quadro.tile-transform-left {
  -webkit-transform: perspective(500px) rotateY(-0.034593rad);
  -moz-transform: perspective(500px) rotateY(-0.034593rad);
  -ms-transform: perspective(500px) rotateY(-0.034593rad);
  -o-transform: perspective(500px) rotateY(-0.034593rad);
  transform: perspective(500px) rotateY(-0.034593rad);
}
.metro .tile.half.tile-transform-left {
  -webkit-transform: perspective(500px) rotateY(-0.276744rad);
  -moz-transform: perspective(500px) rotateY(-0.276744rad);
  -ms-transform: perspective(500px) rotateY(-0.276744rad);
  -o-transform: perspective(500px) rotateY(-0.276744rad);
  transform: perspective(500px) rotateY(-0.276744rad);
}
.metro .tile-transform-top {
  -webkit-transform-origin: 50% bottom;
  -moz-transform-origin: 50% bottom;
  -ms-transform-origin: 50% bottom;
  -o-transform-origin: 50% bottom;
  transform-origin: 50% bottom;
  -webkit-transform: perspective(500px) rotateX(0.138372rad);
  -moz-transform: perspective(500px) rotateX(0.138372rad);
  -ms-transform: perspective(500px) rotateX(0.138372rad);
  -o-transform: perspective(500px) rotateX(0.138372rad);
  transform: perspective(500px) rotateX(0.138372rad);
}
.metro .tile.double.tile-transform-top {
  -webkit-transform: perspective(500px) rotateX(0.069186rad);
  -moz-transform: perspective(500px) rotateX(0.069186rad);
  -ms-transform: perspective(500px) rotateX(0.069186rad);
  -o-transform: perspective(500px) rotateX(0.069186rad);
  transform: perspective(500px) rotateX(0.069186rad);
}
.metro .tile.triple.tile-transform-top {
  -webkit-transform: perspective(500px) rotateX(0.046124rad);
  -moz-transform: perspective(500px) rotateX(0.046124rad);
  -ms-transform: perspective(500px) rotateX(0.046124rad);
  -o-transform: perspective(500px) rotateX(0.046124rad);
  transform: perspective(500px) rotateX(0.046124rad);
}
.metro .tile.quadro.tile-transform-top {
  -webkit-transform: perspective(500px) rotateX(0.034593rad);
  -moz-transform: perspective(500px) rotateX(0.034593rad);
  -ms-transform: perspective(500px) rotateX(0.034593rad);
  -o-transform: perspective(500px) rotateX(0.034593rad);
  transform: perspective(500px) rotateX(0.034593rad);
}
.metro .tile.half.tile-transform-top {
  -webkit-transform: perspective(500px) rotateX(0.276744rad);
  -moz-transform: perspective(500px) rotateX(0.276744rad);
  -ms-transform: perspective(500px) rotateX(0.276744rad);
  -o-transform: perspective(500px) rotateX(0.276744rad);
  transform: perspective(500px) rotateX(0.276744rad);
}
.metro .tile-transform-bottom {
  -webkit-transform-origin: 50% top;
  -moz-transform-origin: 50% top;
  -ms-transform-origin: 50% top;
  -o-transform-origin: 50% top;
  transform-origin: 50% top;
  -webkit-transform: perspective(500px) rotateX(-0.138372rad);
  -moz-transform: perspective(500px) rotateX(-0.138372rad);
  -ms-transform: perspective(500px) rotateX(-0.138372rad);
  -o-transform: perspective(500px) rotateX(-0.138372rad);
  transform: perspective(500px) rotateX(-0.138372rad);
}
.metro .tile.double.tile-transform-bottom {
  -webkit-transform: perspective(500px) rotateX(-0.069186rad);
  -moz-transform: perspective(500px) rotateX(-0.069186rad);
  -ms-transform: perspective(500px) rotateX(-0.069186rad);
  -o-transform: perspective(500px) rotateX(-0.069186rad);
  transform: perspective(500px) rotateX(-0.069186rad);
}
.metro .tile.triple.tile-transform-bottom {
  -webkit-transform: perspective(500px) rotateX(-0.046124rad);
  -moz-transform: perspective(500px) rotateX(-0.046124rad);
  -ms-transform: perspective(500px) rotateX(-0.046124rad);
  -o-transform: perspective(500px) rotateX(-0.046124rad);
  transform: perspective(500px) rotateX(-0.046124rad);
}
.metro .tile.quadro.tile-transform-bottom {
  -webkit-transform: perspective(500px) rotateX(-0.034593rad);
  -moz-transform: perspective(500px) rotateX(-0.034593rad);
  -ms-transform: perspective(500px) rotateX(-0.034593rad);
  -o-transform: perspective(500px) rotateX(-0.034593rad);
  transform: perspective(500px) rotateX(-0.034593rad);
}
.metro .tile.half.tile-transform-bottom {
  -webkit-transform: perspective(500px) rotateX(-0.276744rad);
  -moz-transform: perspective(500px) rotateX(-0.276744rad);
  -ms-transform: perspective(500px) rotateX(-0.276744rad);
  -o-transform: perspective(500px) rotateX(-0.276744rad);
  transform: perspective(500px) rotateX(-0.276744rad);
}
.metro .carousel {
  display: block;
  position: relative;
  overflow: hidden;
  background-color: #eeeeee;
}
.metro .carousel .slide {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: none;
  padding: 0;
}
.metro .carousel .slide div.subslide {
  z-index: 2;
  position: absolute;
  top: 10px;
  left: 10px;
}
.metro .carousel .slide img.cover {
  width: 100%;
  min-height: 100%;
  z-index: 1;
  position: absolute;
  top: 0 !important;
  left: 0 !important;
}
.metro .carousel .slide:first-child {
  display: block;
}
.metro .carousel .controls {
  display: block;
  position: absolute;
  top: 50%;
  left: 15px;
  min-width: 40px;
  min-height: 40px;
  width: auto;
  height: auto;
  margin-top: -20px;
  font-size: 48pt;
  font-weight: 100;
  line-height: 30px;
  color: #fff;
  text-align: center;
  cursor: pointer;
  opacity: .75;
  z-index: 100;
}
.metro .carousel .controls.right {
  left: auto;
  right: 15px;
}
.metro .carousel .controls:hover {
  opacity: 1;
}
.metro .carousel .markers {
  z-index: 100;
  margin: 0;
  padding: 0;
  display: block;
  position: absolute;
  bottom: 10px;
  left: 10px;
}
.metro .carousel .markers > ul {
  padding-left: 0;
  list-style: none;
}
.metro .carousel .markers > ul li ul,
.metro .carousel .markers > ul li ol {
  list-style: none;
  padding-left: 25px;
}
.metro .carousel .markers li {
  display: block;
  float: left;
  margin-right: 5px;
}
.metro .carousel .markers li a {
  background-color: #ffffff;
  display: block;
  float: left;
  border: 1px #ffffff solid;
}
.metro .carousel .markers li.active a {
  background-color: #f0a30a;
}
.metro .carousel .markers.default > ul > li > a {
  width: 32px;
  height: 6px;
}
.metro .carousel .markers.cycle > ul > li > a {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}
.metro .carousel .markers.square > ul > li > a {
  width: 24px;
  height: 24px;
}
.metro .rating {
  color: #2784a7;
  cursor: pointer;
  display: block;
  width: auto;
  margin: 0 10px 10px 0;
  height: 28px;
}
.metro .rating:before,
.metro .rating:after {
  display: table;
  content: "";
}
.metro .rating:after {
  clear: both;
}
.metro .rating ul {
  list-style: none;
  color: inherit;
  display: block;
  margin: 0;
  padding: 0;
  margin-bottom: 5px;
}
.metro .rating ul:before,
.metro .rating ul:after {
  display: table;
  content: "";
}
.metro .rating ul:after {
  clear: both;
}
.metro .rating ul li {
  display: block;
  float: left;
  position: relative;
  font-family: metroSysIcons;
  font-size: 28px;
  line-height: 28px;
  color: inherit;
}
.metro .rating ul li:before {
  content: '\e014';
}
.metro .rating ul li.rated:before {
  content: '\e016';
}
.metro .rating ul li.rated.half:before {
  content: '\e015';
}
.metro .rating ul li.half:before {
  content: '\e015';
}
.metro .rating .score-hint {
  display: block;
  font-size: 12px;
  color: #1d1d1d;
}
.metro .rating.active ul:hover > li:before {
  content: '\e016';
}
.metro .rating.active ul li:hover ~ li:before {
  content: '\e014' !important;
}
.metro .rating.active ul li:hover:before {
  content: '\e016' !important;
}
.metro .rating.small li {
  font-size: 18.66666667px;
  line-height: 18.66666667px;
}
.metro .rating.small .score-hint {
  margin-top: -5px;
  font-size: 12px;
}
.metro .rating.large li {
  font-size: 42px;
}
.metro .rating.large .score-hint {
  margin-top: 10px;
  font-size: 16px;
}
.metro .pagination {
  width: auto;
  margin-bottom: 10px;
}
.metro .pagination > ul {
  padding-left: 0;
  list-style: none;
  margin: 0;
}
.metro .pagination > ul li ul,
.metro .pagination > ul li ol {
  list-style: none;
  padding-left: 25px;
}
.metro .pagination > ul:before,
.metro .pagination > ul:after {
  display: table;
  content: "";
}
.metro .pagination > ul:after {
  clear: both;
}
.metro .pagination > ul li {
  display: block;
  float: left;
  margin-right: 1px;
  position: relative;
}
.metro .pagination > ul li a {
  padding: 4px 12px;
  text-align: center;
  vertical-align: middle !important;
  background-color: #d9d9d9;
  border: 1px transparent solid;
  color: #222222;
  border-radius: 0;
  cursor: pointer;
  display: inline-block;
  outline: none;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-size: 14px;
  line-height: 16px;
  margin: auto;
  color: inherit;
  display: block;
  float: left;
  margin-right: 1px;
}
.metro .pagination > ul li a.default {
  background-color: #008287;
  color: #fff;
}
.metro .pagination > ul li a:focus {
  outline: 0;
  border: 1px #353535 dotted;
}
.metro .pagination > ul li a:disabled,
.metro .pagination > ul li a.disabled {
  background-color: #eaeaea !important;
  color: #bebebe !important;
  cursor: not-allowed !important;
}
.metro .pagination > ul li a * {
  color: inherit;
}
.metro .pagination > ul li a *:hover {
  color: inherit;
}
.metro .pagination > ul li a.primary {
  background-color: #16499a !important;
  color: #ffffff !important;
}
.metro .pagination > ul li a.info {
  background-color: #4390df !important;
  color: #ffffff !important;
}
.metro .pagination > ul li a.success {
  background-color: #60a917 !important;
  color: #ffffff !important;
}
.metro .pagination > ul li a.warning {
  background-color: #fa6800 !important;
  color: #ffffff !important;
}
.metro .pagination > ul li a.danger {
  background-color: #9a1616 !important;
  color: #ffffff !important;
}
.metro .pagination > ul li a.inverse {
  background-color: #1d1d1d !important;
  color: #ffffff !important;
}
.metro .pagination > ul li a.link {
  background-color: transparent !important;
  color: #2e92cf !important;
}
.metro .pagination > ul li a.link:hover {
  text-decoration: underline;
}
.metro .pagination > ul li a.link:disabled,
.metro .pagination > ul li a.link.disabled {
  background-color: transparent !important;
  color: #bebebe !important;
  cursor: not-allowed !important;
}
.metro .pagination > ul li a.link [class*="icon-"] {
  text-decoration: none !important;
}
.metro .pagination > ul li a:active {
  background-color: #1d1d1d !important;
  color: #ffffff !important;
  border-color: transparent;
}
.metro .pagination > ul li a.large {
  padding: 11px 19px;
  font-size: 17.5px;
}
.metro .pagination > ul li a.small {
  padding: 2px 10px;
  font-size: 11.9px;
}
.metro .pagination > ul li a.mini {
  padding: 0px 6px;
  font-size: 10.5px;
}
.metro .pagination > ul li.active a {
  background-color: #4390df;
  color: #ffffff;
}
.metro .pagination > ul li.disabled a,
.metro .pagination > ul li.spaces a {
  background-color: #f2f2f2;
  color: #1e1e1e;
  cursor: not-allowed;
}
.metro .pagination > ul li.disabled a:active,
.metro .pagination > ul li.spaces a:active {
  top: 0;
  left: 0;
}
.metro .pagination > ul li.disabled a {
  color: #1e1e1e;
}
.metro .pagination > ul li.spaces a {
  background-color: #ffffff;
  cursor: default;
}
.metro .pagination.small ul li a {
  padding: 2px 10px;
  font-size: 11.9px;
}
.metro .pagination.mini ul li a {
  padding: 0px 6px;
  font-size: 10.5px;
}
.metro .countdown,
.metro .times {
  width: auto;
  margin: 0 auto;
  font: 20px/1.5 'Open Sans Condensed', sans-serif;
  text-align: center;
  letter-spacing: -3px;
}
.metro .countdown .digit-wrapper,
.metro .times .digit-wrapper {
  display: inline-block;
  height: 1.6em;
  overflow: hidden;
  position: relative;
  width: 1.05em;
}
.metro .countdown .digit-wrapper .digit,
.metro .times .digit-wrapper .digit {
  position: absolute;
  display: block;
  width: 1em;
  text-align: center;
  vertical-align: middle;
  letter-spacing: -1px;
}
.metro .countdown .digit-wrapper .digit.stop,
.metro .times .digit-wrapper .digit.stop {
  background-color: #e51400;
}
.metro .countdown .divider,
.metro .times .divider {
  display: inline-block;
  width: 16px;
  height: 1.5em;
  position: relative;
  font-weight: bold;
}
.metro .countdown .divider:before,
.metro .times .divider:before {
  width: 100%;
  height: 1.5em;
  position: absolute;
  content: ":";
  color: inherit;
  font-size: inherit;
  vertical-align: middle;
  text-align: center;
  left: -20%;
  top: -15%;
}
.metro .countdown.inverse .digit,
.metro .times.inverse .digit {
  color: #333333 !important;
  background-color: #eeeeee !important;
}
.metro .tab-control .tabs {
  padding-left: 0;
  list-style: none;
  margin: 0  !important;
  z-index: 2;
}
.metro .tab-control .tabs li ul,
.metro .tab-control .tabs li ol {
  list-style: none;
  padding-left: 25px;
}
.metro .tab-control .tabs:before,
.metro .tab-control .tabs:after {
  display: table;
  content: "";
}
.metro .tab-control .tabs:after {
  clear: both;
}
.metro .tab-control .tabs > li {
  background-color: #ffffff;
  display: block;
  float: left;
  margin-left: 5px;
  position: relative;
}
.metro .tab-control .tabs > li > a {
  border: 1px #eeeeee solid;
  display: block;
  float: left;
  padding: 5px 10px;
  z-index: 10;
  top: 0;
  left: 0;
  color: inherit;
  background-color: #eeeeee;
}
.metro .tab-control .tabs > li.active a {
  border-bottom-color: #ffffff;
  background-color: #ffffff;
  border-top: 2px #e51400 solid;
}
.metro .tab-control .tabs > li.active:hover a {
  background-color: #ffffff;
  color: #1d1d1d;
}
.metro .tab-control .tabs > li:hover a {
  background-color: #d5d5d5;
  color: #ffffff;
}
.metro .tab-control .tabs > li.place-right {
  float: right;
  margin-right: 5px;
  margin-left: auto;
}
.metro .tab-control .tabs.no-spaces > li {
  margin-left: 0;
}
.metro .tab-control .frames {
  z-index: 1;
  border: 1px #eeeeee solid;
  margin-top: -1px;
}
.metro .tab-control .frames .frame {
  display: none;
}
.metro .tab-control .frames .frame:first-child {
  display: block;
}
.metro .tab-control .frame {
  padding: 20px;
}
.metro .progress-bar {
  display: block;
  position: relative;
  height: 10px;
  width: 100%;
  margin-bottom: 10px;
  background-color: #eeeeee;
}
.metro .progress-bar:before,
.metro .progress-bar:after {
  display: table;
  content: "";
}
.metro .progress-bar:after {
  clear: both;
}
.metro .progress-bar .bar {
  position: relative;
  display: block;
  float: left;
  width: 0;
  background-color: #1ba1e2;
  height: 100%;
}
.metro .progress-bar.small {
  height: 5px;
}
.metro .progress-bar.large {
  height: 15px;
}
.metro .sidebar {
  margin: 0;
  padding: 0;
  background-color: #3D3D3D;
  width: 100%;
  height: 100%;
}
.metro .sidebar > ul {
  margin: 0;
  padding: 0;
  list-style: none;
}
.metro .sidebar > ul li {
  display: block;
  border: 0;
  border-bottom: 1px #5c5c5c solid;
  position: relative;
}
.metro .sidebar > ul li.title {
  padding: 20px 20px 10px 20px;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-weight: 300;
  letter-spacing: 0.00em;
  font-size: 2.5rem;
  line-height: 2.5rem;
  letter-spacing: 0.01em;
  color: #000000;
  font-size: 2.2rem;
  line-height: 2.2rem;
  color: #ffffff;
}
.metro .sidebar > ul li a {
  display: block;
  position: relative;
  padding: 10px 20px;
  text-decoration: none;
  color: #eeeeee;
  background-color: #3D3D3D;
}
.metro .sidebar > ul li a:hover {
  background-color: #262626;
  color: #ffffff;
}
.metro .sidebar > ul li a.dropdown-toggle:after {
  right: 10px;
  bottom: 5px;
}
.metro .sidebar > ul li a [class*="icon-"] {
  margin-right: 15px;
}
.metro .sidebar > ul li.stick {
  position: relative;
}
.metro .sidebar > ul li.stick:before {
  content: "";
  position: absolute;
  width: 7px;
  height: 40px;
  left: -7px;
  text-indent: -9999px;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  background-color: inherit;
}
.metro .sidebar > ul li.disabled a {
  background-color: #8a8a8a;
  cursor: default;
  color: #555555;
}
.metro .sidebar > ul li.active a {
  background-color: #9a1616;
  color: #ffffff;
}
.metro .sidebar > ul ul {
  margin: 0;
  padding: 0;
  list-style: none;
  position: absolute;
  display: none;
  z-index: 1000;
  background: #ffffff;
  min-width: 220px;
  width: auto;
  border: 1px solid #dcddde;
  box-shadow: none;
  position: relative !important;
  background-color: #e5e5e5;
  min-width: 100%;
}
.metro .sidebar > ul ul li {
  display: block;
  margin: 10px 0;
  padding: 0;
  vertical-align: baseline;
  position: relative;
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
}
.metro .sidebar > ul ul li a {
  display: block;
  text-decoration: none;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-weight: 300;
  letter-spacing: 0.00em;
  font-size: 2.5rem;
  line-height: 2.5rem;
  letter-spacing: 0.01em;
  color: #000000;
  line-height: 2.2rem;
  font-size: 2.2rem;
  color: inherit;
}
.metro .sidebar > ul ul li.title {
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-weight: 300;
  color: #000000;
  letter-spacing: 0.00em;
  font-size: 2.5rem;
  line-height: 2.5rem;
  letter-spacing: 0.01em;
  font-size: 4rem;
  margin-bottom: 20px;
}
.metro .sidebar > ul ul.compact a {
  font-weight: normal;
  font-size: 10.5pt;
  line-height: 12pt;
  color: inherit;
}
.metro .sidebar > ul ul.compact li.title {
  margin-bottom: 10px;
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  color: #000000;
  font-size: 1.6rem;
  line-height: 1.6rem;
  letter-spacing: 0.02em;
}
.metro .sidebar > ul ul ul.secondary {
  margin: 0;
  padding: 0;
  list-style: none;
  margin-left: 15px;
}
.metro .sidebar > ul ul ul.secondary li a {
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  color: #000000;
  font-size: 1.6rem;
  line-height: 1.6rem;
  letter-spacing: 0.02em;
  font-weight: normal;
  font-style: normal;
  line-height: 1.4rem;
  color: inherit;
}
.metro .sidebar > ul ul.shadow {
  box-shadow: 3px 3px 0 rgba(236, 236, 236, 0.7);
}
.metro .sidebar > ul ul li {
  margin: 5px 0 0;
  width: 100%;
  border: 1px transparent solid;
}
.metro .sidebar > ul ul li:last-child {
  margin-bottom: 5px;
}
.metro .sidebar > ul ul li.disabled,
.metro .sidebar > ul ul li.disabled:hover {
  cursor: default !important;
  background: transparent !important;
  color: #eeeeee;
}
.metro .sidebar > ul ul li.disabled a,
.metro .sidebar > ul ul li.disabled:hover a {
  color: inherit;
  cursor: inherit;
  background: inherit;
}
.metro .sidebar > ul ul a {
  padding: 8px 32px !important;
  color: inherit;
  font-size: 15px !important;
  line-height: 14px !important;
  cursor: pointer;
}
.metro .sidebar > ul ul li:hover {
  background: #edf4fc;
  border-color: #a8d2fd;
}
.metro .sidebar > ul ul li.checked a:before,
.metro .sidebar > ul ul li.checked-partial a:before,
.metro .sidebar > ul ul li.unchecked a:before {
  font-family: metroSysIcons;
  position: absolute;
  left: 10px;
  content: "\e004";
}
.metro .sidebar > ul ul li.checked-partial a:before {
  content: "\e005";
}
.metro .sidebar > ul ul li.unchecked a:before {
  content: "\e005";
}
.metro .sidebar > ul ul.place-right {
  right: -15px;
  left: auto;
}
.metro .sidebar > ul ul .divider {
  padding: 0;
  height: 1px;
  margin: 9px 1px;
  overflow: hidden;
  background-color: #e5e5e5;
  border-width: 0;
}
.metro .sidebar > ul ul .divider:hover {
  background: #e5e5e5;
}
.metro .sidebar > ul ul.open {
  display: block !important;
}
.metro .sidebar > ul ul .dropdown-menu {
  left: 100%;
  top: -10%;
}
.metro .sidebar > ul ul > li:hover > .dropdown-menu[data-show="hover"] {
  display: block;
}
.metro .sidebar > ul ul > li > a.dropdown-toggle:after {
  font-family: metroSysIcons;
  font-size: .7em;
  content: "\e001";
  left: 90%;
  top: 50%;
  margin-top: -1.4em;
  -webkit-transform: rotate(-90deg);
  -moz-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  -o-transform: rotate(-90deg);
  transform: rotate(-90deg);
}
.metro .sidebar > ul ul.drop-left {
  left: -100%;
}
.metro .sidebar > ul ul.drop-up {
  top: auto;
  bottom: 0;
}
.metro .sidebar > ul ul .menu-title {
  background-color: #f6f7f8;
  font-size: 12px;
  line-height: 14px;
  padding: 4px 8px;
  border: 0;
  border-bottom: 1px #dcddde solid;
  border-top: 1px #dcddde solid;
  color: #646464;
}
.metro .sidebar > ul ul .menu-title:first-child {
  margin: 0;
  border-top-width: 0;
}
.metro .sidebar > ul ul .menu-title:first-child:hover {
  border-top-width: 0;
}
.metro .sidebar > ul ul .menu-title:hover {
  background-color: #f6f7f8;
  cursor: default;
  border: 0;
  border-bottom: 1px #dcddde solid;
  border-top: 1px #dcddde solid;
}
.metro .sidebar > ul ul.inverse {
  border: 2px #3b3b3b solid;
}
.metro .sidebar > ul ul.inverse li {
  border: 0;
}
.metro .sidebar > ul ul.inverse li a {
  padding: 9px 32px !important;
}
.metro .sidebar > ul ul.inverse li:hover {
  border: 0;
  background-color: #e5e5e5;
}
.metro .sidebar > ul ul.inverse .menu-title:hover {
  background-color: #f6f7f8;
}
.metro .sidebar > ul ul.inverse .divider {
  margin-left: 0;
  margin-right: 0;
}
.metro .sidebar > ul ul.dark {
  background-color: #333333;
  border: 2px #333333 solid;
  color: #ffffff;
}
.metro .sidebar > ul ul.dark.opacity {
  background-color: rgba(61, 61, 61, 0.9);
}
.metro .sidebar > ul ul.dark li {
  border: 0;
  color: inherit;
}
.metro .sidebar > ul ul.dark li a {
  padding: 9px 32px !important;
  color: inherit;
}
.metro .sidebar > ul ul.dark li.disabled,
.metro .sidebar > ul ul.dark li.disabled:hover {
  cursor: default !important;
  background: transparent !important;
  color: #555555;
}
.metro .sidebar > ul ul.dark li.disabled a,
.metro .sidebar > ul ul.dark li.disabled:hover a {
  color: inherit;
  cursor: inherit;
  background: inherit;
}
.metro .sidebar > ul ul.dark li:hover {
  border: 0;
  background-color: #201a1a;
  color: #ffffff;
}
.metro .sidebar > ul ul.dark .menu-title {
  background-color: #3b3b3b;
}
.metro .sidebar > ul ul.dark .menu-title:hover {
  background-color: #3b3b3b;
}
.metro .sidebar > ul ul.dark .divider {
  margin-left: 0;
  margin-right: 0;
  background-color: #555555;
}
.metro .sidebar > ul ul.dark .divider:hover {
  background: #555555;
}
.metro .sidebar > ul ul li {
  border: 0;
}
.metro .sidebar > ul ul li a {
  background-color: #e5e5e5;
}
.metro .sidebar > ul ul li.divider {
  background-color: #c2c2c2;
}
.metro .sidebar > ul ul li.divider:hover {
  background-color: #c2c2c2;
}
.metro .sidebar > ul ul li.disabled a {
  color: #999999;
}
.metro .sidebar > ul ul li.disabled a:hover {
  color: #999999;
}
.metro .sidebar > ul ul.open {
  display: block;
}
/* Sidebar colors edited */
.metro .sidebar.light {
  background-color: #ffffff;
 
}
.metro .sidebar.light li {
  border-bottom-color: #62ADE2;
}
.metro .sidebar.light li.title {
  color: #1d1d1d;
}
.metro .sidebar.light li a {
  color: #ffffff;
  background-color: #4295d0;
}
.metro .sidebar.light li a:hover {
  background-color: #2d80bb;
}
.metro .sidebar.light li ul {
  background-color: #ffffff;
  border: 1px transparent solid;
}
.metro .sidebar.light li .divider {
  background-color: #eeeeee;
}
.metro .sidebar.light li .divider:hover {
  background-color: #eeeeee;
}
.metro .sidebar.light li.disabled a {
  background-color: #ffffff;
  cursor: default;
  color: #999999;
}
.metro .sidebar.light li.active a {
  background-color: #2d80bb;
  color: #ffffff;
}
.metro .accordion > .accordion-frame {
  border: 1px #eaeaea solid;
  margin-bottom: 2px;
}
.metro .accordion > .accordion-frame > .heading {
  display: block;
  text-decoration: none;
  background-color: #eeeeee;
  padding: 10px 20px;
  width: 100%;
  position: relative;
  color: #1d1d1d;
}
.metro .accordion > .accordion-frame > .heading [class*="icon-"] {
  margin-right: 10px;
}
.metro .accordion > .accordion-frame > .content {
  padding: 10px;
}
.metro .accordion > .accordion-frame > .content {
  display: none;
}
.metro .accordion.with-marker .heading:before {
  position: absolute;
  display: block;
  left: 5px;
  top: 12px;
  content: '';
  width: 0;
  height: 0;
  border-left: 7px solid transparent;
  border-top: 7px solid transparent;
  border-bottom: 7px solid black;
}
.metro .accordion.with-marker .heading.collapsed:before {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
  margin-left: -4px;
}
.metro .calendar {
  width: 250px;
  border: 1px #eeeeee solid;
  font-size: 10pt;
}
.metro .calendar table {
  width: 100%;
  border-collapse: collapse;
}
.metro .calendar td,
.metro .calendar th {
  padding: 0;
  width: 14.28%;
}
.metro .calendar td {
  border: 1px  solid;
  border-color: #e6f5fc;
}
.metro .calendar td:first-child {
  border-left: 0;
}
.metro .calendar td:last-child {
  border-right: 0;
}
.metro .calendar tr:last-child td {
  border-bottom: 0;
}
.metro .calendar .day-of-week {
  padding: 5px;
  cursor: default;
}
.metro .calendar a {
  display: block;
  padding: 5px;
}
.metro .calendar a:hover {
  background-color: #eeeeee;
  color: #1d1d1d;
}
.metro .calendar .calendar-header {
  background-color: #1ba1e2;
  color: #ffffff;
}
.metro .calendar .calendar-header td,
.metro .calendar .calendar-header th {
  border: 0 !important;
}
.metro .calendar .calendar-header td a,
.metro .calendar .calendar-header th a {
  color: #ffffff;
}
.metro .calendar .calendar-header td a:hover,
.metro .calendar .calendar-header th a:hover {
  background-color: #1ba1e2;
  color: #ffffff;
}
.metro .calendar .calendar-actions td,
.metro .calendar .calendar-actions th {
  padding: 10px;
}
.metro .calendar .today a {
  background-color: #a0e857;
  color: #1d1d1d;
}
.metro .calendar .today a:hover {
  background-color: #128023;
  color: #ffffff;
}
.metro .calendar .day a {
  display: block;
  position: relative;
  border: 2px transparent solid;
}
.metro .calendar .day a.selected {
  border: 4px #4390df solid;
  border-width: 1px;
}
.metro .calendar .day a.selected:after {
  position: absolute;
  display: block;
  border-top: 28px solid #4390df;
  border-left: 28px solid transparent;
  right: 0;
  content: "";
  top: 0;
  z-index: 101;
}
.metro .calendar .day a.selected:before {
  position: absolute;
  display: block;
  content: "\e003";
  color: #fff;
  right: 0;
  font-family: 'metroSysIcons';
  font-size: 9pt;
  font-weight: normal;
  z-index: 102;
  top: 0;
}
.metro .calendar .day a.selected:after {
  border-top-width: 12px;
  border-left-width: 12px;
}
.metro .calendar .day a.selected:before {
  font-size: 4pt;
}
.metro .calendar .month a,
.metro .calendar .year a {
  padding-top: 20px;
  padding-bottom: 20px;
}
.metro .calendar .other-day {
  display: block;
  text-align: center;
  color: #999999;
}
.metro .calendar-dropdown {
  border: 0;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3), 0 3px 8px rgba(0, 0, 0, 0.2);
}
.metro .slider {
  height: 12px;
  width: auto;
  position: relative;
  background-color: #c6c6c6;
  margin-bottom: 10px;
}
.metro .slider .marker {
  height: 12px;
  width: 12px;
  cursor: pointer;
  position: absolute;
  top: 0;
  left: 0;
  background-color: #000000;
  z-index: 2;
}
.metro .slider .marker:focus,
.metro .slider .marker:active,
.metro .slider .markerhover {
  outline: 2px #e51400 solid;
}
.metro .slider .complete {
  height: 100%;
  width: auto;
  background-color: #00828b;
  z-index: 1;
  transition: background .3s ease;
}
.metro .slider > .hint {
  background-color: #ffffff;
  position: absolute;
  z-index: 3;
  border: 1px #ccc solid;
  padding: 2px 4px;
  top: -40px;
  min-width: 30px;
  text-align: center;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-size: 14px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3), 0 3px 8px rgba(0, 0, 0, 0.2);
  display: none;
}
.metro .slider.permanent-hint > .hint {
  display: block;
}
.metro .slider.hint-bottom .hint {
  top: 100%;
  margin-top: 5px;
}
.metro .slider.vertical {
  height: 100px;
  width: 12px;
}
.metro .slider.vertical .marker {
  left: 0 !important;
}
.metro .slider.vertical .complete {
  position: absolute;
  height: auto;
  width: 100% !important;
  bottom: 0;
  left: 0;
}
.metro .slider.vertical .hint {
  left: 100%;
  margin-left: 5px;
  margin-top: 0;
}
.metro .slider.vertical.hint-left .hint {
  left: -40px;
}
.metro .slider:hover .complete {
  background-color: #219297;
}
.metro .slider:active .complete,
.metro .slider:active + .marker:active .complete {
  background-color: #219297;
}
.metro .slider.place-left {
  margin-right: 20px;
}
.metro .slider.place-right {
  margin-left: 20px;
}
.metro.window-overlay {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0);
  z-index: 1049;
}
.metro .window {
  border: 8px #6badf6 solid;
  position: relative;
  background-color: #ffffff;
  min-height: 200px;
  min-width: 200px;
  border-top: 0;
}
.metro .window .caption {
  position: absolute;
  top: 0;
  height: 32px;
  width: 100%;
  background-color: #6badf6;
  cursor: default;
  z-index: 2;
  border-top: 8px #6badf6 solid;
}
.metro .window .caption .icon {
  width: 16px;
  height: 16px;
  font-size: 16px;
  margin-top: 0;
  margin-left: 0px;
  margin-right: 5px;
  position: relative;
  float: left;
}
.metro .window .caption .title {
  font-size: 14px;
  margin-top: -2px;
  margin-left: 4px;
  position: relative;
  float: left;
}
.metro .window .caption .super-title {
  position: absolute;
  top: 6px;
  left: 0;
  display: block;
  width: 100%;
  text-align: center;
  z-index: 0;
}
.metro .window .content {
  position: relative;
  padding: 32px 0;
  width: auto;
  height: auto;
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  -ms-box-sizing: content-box;
  -o-box-sizing: content-box;
  box-sizing: content-box;
}
.metro .window .caption .btn-close,
.metro .window .caption .btn-min,
.metro .window .caption .btn-max {
  background-color: #c75050;
  color: #ffffff;
  position: absolute;
  top: -8px;
  right: 0;
  width: 48px;
  height: 24px;
  min-height: 24px;
  text-align: center;
  vertical-align: middle;
  font-family: 'Cambria_', 'PT Serif Caption', Verdana, Arial, Helvetica, sans-serif;
  font-size: 16pt;
  font-weight: normal;
  padding: 0 0 10px 0;
  z-index: 3;
  outline: none;
}
.metro .window .caption .btn-close:hover,
.metro .window .caption .btn-min:hover,
.metro .window .caption .btn-max:hover {
  background-color: #e04343;
}
.metro .window .caption .btn-close:active,
.metro .window .caption .btn-min:active,
.metro .window .caption .btn-max:active {
  background-color: #b01919 !important;
  top: -8px;
  right: 0;
  left: auto;
}
.metro .window .caption .btn-close:after,
.metro .window .caption .btn-min:after,
.metro .window .caption .btn-max:after {
  content: '\D7';
  position: absolute;
  left: 50%;
  top: 0;
  margin-left: -0.3em;
}
.metro .window .caption .btn-min,
.metro .window .caption .btn-max {
  background-color: transparent;
  color: #000;
  right: 75px;
  width: 24px;
  padding-bottom: 10px;
  font-weight: bold;
}
.metro .window .caption .btn-min:hover,
.metro .window .caption .btn-max:hover {
  background-color: #1b6eae;
  color: #ffffff;
}
.metro .window .caption .btn-min:hover:after,
.metro .window .caption .btn-max:hover:after {
  border-color: #fff;
}
.metro .window .caption .btn-min:active,
.metro .window .caption .btn-max:active {
  background-color: #1e7cc4 !important;
  color: #ffffff;
  top: -8px;
  right: 75px;
  left: auto;
}
.metro .window .caption .btn-min:after,
.metro .window .caption .btn-max:after {
  display: block;
  position: absolute;
  width: 10px;
  height: 10px;
  border: 0px #000 solid;
  border-bottom-width: 2px;
  content: ' ';
  bottom: 6px;
  left: 50%;
  margin-left: -6px;
  top: auto;
}
.metro .window .caption .btn-max {
  right: 50px;
}
.metro .window .caption .btn-max:active {
  right: 50px;
}
.metro .window .caption .btn-max:after {
  height: 6px;
  border: 1px #000 solid;
  border-top-width: 2px;
}
.metro .window.inactive {
  border-color: #ebebeb;
}
.metro .window.inactive .caption {
  background-color: #ebebeb;
  border-top: 8px #ebebeb solid;
}
.metro .window.inactive .content {
  border-color: #ebebeb;
}
.metro .window.inactive .btn-close {
  background-color: #bcbcbc;
}
.metro .window.flat {
  background-color: #ffffff;
  border: 1px #e9e9e9 solid;
}
.metro .window.flat .caption {
  height: 32px;
  background-color: #ffffff;
  padding: 7px 5px;
  border-bottom: 1px #e9e9e9 solid;
  border-top: 0;
}
.metro .window.flat .caption .btn-close,
.metro .window.flat .caption .btn-min,
.metro .window.flat .caption .btn-max {
  background-color: #ffffff;
  color: #777777;
  width: 24px;
  top: 4px;
  right: 4px;
}
.metro .window.flat .caption .btn-close:hover,
.metro .window.flat .caption .btn-min:hover,
.metro .window.flat .caption .btn-max:hover {
  background-color: #cde6f7;
  color: #2a8dd4;
}
.metro .window.flat .caption .btn-close:hover:after,
.metro .window.flat .caption .btn-min:hover:after,
.metro .window.flat .caption .btn-max:hover:after {
  border-color: #2a8dd4;
}
.metro .window.flat .caption .btn-close:active,
.metro .window.flat .caption .btn-min:active,
.metro .window.flat .caption .btn-max:active {
  background-color: #92c0e0 !important;
  color: #ffffff !important;
}
.metro .window.flat .caption .btn-close:after,
.metro .window.flat .caption .btn-min:after,
.metro .window.flat .caption .btn-max:after {
  border-color: #777777;
  width: 10px !important;
  height: 6px !important;
}
.metro .window.flat .caption .btn-max {
  right: 29px;
}
.metro .window.flat .caption .btn-min {
  right: 54px;
}
.metro .window.flat .caption .btn-close:after {
  margin-top: 2px;
}
.metro .window.flat .content {
  padding-top: 38px;
}
.metro .listview .list {
  border: 4px transparent solid;
  width: 300px;
  height: auto;
  position: relative;
  display: block;
  cursor: pointer;
  color: inherit;
  margin: 0 0 10px 0;
}
.metro .listview .list .list-content {
  padding: 10px;
  position: relative;
  display: block;
  color: inherit;
}
.metro .listview .list .list-content:before,
.metro .listview .list .list-content:after {
  display: table;
  content: "";
}
.metro .listview .list .list-content:after {
  clear: both;
}
.metro .listview .list:hover {
  outline: 3px #ccc solid;
}
.metro .listview .list:active {
  outline: 3px #3e3e3e solid;
}
.metro .listview .list {
  border: 1px #eaeaea solid;
}
.metro .listview .list .list-content .icon {
  display: block;
  width: 56px;
  height: 56px;
  font-size: 40px;
  float: left;
  margin-right: 10px;
}
.metro .listview .list .list-content [class*="icon-"].icon {
  padding: 8px;
}
.metro .listview .list .list-content .data {
  display: block;
  padding: 0;
  margin: 0;
  margin-left: 66px;
  min-width: 200px;
  position: relative;
}
.metro .listview .list .list-content .list-title,
.metro .listview .list .list-content .list-remark {
  margin: 0;
  padding: 0;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.metro .listview .list .list-content .list-title {
  font-size: 14px;
  line-height: 16px;
}
.metro .listview .list .list-content .list-remark {
  font-size: 11px;
  line-height: 12px;
}
.metro .listview .list .list-content [class*="span"] {
  float: none;
}
.metro .listview .list .list-content .rating {
  color: inherit;
}
.metro .listview .list .list-content .progress-bar {
  margin: 5px 0;
}
.metro .listview.small .list-content .icon {
  display: block;
  width: 32px;
  height: 32px;
  line-height: 14px;
  font-size: 28px;
  float: left;
  margin-right: 10px;
}
.metro .listview.small .list-content [class*="icon-"].icon {
  padding: 8px 0;
}
.metro .listview.small .list-content [class*="icon-"].border {
  font-size: 22px;
  padding: 8px 4px;
}
.metro .listview.small .list-content .data {
  margin-left: 40px;
}
.metro .listview .list.selected {
  border: 4px #4390df solid;
}
.metro .listview .list.selected:after {
  position: absolute;
  display: block;
  border-top: 28px solid #4390df;
  border-left: 28px solid transparent;
  right: 0;
  content: "";
  top: 0;
  z-index: 101;
}
.metro .listview .list.selected:before {
  position: absolute;
  display: block;
  content: "\e003";
  color: #fff;
  right: 0;
  font-family: 'metroSysIcons';
  font-size: 9pt;
  font-weight: normal;
  z-index: 102;
  top: 0;
}
.metro .listview-outlook {
  position: relative;
  display: block;
}
.metro .listview-outlook .list {
  border: 4px transparent solid;
  width: 300px;
  height: auto;
  position: relative;
  display: block;
  cursor: pointer;
  color: inherit;
  margin: 0 0 10px 0;
}
.metro .listview-outlook .list .list-content {
  padding: 10px;
  position: relative;
  display: block;
  color: inherit;
}
.metro .listview-outlook .list .list-content:before,
.metro .listview-outlook .list .list-content:after {
  display: table;
  content: "";
}
.metro .listview-outlook .list .list-content:after {
  clear: both;
}
.metro .listview-outlook .list:hover {
  outline: 3px #ccc solid;
}
.metro .listview-outlook .list:active {
  outline: 3px #3e3e3e solid;
}
.metro .listview-outlook .list {
  border: 1px #eaeaea solid;
}
.metro .listview-outlook .list .list-content .icon {
  display: block;
  width: 56px;
  height: 56px;
  font-size: 40px;
  float: left;
  margin-right: 10px;
}
.metro .listview-outlook .list .list-content [class*="icon-"].icon {
  padding: 8px;
}
.metro .listview-outlook .list .list-content .data {
  display: block;
  padding: 0;
  margin: 0;
  margin-left: 66px;
  min-width: 200px;
  position: relative;
}
.metro .listview-outlook .list .list-content .list-title,
.metro .listview-outlook .list .list-content .list-remark {
  margin: 0;
  padding: 0;
  display: block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.metro .listview-outlook .list .list-content .list-title {
  font-size: 14px;
  line-height: 16px;
}
.metro .listview-outlook .list .list-content .list-remark {
  font-size: 11px;
  line-height: 12px;
}
.metro .listview-outlook .list .list-content [class*="span"] {
  float: none;
}
.metro .listview-outlook .list .list-content .rating {
  color: inherit;
}
.metro .listview-outlook .list .list-content .progress-bar {
  margin: 5px 0;
}
.metro .listview-outlook.small .list-content .icon {
  display: block;
  width: 32px;
  height: 32px;
  line-height: 14px;
  font-size: 28px;
  float: left;
  margin-right: 10px;
}
.metro .listview-outlook.small .list-content [class*="icon-"].icon {
  padding: 8px 0;
}
.metro .listview-outlook.small .list-content [class*="icon-"].border {
  font-size: 22px;
  padding: 8px 4px;
}
.metro .listview-outlook.small .list-content .data {
  margin-left: 40px;
}
.metro .listview-outlook .list.selected {
  border: 4px #4390df solid;
}
.metro .listview-outlook .list.selected:after {
  position: absolute;
  display: block;
  border-top: 28px solid #4390df;
  border-left: 28px solid transparent;
  right: 0;
  content: "";
  top: 0;
  z-index: 101;
}
.metro .listview-outlook .list.selected:before {
  position: absolute;
  display: block;
  content: "\e003";
  color: #fff;
  right: 0;
  font-family: 'metroSysIcons';
  font-size: 9pt;
  font-weight: normal;
  z-index: 102;
  top: 0;
}
.metro .listview-outlook .list {
  display: block;
  width: 100%;
  border: 0;
  border-bottom: 1px #e1e1e1 solid;
  padding: 2px 0;
  color: #555555;
  margin-bottom: 0;
}
.metro .listview-outlook .list .list-content {
  margin: 2px 0;
  padding: 2px 20px;
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  font-size: 16px;
  color: inherit;
  border-left: 3px transparent solid;
}
.metro .listview-outlook .list .list-content .list-title,
.metro .listview-outlook .list .list-content .list-subtitle,
.metro .listview-outlook .list .list-content .list-remark {
  width: 100%;
  display: block;
  color: inherit;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.metro .listview-outlook .list .list-content .list-title {
  font-size: 16px;
  line-height: 20px;
}
.metro .listview-outlook .list .list-content .list-subtitle,
.metro .listview-outlook .list .list-content .list-remark {
  font-size: 11px;
  font-weight: normal;
  line-height: 15px;
  color: #1d1d1d;
}
.metro .listview-outlook .list .list-content .list-subtitle {
  font-weight: bold;
  color: #0067cb;
}
.metro .listview-outlook .list.marked .list-content {
  border-left: 3px #0067b0 solid !important;
}
.metro .listview-outlook .list:active,
.metro .listview-outlook .list:focus,
.metro .listview-outlook .list.active {
  background-color: #cde6f7 !important;
  outline: 1px #999999 dotted;
  color: #555555 !important;
}
.metro .listview-outlook .list:hover {
  background-color: #e6f2fa;
  outline: none;
}
.metro .listview-outlook .list:hover .list-content {
  border-left: 3px #b1d6f0 solid;
}
.metro .listview-outlook .list-group {
  display: block;
  position: relative;
}
.metro .listview-outlook .list-group .group-title {
  display: block;
  margin-bottom: 2px;
  background-color: #f0f0f0;
  padding: 4px 20px;
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  font-size: 11px;
  font-weight: bold;
  color: #333333;
  padding-left: 24px;
}
.metro .listview-outlook .list-group .group-title:before {
  position: absolute;
  display: block;
  left: 10px;
  top: 1px;
  content: '';
  width: 0;
  height: 0;
  border-left: 7px solid transparent;
  border-top: 7px solid transparent;
  border-bottom: 7px solid black;
}
.metro .listview-outlook .list-group .group-content {
  display: block;
}
.metro .listview-outlook .list-group.collapsed .group-title:before {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
  margin-left: -4px;
}
.metro .listview-outlook .list-group .group-title:hover:before {
  border-bottom-color: #0067cb;
}
.metro .listview-outlook .list-content .list-icon {
  float: left;
  width: 48px;
  max-height: 48px;
  margin-right: 10px;
  margin-top: 5px;
}
.metro .treeview {
  background-color: #fdfdfd;
  color: #555555;
  display: block;
  list-style: none;
  margin: 0;
  padding: 0;
}
.metro .treeview ul {
  color: #555555;
  display: block;
  list-style: none;
  margin: 0;
  padding: 0;
}
.metro .treeview ul li a {
  padding: 0 0 0 15px;
}
.metro .treeview a {
  font-size: 12px;
  color: inherit;
  display: block;
}
.metro .treeview .node > a {
  margin-bottom: 5px;
  padding: 0 0 0 15px;
  font-size: 12px;
  position: relative;
  color: #000;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.metro .treeview .node:not(:first-child) {
  margin-top: 0;
}
.metro .treeview .node-toggle {
  display: inline-block;
  position: relative;
  float: left;
  margin-right: 3px;
  width: 10px;
  height: 10px;
}
.metro .treeview .node-toggle:before {
  position: absolute;
  display: block;
  left: 2px;
  top: 0;
  content: '';
  width: 0;
  height: 0;
  border-left: 7px solid transparent;
  border-top: 7px solid transparent;
  border-bottom: 7px #1d1d1d solid;
}
.metro .treeview .node-toggle:hover:before {
  border-bottom-color: #0050ef;
}
.metro .treeview li.collapsed .node-toggle:before {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
  margin-left: -4px;
}
.metro .treeview li a:hover {
  background-color: #e6f2fa;
}
.metro .treeview .active {
  background-color: #e1e1e1;
  font-weight: bold;
  color: #1d1d1d;
}
.metro .treeview a .value {
  color: #0067cb;
}
.metro .fluent-menu {
  font-size: 14px;
}
.metro .fluent-menu .tabs-holder {
  list-style: none;
  position: relative;
  margin: 0;
  padding: 0;
  display: block;
  z-index: 2;
}
.metro .fluent-menu .tabs-holder:before,
.metro .fluent-menu .tabs-holder:after {
  display: table;
  content: "";
}
.metro .fluent-menu .tabs-holder:after {
  clear: both;
}
.metro .fluent-menu .tabs-holder li {
  display: block;
  float: left;
  margin-right: 5px;
  background-color: #ffffff;
}
.metro .fluent-menu .tabs-holder li a {
  display: block;
  float: left;
  padding: 4px 15px;
  text-transform: uppercase;
  font-size: .8em;
  color: #444444;
}
.metro .fluent-menu .tabs-holder li a:hover {
  color: #0072c6;
}
.metro .fluent-menu .tabs-holder li:first-child {
  margin-left: 0px;
}
.metro .fluent-menu .tabs-holder li.active {
  border: 1px #d4d4d4 solid;
  border-bottom-color: #ffffff;
}
.metro .fluent-menu .tabs-holder li.active a {
  color: #0072c6;
}
.metro .fluent-menu .tabs-holder li.special {
  border: 1px #0072c6 solid;
  background-color: #0072c6;
}
.metro .fluent-menu .tabs-holder li.special a {
  color: #ffffff;
}
.metro .fluent-menu .tabs-holder li.special a:hover {
  color: #ffffff;
}
.metro .fluent-menu .tabs-content {
  z-index: 1;
  position: relative;
  margin-top: -1px;
  border: 1px #d4d4d4 solid;
  background-color: #ffffff;
  height: 120px;
}
.metro .fluent-menu .tabs-content .tab-panel {
  display: block;
  height: 100%;
  padding: 5px 0 2px;
}
.metro .fluent-menu .tabs-content .tab-panel .tab-panel-group {
  height: 100%;
  position: relative;
  display: block;
  float: left;
  padding: 0 5px;
  border-right: 1px #d4d4d4 solid;
}
.metro .fluent-menu .tabs-content .tab-panel .tab-panel-group:last-child {
  margin-right: 0;
}
.metro .fluent-menu .tabs-content .tab-panel .tab-group-caption {
  font-size: 10px;
  margin: 2px 0;
  text-align: center;
  display: block;
  position: absolute;
  bottom: 0px;
  right: 0;
  left: 0;
  white-space: nowrap;
}
.metro .fluent-menu .tabs-content .tab-panel .tab-content-segment {
  display: block;
  float: left;
  position: relative;
}
.metro .fluent-menu .fluent-button {
  background-color: #ffffff;
  padding: 5px 5px;
  font-size: 14px;
  line-height: 14px;
  display: block;
  cursor: default;
}
.metro .fluent-menu .fluent-button:hover {
  background-color: #cde6f7;
}
.metro .fluent-menu .fluent-button img {
  display: block;
  float: left;
  margin-right: 5px;
  width: 16px;
  height: 16px;
}
.metro .fluent-menu .fluent-button [class*="icon-"] {
  color: #444444;
}
.metro .fluent-menu .fluent-button .button-label {
  display: inline-block;
  color: inherit;
  font: inherit;
}
.metro .fluent-menu .fluent-button:active {
  top: 0;
  left: 0;
  background-color: #75bae9 !important;
}
.metro .fluent-menu .fluent-big-button {
  background-color: #ffffff;
  padding: 5px 5px;
  font-size: 14px;
  line-height: 14px;
  display: block;
  cursor: default;
  padding: 7px 5px;
  text-align: center;
  white-space: normal;
  line-height: 12px;
  float: left;
  position: relative;
}
.metro .fluent-menu .fluent-big-button:hover {
  background-color: #cde6f7;
}
.metro .fluent-menu .fluent-big-button img {
  display: block;
  float: left;
  margin-right: 5px;
  width: 16px;
  height: 16px;
}
.metro .fluent-menu .fluent-big-button [class*="icon-"] {
  color: #444444;
}
.metro .fluent-menu .fluent-big-button .button-label {
  display: inline-block;
  color: inherit;
  font: inherit;
}
.metro .fluent-menu .fluent-big-button:active {
  top: 0;
  left: 0;
  background-color: #75bae9 !important;
}
.metro .fluent-menu .fluent-big-button img,
.metro .fluent-menu .fluent-big-button [class*="icon-"] {
  display: block;
  width: 40px;
  height: 40px;
  font-size: 40px;
  float: none;
  text-align: center;
  margin: 5px auto 5px;
}
.metro .fluent-menu .fluent-big-button br {
  line-height: 4px;
  height: 4px;
  font-size: 4px;
}
.metro .fluent-menu .fluent-tool-button {
  background-color: #ffffff;
  padding: 5px 5px;
  font-size: 14px;
  line-height: 14px;
  display: block;
  cursor: default;
  padding: 4px;
}
.metro .fluent-menu .fluent-tool-button:hover {
  background-color: #cde6f7;
}
.metro .fluent-menu .fluent-tool-button img {
  display: block;
  float: left;
  margin-right: 5px;
  width: 16px;
  height: 16px;
}
.metro .fluent-menu .fluent-tool-button [class*="icon-"] {
  color: #444444;
}
.metro .fluent-menu .fluent-tool-button .button-label {
  display: inline-block;
  color: inherit;
  font: inherit;
}
.metro .fluent-menu .fluent-tool-button:active {
  top: 0;
  left: 0;
  background-color: #75bae9 !important;
}
.metro .fluent-menu .fluent-tool-button img,
.metro .fluent-menu .fluent-tool-button [class*="icon-"] {
  display: block;
  width: 16px;
  height: 16px;
  font-size: 16px;
  float: none;
  text-align: center;
}
.metro .fluent-menu .fluent-tool-button img {
  margin-right: 0;
}
.metro .fluent-menu .dropdown-toggle:before,
.metro .fluent-menu .dropdown-toggle:after {
  content: "";
}
.metro .fluent-menu .dropdown-toggle .button-label {
  padding-right: 5px;
}
.metro .fluent-menu .dropdown-toggle .button-label:before,
.metro .fluent-menu .dropdown-toggle .button-label:after {
  content: "";
}
.metro .fluent-menu .dropdown-toggle .button-label:after {
  content: "\e012";
  font-family: metroSysIcons;
  position: absolute;
  right: 0px;
  bottom: 3px;
  font-size: .8em;
}
.metro .fluent-menu .dropdown-menu {
  position: absolute;
  top: 100%;
  z-index: 100;
}
.metro .fluent-menu .dropdown-menu a {
  font-size: 10pt !important;
}
.metro .fluent-menu .dropdown-menu a:hover {
  background-color: #cde6f7;
  color: #444444;
}
.metro p [data-hint] {
  border-bottom: 1px #373737 dotted;
  white-space: nowrap;
}
.metro .hint {
  position: fixed;
  color: #1d1d1d;
  padding: 10px;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 12px;
  width: auto;
  max-width: 220px;
  margin-top: 10px;
  z-index: 1030;
  display: none;
  border: 1px solid;
  border-color: #6a6a6a;
}
.metro .hint .hint-title,
.metro .hint .hint-text {
  color: inherit;
  text-align: left;
}
.metro .hint .hint-title {
  font-size: 1.2em;
  font-weight: bold;
}
.metro .hint:before,
.metro .hint:after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
}
.metro .hint:before {
  z-index: 2;
}
.metro .hint:after {
  z-index: 1;
  border-color: inherit;
}
.metro .hint.bottom:before,
.metro .hint.bottom:after {
  top: 1px;
  left: 5px;
  margin: -10px 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 10px solid #fffcc0;
}
.metro .hint.bottom:after {
  top: 0;
  border-bottom: 10px solid;
}
.metro .hint.top:before,
.metro .hint.top:after {
  top: 100%;
  margin-top: -1px;
  left: 5px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 10px solid #fffcc0;
}
.metro .hint.top:after {
  margin-top: 1px;
  border-top: 10px solid;
}
.metro .hint.left:before,
.metro .hint.left:after {
  top: 5px;
  left: 100%;
  margin-left: -1px;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 10px solid #fffcc0;
}
.metro .hint.left:after {
  margin-left: 0;
  border-left: 10px solid;
}
.metro .hint.right:before,
.metro .hint.right:after {
  top: 5px;
  left: -9px;
  margin: 0;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-right: 10px solid #fffcc0;
}
.metro .hint.right:after {
  left: -10px;
  border-right: 10px solid;
}
.metro .hint2 {
  position: fixed;
  color: #1d1d1d;
  padding: 10px;
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 12px;
  width: auto;
  max-width: 220px;
  margin-top: 10px;
  z-index: 1030;
  display: none;
  border: 1px solid;
  border-color: #6a6a6a;
}
.metro .hint2 .hint-title,
.metro .hint2 .hint-text {
  color: inherit;
  text-align: left;
}
.metro .hint2 .hint-title {
  font-size: 1.2em;
  font-weight: bold;
}
.metro .hint2:before,
.metro .hint2:after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
}
.metro .hint2:before {
  z-index: 2;
}
.metro .hint2:after {
  z-index: 1;
  border-color: inherit;
}
.metro .hint2.bottom:before,
.metro .hint2.bottom:after {
  top: 1px;
  left: 5px;
  margin: -10px 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 10px solid #fffcc0;
}
.metro .hint2.bottom:after {
  top: 0;
  border-bottom: 10px solid;
}
.metro .hint2.top:before,
.metro .hint2.top:after {
  top: 100%;
  margin-top: -1px;
  left: 5px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 10px solid #fffcc0;
}
.metro .hint2.top:after {
  margin-top: 1px;
  border-top: 10px solid;
}
.metro .hint2.left:before,
.metro .hint2.left:after {
  top: 5px;
  left: 100%;
  margin-left: -1px;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 10px solid #fffcc0;
}
.metro .hint2.left:after {
  margin-left: 0;
  border-left: 10px solid;
}
.metro .hint2.right:before,
.metro .hint2.right:after {
  top: 5px;
  left: -9px;
  margin: 0;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-right: 10px solid #fffcc0;
}
.metro .hint2.right:after {
  left: -10px;
  border-right: 10px solid;
}
.metro .hint2.bottom:before,
.metro .hint2.bottom:after {
  top: 1px;
  left: 50%;
  margin: -10px 0 0 -5px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 10px solid #fffcc0;
}
.metro .hint2.bottom:after {
  top: 0;
  border-bottom: 10px solid;
}
.metro .hint2.top:before,
.metro .hint2.top:after {
  top: 100%;
  margin-top: -1px;
  left: 50%;
  margin-left: -5px;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 10px solid #fffcc0;
}
.metro .hint2.top:after {
  margin-top: 1px;
  border-top: 10px solid;
}
.metro .hint2.left:before,
.metro .hint2.left:after {
  top: 50%;
  margin-top: -5px;
  left: 100%;
  margin-left: -1px;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 10px solid #fffcc0;
}
.metro .hint2.left:after {
  margin-left: 0;
  border-left: 10px solid;
}
.metro .hint2.right:before,
.metro .hint2.right:after {
  top: 50%;
  margin: 0;
  margin-top: -5px;
  left: -9px;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-right: 10px solid #fffcc0;
}
.metro .hint2.right:after {
  left: -10px;
  border-right: 10px solid;
}
.metro .balloon {
  display: block;
  width: auto;
  height: auto;
  margin: 0;
  padding: 0;
  border: 1px #555555 solid;
  background-color: #ffffff;
  position: relative;
  margin-bottom: 30px;
}
.metro .balloon:before,
.metro .balloon:after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
}
.metro .balloon:before {
  z-index: 2;
}
.metro .balloon:after {
  z-index: 1;
}
.metro .balloon.bottom:before,
.metro .balloon.bottom:after {
  top: 1px;
  left: 20px;
  margin: -20px 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 20px solid #ffffff;
}
.metro .balloon.bottom:after {
  top: 0;
  border-bottom: 20px solid #555555;
}
.metro .balloon.top:before,
.metro .balloon.top:after {
  top: 100%;
  margin-top: -1px;
  left: 20px;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-top: 20px solid #ffffff;
}
.metro .balloon.top:after {
  margin-top: 1px;
  border-top: 20px solid #555555;
}
.metro .balloon.left:before,
.metro .balloon.left:after {
  top: 10px;
  left: 100%;
  margin-left: -1px;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-left: 20px solid #ffffff;
}
.metro .balloon.left:after {
  margin-left: 0;
  border-left: 21px solid #555555;
}
.metro .balloon.right:before,
.metro .balloon.right:after {
  top: 10px;
  left: -19px;
  margin: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 20px solid #ffffff;
}
.metro .balloon.right:after {
  left: -20px;
  border-right: 20px solid #555555;
}
.metro .stepper {
  margin: 10px 0;
}
.metro .stepper:before,
.metro .stepper:after {
  display: table;
  content: "";
}
.metro .stepper:after {
  clear: both;
}
.metro .stepper > ul {
  counter-reset: li;
  border-top: 1px #1d1d1d dotted;
  position: relative;
  padding: 0;
  margin: 30px 0;
  width: 100%;
}
.metro .stepper > ul li {
  list-style: none;
  float: left;
  width: 2em;
  height: 2em;
  margin-top: -1em;
  position: absolute;
  left: 0;
  background: #666;
  cursor: pointer;
}
.metro .stepper > ul li:before {
  content: counter(li);
  counter-increment: li;
  position: absolute;
  box-sizing: border-box;
  padding: .3em 10px;
  color: #fff;
  font-weight: bold;
  font-family: "Helvetica Neue", Arial, sans-serif;
  font-size: 1.5rem;
  text-align: center;
}
.metro .stepper > ul li:hover {
  background-color: #999999;
}
.metro .stepper > ul li.current,
.metro .stepper > ul li.complete {
  transition: all 0.2s ease;
}
.metro .stepper > ul li.current {
  background-color: #1ba1e2;
}
.metro .stepper > ul li.current:hover {
  background-color: #00ccff;
}
.metro .stepper > ul li.complete {
  background-color: #60a917;
}
.metro .stepper > ul li.complete:hover {
  background-color: #7ad61d;
}
.metro .stepper.rounded li {
  border-radius: 100%;
}
.metro .stepper.diamond li {
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}
.metro .stepper.diamond li:before {
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
}
.metro .streamer {
  position: relative;
  display: block;
  width: 100%;
  overflow: hidden;
}
.metro .streamer .meter {
  height: 25px;
  width: auto;
  list-style: none;
  margin: 0;
  padding: 0;
  display: block;
  overflow: hidden;
}
.metro .streamer .meter li {
  display: block;
  float: left;
  width: 213px;
  padding: 2px 3px;
  background: url("/images/meter-210.png") top left repeat-x;
}
.metro .streamer .meter li em {
  font-size: 10px;
  font-style: normal;
}
.metro .streamer .streams {
  width: 142px;
  padding-top: 25px;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 2;
  background-color: #ffffff;
}
.metro .streamer .streams .streams-title {
  position: absolute;
  top: 0;
}
.metro .streamer .streams .stream {
  position: relative;
  display: block;
  width: 100%;
  height: 75px;
  margin: 0 2px 2px 0;
  padding: 5px;
  color: #ffffff;
  cursor: pointer;
}
.metro .streamer .streams .stream .stream-title {
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-size: 12px;
  line-height: 12px;
}
.metro .streamer .streams .stream .stream-number {
  position: absolute;
  left: 5px;
  bottom: 5px;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-size: 11px;
  line-height: 11px;
}
.metro .streamer .events {
  padding-left: 143px;
  overflow: hidden;
  height: 100%;
  min-height: 100%;
  overflow-x: scroll;
}
.metro .streamer .events .double {
  width: 424px;
}
.metro .streamer .events .triple {
  width: 637px;
}
.metro .streamer .events .quadro {
  width: 850px;
}
.metro .streamer .events .events-area {
  height: 100%;
  min-height: 100%;
  overflow: hidden;
}
.metro .streamer .events .events-area:before,
.metro .streamer .events .events-area:after {
  display: table;
  content: "";
}
.metro .streamer .events .events-area:after {
  clear: both;
}
.metro .streamer .events .events-grid {
  height: 100%;
  min-height: 100%;
}
.metro .streamer .events .events-grid:before,
.metro .streamer .events .events-grid:after {
  display: table;
  content: "";
}
.metro .streamer .events .events-grid:after {
  clear: both;
}
.metro .streamer .events .event-group {
  height: 460px;
  min-width: 211px;
  margin: 0 2px 2px 0;
  float: left;
}
.metro .streamer .events .event-super {
  height: 100%;
  min-height: 100%;
  border: 1px #eaeaea solid;
}
.metro .streamer .events .event-stream {
  height: 75px;
}
.metro .streamer .events .event-stream .event {
  min-width: 211px;
  height: 75px;
  float: left;
  display: block;
  margin: 0 2px 2px 0;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  border: 1px #eaeaea solid;
}
.metro .streamer .events .event-stream .event:last-child {
  margin-right: 0;
}
.metro .streamer .events .event-stream .event.event-disable {
  opacity: .2;
}
.metro .streamer .events .event-stream .event .event-content {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
  display: none;
}
.metro .streamer .events .event-stream .event .event-content:first-child {
  display: block;
}
.metro .streamer .events .event-stream .event .event-content-logo {
  display: block;
  float: left;
  margin-right: 5px;
  padding: 3px;
}
.metro .streamer .events .event-stream .event .event-content-logo .icon {
  position: relative;
  width: 39px;
  height: 39px;
  margin-bottom: 1px;
}
.metro .streamer .events .event-stream .event .event-content-logo .icon img {
  width: 100%;
  height: 100%;
}
.metro .streamer .events .event-stream .event .event-content-logo .time {
  position: relative;
  width: 39px;
  padding: 8px 4px;
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  font-size: 12px;
  color: #ffffff;
  line-height: 12px;
}
.metro .streamer .events .event-stream .event .event-content-data {
  display: block;
  padding: 0;
  margin: 0;
  position: relative;
  margin-left: 50px;
}
.metro .streamer .events .event-stream .event .event-content-data .title {
  position: relative;
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
  font-size: 14px;
  line-height: 14px;
  margin: 3px 0 0;
  padding: 0;
}
.metro .streamer .events .event-stream .event .event-content-data .subtitle {
  position: relative;
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  font-size: 10px;
  line-height: 10px;
  margin: 0;
  padding: 0;
  margin-bottom: 10px;
}
.metro .streamer .events .event-stream .event .event-content-data .remark {
  position: absolute;
  display: block;
  top: 36px;
  margin-right: 4px;
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  font-size: 11px;
  line-height: 11px;
  color: #999999;
}
.metro .streamer .events .event-stream .event:hover {
  border-color: #999999;
}
.metro .streamer .events .event-stream .event.selected {
  border: 4px #4390df solid;
  border-width: 1px;
}
.metro .streamer .events .event-stream .event.selected:after {
  position: absolute;
  display: block;
  border-top: 28px solid #4390df;
  border-left: 28px solid transparent;
  right: 0;
  content: "";
  top: 0;
  z-index: 101;
}
.metro .streamer .events .event-stream .event.selected:before {
  position: absolute;
  display: block;
  content: "\e003";
  color: #fff;
  right: 0;
  font-family: 'metroSysIcons';
  font-size: 9pt;
  font-weight: normal;
  z-index: 102;
  top: 0;
}
.metro .streamer .events .event-stream .event.selected:before {
  right: 3px;
}
.metro .scrollbar-button {
  background-color: #ffffff;
  border: 1px #ababab solid;
}
.metro .scrollbar {
  position: relative;
  overflow: hidden;
}
.metro .scrollbar-container {
  position: relative;
  overflow: hidden;
}
.metro .scrollbar-v,
.metro .scrollbar-h {
  position: absolute;
  background: #f3f3f3;
}
.metro .scrollbar-v {
  width: 15px;
  height: 100%;
  right: 0;
  top: 0;
}
.metro .scrollbar-h {
  height: 15px;
  width: 100%;
  left: 0;
  bottom: 0;
}
.metro .scrollbar-v-up,
.metro .scrollbar-v-down,
.metro .scrollbar-h-up,
.metro .scrollbar-h-down,
.metro .scrollbar-line-v-btn,
.metro .scrollbar-line-h-btn {
  cursor: pointer;
  position: absolute;
  background-color: #ffffff;
  border: 1px #ababab solid;
}
.metro .scrollbar-line-v-btn:hover,
.metro .scrollbar-line-h-btn:hover {
  background-color: #f0f0f0;
}
.metro .scrollbar-v-up,
.metro .scrollbar-h-up {
  width: 15px;
  height: 15px;
  left: 0;
  top: 0;
}
.metro .scrollbar-v-down,
.metro .scrollbar-h-down {
  width: 15px;
  height: 15px;
  left: 0;
  bottom: 0;
}
.metro .scrollbar-h-down {
  left: auto;
  right: 0;
}
.metro .scrollbar-line-v,
.metro .scrollbar-line-h {
  position: absolute;
  z-index: 3;
}
.metro .scrollbar-line-v {
  width: 100%;
  left: 0;
  top: 15px;
}
.metro .scrollbar-line-h {
  height: 15px;
  left: 15px;
  top: 0;
}
.metro .scrollbar-line-v-btn {
  width: 100%;
  left: 0 !important;
  top: 0;
}
.metro .scrollbar-line-h-btn {
  height: 100%;
  left: 0;
  top: 0 !important;
}
.metro .scrollbar-both {
  position: absolute;
  width: 15px;
  height: 15px;
  right: 0;
  bottom: 0;
}
.metro .scrollbar-line-h,
.metro .scrollbar-line-v,
.metro .scrollbar-both {
  background-color: #f3f3f3;
}
.metro .scrollbar-v-up:before,
.metro .scrollbar-v-down:before,
.metro .scrollbar-h-up:before,
.metro .scrollbar-h-down:before {
  position: absolute;
  content: "";
  font-family: metroSysIcons;
  font-size: 14px;
}
.metro .scrollbar-v-up:before {
  content: "\e010";
}
.metro .scrollbar-v-down:before {
  content: "\e012";
}
.metro .scrollbar-h-up:before {
  content: "\e010";
  margin-top: -1px;
  -webkit-transform: rotate(-90deg);
  -moz-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  -o-transform: rotate(-90deg);
  transform: rotate(-90deg);
}
.metro .scrollbar-h-down:before {
  content: "\e010";
  margin-top: -1px;
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);
}
.metro.notify-container {
  position: fixed;
  right: 0;
  top: 0;
  z-index: 1060;
}
.metro.notify-container .notify {
  background-color: #ffffff;
  display: block;
  position: relative;
  min-width: 220px;
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  color: #1d1d1d;
  padding: 10px;
  margin: 2px 2px 2px 0;
}
.metro.notify-container .notify .caption {
  font-size: 18px;
  line-height: 20px;
  margin-bottom: 5px;
}
.metro.notify-container .notify .content {
  font-size: 14px;
  line-height: 16px;
}
.metro .notify-wrapper {
  position: relative;
  background-color: #1d1d1d;
  width: auto;
  height: auto;
  min-width: 250px;
  color: #ffffff;
}
.metro .notice {
  display: block;
  min-width: 200px;
  height: auto;
  position: relative;
  background-color: #4390df;
  padding: 20px;
}
.metro .notice * {
  color: inherit;
}
.metro .notice:before {
  content: "";
  width: 10px;
  height: 10px;
  display: block;
  position: absolute;
  background-color: inherit;
  left: -5px;
  top: 50%;
  margin-top: -5px;
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
}
.metro .notice.marker-on-top:before {
  top: 0;
  left: 50%;
  margin-left: -5px;
}
.metro .notice.marker-on-right:before {
  top: 50%;
  margin-top: -5px;
  left: 100%;
  margin-left: -5px;
}
.metro .notice.marker-on-bottom:before {
  top: 100%;
  margin-left: -5px;
  left: 50%;
  margin-top: -5px;
}
.metro .dataTable {
  clear: both;
}
.metro .dataTable thead .sorting_asc,
.metro .dataTable thead .sorting_desc,
.metro .dataTable thead .sorting {
  cursor: pointer;
}
.metro .dataTable thead .sorting_asc:after,
.metro .dataTable thead .sorting_desc:after,
.metro .dataTable thead .sorting:after {
  font-family: metroSysIcons;
  position: relative;
  float: right;
  right: 0;
  color: #999999;
  padding-right:20px; /* added padding for table headers 12-2-15*/
}
.metro .dataTable thead .sorting:after {
  content: "\e011";
}
.metro .dataTable thead .sorting_asc:after {
  content: "\e010";
}
.metro .dataTable thead .sorting_desc:after {
  content: "\e012";
}
.metro .dataTable thead .sorting_asc:after,
.metro .dataTable thead .sorting_desc:after {
  color: #1d1d1d;
}
.metro .dataTables_wrapper .dataTables_filter label,
.metro .dataTables_wrapper .dataTables_length label {
  vertical-align: middle;
}
.metro .dataTables_wrapper .dataTables_filter input,
.metro .dataTables_wrapper .dataTables_length input,
.metro .dataTables_wrapper .dataTables_filter select,
.metro .dataTables_wrapper .dataTables_length select {
  border: 1px #d9d9d9 solid;
  width: auto;
  height: 100%;
  padding: 5px;
  z-index: 1;
  position: relative;
}
.metro .dataTables_wrapper .dataTables_filter input:focus,
.metro .dataTables_wrapper .dataTables_length input:focus,
.metro .dataTables_wrapper .dataTables_filter select:focus,
.metro .dataTables_wrapper .dataTables_length select:focus {
  outline: 0;
  border-color: #919191;
}
.metro .dataTables_wrapper .dataTables_length {
  float: left;
}
.metro .dataTables_wrapper .dataTables_length select {
  margin: 0 5px;
}
.metro .dataTables_wrapper .dataTables_filter {
  float: right;
}
.metro .dataTables_wrapper .dataTables_filter input {
  margin-left: 5px;
}
.metro .dataTables_wrapper .dataTables_paginate {
  float: right;
  text-align: right;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button {
  padding: 4px 12px;
  text-align: center;
  vertical-align: middle !important;
  background-color: #d9d9d9;
  border: 1px transparent solid;
  color: #222222;
  border-radius: 0;
  cursor: pointer;
  display: inline-block;
  outline: none;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-size: 14px;
  line-height: 16px;
  margin: auto;
  color: inherit;
  display: block;
  float: left;
  margin-right: 1px;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button.default {
  background-color: #008287;
  color: #fff;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button:focus {
  outline: 0;
  border: 1px #353535 dotted;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button:disabled,
.metro .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
  background-color: #eaeaea !important;
  color: #bebebe !important;
  cursor: not-allowed !important;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button * {
  color: inherit;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button *:hover {
  color: inherit;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button.primary {
  background-color: #16499a !important;
  color: #ffffff !important;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button.info {
  background-color: #4390df !important;
  color: #ffffff !important;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button.success {
  background-color: #60a917 !important;
  color: #ffffff !important;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button.warning {
  background-color: #fa6800 !important;
  color: #ffffff !important;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button.danger {
  background-color: #9a1616 !important;
  color: #ffffff !important;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button.inverse {
  background-color: #1d1d1d !important;
  color: #ffffff !important;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button.link {
  background-color: transparent !important;
  color: #2e92cf !important;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button.link:hover {
  text-decoration: underline;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button.link:disabled,
.metro .dataTables_wrapper .dataTables_paginate .paginate_button.link.disabled {
  background-color: transparent !important;
  color: #bebebe !important;
  cursor: not-allowed !important;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button.link [class*="icon-"] {
  text-decoration: none !important;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button:active {
  background-color: #1d1d1d !important;
  color: #ffffff !important;
  border-color: transparent;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button.large {
  padding: 11px 19px;
  font-size: 17.5px;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button.small {
  padding: 2px 10px;
  font-size: 11.9px;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button.mini {
  padding: 0px 6px;
  font-size: 10.5px;
}
.metro .dataTables_wrapper .dataTables_paginate .paginate_button.current {
  background-color: #4390df;
  color: #ffffff;
}
.metro .dataTables_wrapper .dataTables_info {
  float: left;
}
.metro .dataTables_wrapper .dataTables_scrollHeadInner table {
  margin: 0 !important;
}
.metro .dataTables_wrapper .dataTables_processing {
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -100px;
  margin-top: -2em;
  width: 200px;
  padding: 20px;
  text-align: center;
  font-size: 1.2rem;
  background-color: #ffffff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3), 0 3px 8px rgba(0, 0, 0, 0.2);
}
.metro .dataTables_wrapper .dataTables_scroll {
  clear: both;
}
.metro .dataTables_wrapper:after {
  visibility: hidden;
  display: block;
  content: "";
  clear: both;
  height: 0;
}
.metro .wizard .steps {
  margin: 10px 0;
  padding: 20px;
  border: 1px #eeeeee solid;
  position: relative;
}
.metro .wizard .steps .step {
  position: relative;
  width: 100%;
  height: 100%;
  display: none;
}
.metro .wizard .steps .step:first-child {
  display: block;
}
.metro .wizard .actions .group-right {
  float: right;
}
.metro .wizard .actions .group-left {
  float: left;
}
.metro .wizard .actions button {
  padding: 4px 12px;
  text-align: center;
  vertical-align: middle !important;
  background-color: #d9d9d9;
  border: 1px transparent solid;
  color: #222222;
  border-radius: 0;
  cursor: pointer;
  display: inline-block;
  outline: none;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-size: 14px;
  line-height: 16px;
  margin: auto;
  margin-right: 2px;
}
.metro .wizard .actions button.default {
  background-color: #008287;
  color: #fff;
}
.metro .wizard .actions button:focus {
  outline: 0;
  border: 1px #353535 dotted;
}
.metro .wizard .actions button:disabled,
.metro .wizard .actions button.disabled {
  background-color: #eaeaea !important;
  color: #bebebe !important;
  cursor: not-allowed !important;
}
.metro .wizard .actions button * {
  color: inherit;
}
.metro .wizard .actions button *:hover {
  color: inherit;
}
.metro .wizard .actions button.primary {
  background-color: #16499a !important;
  color: #ffffff !important;
}
.metro .wizard .actions button.info {
  background-color: #4390df !important;
  color: #ffffff !important;
}
.metro .wizard .actions button.success {
  background-color: #60a917 !important;
  color: #ffffff !important;
}
.metro .wizard .actions button.warning {
  background-color: #fa6800 !important;
  color: #ffffff !important;
}
.metro .wizard .actions button.danger {
  background-color: #9a1616 !important;
  color: #ffffff !important;
}
.metro .wizard .actions button.inverse {
  background-color: #1d1d1d !important;
  color: #ffffff !important;
}
.metro .wizard .actions button.link {
  background-color: transparent !important;
  color: #2e92cf !important;
}
.metro .wizard .actions button.link:hover {
  text-decoration: underline;
}
.metro .wizard .actions button.link:disabled,
.metro .wizard .actions button.link.disabled {
  background-color: transparent !important;
  color: #bebebe !important;
  cursor: not-allowed !important;
}
.metro .wizard .actions button.link [class*="icon-"] {
  text-decoration: none !important;
}
.metro .wizard .actions button:active {
  background-color: #1d1d1d !important;
  color: #ffffff !important;
  border-color: transparent;
}
.metro .wizard .actions button.large {
  padding: 11px 19px;
  font-size: 17.5px;
}
.metro .wizard .actions button.small {
  padding: 2px 10px;
  font-size: 11.9px;
}
.metro .wizard .actions button.mini {
  padding: 0px 6px;
  font-size: 10.5px;
}
.metro .wizard .actions button:last-child {
  margin-right: 0;
}
.metro .wizard .actions button.btn-finish {
  background-color: #60a917;
  color: #ffffff;
}
.metro .wizard .actions button:disabled {
  background-color: #6f6f6f !important;
}
.metro .panel {
  border: 1px #eaeaea solid;
}
.metro .panel .panel-header {
  padding: 10px;
  background-color: #eeeeee;
  width: 100%;
  font-family: 'Segoe UI Light_', 'Open Sans Light', Verdana, Arial, Helvetica, sans-serif;
  font-weight: 300;
  letter-spacing: 0.00em;
  font-size: 2.5rem;
  line-height: 2.5rem;
  letter-spacing: 0.01em;
  color: #000000;
  font-size: 2.2rem;
  line-height: 2.2rem;
  color: #1d1d1d;
  cursor: default;
}
.metro .panel .panel-content {
  width: 100%;
  height: auto;
  padding: 10px;
}
.metro [data-role=panel] .panel-header {
  cursor: pointer;
}
.metro [data-role=panel] .panel-header:after {
  font-family: metroSysIcons;
  content: "\e000";
  float: right;
  color: inherit;
  -webkit-transform: rotate(180deg);
  -moz-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  transform: rotate(180deg);
}
.metro [data-role=panel].collapsed .panel-header:after {
  content: "\e000";
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);
}
@font-face {
  font-family: 'metroSysIcons';
  src: url('../fonts/metroSysIcons.woff') format('woff'), url('../fonts/metroSysIcons.ttf') format('truetype'), url('../fonts/metroSysIcons.svg#metroSysIcons') format('svg');
  font-weight: normal;
  font-style: normal;
}
.metro .example {
  padding: 20px 40px 20px 60px;
  border: 1px #ccc solid;
  position: relative;
  margin: 0 0 10px 0;
  background-color: #fdfdfd !important;
  min-height: 100px;
}
.metro .example:before,
.metro .example:after {
  display: table;
  content: "";
}
.metro .example:after {
  clear: both;
}
.metro .example:before {
  position: absolute;
  content: "example";
  left: -21px;
  top: 30px;
  color: #999999 !important;
  margin: 5px 10px;
  font-family: 'Segoe UI Semibold_', 'Open Sans Bold', Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  font-size: 1.6rem;
  line-height: 1.6rem;
  letter-spacing: 0.02em;
  color: #000000;
  font-size: 1.4rem;
  line-height: 1.4rem;
  -webkit-transform: rotate(-90deg);
  -moz-transform: rotate(-90deg);
  -ms-transform: rotate(-90deg);
  -o-transform: rotate(-90deg);
  transform: rotate(-90deg);
}
.metro code,
.metro .code {
  font-family: 'Segoe UI_', 'Open Sans', Verdana, Arial, Helvetica, sans-serif;
  font-weight: normal;
  font-style: normal;
  color: #000000;
  font-size: 11pt;
  line-height: 15pt;
  letter-spacing: 0.02em;
  font-family: 'Courier New', monospace;
  display: inline-block;
  padding: 3px 5px;
  margin: 0;
  background-color: #eeeeee;
  color: #e51400;
  border: 1px #d4d4d4 solid;
  font-size: 90%;
  line-height: 90%;
}
.metro .label {
  display: inline-block;
  padding: 3px 5px;
  margin: 0;
  font-size: 90%;
  font-weight: normal !important;
  line-height: 90%;
  background: #eeeeee;
  color: #555555;
  font-weight: bold;
}
.metro .label.alert,
.metro .label.error {
  background-color: #e51400;
  color: #ffffff;
}
.metro .label.important,
.metro .label.warning {
  background-color: #fa6800;
  color: #ffffff;
}
.metro .label.success {
  background-color: #128023;
  color: #ffffff;
}
.metro .label.info {
  background-color: #1ba1e2;
  color: #ffffff;
}
.metro hr {
  height: 1px;
  margin: 20px 0;
  border: 0;
  color: #eeeeee;
  background-color: #eeeeee;
}
.metro .bg-black {
  background-color: #000000 !important;
}
.metro .bg-white {
  background-color: #ffffff !important;
}
.metro .bg-lime {
  background-color: #a4c400 !important;
}
.metro .bg-green {
  background-color: #60a917 !important;
}
.metro .bg-emerald {
  background-color: #008a00 !important;
}
.metro .bg-teal {
  background-color: #00aba9 !important;
}
.metro .bg-cyan {
  background-color: #1ba1e2 !important;
}
.metro .bg-cobalt {
  background-color: #0050ef !important;
}
.metro .bg-indigo {
  background-color: #6a00ff !important;
}
.metro .bg-violet {
  background-color: #aa00ff !important;
}
.metro .bg-pink {
  background-color: #dc4fad !important;
}
.metro .bg-magenta {
  background-color: #d80073 !important;
}
.metro .bg-crimson {
  background-color: #a20025 !important;
}
.metro .bg-red {
  background-color: #e51400 !important;
}
.metro .bg-orange {
  background-color: #fa6800 !important;
}
.metro .bg-amber {
  background-color: #f0a30a !important;
}
.metro .bg-yellow {
  background-color: #e3c800 !important;
}
.metro .bg-brown {
  background-color: #825a2c !important;
}
.metro .bg-olive {
  background-color: #6d8764 !important;
}
.metro .bg-steel {
  background-color: #647687 !important;
}
.metro .bg-mauve {
  background-color: #76608a !important;
}
.metro .bg-taupe {
  background-color: #87794e !important;
}
.metro .bg-gray {
  background-color: #555555 !important;
}
.metro .bg-dark {
  background-color: #333333 !important;
}
.metro .bg-darker {
  background-color: #222222 !important;
}
.metro .bg-transparent {
  background-color: transparent !important;
}
.metro .bg-darkBrown {
  background-color: #63362f !important;
}
.metro .bg-darkCrimson {
  background-color: #640024 !important;
}
.metro .bg-darkMagenta {
  background-color: #81003c !important;
}
.metro .bg-darkIndigo {
  background-color: #4b0096 !important;
}
.metro .bg-darkCyan {
  background-color: #1b6eae !important;
}
.metro .bg-darkCobalt {
  background-color: #00356a !important;
}
.metro .bg-darkTeal {
  background-color: #004050 !important;
}
.metro .bg-darkEmerald {
  background-color: #003e00 !important;
}
.metro .bg-darkGreen {
  background-color: #128023 !important;
}
.metro .bg-darkOrange {
  background-color: #bf5a15 !important;
}
.metro .bg-darkRed {
  background-color: #9a1616 !important;
}
.metro .bg-darkPink {
  background-color: #9a165a !important;
}
.metro .bg-darkViolet {
  background-color: #57169a !important;
}
.metro .bg-darkBlue {
  background-color: #16499a !important;
}
.metro .bg-lightBlue {
  background-color: #4390df !important;
}
.metro .bg-lightRed {
  background-color: #ff2d19 !important;
}
.metro .bg-lightGreen {
  background-color: #7ad61d !important;
}
.metro .bg-lighterBlue {
  background-color: #00ccff !important;
}
.metro .bg-lightTeal {
  background-color: #45fffd !important;
}
.metro .bg-lightOlive {
  background-color: #78aa1c !important;
}
.metro .bg-lightOrange {
  background-color: #c29008 !important;
}
.metro .bg-lightPink {
  background-color: #f472d0 !important;
}
.metro .bg-grayDark {
  background-color: #333333 !important;
}
.metro .bg-grayDarker {
  background-color: #222222 !important;
}
.metro .bg-grayLight {
  background-color: #999999 !important;
}
.metro .bg-grayLighter {
  background-color: #eeeeee !important;
}
.metro .bg-blue {
  background-color: #00aff0 !important;
}
.metro .fg-black {
  color: #000000 !important;
}
.metro .fg-white {
  color: #ffffff !important;
}
.metro .fg-lime {
  color: #a4c400 !important;
}
.metro .fg-green {
  color: #60a917 !important;
}
.metro .fg-emerald {
  color: #008a00 !important;
}
.metro .fg-teal {
  color: #00aba9 !important;
}
.metro .fg-cyan {
  color: #1ba1e2 !important;
}
.metro .fg-cobalt {
  color: #0050ef !important;
}
.metro .fg-indigo {
  color: #6a00ff !important;
}
.metro .fg-violet {
  color: #aa00ff !important;
}
.metro .fg-pink {
  color: #dc4fad !important;
}
.metro .fg-magenta {
  color: #d80073 !important;
}
.metro .fg-crimson {
  color: #a20025 !important;
}
.metro .fg-red {
  color: #e51400 !important;
}
.metro .fg-orange {
  color: #fa6800 !important;
}
.metro .fg-amber {
  color: #f0a30a !important;
}
.metro .fg-yellow {
  color: #e3c800 !important;
}
.metro .fg-brown {
  color: #825a2c !important;
}
.metro .fg-olive {
  color: #6d8764 !important;
}
.metro .fg-steel {
  color: #647687 !important;
}
.metro .fg-mauve {
  color: #76608a !important;
}
.metro .fg-taupe {
  color: #87794e !important;
}
.metro .fg-gray {
  color: #555555 !important;
}
.metro .fg-dark {
  color: #333333 !important;
}
.metro .fg-darker {
  color: #222222 !important;
}
.metro .fg-transparent {
  color: transparent !important;
}
.metro .fg-darkBrown {
  color: #63362f !important;
}
.metro .fg-darkCrimson {
  color: #640024 !important;
}
.metro .fg-darkMagenta {
  color: #81003c !important;
}
.metro .fg-darkIndigo {
  color: #4b0096 !important;
}
.metro .fg-darkCyan {
  color: #1b6eae !important;
}
.metro .fg-darkCobalt {
  color: #00356a !important;
}
.metro .fg-darkTeal {
  color: #004050 !important;
}
.metro .fg-darkEmerald {
  color: #003e00 !important;
}
.metro .fg-darkGreen {
  color: #128023 !important;
}
.metro .fg-darkOrange {
  color: #bf5a15 !important;
}
.metro .fg-darkRed {
  color: #9a1616 !important;
}
.metro .fg-darkPink {
  color: #9a165a !important;
}
.metro .fg-darkViolet {
  color: #57169a !important;
}
.metro .fg-darkBlue {
  color: #16499a !important;
}
.metro .fg-lightBlue {
  color: #4390df !important;
}
.metro .fg-lighterBlue {
  color: #00ccff !important;
}
.metro .fg-lightTeal {
  color: #45fffd !important;
}
.metro .fg-lightOlive {
  color: #78aa1c !important;
}
.metro .fg-lightOrange {
  color: #c29008 !important;
}
.metro .fg-lightPink {
  color: #f472d0 !important;
}
.metro .fg-lightRed {
  color: #ff2d19 !important;
}
.metro .fg-lightGreen {
  color: #7ad61d !important;
}
.metro .fg-grayDark {
  color: #333333 !important;
}
.metro .fg-grayDarker {
  color: #222222 !important;
}
.metro .fg-grayLight {
  color: #999999 !important;
}
.metro .fg-grayLighter {
  color: #eeeeee !important;
}
.metro .fg-blue {
  color: #00aff0 !important;
}
.metro .ol-black {
  outline-color: #000000 !important;
}
.metro .ol-white {
  outline-color: #ffffff !important;
}
.metro .ol-lime {
  outline-color: #a4c400 !important;
}
.metro .ol-green {
  outline-color: #60a917 !important;
}
.metro .ol-emerald {
  outline-color: #008a00 !important;
}
.metro .ol-teal {
  outline-color: #00aba9 !important;
}
.metro .ol-cyan {
  outline-color: #1ba1e2 !important;
}
.metro .ol-cobalt {
  outline-color: #0050ef !important;
}
.metro .ol-indigo {
  outline-color: #6a00ff !important;
}
.metro .ol-violet {
  outline-color: #aa00ff !important;
}
.metro .ol-pink {
  outline-color: #dc4fad !important;
}
.metro .ol-magenta {
  outline-color: #d80073 !important;
}
.metro .ol-crimson {
  outline-color: #a20025 !important;
}
.metro .ol-red {
  outline-color: #e51400 !important;
}
.metro .ol-orange {
  outline-color: #fa6800 !important;
}
.metro .ol-amber {
  outline-color: #f0a30a !important;
}
.metro .ol-yellow {
  outline-color: #e3c800 !important;
}
.metro .ol-brown {
  outline-color: #825a2c !important;
}
.metro .ol-olive {
  outline-color: #6d8764 !important;
}
.metro .ol-steel {
  outline-color: #647687 !important;
}
.metro .ol-mauve {
  outline-color: #76608a !important;
}
.metro .ol-taupe {
  outline-color: #87794e !important;
}
.metro .ol-gray {
  outline-color: #555555 !important;
}
.metro .ol-dark {
  outline-color: #333333 !important;
}
.metro .ol-darker {
  outline-color: #222222 !important;
}
.metro .ol-transparent {
  outline-color: transparent !important;
}
.metro .ol-darkBrown {
  outline-color: #63362f !important;
}
.metro .ol-darkCrimson {
  outline-color: #640024 !important;
}
.metro .ol-darkMagenta {
  outline-color: #81003c !important;
}
.metro .ol-darkIndigo {
  outline-color: #4b0096 !important;
}
.metro .ol-darkCyan {
  outline-color: #1b6eae !important;
}
.metro .ol-darkCobalt {
  outline-color: #00356a !important;
}
.metro .ol-darkTeal {
  outline-color: #004050 !important;
}
.metro .ol-darkEmerald {
  outline-color: #003e00 !important;
}
.metro .ol-darkGreen {
  outline-color: #128023 !important;
}
.metro .ol-darkOrange {
  outline-color: #bf5a15 !important;
}
.metro .ol-darkRed {
  outline-color: #9a1616 !important;
}
.metro .ol-darkPink {
  outline-color: #9a165a !important;
}
.metro .ol-darkViolet {
  outline-color: #57169a !important;
}
.metro .ol-darkBlue {
  outline-color: #16499a !important;
}
.metro .ol-lightBlue {
  outline-color: #4390df !important;
}
.metro .ol-lighterBlue {
  outline-color: #00ccff !important;
}
.metro .ol-lightTeal {
  outline-color: #45fffd !important;
}
.metro .ol-lightOlive {
  outline-color: #78aa1c !important;
}
.metro .ol-lightOrange {
  outline-color: #c29008 !important;
}
.metro .ol-lightPink {
  outline-color: #f472d0 !important;
}
.metro .ol-lightRed {
  outline-color: #ff2d19 !important;
}
.metro .ol-lightGreen {
  outline-color: #7ad61d !important;
}
.metro .ol-grayDark {
  outline-color: #333333 !important;
}
.metro .ol-grayDarker {
  outline-color: #222222 !important;
}
.metro .ol-grayLight {
  outline-color: #999999 !important;
}
.metro .ol-grayLighter {
  outline-color: #eeeeee !important;
}
.metro .ol-blue {
  outline-color: #00aff0 !important;
}
.metro .bd-black {
  border-color: #000000 !important;
}
.metro .bd-white {
  border-color: #ffffff !important;
}
.metro .bd-lime {
  border-color: #a4c400 !important;
}
.metro .bd-green {
  border-color: #60a917 !important;
}
.metro .bd-emerald {
  border-color: #008a00 !important;
}
.metro .bd-teal {
  border-color: #00aba9 !important;
}
.metro .bd-cyan {
  border-color: #1ba1e2 !important;
}
.metro .bd-cobalt {
  border-color: #0050ef !important;
}
.metro .bd-indigo {
  border-color: #6a00ff !important;
}
.metro .bd-violet {
  border-color: #aa00ff !important;
}
.metro .bd-pink {
  border-color: #dc4fad !important;
}
.metro .bd-magenta {
  border-color: #d80073 !important;
}
.metro .bd-crimson {
  border-color: #a20025 !important;
}
.metro .bd-red {
  border-color: #e51400 !important;
}
.metro .bd-orange {
  border-color: #fa6800 !important;
}
.metro .bd-amber {
  border-color: #f0a30a !important;
}
.metro .bd-yellow {
  border-color: #e3c800 !important;
}
.metro .bd-brown {
  border-color: #825a2c !important;
}
.metro .bd-olive {
  border-color: #6d8764 !important;
}
.metro .bd-steel {
  border-color: #647687 !important;
}
.metro .bd-mauve {
  border-color: #76608a !important;
}
.metro .bd-taupe {
  border-color: #87794e !important;
}
.metro .bd-gray {
  border-color: #555555 !important;
}
.metro .bd-dark {
  border-color: #333333 !important;
}
.metro .bd-darker {
  border-color: #222222 !important;
}
.metro .bd-transparent {
  border-color: transparent !important;
}
.metro .bd-darkBrown {
  border-color: #63362f !important;
}
.metro .bd-darkCrimson {
  border-color: #640024 !important;
}
.metro .bd-darkMagenta {
  border-color: #81003c !important;
}
.metro .bd-darkIndigo {
  border-color: #4b0096 !important;
}
.metro .bd-darkCyan {
  border-color: #1b6eae !important;
}
.metro .bd-darkCobalt {
  border-color: #00356a !important;
}
.metro .bd-darkTeal {
  border-color: #004050 !important;
}
.metro .bd-darkEmerald {
  border-color: #003e00 !important;
}
.metro .bd-darkGreen {
  border-color: #128023 !important;
}
.metro .bd-darkOrange {
  border-color: #bf5a15 !important;
}
.metro .bd-darkRed {
  border-color: #9a1616 !important;
}
.metro .bd-darkPink {
  border-color: #9a165a !important;
}
.metro .bd-darkViolet {
  border-color: #57169a !important;
}
.metro .bd-darkBlue {
  border-color: #16499a !important;
}
.metro .bd-lightBlue {
  border-color: #4390df !important;
}
.metro .bd-lightTeal {
  border-color: #45fffd !important;
}
.metro .bd-lightOlive {
  border-color: #78aa1c !important;
}
.metro .bd-lightOrange {
  border-color: #c29008 !important;
}
.metro .bd-lightPink {
  border-color: #f472d0 !important;
}
.metro .bd-lightRed {
  border-color: #ff2d19 !important;
}
.metro .bd-lightGreen {
  border-color: #7ad61d !important;
}
.metro .bd-grayDark {
  border-color: #333333 !important;
}
.metro .bd-grayDarker {
  border-color: #222222 !important;
}
.metro .bd-grayLight {
  border-color: #999999 !important;
}
.metro .bd-grayLighter {
  border-color: #eeeeee !important;
}
.metro .bd-blue {
  border-color: #00aff0 !important;
}
.metro .bg-hover-black:hover {
  background-color: #000000 !important;
}
.metro .bg-hover-white:hover {
  background-color: #ffffff !important;
}
.metro .bg-hover-lime:hover {
  background-color: #a4c400 !important;
}
.metro .bg-hover-green:hover {
  background-color: #60a917 !important;
}
.metro .bg-hover-emerald:hover {
  background-color: #008a00 !important;
}
.metro .bg-hover-teal:hover {
  background-color: #00aba9 !important;
}
.metro .bg-hover-cyan:hover {
  background-color: #1ba1e2 !important;
}
.metro .bg-hover-cobalt:hover {
  background-color: #0050ef !important;
}
.metro .bg-hover-indigo:hover {
  background-color: #6a00ff !important;
}
.metro .bg-hover-violet:hover {
  background-color: #aa00ff !important;
}
.metro .bg-hover-pink:hover {
  background-color: #dc4fad !important;
}
.metro .bg-hover-magenta:hover {
  background-color: #d80073 !important;
}
.metro .bg-hover-crimson:hover {
  background-color: #a20025 !important;
}
.metro .bg-hover-red:hover {
  background-color: #e51400 !important;
}
.metro .bg-hover-orange:hover {
  background-color: #fa6800 !important;
}
.metro .bg-hover-amber:hover {
  background-color: #f0a30a !important;
}
.metro .bg-hover-yellow:hover {
  background-color: #e3c800 !important;
}
.metro .bg-hover-brown:hover {
  background-color: #825a2c !important;
}
.metro .bg-hover-olive:hover {
  background-color: #6d8764 !important;
}
.metro .bg-hover-steel:hover {
  background-color: #647687 !important;
}
.metro .bg-hover-mauve:hover {
  background-color: #76608a !important;
}
.metro .bg-hover-taupe:hover {
  background-color: #87794e !important;
}
.metro .bg-hover-gray:hover {
  background-color: #555555 !important;
}
.metro .bg-hover-dark:hover {
  background-color: #333333 !important;
}
.metro .bg-hover-darker:hover {
  background-color: #222222 !important;
}
.metro .bg-hover-transparent:hover {
  background-color: transparent !important;
}
.metro .bg-hover-darkBrown:hover {
  background-color: #63362f !important;
}
.metro .bg-hover-darkCrimson:hover {
  background-color: #640024 !important;
}
.metro .bg-hover-darkMagenta:hover {
  background-color: #81003c !important;
}
.metro .bg-hover-darkIndigo:hover {
  background-color: #4b0096 !important;
}
.metro .bg-hover-darkCyan:hover {
  background-color: #1b6eae !important;
}
.metro .bg-hover-darkCobalt:hover {
  background-color: #00356a !important;
}
.metro .bg-hover-darkTeal:hover {
  background-color: #004050 !important;
}
.metro .bg-hover-darkEmerald:hover {
  background-color: #003e00 !important;
}
.metro .bg-hover-darkGreen:hover {
  background-color: #128023 !important;
}
.metro .bg-hover-darkOrange:hover {
  background-color: #bf5a15 !important;
}
.metro .bg-hover-darkRed:hover {
  background-color: #9a1616 !important;
}
.metro .bg-hover-darkPink:hover {
  background-color: #9a165a !important;
}
.metro .bg-hover-darkViolet:hover {
  background-color: #57169a !important;
}
.metro .bg-hover-darkBlue:hover {
  background-color: #16499a !important;
}
.metro .bg-hover-lightBlue:hover {
  background-color: #4390df !important;
}
.metro .bg-hover-lightTeal:hover {
  background-color: #45fffd !important;
}
.metro .bg-hover-lightOlive:hover {
  background-color: #78aa1c !important;
}
.metro .bg-hover-lightOrange:hover {
  background-color: #c29008 !important;
}
.metro .bg-hover-lightPink:hover {
  background-color: #f472d0 !important;
}
.metro .bg-hover-lightRed:hover {
  background-color: #ff2d19 !important;
}
.metro .bg-hover-lightGreen:hover {
  background-color: #7ad61d !important;
}
.metro .bg-hover-grayDark:hover {
  background-color: #333333 !important;
}
.metro .bg-hover-grayDarker:hover {
  background-color: #222222 !important;
}
.metro .bg-hover-grayLight:hover {
  background-color: #999999 !important;
}
.metro .bg-hover-grayLighter:hover {
  background-color: #eeeeee !important;
}
.metro .bg-hover-blue:hover {
  background-color: #00aff0 !important;
}
.metro .fg-hover-black:hover {
  color: #000000 !important;
}
.metro .fg-hover-white:hover {
  color: #ffffff !important;
}
.metro .fg-hover-lime:hover {
  color: #a4c400 !important;
}
.metro .fg-hover-green:hover {
  color: #60a917 !important;
}
.metro .fg-hover-emerald:hover {
  color: #008a00 !important;
}
.metro .fg-hover-teal:hover {
  color: #00aba9 !important;
}
.metro .fg-hover-cyan:hover {
  color: #1ba1e2 !important;
}
.metro .fg-hover-cobalt:hover {
  color: #0050ef !important;
}
.metro .fg-hover-indigo:hover {
  color: #6a00ff !important;
}
.metro .fg-hover-violet:hover {
  color: #aa00ff !important;
}
.metro .fg-hover-pink:hover {
  color: #dc4fad !important;
}
.metro .fg-hover-magenta:hover {
  color: #d80073 !important;
}
.metro .fg-hover-crimson:hover {
  color: #a20025 !important;
}
.metro .fg-hover-red:hover {
  color: #e51400 !important;
}
.metro .fg-hover-orange:hover {
  color: #fa6800 !important;
}
.metro .fg-hover-amber:hover {
  color: #f0a30a !important;
}
.metro .fg-hover-yellow:hover {
  color: #e3c800 !important;
}
.metro .fg-hover-brown:hover {
  color: #825a2c !important;
}
.metro .fg-hover-olive:hover {
  color: #6d8764 !important;
}
.metro .fg-hover-steel:hover {
  color: #647687 !important;
}
.metro .fg-hover-mauve:hover {
  color: #76608a !important;
}
.metro .fg-hover-taupe:hover {
  color: #87794e !important;
}
.metro .fg-hover-gray:hover {
  color: #555555 !important;
}
.metro .fg-hover-dark:hover {
  color: #333333 !important;
}
.metro .fg-hover-darker:hover {
  color: #222222 !important;
}
.metro .fg-hover-transparent:hover {
  color: transparent !important;
}
.metro .fg-hover-darkBrown:hover {
  color: #63362f !important;
}
.metro .fg-hover-darkCrimson:hover {
  color: #640024 !important;
}
.metro .fg-hover-darkMagenta:hover {
  color: #81003c !important;
}
.metro .fg-hover-darkIndigo:hover {
  color: #4b0096 !important;
}
.metro .fg-hover-darkCyan:hover {
  color: #1b6eae !important;
}
.metro .fg-hover-darkCobalt:hover {
  color: #00356a !important;
}
.metro .fg-hover-darkTeal:hover {
  color: #004050 !important;
}
.metro .fg-hover-darkEmerald:hover {
  color: #003e00 !important;
}
.metro .fg-hover-darkGreen:hover {
  color: #128023 !important;
}
.metro .fg-hover-darkOrange:hover {
  color: #bf5a15 !important;
}
.metro .fg-hover-darkRed:hover {
  color: #9a1616 !important;
}
.metro .fg-hover-darkPink:hover {
  color: #9a165a !important;
}
.metro .fg-hover-darkViolet:hover {
  color: #57169a !important;
}
.metro .fg-hover-darkBlue:hover {
  color: #16499a !important;
}
.metro .fg-hover-lightBlue:hover {
  color: #4390df !important;
}
.metro .fg-hover-lightTeal:hover {
  color: #45fffd !important;
}
.metro .fg-hover-lightOlive:hover {
  color: #78aa1c !important;
}
.metro .fg-hover-lightOrange:hover {
  color: #c29008 !important;
}
.metro .fg-hover-lightPink:hover {
  color: #f472d0 !important;
}
.metro .fg-hover-lightRed:hover {
  color: #ff2d19 !important;
}
.metro .fg-hover-lightGreen:hover {
  color: #7ad61d !important;
}
.metro .fg-hover-grayDark:hover {
  color: #333333 !important;
}
.metro .fg-hover-grayDarker:hover {
  color: #222222 !important;
}
.metro .fg-hover-grayLight:hover {
  color: #999999 !important;
}
.metro .fg-hover-grayLighter:hover {
  color: #eeeeee !important;
}
.metro .fg-hover-blue:hover {
  color: #00aff0 !important;
}
.metro .bg-active-black:active {
  background-color: #000000 !important;
}
.metro .bg-active-white:active {
  background-color: #ffffff !important;
}
.metro .bg-active-lime:active {
  background-color: #a4c400 !important;
}
.metro .bg-active-green:active {
  background-color: #60a917 !important;
}
.metro .bg-active-emerald:active {
  background-color: #008a00 !important;
}
.metro .bg-active-teal:active {
  background-color: #00aba9 !important;
}
.metro .bg-active-cyan:active {
  background-color: #1ba1e2 !important;
}
.metro .bg-active-cobalt:active {
  background-color: #0050ef !important;
}
.metro .bg-active-indigo:active {
  background-color: #6a00ff !important;
}
.metro .bg-active-violet:active {
  background-color: #aa00ff !important;
}
.metro .bg-active-pink:active {
  background-color: #dc4fad !important;
}
.metro .bg-active-magenta:active {
  background-color: #d80073 !important;
}
.metro .bg-active-crimson:active {
  background-color: #a20025 !important;
}
.metro .bg-active-red:active {
  background-color: #e51400 !important;
}
.metro .bg-active-orange:active {
  background-color: #fa6800 !important;
}
.metro .bg-active-amber:active {
  background-color: #f0a30a !important;
}
.metro .bg-active-yellow:active {
  background-color: #e3c800 !important;
}
.metro .bg-active-brown:active {
  background-color: #825a2c !important;
}
.metro .bg-active-olive:active {
  background-color: #6d8764 !important;
}
.metro .bg-active-steel:active {
  background-color: #647687 !important;
}
.metro .bg-active-mauve:active {
  background-color: #76608a !important;
}
.metro .bg-active-taupe:active {
  background-color: #87794e !important;
}
.metro .bg-active-gray:active {
  background-color: #555555 !important;
}
.metro .bg-active-dark:active {
  background-color: #333333 !important;
}
.metro .bg-active-darker:active {
  background-color: #222222 !important;
}
.metro .bg-active-transparent:active {
  background-color: transparent !important;
}
.metro .bg-active-darkBrown:active {
  background-color: #63362f !important;
}
.metro .bg-active-darkCrimson:active {
  background-color: #640024 !important;
}
.metro .bg-active-darkMagenta:active {
  background-color: #81003c !important;
}
.metro .bg-active-darkIndigo:active {
  background-color: #4b0096 !important;
}
.metro .bg-active-darkCyan:active {
  background-color: #1b6eae !important;
}
.metro .bg-active-darkCobalt:active {
  background-color: #00356a !important;
}
.metro .bg-active-darkTeal:active {
  background-color: #004050 !important;
}
.metro .bg-active-darkEmerald:active {
  background-color: #003e00 !important;
}
.metro .bg-active-darkGreen:active {
  background-color: #128023 !important;
}
.metro .bg-active-darkOrange:active {
  background-color: #bf5a15 !important;
}
.metro .bg-active-darkRed:active {
  background-color: #9a1616 !important;
}
.metro .bg-active-darkPink:active {
  background-color: #9a165a !important;
}
.metro .bg-active-darkViolet:active {
  background-color: #57169a !important;
}
.metro .bg-active-darkBlue:active {
  background-color: #16499a !important;
}
.metro .bg-active-lightBlue:active {
  background-color: #4390df !important;
}
.metro .bg-active-lightTeal:active {
  background-color: #45fffd !important;
}
.metro .bg-active-lightOlive:active {
  background-color: #78aa1c !important;
}
.metro .bg-active-lightOrange:active {
  background-color: #c29008 !important;
}
.metro .bg-active-lightPink:active {
  background-color: #f472d0 !important;
}
.metro .bg-active-lightRed:active {
  background-color: #ff2d19 !important;
}
.metro .bg-active-lightGreen:active {
  background-color: #7ad61d !important;
}
.metro .bg-active-grayDark:active {
  background-color: #333333 !important;
}
.metro .bg-active-grayDarker:active {
  background-color: #222222 !important;
}
.metro .bg-active-grayLight:active {
  background-color: #999999 !important;
}
.metro .bg-active-grayLighter:active {
  background-color: #eeeeee !important;
}
.metro .bg-active-blue:active {
  background-color: #00aff0 !important;
}
.metro .fg-active-black:active {
  color: #000000 !important;
}
.metro .fg-active-white:active {
  color: #ffffff !important;
}
.metro .fg-active-lime:active {
  color: #a4c400 !important;
}
.metro .fg-active-green:active {
  color: #60a917 !important;
}
.metro .fg-active-emerald:active {
  color: #008a00 !important;
}
.metro .fg-active-teal:active {
  color: #00aba9 !important;
}
.metro .fg-active-cyan:active {
  color: #1ba1e2 !important;
}
.metro .fg-active-cobalt:active {
  color: #0050ef !important;
}
.metro .fg-active-indigo:active {
  color: #6a00ff !important;
}
.metro .fg-active-violet:active {
  color: #aa00ff !important;
}
.metro .fg-active-pink:active {
  color: #dc4fad !important;
}
.metro .fg-active-magenta:active {
  color: #d80073 !important;
}
.metro .fg-active-crimson:active {
  color: #a20025 !important;
}
.metro .fg-active-red:active {
  color: #e51400 !important;
}
.metro .fg-active-orange:active {
  color: #fa6800 !important;
}
.metro .fg-active-amber:active {
  color: #f0a30a !important;
}
.metro .fg-active-yellow:active {
  color: #e3c800 !important;
}
.metro .fg-active-brown:active {
  color: #825a2c !important;
}
.metro .fg-active-olive:active {
  color: #6d8764 !important;
}
.metro .fg-active-steel:active {
  color: #647687 !important;
}
.metro .fg-active-mauve:active {
  color: #76608a !important;
}
.metro .fg-active-taupe:active {
  color: #87794e !important;
}
.metro .fg-active-gray:active {
  color: #555555 !important;
}
.metro .fg-active-dark:active {
  color: #333333 !important;
}
.metro .fg-active-darker:active {
  color: #222222 !important;
}
.metro .fg-active-transparent:active {
  color: transparent !important;
}
.metro .fg-active-darkBrown:active {
  color: #63362f !important;
}
.metro .fg-active-darkCrimson:active {
  color: #640024 !important;
}
.metro .fg-active-darkMagenta:active {
  color: #81003c !important;
}
.metro .fg-active-darkIndigo:active {
  color: #4b0096 !important;
}
.metro .fg-active-darkCyan:active {
  color: #1b6eae !important;
}
.metro .fg-active-darkCobalt:active {
  color: #00356a !important;
}
.metro .fg-active-darkTeal:active {
  color: #004050 !important;
}
.metro .fg-active-darkEmerald:active {
  color: #003e00 !important;
}
.metro .fg-active-darkGreen:active {
  color: #128023 !important;
}
.metro .fg-active-darkOrange:active {
  color: #bf5a15 !important;
}
.metro .fg-active-darkRed:active {
  color: #9a1616 !important;
}
.metro .fg-active-darkPink:active {
  color: #9a165a !important;
}
.metro .fg-active-darkViolet:active {
  color: #57169a !important;
}
.metro .fg-active-darkBlue:active {
  color: #16499a !important;
}
.metro .fg-active-lightBlue:active {
  color: #4390df !important;
}
.metro .fg-active-lightTeal:active {
  color: #45fffd !important;
}
.metro .fg-active-lightOlive:active {
  color: #78aa1c !important;
}
.metro .fg-active-lightOrange:active {
  color: #c29008 !important;
}
.metro .fg-active-lightPink:active {
  color: #f472d0 !important;
}
.metro .fg-active-lightRed:active {
  color: #ff2d19 !important;
}
.metro .fg-active-lightGreen:active {
  color: #7ad61d !important;
}
.metro .fg-active-grayDark:active {
  color: #333333 !important;
}
.metro .fg-active-grayDarker:active {
  color: #222222 !important;
}
.metro .fg-active-grayLight:active {
  color: #999999 !important;
}
.metro .fg-active-grayLighter:active {
  color: #eeeeee !important;
}
.metro .fg-active-blue:active {
  color: #00aff0 !important;
}
.metro .bg-focus-black:focus {
  background-color: #000000 !important;
}
.metro .bg-focus-white:focus {
  background-color: #ffffff !important;
}
.metro .bg-focus-lime:focus {
  background-color: #a4c400 !important;
}
.metro .bg-focus-green:focus {
  background-color: #60a917 !important;
}
.metro .bg-focus-emerald:focus {
  background-color: #008a00 !important;
}
.metro .bg-focus-teal:focus {
  background-color: #00aba9 !important;
}
.metro .bg-focus-cyan:focus {
  background-color: #1ba1e2 !important;
}
.metro .bg-focus-cobalt:focus {
  background-color: #0050ef !important;
}
.metro .bg-focus-indigo:focus {
  background-color: #6a00ff !important;
}
.metro .bg-focus-violet:focus {
  background-color: #aa00ff !important;
}
.metro .bg-focus-pink:focus {
  background-color: #dc4fad !important;
}
.metro .bg-focus-magenta:focus {
  background-color: #d80073 !important;
}
.metro .bg-focus-crimson:focus {
  background-color: #a20025 !important;
}
.metro .bg-focus-red:focus {
  background-color: #e51400 !important;
}
.metro .bg-focus-orange:focus {
  background-color: #fa6800 !important;
}
.metro .bg-focus-amber:focus {
  background-color: #f0a30a !important;
}
.metro .bg-focus-yellow:focus {
  background-color: #e3c800 !important;
}
.metro .bg-focus-brown:focus {
  background-color: #825a2c !important;
}
.metro .bg-focus-olive:focus {
  background-color: #6d8764 !important;
}
.metro .bg-focus-steel:focus {
  background-color: #647687 !important;
}
.metro .bg-focus-mauve:focus {
  background-color: #76608a !important;
}
.metro .bg-focus-taupe:focus {
  background-color: #87794e !important;
}
.metro .bg-focus-gray:focus {
  background-color: #555555 !important;
}
.metro .bg-focus-dark:focus {
  background-color: #333333 !important;
}
.metro .bg-focus-darker:focus {
  background-color: #222222 !important;
}
.metro .bg-focus-transparent:focus {
  background-color: transparent !important;
}
.metro .bg-focus-darkBrown:focus {
  background-color: #63362f !important;
}
.metro .bg-focus-darkCrimson:focus {
  background-color: #640024 !important;
}
.metro .bg-focus-darkMagenta:focus {
  background-color: #81003c !important;
}
.metro .bg-focus-darkIndigo:focus {
  background-color: #4b0096 !important;
}
.metro .bg-focus-darkCyan:focus {
  background-color: #1b6eae !important;
}
.metro .bg-focus-darkCobalt:focus {
  background-color: #00356a !important;
}
.metro .bg-focus-darkTeal:focus {
  background-color: #004050 !important;
}
.metro .bg-focus-darkEmerald:focus {
  background-color: #003e00 !important;
}
.metro .bg-focus-darkGreen:focus {
  background-color: #128023 !important;
}
.metro .bg-focus-darkOrange:focus {
  background-color: #bf5a15 !important;
}
.metro .bg-focus-darkRed:focus {
  background-color: #9a1616 !important;
}
.metro .bg-focus-darkPink:focus {
  background-color: #9a165a !important;
}
.metro .bg-focus-darkViolet:focus {
  background-color: #57169a !important;
}
.metro .bg-focus-darkBlue:focus {
  background-color: #16499a !important;
}
.metro .bg-focus-lightBlue:focus {
  background-color: #4390df !important;
}
.metro .bg-focus-lightTeal:focus {
  background-color: #45fffd !important;
}
.metro .bg-focus-lightOlive:focus {
  background-color: #78aa1c !important;
}
.metro .bg-focus-lightOrange:focus {
  background-color: #c29008 !important;
}
.metro .bg-focus-lightPink:focus {
  background-color: #f472d0 !important;
}
.metro .bg-focus-lightRed:focus {
  background-color: #ff2d19 !important;
}
.metro .bg-focus-lightGreen:focus {
  background-color: #7ad61d !important;
}
.metro .bg-focus-grayDark:focus {
  background-color: #333333 !important;
}
.metro .bg-focus-grayDarker:focus {
  background-color: #222222 !important;
}
.metro .bg-focus-grayLight:focus {
  background-color: #999999 !important;
}
.metro .bg-focus-grayLighter:focus {
  background-color: #eeeeee !important;
}
.metro .bg-focus-blue:focus {
  background-color: #00aff0 !important;
}
.metro .fg-focus-black:focus {
  color: #000000 !important;
}
.metro .fg-focus-white:focus {
  color: #ffffff !important;
}
.metro .fg-focus-lime:focus {
  color: #a4c400 !important;
}
.metro .fg-focus-green:focus {
  color: #60a917 !important;
}
.metro .fg-focus-emerald:focus {
  color: #008a00 !important;
}
.metro .fg-focus-teal:focus {
  color: #00aba9 !important;
}
.metro .fg-focus-cyan:focus {
  color: #1ba1e2 !important;
}
.metro .fg-focus-cobalt:focus {
  color: #0050ef !important;
}
.metro .fg-focus-indigo:focus {
  color: #6a00ff !important;
}
.metro .fg-focus-violet:focus {
  color: #aa00ff !important;
}
.metro .fg-focus-pink:focus {
  color: #dc4fad !important;
}
.metro .fg-focus-magenta:focus {
  color: #d80073 !important;
}
.metro .fg-focus-crimson:focus {
  color: #a20025 !important;
}
.metro .fg-focus-red:focus {
  color: #e51400 !important;
}
.metro .fg-focus-orange:focus {
  color: #fa6800 !important;
}
.metro .fg-focus-amber:focus {
  color: #f0a30a !important;
}
.metro .fg-focus-yellow:focus {
  color: #e3c800 !important;
}
.metro .fg-focus-brown:focus {
  color: #825a2c !important;
}
.metro .fg-focus-olive:focus {
  color: #6d8764 !important;
}
.metro .fg-focus-steel:focus {
  color: #647687 !important;
}
.metro .fg-focus-mauve:focus {
  color: #76608a !important;
}
.metro .fg-focus-taupe:focus {
  color: #87794e !important;
}
.metro .fg-focus-gray:focus {
  color: #555555 !important;
}
.metro .fg-focus-dark:focus {
  color: #333333 !important;
}
.metro .fg-focus-darker:focus {
  color: #222222 !important;
}
.metro .fg-focus-transparent:focus {
  color: transparent !important;
}
.metro .fg-focus-darkBrown:focus {
  color: #63362f !important;
}
.metro .fg-focus-darkCrimson:focus {
  color: #640024 !important;
}
.metro .fg-focus-darkMagenta:focus {
  color: #81003c !important;
}
.metro .fg-focus-darkIndigo:focus {
  color: #4b0096 !important;
}
.metro .fg-focus-darkCyan:focus {
  color: #1b6eae !important;
}
.metro .fg-focus-darkCobalt:focus {
  color: #00356a !important;
}
.metro .fg-focus-darkTeal:focus {
  color: #004050 !important;
}
.metro .fg-focus-darkEmerald:focus {
  color: #003e00 !important;
}
.metro .fg-focus-darkGreen:focus {
  color: #128023 !important;
}
.metro .fg-focus-darkOrange:focus {
  color: #bf5a15 !important;
}
.metro .fg-focus-darkRed:focus {
  color: #9a1616 !important;
}
.metro .fg-focus-darkPink:focus {
  color: #9a165a !important;
}
.metro .fg-focus-darkViolet:focus {
  color: #57169a !important;
}
.metro .fg-focus-darkBlue:focus {
  color: #16499a !important;
}
.metro .fg-focus-lightBlue:focus {
  color: #4390df !important;
}
.metro .fg-focus-lightTeal:focus {
  color: #45fffd !important;
}
.metro .fg-focus-lightOlive:focus {
  color: #78aa1c !important;
}
.metro .fg-focus-lightOrange:focus {
  color: #c29008 !important;
}
.metro .fg-focus-lightPink:focus {
  color: #f472d0 !important;
}
.metro .fg-focus-lightRed:focus {
  color: #ff2d19 !important;
}
.metro .fg-focus-lightGreen:focus {
  color: #7ad61d !important;
}
.metro .fg-focus-grayDark:focus {
  color: #333333 !important;
}
.metro .fg-focus-grayDarker:focus {
  color: #222222 !important;
}
.metro .fg-focus-grayLight:focus {
  color: #999999 !important;
}
.metro .fg-focus-grayLighter:focus {
  color: #eeeeee !important;
}
.metro .fg-focus-blue:focus {
  color: #00aff0 !important;
}
.metro .ribbed-black {
  background-color: #000000 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-white {
  background-color: #ffffff !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-lime {
  background-color: #a4c400 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-green {
  background-color: #60a917 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-emerald {
  background-color: #008a00 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-teal {
  background-color: #00aba9 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-cyan {
  background-color: #1ba1e2 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-cobalt {
  background-color: #0050ef !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-indigo {
  background-color: #6a00ff !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-violet {
  background-color: #aa00ff !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-pink {
  background-color: #dc4fad !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-magenta {
  background-color: #d80073 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-crimson {
  background-color: #a20025 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-red {
  background-color: #e51400 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-orange {
  background-color: #fa6800 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-amber {
  background-color: #f0a30a !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-yellow {
  background-color: #e3c800 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-brown {
  background-color: #825a2c !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-olive {
  background-color: #6d8764 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-steel {
  background-color: #647687 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-mauve {
  background-color: #76608a !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-taupe {
  background-color: #87794e !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-dark {
  background-color: #1d1d1d !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-darkBrown {
  background-color: #63362f !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-darkCrimson {
  background-color: #640024 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-darkMagenta {
  background-color: #81003c !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-darkIndigo {
  background-color: #4b0096 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-darkCyan {
  background-color: #1b6eae !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-darkCobalt {
  background-color: #00356a !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-darkTeal {
  background-color: #004050 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-darkEmerald {
  background-color: #003e00 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-darkGreen {
  background-color: #128023 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-darkOrange {
  background-color: #bf5a15 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-darkRed {
  background-color: #9a1616 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-darkPink {
  background-color: #9a165a !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-darkViolet {
  background-color: #57169a !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-darkBlue {
  background-color: #16499a !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-lightTeal {
  background-color: #45fffd !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-lightOlive {
  background-color: #78aa1c !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-lightOrange {
  background-color: #c29008 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-lightPink {
  background-color: #f472d0 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-lightRed {
  background-color: #ff2d19 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-lightGreen {
  background-color: #7ad61d !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-grayed {
  background-color: #585858 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-grayDarker {
  background-color: #222222 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-gray {
  background-color: #555555 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-grayLight {
  background-color: #999999 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-grayLighter {
  background-color: #eeeeee !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.metro .ribbed-blue {
  background-color: #00aff0 !important;
  background-image: -webkit-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -ms-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -o-linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(-45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}

/* New added */
/* user name font */
.metro .user {
    font: 16px Regular, Open Sans;
}
a1{
font: 16px Regular, Open Sans;

}
img1{
Size: 36px Square
Border: 1px #d8d8d8
}

/* Navigation */


/* Main containers
 */


CSS Document

.mmcontainer {
width:950px;
margin:auto;
height:800px;
border:1px #000 solid;
}
.top 
{

background-color:white;
height:55px;
}

.navc
{

background-color:#0070c0;
height:30px;
}

.mcontainer{
margin-top:10px;
border:1px #000 solid;
width:500px;
height:500px;
float:left;
}

.bottom
{

background-color:white;

}



	/* Date align */
	p.date {
    text-align: right;
}
.mmcontainer {
            width: 1040px;
			background-color:white;
			
			padding:0.4px 15px 0px 15px;
        }
        
/* left sidebar that is the left side column of */
/* 3 columns div layout */
#left-sidebar {
    width: 230px;
    height: 100%;
    background-color: #62ade2;
    color: #ffffff;
    float: left;
}
/*Text in left sidebar of audit form*/
#left-sidebar h2{
	margin-left: 20px;
	margin-right:20px;
	font: Open Sans;
	font-weight: lighter;
	font-size: 24px;
}
#left-sidebar h3{
	margin-left: 20px;
	margin-right:20px;
	font: Open Sans;
	font-weight: lighter;
	font-size: 14px;
}
/* options in left sidebar of audit form*/
#left-sidebar a{
	margin-left: 0px;
	margin-right:0px;
	text-decoration: none;
	
	font-size: 18px;
	border-bottom-width: 1px;
	border-bottom-color: #2573ab;
	width: 230px;
	height: 60px;
}
#left-sidebar a:VISITED{
	width: 180px;
	text-decoration: none;
	
	font-size: 18px;
}
/* image for options in left sidebar of audit form*/
#left-sidebar a img{
	border: none;
}        
        
   #content-container {
    
    background-color: #ffffff;
    float: left;
}
/* content container headings*/
#content-container  h1{
  font-weight: normal;
  color:#505050;
}
#content-container  h2{
  font-weight: normal;
  color:#298fd8;
}
/*links in the headings of content-container*/
#content-container  h2 a{
	float:right;
}
        
   /* Datepicker */
   /* Datepicker */
.calendar {
				font-family: 'Trebuchet MS', Tahoma, Verdana, Arial, sans-serif;
				font-size: 0.9em;
				background-color: #EEE;
				color: #333;
				border: 1px solid #DDD;
				-moz-border-radius: 4px;
				-webkit-border-radius: 4px;
				border-radius: 4px;
				padding: 0.2em;
				width: 14em;
			}
			
			.calendar .months {
				background-color: #2573AB;
				border: 1px solid #0070C0;
				-moz-border-radius: 4px;
				-webkit-border-radius: 4px;
				border-radius: 4px;
				color: #FFF;
				padding: 0.2em;
				text-align: center;
			}
			
			.calendar .prev-month,
			.calendar .next-month {
				padding: 0;
			}
			
			.calendar .prev-month {
				float: left;
			}
			
			.calendar .next-month {
				float: right;
			}
			
			.calendar .current-month {
				margin: 0 auto;
			}
			
			.calendar .months .prev-month,
			.calendar .months .next-month {
				color: #FFF;
				text-decoration: none;
				padding: 0 0.4em;
				-moz-border-radius: 4px;
				-webkit-border-radius: 4px;
				border-radius: 4px;
				cursor: pointer;
			}
			
			.calendar .months .prev-month:hover,
			.calendar .months .next-month:hover {
				background-color: #FDF5CE;
				color: #C77405;
			}
			
			.calendar table {
				border-collapse: collapse;
				padding: 0;
				font-size: 0.8em;
				width: 100%;
			}
			
			.calendar th {
				text-align: center;
			}
			
			.calendar td {
				text-align: right;
				padding: 1px;
				width: 14.3%;
			}
			
			.calendar td span {
				display: block;
				color: #1C94C4;
				background-color: #F6F6F6;
				border: 1px solid #CCC;
				text-decoration: none;
				padding: 0.2em;
				cursor: pointer;
			}
			
			.calendar td span:hover {
				color: #C77405;
				background-color: #FDF5CE;
				border: 1px solid #FBCB09;
			}
			
			.calendar td.today span {
				background-color: #FFF0A5;
				border: 1px solid #FED22F;
				color: #363636;
			}
        
 /* New Nav */
 /* fiddle */
/* Menu Styles */

.third-level-menu
{
    position: absolute;
    top: 0;
    right: -250px;
    width: 250px;
    list-style: none;
    padding: 0;
    margin: 0;
    display: none;
}

.third-level-menu > li
{
    height: 30px;
    background: #065186;
}
.third-level-menu > li:hover { background: #06436f; }

.second-level-menu
{
    position: absolute;
    top: 30px;
    left: 0;
    width: 250px;
    list-style: none;
    padding: 0;
    margin: 0;
    display: none;
}

.second-level-menu > li
{
    position: relative;
    height: 30px;
    background: #025d9e;
}
.second-level-menu > li:hover { background: #065186; }

.top-level-menu
{
    list-style: none;
    padding: 0;
    margin: 0;
}

.top-level-menu > li
{
    position: relative;
    float: left;
    height: 30px;
    width: 200px;
    background: #0070c0;
}
.top-level-menu > li:hover { background: #025d9e; }

.top-level-menu li:hover > ul
{
    /* On hover, display the next level's menu */
    display: inline;
    z-index:999;
}


/* Menu Link Styles */

.top-level-menu a /* Apply to all links inside the multi-level menu */
{
   
    color: #FFFFFF;
    text-decoration: none;
    padding: 0 0 0 10px;
    
    /* Make the link cover the entire list item-container */
    display: block;
    line-height: 30px;
}
.top-level-menu a:hover { color: white; }
 
 /* Line Seperator */
.line-separator{

height:1px;

background:#ebebeb;

border-bottom:1px solid #ebebeb;

width:750px;


}

//Audit PI calculator
#popupPIouter {
width:100%;
height:100%;
opacity:0.5;
top:0;
left:0;
display:none;
position:fixed;
background-color:#F8F8F8;
overflow:auto
}
img#close {
position:absolute;
right:1px;
top:1px;
cursor:pointer
}
div#popupPI {
position:fixed;
border:1px solid #383838;
background-color:#FFFFFF;
left:50%;
top:17%;
margin-left:-202px;
display:none;
font-family:'Raleway',sans-serif
}

p {
margin-top:30px
}

hr {
margin:10px -50px;
border:0;
border-top:1px solid #ccc
}

#days {
background-image:url(../images/name.jpg);
background-repeat:no-repeat;
background-position:5px 7px
}
#amount {
background-image:url(../images/email.png);
background-repeat:no-repeat;
background-position:5px 7px
}

#submit {
text-decoration:none;
width:30%;
text-align:center;
display:block;
background-color:#62ade2;
color:#fff;
border:1px solid #62ade2;
padding:5px 0;
font-size:12px;
cursor:pointer;
}

#cancel {
text-decoration:none;
width:20%;
text-align:center;
display:block;
background-color:#3d3d3d;
color:#fff;
border:1px solid #d9d9d9;
padding:5px 0;
font-size:12px;
cursor:pointer;
}

/* Form Table1 */
.metro .tableForm {
  width: 98%;
  margin-bottom: 14pt;
}
.metro .table th,
/* Table border removed and padding decr */
.metro .tableForm td {
  padding: 1px;
  font-size: 14px;
  line-height: 14pt;
  vertical-align: top;
  width:100px;
}
/* Form Table2 */
.metro .tableForm1 {
  width: 65%;
  margin-bottom: 14pt;
}
.metro .table th,
/* Table border removed and padding decr */
.metro .tableForm1 td {
  padding: 1px;
  font-size: 14px;
  line-height: 14pt;
  vertical-align: top;
  
}


/* Select Placeholder */
.s
{
    color:#ebebeb;
    font-size:0px;
    display:none;
}

.bread1
{
    color:#505050;
    font-size:12px;
   
}
.bread2
{
    color:#bbbbbb;
    font-size:12px;
    
}


/* datepicker css*/

/* Success div */
div.success {
	padding-left: 5px;
	padding-top: 5px;
	height: 30px; 
	background: #73AD21; 
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	color : white;
}