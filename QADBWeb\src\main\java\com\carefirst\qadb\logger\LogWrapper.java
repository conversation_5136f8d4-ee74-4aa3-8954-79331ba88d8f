package com.carefirst.qadb.logger;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.log4j.Logger;

/**
 * This class is the logger wrapper class for QADB application
 * 
 * <AUTHOR>
 * 
 *         ********** Modification History ********** Date: Modified By: Change
 *         Description:
 */
public class LogWrapper extends Logger{
	private Log log;
	private static Logger logger = null;
	public LogWrapper(String name) {
		super(name);
		log=LogFactory.getLog(name);
		logger = Logger.getLogger(name);
	}
	/*public static Logger getLogger(String loggerName) {

		logger = Logger.getLogger(loggerName);

		return logger;
	}*/

	public void info(Object message) {
		// StackTraceElement stackTraceElement []
		// =Thread.currentThread().getStackTrace();
		// System.out.println(stackTraceElement.length+"sfsd"+stackTraceElement[stackTraceElement.length-2].getMethodName());
		// log.info("This is being printed from a generic extended class");
		// if (log.isInfoEnabled()) {		
		log.info("[ " + Thread.currentThread().getId() + " ] "
				+ "; Session ID:" + LogContextManager.get().getSessionId()
				+ "; User ID:" + LogContextManager.get().getUserId() + "   "
				+ message);
		// }
	}

	public void debug(Object message) {
		// StackTraceElement stackTraceElement []
		// =Thread.currentThread().getStackTrace();
		// System.out.println(stackTraceElement.length+"sfsd"+stackTraceElement[stackTraceElement.length-2].getMethodName());
		// log.info("This is being printed from a generic extended class");
		// if (log.isDebugEnabled()) {
		logger.debug("[ Thread ID:" + Thread.currentThread().getId() + " ] "
				+ "; Session ID:" + LogContextManager.get().getSessionId()
				+ "; User ID:" + LogContextManager.get().getUserId() + "   "
				+ message);
		log.debug("[ " + Thread.currentThread().getId() + " ] "
				+ "; Session ID:" + LogContextManager.get().getSessionId()
				+ "; User ID:" + LogContextManager.get().getUserId() + "   "
				+ message);
		// }
	}

	public void error(Object message) {
		log.error("[ " + Thread.currentThread().getId() + " ] "
				+ "; Session ID:" + LogContextManager.get().getSessionId()
				+ "; User ID:" + LogContextManager.get().getUserId() + "   "
				+ message);
	}
}
