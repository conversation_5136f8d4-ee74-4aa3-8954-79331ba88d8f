<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.carefirst.websphere.portal</groupId>
		<artifactId>parent-portlet</artifactId>
		<version>2.0.0-SNAPSHOT</version>
	</parent>

	<groupId>carefirst.QADBWeb</groupId>
	<artifactId>QADBWeb</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>war</packaging>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>

			</plugin>
		</plugins>
	</build>

	<properties>
		<spring.version>3.0.5.RELEASE</spring.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>bundle</artifactId>
			<version>3.2.4.RELEASE</version>
			<type>pom</type>
		</dependency>
		<dependency>
			<!-- Websphere Version:[6.1, 7.0, 8.0, 8.5 RELEASE] -->
			<groupId>com.ibm.websphere.portal</groupId>
			<artifactId>runtimes</artifactId>
			<version>8.0</version>
			<type>pom</type>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>log4j</groupId>
			<artifactId>log4j</artifactId>
			<version>1.2.17</version>
		</dependency>
		<dependency>
			<groupId>com.carefirst.websphere.webservices.qadb</groupId>
			<artifactId>ClaimsQASamplingClient</artifactId>
			<version>1.0.5</version>
		</dependency>


		<dependency>
			<groupId>com.thoughtworks.xstream</groupId>
			<artifactId>xstream</artifactId>
			<version>1.4.7</version>
		</dependency>

		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>5.1.26</version>
		</dependency>
		<dependency>
			<groupId>ojdbc14</groupId>
			<artifactId>ojdbc14</artifactId>
			<version>10.2.0.4.0</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
			<version>3.2.4.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>net.sf.jasperreports</groupId>
			<artifactId>jasperreports</artifactId>
			<version>6.1.0</version>

		</dependency>
		<dependency>
			<groupId>net.sf.jasperreports</groupId>
			<artifactId>jasperreports-fonts</artifactId>
			<version>6.0.0</version>
		</dependency>
		
		<dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>3.6</version>
            <type>jar</type>
            <scope>compile</scope>
        </dependency>
		
		<!--  EAPM jars start -->

        <dependency>
            <groupId>javapasswordsdk</groupId>
            <artifactId>javapasswordsdk</artifactId>
            <version>1.0.0</version>
        </dependency>  
        <dependency>
           <groupId>com.carefirst.eapm.cyberArk</groupId>
           <artifactId>EAPMCyberArk</artifactId>
           <version>2.0</version>
        </dependency>
        <dependency>
		    <groupId>net.sf.jasperreports</groupId>
		    <artifactId>jasperreports-htmlcomponent</artifactId>
		    <version>5.0.1</version>
		</dependency>

	    <!-- EAPM jars end -->
	</dependencies>


</project>