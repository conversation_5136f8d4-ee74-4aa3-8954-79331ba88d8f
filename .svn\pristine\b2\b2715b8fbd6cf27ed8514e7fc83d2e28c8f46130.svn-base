<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib  prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<head>
<link href="webResources/css/qadb.css" rel="stylesheet">

<script type="text/javascript" src="webResources/js/metro.min.js"></script>
<!-- Local JavaScript -->

<link href="webResources/css/iconFont.css" rel="stylesheet">
<link href="webResources/css/docs.css" rel="stylesheet">
<link href="webResources/js/prettify/prettify.css" rel="stylesheet">

<!-- Load JavaScript Libraries -->
<script src="webResources/js/jquery/jquery.min.js"></script>
<script src="webResources/js/jquery/jquery.widget.min.js"></script>
<script src="webResources/js/jquery/jquery.dataTables.js"></script>
<style>
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
	margin-right: auto;
	height: 150%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}
.hidden
        {
            display: none;
        }
</style>



<title><spring:message code="admin.rootCause.title" /></title>
</head>
<body>
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container2">

		<!-- Sidebar Start-->
		<div id="left-sidebar">
			<h2 style="color: white;padding-top:32px"><spring:message code="admin.rootCause.edit.leftNav.heading1" /> </h2>
			<h3 style="color: white;padding-top:10px"><spring:message code="admin.rootCause.edit.leftNav.heading2" /> </h3>

		
		</div>
		<!-- Sidebar End-->

		<div id="content-container" style="padding-left: 40px">

			<form:form id="rootCauseSearchForm" name="rootCauseSearchForm" method="GET"
				commandName="rootCauseSearchForm" action="editRootCauseRes" style="width:700px">


				<div style="padding: 10px 0px 0px 0px;">
					<span class="bread1"><spring:message
							code="admin.rootCause.bread1" /> </span><span class="bread2"><spring:message
							code="admin.rootCause.edit.leftNav.heading1" />
					</span>
				</div>
				<br>
				<span style="color: #0070c0; font-size: 16px"><spring:message code="admin.rootCause.edit.rootCause" /></span>
				<div class="line-separator"></div>
				<br>

				<table class="tableForm">
					<tr>
						<td><spring:message code="admin.rootCause.edit.rootCause" /></td>
						<td><div class="input-control text">
								<input type="text" id="rootCauseName" name="rootCauseName" value="${rootCauseName }" />
							</div></td>
					</tr>
					<c:set var="userRole" > <%=request.getHeader("iv-groups")%> </c:set> 
						<c:if test="${(fn:contains(userRole, 'qadb-superadmin_users')) && (!(fn:contains(userRole, 'null')))}">
									<input type="hidden" name="userGrp" value="ALL">
						</c:if>
						<c:if test="${(fn:contains(userRole, 'qadb-samd-admin_users')) && (!(fn:contains(userRole, 'null')))}">
									<input type="hidden" name="userGrp" value="SAMMD">
						</c:if>
						<c:if test="${(fn:contains(userRole, 'qadb-cd-admin_users')) && (!(fn:contains(userRole, 'null')))}">
									<input type="hidden" name="userGrp" value="CD">
						</c:if>

				</table>
				

				<div style="width: 750px">
					<div style="float: right">
						<button type="button" onclick="clearText('rootCauseName')" class="button inverse"><spring:message code="qadb.reset" /></button>
						<button style="background-color: #298fd8"
							class="button default"> <spring:message code="qadb.search" /></button>
					</div>
				</div>

			</form:form>


			<c:if test="${null!=rootCauseRO }">
			<br> <br> <span style="color: #0070c0; font-size: 16px"> <spring:message code="qadb.searchResults" />
				</span> <br>
			<br>
			<div class="line-separator"></div>
			<table class="table striped hovered dataTable" id="searchDatatable"
				width="700px">
				<thead>
					<tr style="height: 30px">

						<td class="text-left" font-size="25px"></td>
						<td class="text-left" font-size="25px"><spring:message code="admin.rootCause.edit.name" /></td>
						<td class="text-left" font-size="25px"><spring:message code="admin.rootCause.edit.description" /></td>
						<td class="text-left" font-size="25px"><spring:message code="admin.rootCause.edit.status" /></td>
						


					</tr>
				</thead>

				<tbody>

					<c:forEach items="${rootCauseRO}" var="rs">
						<tr style="height: 30px">
							<td>${rs.rootCauseId}</td>
							<td>${rs.rootCauseName}</td>
							<td>${rs.rootCauseDescription}</td>
							<td>${rs.rootCauseStatus}</td>
						</tr>
					</c:forEach>

				</tbody>


			</table>
			</c:if>
			 <script>
				$(function() {
					$('#searchDatatable').dataTable({
										
										
					"columns" : [
					
					{
           
            sClass: "hidden"
        }, 
					{
					"render": function(data, type, full, meta) {
					/* return '<a href="'+full[0]+'"><u>'+data+'<u></a>'; */
					return '<a href="getRootCause?id='+full[0]+'"><u>'+data+'<u></a>';
                   
            }
            }, {}, {} ]
									});
				});
			</script> 

		</div>
	</div>

	<jsp:include page="footer.jsp"></jsp:include>

</body>

</html>
