<!DOCTYPE html>
<html>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib  prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<head>
<link href="webResources/css/qadb.css" rel="stylesheet">

<script type="text/javascript" src="webResources/js/qadb.js"></script>
<style>
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.hidden {
	display: none;
}

#popupAssociatesRecordsD {
	width: 100%;
	height: 100%;
	opacity: 0.5;
	top: 0;
	left: 0;
	display: none;
	position: fixed;
	background-color: #F8F8F8;
	overflow: auto;
	z-index: 1;
}

div#popupAssociatesD {
	position: fixed;
	border: 1px solid #383838;
	background-color: #FFFFFF;
	left: 55%;
	top: 40%;
	margin-left: -270px;
	display: none;
	z-index: 2;
	font-family: 'Raleway', sans-serif
}

#popupPerfGrpRecordsD {
	width: 100%;
	height: 100%;
	opacity: 0.5;
	top: 0;
	left: 0;
	display: none;
	position: fixed;
	background-color: #F8F8F8;
	overflow: auto;
	z-index: 1;
}

div#popupPerfGrpD {
	position: fixed;
	border: 1px solid #383838;
	background-color: #FFFFFF;
	left: 55%;
	top: 40%;
	margin-left: -270px;
	display: none;
	z-index: 2;
	font-family: 'Raleway', sans-serif
}

</style>

</head>



<body class="metro">
	<div class="top">
		<div class="container">
			<header class="margin5 nrm nlm">
				<div class="clearfix">
					<div class="place-right" style="height:40px">
						<form>
							<table>
								<tr>
									<td><img src="webResources/images/Misc/Img_User.png"
										class="shadow" align="left"></td>
									<td></td>
									<td></td>

									<td></td>
									<td></td>
									<td><a1>Welcome</a1>
										<p style="padding-left:0px" class="user"><%=request.getHeader("iv-user")%></p></td>
									<td></td>
									<td></td>
									</td>
									<td></td>
									<td></td>
									</td>
									<td></td>
									<td></td>
									</td>
									<td></td>
									<td></td>
									<td>
										<button formaction="logout" class="button inverse">Logout</button>
									</td>
								</tr>
							</table>
						</form>
					</div>
					<div class="place-left" style="height:45px;">
						<form style="padding-top:10px">
							<img style="height:30px;" src="webResources/images/Misc/logo.jpg" alt="CareFirst">

						</form>
					</div>
				</div>

			</header>
		</div>
	</div>
	<!-- Metro -->

	<div class="navc">
		<div class="container" style="padding: 0px 0px 0px 0px;">


			<nav class="navigation-bar dark">
				<nav class="navigation-bar-content">
				
					<c:set var="userRole" > <%=request.getHeader("iv-groups")%> </c:set> 
					<%-- <c:set var="userRole" > "Contractor","PeopleSoft_ELM_User","onestop_users","null" </c:set> --%>
					<%-- <c:set var="userRole" > "Contractor","picapp_users","PeopleSoft_ELM_User" </c:set>  --%>
					<ul class="top-level-menu">
					   <c:if test="${(fn:contains(userRole, 'qadb-samd-auditor_users'))||(fn:contains(userRole, 'qadb-cd-auditor_users'))||(fn:contains(userRole, 'qadb-samd-admin_users'))||(fn:contains(userRole, 'qadb-cd-admin_users'))||(fn:contains(userRole, 'qadb-superadmin_users'))}">
						<li><a href="#">Auditing <img
								style="float:right;padding-top: 4px;"
								src="webResources/images/Navbar/Icn_Down_Arrow.png"></a>
							<ul class="second-level-menu">
								<li><a href="audit">Add Audit</a></li> 
								<li><a href="searchBasic">Search For Audits</a></li>
							</ul>
						</li>
					  </c:if>
						<item class="element-divider"></item>

						<li><a href="#">Reports and Trends <img
								style="float:right;padding-top: 4px;"
								src="webResources/images/Navbar/Icn_Down_Arrow.png"></a>

							<ul class="second-level-menu">
								<li><a href="scores">Scores and Trends</a></li>
								
								<!-- Changes made to restrict the view for read-only users -->
								<c:if test="${(!fn:contains(userRole, 'qadb-samd-readonly_users'))&&(!fn:contains(userRole, 'qadb-cd-readonly_users'))}">
								
								<li><a href="userReports">User Reports </a></li>
								<li><a href="performanceReport">Performance Group
										Monthly Error Sheet</a></li>
								<li><a href="claimsAuditAssessmentReport">Claims Audit
										Assessment Report</a></li>

								<li><a href="editCodeReport">Edit Code Report</a></li>
								</c:if>
								<c:if test="${(fn:contains(userRole, 'qadb-samd-auditor_users'))||(fn:contains(userRole, 'qadb-cd-auditor_users'))||(fn:contains(userRole, 'qadb-samd-admin_users'))||(fn:contains(userRole, 'qadb-cd-admin_users'))||(fn:contains(userRole, 'qadb-superadmin_users'))}">
									<li><a href="inSampleOutOfSampleReport">In Sample-Out of Sample Report</a></li> 
								</c:if>								
							</ul></li>
					
					<c:if test="${(fn:contains(userRole, 'qadb-superadmin_users'))||(fn:contains(userRole, 'qadb-samd-admin_users'))||(fn:contains(userRole, 'qadb-cd-admin_user'))}">
					
						<item class="element-divider"></item>
						<li><a href="#">Administrative Options <img
								style="float:right;padding-top: 4px;"
								src="webResources/images/Navbar/Icn_Down_Arrow.png"></a>

							<ul class="second-level-menu">
								<li><a href="#">Associate <img style="float:right;"
										src="webResources/images/Navbar/Icn_Right_Arrow.png"></a>
									<ul class="third-level-menu">
										<li><a href="addAssociate">Add Associate</a></li>
										<li><a href="editAssociate">Edit Associates</a></li>
										<li><a href="#" onclick="divReport_show();"> List Of
												Associates</a></li>
									</ul></li>
								<li><a href="#">Operational unit <img
										style="float:right;"
										src="webResources/images/Navbar/Icn_Right_Arrow.png"></a>
									<ul class="third-level-menu">
										<li><a href="addOperationalUnit">Add Operational unit</a>
										</li>
										<li><a href="editOperationalUnit"> Edit Operational
												unit</a></li>
										<li><a href="listOfOperationalUnit"> List Of Operational Unit</a></li>

									</ul></li>
								<li><a href="#">Error Codes <img style="float:right;"
										src="webResources/images/Navbar/Icn_Right_Arrow.png"></a>
									<ul class="third-level-menu">
										<li><a href="addErrorCodes">Add Error Codes</a></li>
										<li><a href="editErrorCodes">Edit Error Codes</a></li>
										<li><a href="listOfErrorCode"> List Of
												Error Codes</a></li>
									</ul></li>
								<li><a href="#">Performance Group <img
										style="float:right;"
										src="webResources/images/Navbar/Icn_Right_Arrow.png"></a>
									<ul class="third-level-menu">
										<li><a href="addPerformanceGroup">Add Performance
												Group</a></li>
										<li><a href="editPerformanceGroup">Edit Performance
												Group</a></li>
										<li><a href="#" onclick="divPerfGrpReport_show();"> List Of
												Performance Groups</a></li>
									</ul></li>
								<li><a href="#">Root Cause <img style="float:right;"
										src="webResources/images/Navbar/Icn_Right_Arrow.png"></a>
									<ul class="third-level-menu">
										<li><a href="addRootCause">Add Root Cause</a></li>
										<li><a href="editRootCause">Edit Root Cause</a></li>
										<li><a href="listOfRootCause"> List Of
												Root Cause</a></li>
									</ul></li>
								<li><a href="#">Mapping <img style="float:right;"
										src="webResources/images/Navbar/Icn_Right_Arrow.png"></a>
									<ul class="third-level-menu">
										<li><a href="addMapping">Add New Mapping</a></li>
										<li><a href="editMapping">Edit Mapping</a></li>
									</ul></li>
								<li><a href="addJobTitles">Manage Job Titles</a>
								</li>
								<li><a href="addSpecialty">Manage Specialty</a>
								</li>
							</ul></li>
						
						</c:if>
						
					</ul>
					<a href="/QADBWeb/"
						style="float: right; color: white;padding-right: 15px;"><img
						src="webResources/images/Navbar/home2.png" style="border: none;">Home</a>
				</nav>
			</nav>

		</div>
	</div>


	<div id="popupAssociatesRecordsD">
		<!-- popup claim records Starts Here -->


	</div>
	<div id="popupAssociatesD"
		style="text-align: center; padding: 25px 25px 5px 25px;box-shadow: 5px 5px 5px #888888;
	border-radius: 5px;">
		<img id="close" src="webResources/images/Misc/Icn_Close2.png"
			onclick="divReport_hide()"> <span style="font-size: 15px;"><b>
				Please select auditing status for List of associates</b> </span> <br> <br>
		<div align="center" style="width: 450">
			<form:form id="associateListForm" name="associateListForm"
				method="GET" commandName="associateListForm"
				action="ListOfAssociates" style="width:100%;">
				<table>
					<tr>
						<td>Status</td>
						<td>
							<div class="input-control radio default-style margin10">
								<label> <input type="radio" name="auditingStatus"
									value="Y" checked="checked" /> <span class="check"></span>
									Enabled
								</label>

							</div>
							<div class="input-control radio default-style margin10">
								<label> <input type="radio" name="auditingStatus"
									value="N" /> <span class="check"></span> Disabled
								</label>

							</div>
							<div class="input-control radio default-style margin10">
								<label> <input type="radio" name="auditingStatus"
									value="A" /> <span class="check"></span> All
								</label>

							</div>


						</td>
					</tr>


					<tr>
						<td></td>
						<td style="padding:10px 0px 0px 155px"><a 
							onclick="divReport_hide();">
								<button class="button inverse" type="button">Cancel</button>
						</a>
							<button style="background-color: #298fd8" type="submit"
								class="button default">Generate</button> </td>
					</tr>

				</table>
			</form:form>
		</div>
	</div>
	
	<!-- For Performance Group -->
	
	<div id="popupPerfGrpRecordsD">
		<!-- popup claim records Starts Here -->


	</div>
	<div id="popupPerfGrpD"
		style="text-align: center; padding: 25px 25px 5px 25px;box-shadow: 5px 5px 5px #888888;
	border-radius: 5px;">
		<img id="close" src="webResources/images/Misc/Icn_Close2.png"
			onclick="divPerfGrpReport_hide()"> <span style="font-size: 15px;"><b>
				Please select Group Type for List of Performance Group</b> </span> <br> <br>
		<div align="center" style="width: 450">
			<form:form id="associateListForm" name="associateListForm"
				method="GET" commandName="associateListForm"
				action="listOfPerformanceGroup" style="width:100%;">
				<table>
					<tr>
						<td>Type</td>
						<td>
							<div class="input-control radio default-style margin10">
								<label> <input type="radio" name="auditingStatus"
									value="P" checked="checked" /> <span class="check"></span>
									Primary
								</label>

							</div>
							<div class="input-control radio default-style margin10">
								<label> <input type="radio" name="auditingStatus"
									value="S" /> <span class="check"></span> Secondary
								</label>

							</div>
							<!-- <div class="input-control radio default-style margin10">
								<label> <input type="radio" name="auditingStatus"
									value="A" /> <span class="check"></span> All
								</label>

							</div>-->


						</td>
					</tr>


					<tr>
						<td></td>
						<td style="padding:10px 0px 0px 155px"><a 
							onclick="divPerfGrpReport_hide();">
								<button class="button inverse" type="button">Cancel</button>
						</a>
							<button style="background-color: #298fd8" type="submit"
								class="button default">Generate</button> </td>
					</tr>

				</table>
			</form:form>
		</div>
	</div>

</body>
</html>
