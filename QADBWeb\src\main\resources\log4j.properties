## Set root logger level to DEBUG .
log4j.rootLogger=INFO

log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target=System.out
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n

## ReferralReDesign portlet logs
log4j.logger.QADBWebAppLogger=INFO, QADBWebAppLog
log4j.appender.QADBWebAppLog=org.apache.log4j.RollingFileAppender
log4j.appender.QADBWebAppLog.File=QADBWebApp.log
 log4j.appender.QADBWebAppLog.MaxFileSize=50MB
 log4j.appender.QADBWebAppLog.MaxBackupIndex=10
log4j.appender.QADBWebAppLog.layout=org.apache.log4j.PatternLayout
log4j.appender.QADBWebAppLog.layout.ConversionPattern=%d %-5p [%t] (%F:%L) - %m%n
	