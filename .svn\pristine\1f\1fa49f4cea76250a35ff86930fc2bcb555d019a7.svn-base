package com.carefirst.audit.model;


public class AuditCounts {
	
	
	private String facetsId;
	private String inpProccessDate;
	private String insCount;
	private String insNonMockCount;
	private String insMockCount;
	private String insPrePayCount;
	private String insPostPayCount;
	private String oosCount;
	private String oosNonMockCount;
	private String oosMockCount;
	private String oosPrePayCount;
	private String oosPostPayCount;
	private String totalAuditCount;
	private String processDate;
	private String associateName;
	private String statusCode;
	private String statusMsg;
	
	
	
	public String getFacetsId() {
		return facetsId;
	}
	public void setFacetsId(String facetsId) {
		this.facetsId = facetsId;
	}
	public String getInpProccessDate() {
		return inpProccessDate;
	}
	public void setInpProccessDate(String inpProccessDate) {
		this.inpProccessDate = inpProccessDate;
	}
	public String getProcessDate() {
		return processDate;
	}
	public void setProcessDate(String processDate) {
		this.processDate = processDate;
	}
	public String getAssociateName() {
		return associateName;
	}
	public void setAssociateName(String associateName) {
		this.associateName = associateName;
	}
	public String getInsCount() {
		return insCount;
	}
	public void setInsCount(String insCount) {
		this.insCount = insCount;
	}
	public String getInsNonMockCount() {
		return insNonMockCount;
	}
	public void setInsNonMockCount(String insNonMockCount) {
		this.insNonMockCount = insNonMockCount;
	}
	public String getInsMockCount() {
		return insMockCount;
	}
	public void setInsMockCount(String insMockCount) {
		this.insMockCount = insMockCount;
	}
	public String getInsPrePayCount() {
		return insPrePayCount;
	}
	public void setInsPrePayCount(String insPrePayCount) {
		this.insPrePayCount = insPrePayCount;
	}
	public String getInsPostPayCount() {
		return insPostPayCount;
	}
	public void setInsPostPayCount(String insPostPayCount) {
		this.insPostPayCount = insPostPayCount;
	}
	public String getOosCount() {
		return oosCount;
	}
	public void setOosCount(String oosCount) {
		this.oosCount = oosCount;
	}
	public String getOosNonMockCount() {
		return oosNonMockCount;
	}
	public void setOosNonMockCount(String oosNonMockCount) {
		this.oosNonMockCount = oosNonMockCount;
	}
	public String getOosMockCount() {
		return oosMockCount;
	}
	public void setOosMockCount(String oosMockCount) {
		this.oosMockCount = oosMockCount;
	}
	public String getOosPrePayCount() {
		return oosPrePayCount;
	}
	public void setOosPrePayCount(String oosPrePayCount) {
		this.oosPrePayCount = oosPrePayCount;
	}
	public String getOosPostPayCount() {
		return oosPostPayCount;
	}
	public void setOosPostPayCount(String oosPostPayCount) {
		this.oosPostPayCount = oosPostPayCount;
	}
	public String getTotalAuditCount() {
		return totalAuditCount;
	}
	public void setTotalAuditCount(String totalAuditCount) {
		this.totalAuditCount = totalAuditCount;
	}
	public String getStatusCode() {
		return statusCode;
	}
	public void setStatusCode(String statusCode) {
		this.statusCode = statusCode;
	}
	public String getStatusMsg() {
		return statusMsg;
	}
	public void setStatusMsg(String statusMsg) {
		this.statusMsg = statusMsg;
	}
	
	
	

}
