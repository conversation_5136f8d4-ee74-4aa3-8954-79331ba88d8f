<!DOCTYPE HTML><%@page language="java"
	contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<html>
<head>
<title>pg_monthly_error_sheet</title>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
</head>
<body>
	<div id="outer-container">
		<div id="menu1">
		 	<jsp:include page="../jsp/header.jsp"></jsp:include>
		</div>
		<div style="clear: both">	
    	</div>
		<div id="left-sidebar">
        	<h2>Performance Group Monthly Error Sheet</h2>
        	<h3>Generate performance group monthly error sheet for a given time period.</h3>
    	</div>
    	<div id="content-container">
    		Performance Group Monthly Error Sheet
    		<h2>Report Parameters</h2>
    		
    			
						From
						<input size="2" name="month_pg_monthly_from">
						<input size="2" name="day_pg_monthly_from">
						<input size="4" name="yr_pg_monthly_from">
						<button style="border: none; background-color: transparent;">
								<img alt="Calender" src="../images/Forms/Icn_Date_Picker.png">
							</button><br>
						
					
					
						To
						<input size="2" name="month_pg_monthly_to">
						<input size="2" name="day_pg_monthly_to">
						<input size="4" name="yr_pg_monthly_to">
						<button style="border: none; background-color: transparent;">
								<img alt="Calender" src="../images/Forms/Icn_Date_Picker.png">
							</button><br>
						
					
					
						Primary PGA/ Special Audits
						<select id="primary_pga" >
  							<option value="value">Value 1</option>
        					<option value="value">Value 2</option>
    						<option value="value">Value 3</option>
  							<option value="value">Value 4</option>
  						</select><br>
					
					
						Secondary PGA/ Special Audits
						<select id="secondary_pga" >
  							<option value="value">Value 1</option>
        					<option value="value">Value 2</option>
    						<option value="value">Value 3</option>
  							<option value="value">Value 4</option>
  						</select><br>
					
    		
    		<div style="background-color: #dddfe1; float: none; padding: 5px;">
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
				<button style="background-color: #505050; border: 1px; color: #ffffff">Reset</button>
				<button style="background-color: #298fd8; border: 1px; color: #ffffff">Generate</button>
			</div>
    	</div>
    
    	<div id="right-sidebar">
        <!-- right content -->
    	</div>
    	<div style="clear: both">	
    	</div>
	
	
		 <jsp:include page="../jsp/footer.jsp" ></jsp:include>
	
		
	</div>
</body>
</html>