package com.carefirst.qadb.dao;

import java.sql.SQLException;
import java.util.List;

import com.carefirst.audit.model.PerformanceGroup;

import net.sf.jasperreports.engine.JRDataSource;

public interface PerformanceGroupDAO {
	public PerformanceGroup savePerformanceGroup(PerformanceGroup performanceGroup);
	public List<PerformanceGroup> searchperformanceGroup(PerformanceGroup performanceGroup) throws SQLException;
	public PerformanceGroup getPerformanceGroupData(PerformanceGroup performanceGroupTO)throws SQLException;
	public JRDataSource searchperformanceGroupList(PerformanceGroup performanceGroup) throws SQLException;
}
