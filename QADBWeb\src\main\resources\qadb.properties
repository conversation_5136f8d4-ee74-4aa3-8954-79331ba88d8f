#Start QADB

#New Audit page start
QADB.title=QADB
QADB.header=QADB Header;
audit.new.leftNav.title=Audit_Left
audit.new.leftNav.heading=New Audit
audit.new.leftNav.text=Create a new audit. Enter  DCN number to begin.
audit.new.leftNav.heading1=General Info
audit.new.leftNav.heading2=Errors
audit.new.bread1=Auditing

#New Audit page - General Info tab start
audit.new.associate=Associate
audit.new.associate.id=Associate ID
audit.new.associate.supervisor=Supervisor
audit.new.associate.manager=Manager
audit.new.associate.director=Director

audit.new.claim.details=Claim Details
audit.new.claim.dcn=DCN
audit.new.claim.number=Claim Number
audit.new.claim.type=Claim Type
audit.new.claim.lob=LOB
audit.new.claim.member.id=Member ID
audit.new.claim.process.date=Process Date
audit.new.claim.paid.date=Paid Date
audit.new.claim.total.charge=Total Charge
audit.new.claim.paid=Paid

audit.new.monetary.info=Monetary Info
audit.new.monetary.error=Monetary Error
audit.new.monetary.error.overpaid=OverPaid 
audit.new.monetary.error.underpaid=UnderPaid
audit.new.monetary.error.none=None
audit.new.monetary.amount=Amount
audit.new.monetary.theoretical.paid=Theoretical Paid
audit.new.monetary.penalty.interest=Penalty Interest
audit.new.monetary.penalty.interest.type=Penalty Interest <p>Type</p>
audit.new.monetary.calculate.pi=Calculate PI
audit.new.monetary.calculate=Calculate

audit.new.details=Audit Details
audit.new.type=Audit Type
audit.new.select.type=Select Audit Type 
audit.new.primary.pga.special.audits=Primary PGA/<p>Special Audits</p>
audit.new.select.primary.pga=Select Primary PGA
audit.new.secondary.pga.special.audits=Secondary PGA/<p>Special Audits</p>
audit.new.select.secondary.pga=Select Secondary PGA
audit.new.mock=Mock
audit.new.non.mock=Non-Mock
audit.new.oos=OOS
audit.new.oos.in.sample=In Sample
audit.new.oos.out.sample=Out Of Sample
audit.new.e2e=E2E
audit.new.non.e2e=Non-E2E
audit.new.risk.account=Risk Account
audit.new.risk.account.yes=Yes
audit.new.risk.account.no=No
audit.new.fyi =FYI/HIDO Review Notes
audit.new.process.type=Process Type
audit.new.select.process.type=Select Process Type
audit.new.platform=Platform 
audit.new.platform.facetslegacy=Facets Legacy 
audit.new.platform.facetsg6=Facets G6 

audit.new.dcn.records.header=Records for DCN
audit.new.dcn.records.column2=Time
audit.new.dcn.records.column3=Date
audit.new.dcn.records.column4=Status
audit.new.dcn.records.previous=Previous
audit.new.dcn.records.next=Next

audit.new.calculate.penalty.header=Calculate Penalty Interest Rate
audit.new.calculate.penalty.jurisdiction=Jurisdiction
audit.new.calculate.penalty.product=Product
audit.new.calculate.penalty.interest.from=Interest From Date
audit.new.calculate.penalty.interest.to=Interest To Date
audit.new.calculate.penalty.interest.days=Days Of Interest
audit.new.calculate.penalty.interest.amount=Amount to which Interest is Applicable
audit.new.calculate.penalty.ok=Ok
audit.new.calculate.penalty.cancel=Cancel

audit.new.added.success.message="New Audit added Successfully \! " 
audit.new.updated.success.message="Audit Updated Successfully ! "
audit.new.deleted.success.message="Audit Deleted Successfully ! "
audit.new.high.dollar.message=$$ High Dollar !

audit.new.popup.count.message1=For the process month of
audit.new.popup.count.message2=has :
audit.new.popup.count.in.sample.audits=In-Sample Audits
audit.new.popup.count.out.sample.audits=Out-of-Sample Audits
audit.new.popup.count.total.audits=Total Audits
audit.new.popup.count.ok=OK
#New Audit page - General Info tab end

#New Audit page - Error tab start
audit.new.error.information=Error Information
audit.new.error.code=Error Code
audit.new.error.monetary=Monetary
audit.new.error.procedural=Procedural
audit.new.error.specialty=Specialty
audit.new.error.root.cause=Root Cause
audit.new.error.edit.code=Edit Code
audit.new.error.select=Select
audit.new.error.selectOne=Select
audit.new.error.y=Y
audit.new.error.n=N
audit.new.error.add.row=Add Row

audit.new.claim.error.type=Error Type
audit.new.claim.sop=SOP / NewsFlash<p> Reference</p>
audit.new.claim.sop.placeholder=SOP/NewsFlash Reference
audit.new.claim.reason=Reason for Error
audit.new.claim.reason.text=<span style="font-size:11px">(prints on audit sheet)
audit.new.claim.comments=Internal<p>Comments</p>
audit.new.claim.comments.text=<span style="font-size:11px">( Doesn't prints on audit <p>sheet)</p>

audit.new.adjustments=Adjustments
audit.new.adjustments.required=Required
audit.new.adjustments.completed=Completed
audit.new.adjustments.date.adjusted=Date Adjusted
audit.new.adjustments.appeal=Appeal

audit.new.delete.message=Are you sure to delete this audit?
#New Audit page - Error tab end
#New Audit page end

#Edit Audit page - Statistics tab start
audit.edit.leftNav.heading=DCN #700170041
audit.edit.leftNav.heading1=Statistics
audit.edit.leftNav.text=Saved By
audit.edit.header=DCN \# 
audit.edit.bread1=Audits
audit.edit.created=Created By
audit.edit.history=History
audit.edit.history.column1=Updated By
#Edit Audit page - Statistics tab 

#Search Audit page start
audit.search.title=Audit-Search
audit.search.leftNav.heading=Search Audits
audit.search.leftNav.text=Search for an audit record. Use the basic or advanced mode to search for records.
audit.search.leftNav.heading1=Basic Search
audit.search.leftNav.heading2=Advanced Search

#Search Audit page - Basic Search tab start
audit.search.associate.name= Associate Name
audit.search.select.associate= Select Associate
audit.search.employee=Employee #
audit.search.select.employee= Select Employee #
audit.search.reset=Reset
audit.search.search=Search
audit.search.results=Search Results
audit.search.results.audit.id=Audit ID
audit.search.results.processed.date=Processed Date
audit.search.results.auditor=Auditor
audit.search.results.audit.date=Audit Date
#Search Audit page - Basic Search tab end

#Search Audit page - Advanced Search tab start
audit.search.errors.title=Audit-Errors
audit.search.processed.date.from=From
audit.search.processed.date.to=To
audit.search.auditor.name=Name
audit.search.select.auditor= Select Auditor
audit.search.miscellaneous.filters=Miscellaneous Filters
audit.search.select.error.code=Select Error Code
audit.search.select.pi.type=Select PI Type
audit.search.sample=Sample
audit.search.select.sample.type=Select Sample Type
audit.search.platform=Platform
audit.search.select.platform.type=Select Platform Type
audit.search.select.e2e.type=Select E2E Type
#Search Audit page - Advanced Search tab end

#Search Audit page end

#Reports Scores start
reports.bread1=Reports & Trends >
reports.scores.leftNav.heading1=Scores & Trends
reports.scores.leftNav.heading2=View data for a given time period by Associate, Supervisor, Manager, Director, Division, Primary Performance Group or Secondary Performance Group
reports.scores.subjectOfReport=Subject of Report
reports.scores.subjectOfReport.subject=Subject
reports.scores.subjectOfReport.Name=Name
reports.scores.reportType=Report Type
reports.scores.reportType.type=Type
reports.scores.reportType.subType=Sub-Type
reports.scores.reportType.e2e=E2E/Non-E2E/All
reports.scores.timePeriod = Time Period
reports.scores.timePeriod.use=Use
reports.scores.timePeriod.use.timeFrame=Time Frame
reports.scores.timePeriod.use.date=Date Range
reports.scores.timePeriod.period=Period
reports.scores.timePeriod.from=From
reports.scores.timePeriod.to=To
reports.scores.statusHeader=Status (All/Enabled/Disabled)
reports.scores.status=Status

#Reports Scores end

#Reports User Report start
reports.user.leftNav.heading1=User Reports
reports.user.leftNav.heading2=Generate list of Error Codes, Processors, Supervisors or view Adjustments Required Report.
reports.user.adjustmentReport=Adjustments Required Report
reports.user.adjustmentReport.name=Auditor
reports.user.adjustmentReport.from=From
reports.user.adjustmentReport.to=To
reports.user.otherReport=Other Reports
reports.user.otherReport.reportType=Report Type
reports.user.otherReport.reportType.errorCodes=Error Codes
reports.user.otherReport.reportType.currentProcessors=Current Processors
reports.user.otherReport.reportType.supervisors=List of Supervisors
reports.user.otherReport.status=Status
#Reports User Report end

#Reports Performance Error Sheet start
reports.performance.title =Reports-Performance Group Monthly Error Sheet
reports.performance.leftNav.heading1=Performance Group Monthly Error Sheet
reports.performance.leftNav.heading2=Generate performance group monthly error sheet for a given time period.
reports.performance.reportParameters=Report Parameters
reports.performance.month=Month
reports.performance.year=Year
reports.performance.groupType=Group Type
reports.performance.groupName=Group Name
#Reports Performance Error Sheet end

#Reports claims audit assessment start
reports.claimsAuditAssessment.title = Reports-Claims Audit Assessment Report
reports.claimsAuditAssessment.leftNav.heading1= Claims Audit Assessment Report
reports.claimsAuditAssessment.leftNav.heading2=Generate Claims Audit Assessment report for a given time period.
#Reports claims audit assessment end

#Reports edit code start
reports.editCode.title = Reports-Edit Code Report
reports.editCode.leftNav.heading1=Edit Code Report
reports.editCode.leftNav.heading2=Generate edit code report for a given time period.
reports.editCode.reportParameters=Report Parameters
reports.editCode.timeFrame =Processed Date
reports.editCode.from=From
reports.editCode.to=To
reports.editCode.errorCode =Error Code
reports.editCode.unitManager =Unit Manager
#Reports edit code end

#Reports InSample Out Of Sample Report start
reports.isOosReport.title =Reports-In Sample-Out Of Sample Report 
reports.isOosReport.leftNav.heading1=In Sample-Out Of Sample Report
reports.isOosReport.leftNav.heading2=Generate In Sample-Out Of Sample Report for a given time period.
#Reports InSample Out Of Sample Report end

#Admin Associate Start
admin.associate.title = Admin - Associate
admin.associate.leftNav.heading1 = Add Associate
admin.associate.leftNav.heading2 = Add new associate. Enter details and click on save button to create associate.
admin.associate.bread1 = Administrative Options >
admin.associate.name = Name
admin.associate.name.first = First
admin.associate.name.middle = Middle
admin.associate.name.last = Last
admin.associate.job	= Job
admin.associate.job.title = Title
admin.associate.job.dateOfHire = Date of Hire
admin.associate.office = Office
admin.associate.office.location = Location
admin.associate.office.workPhone = Work Phone  
admin.associate.workUnit = Work Unit
admin.associate.workUnit.id = Unit ID
admin.associate.workUnit.startDate = Start Date
admin.associate.workUnit.endDate = End Date
admin.associate.database = User ID
admin.associate.database.facetsId = FACETS ID
admin.associate.auditingstatus = Auditing Status 
admin.associate.auditingstatus.enabled = Enabled
admin.associate.auditingstatus.disabled = Disabled
admin.associate.auditingstatus.disableDate = Disabled Date
admin.associate.other = Other
admin.associate.other.comments = Notes / Comments
admin.associate.edit.header1 = Edit Associates
admin.associate.edit.header2 = Search for an associate record and select to edit.
admin.associate.edit.bread2 = Edit Associates
admin.associate.edit.associate = Associate
admin.associate.edit.associateId = Associate ID
admin.associate.edit.status = Status
admin.associate.edit.action = Action
admin.associate.update.bread = Associates >  
#Admin Associate end

#Admin Op Unit Start
admin.opUnit.title = Admin - Operational Unit
admin.opUnit.leftNav.heading1 = Add Operational Unit
admin.opUnit.leftNav.heading2 = Add new operational unit. Enter details and click on save button to create operational unit.
admin.opUnit.generalInformation = General Information
admin.opUnit.generalInformation.name = Name 
admin.opUnit.generalInformation.unit.name =Unit Name 
admin.opUnit.generalInformation.enabled = Enabled
admin.opUnit.effectiveDate = Effective Time Frame
admin.opUnit.effectiveDate.start = Start Date 
admin.opUnit.effectiveDate.end = End Date 
admin.opUnit.organization = Organization 
admin.opUnit.organization.operation = Operation 
admin.opUnit.organization.division = Division 
admin.opUnit.organization.director = Director 
admin.opUnit.organization.manager = Manager 
admin.opUnit.organization.supervisor = Supervisor 
admin.opUnit.office = Office 
admin.opUnit.location = Location 
admin.opUnit.edit.leftNav.heading1 = Edit Operational Units
admin.opUnit.edit.leftNav.heading2 = Search for an operational unit and select to edit.
admin.opUnit.edit.bread2 = Edit Operational Unit 
admin.opUnit.edit.operationalUnit = Operational Unit
admin.opUnit.edit.operationalUnit.unitId = Unit ID
admin.opUnit.edit.status = Status
admin.opUnit.update.leftNav.tag1 = General
admin.opUnit.update.leftNav.tag2 = Associates
admin.opUnit.update.bread2 = Operational Units >
#Admin Op Unit end 


#Admin ErrorCodes Start
admin.errorCodes.title = Admin - Error Code
admin.errorCodes.leftNav.heading1 = Add Error Code
admin.errorCodes.leftNav.heading2 = Add a new error code. Enter details and click on save icon.
admin.errorCodes.bread1 = Administrative Options >
admin.errorCodes.errorDetails = Error Details
admin.errorCodes.name = Name
admin.errorCodes.error = Error Code #
admin.errorCodes.error.name = Error Name
admin.errorCodes.errorDescription = Error Description
admin.errorCodes.effective = Effective Time Frame
admin.errorCodes.start = Start Date 
admin.errorCodes.end = End Date 
admin.errorCodes.edit.leftNav.heading1 = Edit Error Code
admin.errorCodes.edit.leftNav.heading2 = Search for an error code and select to edit.
admin.errorCodes.edit.errorCode = Error Code
admin.errorCodes.edit.description = Description
admin.errorCodes.edit.status = Status
admin.errorCodes.update.leftNav.heading2 = Edit error code. Make the changes and click on save icon.
admin.errorCodes.update.bread2 = Error Codes >
admin.errorCodes.update.enabled = Enabled
#Admin ErrorCodes end 


#Admin Performance Group Start
admin.perfGroup.title = Admin - Performance Group
admin.perfGroup.leftNav.heading1 = Add Performance Group
admin.perfGroup.leftNav.heading2 = Add a new performance group. Enter details and click on save icon.
admin.perfGroup.bread1 = Administrative Options >
admin.perfGroup.groupDetails = Group Details
admin.perfGroup.groupDetails.type = Type
admin.perfGroup.groupDetails.primary = Primary
admin.perfGroup.groupDetails.secondary = Secondary
admin.perfGroup.groupDetails.groupId = Group ID
admin.perfGroup.groupDetails.groupName = Group Name
admin.perfGroup.groupDetails.priPGAMapping = Primary PGA Mapping
admin.perfGroup.effectiveTimeFrame = Effective Time Frame
admin.perfGroup.effectiveTimeFrom = Start Date
admin.perfGroup.effectiveTimeTo = End Date
admin.perfGroup.procedural = Procedural Accuracy Guidelines
admin.perfGroup.expected = Expected Accuracy (%)
admin.perfGroup.penalty = Monthly Penalty ($)
admin.perfGroup.dollarFrequency = Dollar Frequency Guidelines
admin.perfGroup.dollarAccuracy = Dollar Accuracy Guidelines
admin.perfGroup.edit.leftNav.heading1 = Edit Performance Group
admin.perfGroup.edit.leftNav.heading2 = Search for a performance group and select to edit.
admin.perfGroup.edit.group = Group
admin.perfGroup.edit.id = Id
admin.perfGroup.edit.status = Status
admin.perfGroup.update.bread2 = Performance Groups 
admin.perfGroup.update.disable = Disable
admin.perfGroup.update.from = Start Date 
admin.perfGroup.update.to = End Date 
#Admin Performance Group end 

#Admin RootCause Start
admin.rootCause.title = Admin - Root Cause
admin.rootCause.leftNav.heading1 = Add Root Cause
admin.rootCause.leftNav.heading2 = Add a new root cause. Enter details and click on save icon.
admin.rootCause.bread1 = Administrative Options >
admin.rootCause.rootCauseDetails = Root Cause Details
admin.rootCause.rootCauseDetails.name = Name
admin.rootCause.rootCauseDetails.rootCauseDescription = Root Cause Description
admin.rootCause.rootCauseDetails.errorTypeMapping = Error Type Mapping
admin.rootCause.applicableTo = Applicable To 
admin.rootCause.applicableTo.type =Report Type
admin.rootCause.applicableTo.applicable = Applicable
admin.rootCause.applicableTo.associate = Associate 
admin.rootCause.applicableTo.supervisor = Supervisor
admin.rootCause.applicableTo.manager = Manager
admin.rootCause.applicableTo.director = Director
admin.rootCause.applicableTo.division = Division
admin.rootCause.applicableTo.priPG = Primary Performance Group
admin.rootCause.applicableTo.secPG = Secondary Performance Group
admin.rootCause.effective = Effective Time Frame
admin.rootCause.effective.start = Start Date
admin.rootCause.effective.end = End Date 
admin.rootCause.edit.leftNav.heading1 = Edit Root Cause
admin.rootCause.edit.leftNav.heading2 = Search for a root cause and select to edit.
admin.rootCause.edit.rootCause = Root Cause
admin.rootCause.edit.name = Name
admin.rootCause.edit.description = Description
admin.rootCause.edit.status = Status
admin.rootCause.update.leftNav.heading2 = Edit error code. Make the changes and click on save icon.
admin.rootCause.update.bread2 = Root Cause >
admin.rootCause.update.disable = Disable
admin.rootCause.update.disable.from = From
admin.rootCause.update.disable.to = To
#Admin RootCause end



#Admin Job Title Start
admin.jobTitle.title = Admin - Job Title
admin.jobTitle.leftNav.heading1 = Manage Job Titles
admin.jobTitle.leftNav.heading2 =Add new job title or edit an existing job title
admin.jobTitle.bread1 = Administrative Options >
admin.jobTitle.addJobTitles = Add Job Title
admin.jobTitle.updateJobTitles = Update Job Title
admin.jobTitle.titleName = Title
admin.jobTitle.titleId = Title Id
admin.jobTitle.jobTitles = List of Job Titles
admin.jobTitle.enabled = Enabled
admin.jobTitle.status = Status
#Admin Job Title end

#Admin Specialty Start
admin.specialty.title = Admin - Specialty
admin.specialty.leftNav.heading1 = Manage Specialty
admin.specialty.leftNav.heading2 = Add new specialty or edit an existing specialty
admin.specialty.bread1 = Administrative Options >
admin.specialty.addSpecialty = Add Specialty
admin.specialty.updateSpecialty = Update Specialty
admin.specialty.specialty = Specialty
admin.specialty.id = Id
admin.specialty.specialties = List of Specialties
admin.specialty.enabled = Enabled
admin.specialty.status = Status
#Admin Specialty end

#Admin Mapping Start
admin.mapping.title = Admin - Mapping
admin.mapping.leftNav.heading1 = Add New Mapping
admin.mapping.leftNav.heading2 = Add a new mapping. Enter details and click on save icon.
admin.mapping.bread1 = Administrative Options >
admin.mapping.details = Details
admin.mapping.auditor = Auditor
admin.mapping.associates = Associates
admin.mapping.timeframe = Effective Time frame
admin.mapping.month = Month
admin.mapping.year = Year
admin.mapping.edit.leftNav.heading1 = Edit Mapping
admin.mapping.edit.leftNav.heading2 = Search for auditor,month or year and select auditor to edit mapping.
admin.mapping.update.leftNav.heading2 = Edit mapping. Make the changes and click on save icon.
admin.mapping.update.bread2 = Mapping >
admin.mapping.update.enabled = Enabled
#Admin Mapping end 

#Admin Op Unit Start

#Admin Op Unit end

#Additional Claim Details Start
claim.details.subscriberCK=Subscriber CK :
claim.details.accountID=Account ID :
claim.details.accountName=Account Name :
claim.details.cjaJurisdiction=Jurisdiction :
claim.details.sbu=Group :
claim.details.productID=Product ID :
claim.details.adjustmentReasoncode=Adjustment Reason Code :
claim.details.claimCurrentstatus=Claim Current Status :
claim.details.bsbsCode=BSBS Code
claim.details.groupID=Group ID
claim.details.productLine=Product Line
claim.details.productDescription=Product Description
#Additional Claim End

#General
qadb.backToTop = Back To Top
qadb.searchResults = Search Results
qadb.search = Search
qadb.reset = Reset
qadb.search.message=Please enter associate details or claim number to search.
qadb.admin.area= Area
qadb.admin.area.sammd = SAMMD
qadb.admin.area.cd = CD

checkBoxTicked=classpath\:check2.png
checkBoxUnticked=classpath\:check1.png
