package com.carefirst.audit.model;


public class SearchCriteria{
    
   
	private String searchType;
	private String pageType;
	private String associate;
	private String employeeNumber;
	private String DCN ; //Claim identifier(Document control number).
	private String processDateFrom ;
	private String processDateTo ;
	private String auditId ;
	private String auditor ;
	private String auditDateFrom ;
	private String auditDateTo ;
	private String processType;
	private String errorCode;
	private String piType;
	private String priPGA;
	private String secPGA;
	private String memberId;
	private String platform;
	
	public String getPlatform() { 
		return platform;
		}

		public void setPlatform(String platform) { 
		this.platform = platform;
		}
	
	public String getSearchType() {
		return searchType;
	}
	public void setSearchType(String searchType) {
		this.searchType = searchType;
	}
	public String getPageType() {
		return pageType;
	}
	public void setPageType(String pageType) {
		this.pageType = pageType;
	}
	public String getAssociate() {
		return associate;
	}
	public void setAssociate(String associate) {
		this.associate = associate;
	}
	public String getEmployeeNumber() {
		return employeeNumber;
	}
	public void setEmployeeNumber(String employeeNumber) {
		this.employeeNumber = employeeNumber;
	}
	public String getDCN() {
		return DCN;
	}
	public void setDCN(String dCN) {
		DCN = dCN;
	}
	public String getProcessDateFrom() {
		return processDateFrom;
	}
	public void setProcessDateFrom(String processDateFrom) {
		this.processDateFrom = processDateFrom;
	}
	public String getProcessDateTo() {
		return processDateTo;
	}
	public void setProcessDateTo(String processDateTo) {
		this.processDateTo = processDateTo;
	}
	public String getAuditId() {
		return auditId;
	}
	public void setAuditId(String auditId) {
		this.auditId = auditId;
	}
	public String getAuditor() {
		return auditor;
	}
	public void setAuditor(String auditor) {
		this.auditor = auditor;
	}
	public String getAuditDateFrom() {
		return auditDateFrom;
	}
	public void setAuditDateFrom(String auditDateFrom) {
		this.auditDateFrom = auditDateFrom;
	}
	public String getAuditDateTo() {
		return auditDateTo;
	}
	public void setAuditDateTo(String auditDateTo) {
		this.auditDateTo = auditDateTo;
	}
	public String getProcessType() {
		return processType;
	}
	public void setProcessType(String processType) {
		this.processType = processType;
	}
	public String getErrorCode() {
		return errorCode;
	}
	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}
	public String getPiType() {
		return piType;
	}
	public void setPiType(String piType) {
		this.piType = piType;
	}
	public String getPriPGA() {
		return priPGA;
	}
	public void setPriPGA(String priPGA) {
		this.priPGA = priPGA;
	}
	public String getSecPGA() {
		return secPGA;
	}
	public void setSecPGA(String secPGA) {
		this.secPGA = secPGA;
	}
	public String getMemberId() {
		return memberId;
	}
	public void setMemberId(String memberId) {
		this.memberId = memberId;
	}
	
	
	
	
   
    
    
   
   
    
    

}
