package com.carefirst.qadb.controller;


import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import com.carefirst.audit.model.AuditAssessment;
import com.carefirst.qadb.constant.QADBConstants;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.xml.DomDriver;


public class HTMLCodeUtils {
	
	
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);
	static String part2="";
	static String part3="";
	
	public static String objectToXML(Object obj) {
		XStream xstream = new XStream(new DomDriver());
		return xstream.toXML(obj);
	}
	
	public static List<String> convertHTMLToString(List<AuditAssessment> claimsAuditAssessmentList) throws Exception{
		logger.debug("Fetching resource ");
		String checkUnticked="";
		String checkTicked="";
		DecimalFormat formatter1 = new DecimalFormat("#,##0.00");
		
	    checkUnticked = HTMLCodeUtils.class.getClassLoader().getResource("check1.png").toString();
	    checkTicked= HTMLCodeUtils.class.getClassLoader().getResource("check2.png").toString();
	    logger.debug("path ticked "+checkTicked+"path unticked "+checkUnticked);
		
		logger.debug("Size of List "+claimsAuditAssessmentList.size());
		for(AuditAssessment a:claimsAuditAssessmentList){
			logger.debug("List Value fetched in HTMLCodeUtils : "+objectToXML(a));
		}
		
		List<String> list=new ArrayList<String>();
		
		part2="<html><body style =\"font-size : 15px \"><table cellspacing=0 style=\"empty-cells:show\">";
		
		for(AuditAssessment audit:claimsAuditAssessmentList){
			if(null!=audit.getIsDcn()){
				logger.debug(audit.getIsAuditType());
				logger.debug("Header Information :  "+audit.getAssociateName()+"Employee Number "+audit.getEmpNO()+"Supervisor "+audit.getSupervisor());
				part2=part2+" <tr> <td style=\"text-align: center; padding-left: 10px; border-top: 5px dashed black; width: 84px;\">"+audit.getIsSno()+"."+audit.getIsDcn()+"</td>"
				+ " <td style=\"text-align: center; padding-left: 10px; border-top: 5px dashed black; width: 95px;\">$"+formatter1.format(Double.parseDouble(audit.getIsPaid()))+"</td>"
				+ " <td style=\"text-align: center; border-top: 5px dashed black; width: 80px;\"></td>"
				+ " <td style=\"text-align: center; border-top: 5px dashed black; width: 80px;\">"+audit.getIsAuditType()+"</td>"
				+ " <td style=\"text-align: center; border-top: 5px dashed black; width: 80px;\">"
				+((audit.getIsAdjRequired().equals("Y"))?"<img src=\""+checkTicked+"\">":"<img src=\""+checkUnticked+"\">")+"</td>"
				+ " <td style=\"text-align: center; border-top: 5px dashed black; width: 80px;\">"+audit.getIsErrorCode()+"</td>"
				+ " <td style=\"text-align: center; border-top: 5px dashed black; width: 80px;\">"
				+((audit.getIsMonetary().equals("Y"))?"<img src=\""+checkTicked+"\">":"<img src=\""+checkUnticked+"\">")+"</td>"
				+ " <td style=\"text-align: center; border-top: 5px dashed black; width: 80px;\">"
				+((audit.getIsProcedural().equals("Y"))?"<img src=\""+checkTicked+"\">":"<img src=\""+checkUnticked+"\">")+"</td>"
				+ " <td style=\"text-align: center; border-top: 5px dashed black; width: 90px;\">"+audit.getIsErrorType().split(" ")[0]+"</td>"
				+ " <td style=\"text-align: center; border-top: 5px dashed black; width: 230px; padding-left: 10px;\">"+audit.getIsSop()+"</td>"
				+ " <td style=\"text-align: center; border-top: 5px dashed black; width: 100px; padding-left: 10px;\">"+audit.getIsAuditor()+"</td>"
				+ " </tr>"
				+ " <tr> <td style=\"text-align: center; width: 84px;\">"+audit.getIsMemId()+"</td>"
				+ " <td style=\"text-align: center; width: 95px;\">$"+formatter1.format(Double.parseDouble(audit.getIsOverPaid()))+"</td>"
				+ " <td style=\"text-align: center; width: 80px;\">"
				+((audit.getIsHighDoller().equals("Y"))?"<img src=\""+checkTicked+"\">":"<img src=\""+checkUnticked+"\">")+"</td>"
				+ " <td style=\"text-align: center; width: 80px;\">"
				+((audit.getIsMockAudit().equals("Y"))?"<img src=\""+checkTicked+"\">":"<img src=\""+checkUnticked+"\">")+"</td>"
				+ " <td style=\"text-align: center; width: 80px;\"></td>"
				+ " <td style=\"text-align: center; width: 80px;\"></td>"
				+ " <td style=\"text-align: center; width: 80px;\"></td>"
				+ " <td style=\"text-align: center; width: 80px;\"></td>"
				+ " <td style=\"text-align: center; width: 90px;\"></td>"
				+ " <td style=\"text-align: center; width: 230px;\">"+"</td>"
				+ " <td style=\"text-align: center; width: 100px;\"></td>"
				+ " </tr>"
				+ " <tr> <td style=\"text-align: center; width: 84px;\">"+audit.getIsPlatForm()+"</td>"
				+ " <td style=\"text-align: center; width: 95px;\">$"+formatter1.format(Double.parseDouble(audit.getIsUnderPaid()))+"</td>"
				+ " <td style=\"text-align: center; width: 90px;\"></td>"
				+ " <td style=\"text-align: center; width: 80px;\"></td>"
				+ " <td style=\"text-align: center; width: 80px;\">"+audit.getIsProcessOn()+"</td>"
				+ " <td style=\"text-align: center; width: 80px;\"></td>"
				+ " <td style=\"text-align: center; width: 80px;\"></td>"
				+ " <td style=\"text-align: center; width: 80px;\"></td>"
				+ " <td style=\"text-align: center; width: 90px;\"></td>"
				+ " <td style=\"text-align: center; width: 230px\">"+audit.getIsPriPerfGroup()+"</td>"
				+ " <td style=\"text-align: center; width: 100px;\">"+audit.getIsAuditDate()+"</td>"
				+ " </tr>"
				+ "<tr> <td style=\"text-align: center; width: 84px;\"></td>"
				+ " <td style=\"text-align: center; width: 95px;\"></td>"
				+ " <td style=\"text-align: center; width: 80px;\"></td>"
				+ " <td style=\"text-align: center; width: 80px;\"></td>"
				+ " <td style=\"text-align: center; width: 80px;\"></td>"
				+ " <td style=\"text-align: center; width: 80px;\"></td>"
				+ " <td style=\"text-align: center; width: 80px;\"></td>"
				+ " <td style=\"text-align: center; width: 80px;\"></td>"
				+ " <td style=\"text-align: center; width: 90px;\"></td>"
				+ " <td style=\"text-align: center; width: 230px\">"+audit.getIsSecPerfGroup()+"</td>"
				+ " <td style=\"text-align: center; width: 100px\"></td>"
				+ " </tr>";
				if(!audit.getIsFyi().isEmpty()){
					part2=part2+ " <tr><td colspan=\"12\" style=\"text-align: left;width: 1350px\"><b>FYI/HIDO Review Notes -</b>"+" "+audit.getIsFyi().split(" - ")[1]+"</td></tr>";
				}else{
					part2=part2+" <tr><td colspan=\"12\" style=\"text-align: left;width: 1350px\">"+audit.getIsFyi()+"</td></tr>";
				}
				part2 = part2 + "<tr>"
				+ " <td colspan=\"12\" rowspan=\"2\" style=\"text-align: left;width: 1350px\"><b>Explanation -</b>"+" "+audit.getIsExplanation()+"</td>"
				+ " </tr> </table>"
				+ "</body></html>";
				
				list.add(part2);
				part2="<html><body style =\"font-size : 15px \"><table cellspacing=0 style=\"empty-cells:show\">";
			
			}else{
				part3=" <p>Summary for 'Associate'= (1 claims)</p> <table cellspacing=0 style=\"padding-bottom: 20px;\">"
			+ " <tr> <td style=\"padding-right: 30px;\">"
			+ " <table cellspacing=0 style=\"border:1px solid black; height:100px; padding-left:10px;\">"
			+ " <tr> <td style=\"width: 100px;\">Total Claims</td>"
			+ " <td style=\"background-color: grey; width: 50px; text-align: center; border: 1px solid black;\">"+audit.getIsTotalClaims()+"</td> </tr>"
			+ " <tr> <td style=\"width: 100px;\">Procedural Error Claims</td>"
			+ " <td style=\"background-color: grey; width: 50px; text-align: center; border: 1px solid black;\">"+audit.getIsProcClaims()+"</td>"
			+ " </tr> <tr> <td style=\"width: 100px;\">Monetary Error Claims</td>"
			+ " <td style=\"background-color: grey; width: 50px; text-align: center; border: 1px solid black;\">"+audit.getIsMonClaims()+"</td>"
			+ " </tr> </table> </td> <td> <table cellspacing=0 style=\"border:1px solid black; height:100px; padding-left: 10px;\">"
			+ " <tr> <td style=\"width: 100px;\">Total Paid</td>"
			+ " <td style=\"background-color: grey; width: 50px; text-align: center; border: 1px solid black;\">$"+formatter1.format(Double.parseDouble(audit.getIsTotalPaid()))+"</td> </tr>"
			+ " <tr> <td style=\"width: 100px;\">Total Over Paid</td>"
			+ " <td style=\"background-color: grey; width: 50px; text-align: center; border: 1px solid black;\">$"+formatter1.format(Double.parseDouble(audit.getIsTotalOvPaid()))+"</td> </tr>"
			+ " <tr> <td style=\"width: 100px;\">Total Under Paid</td>"
			+ " <td style=\"background-color: grey; width: 50px; text-align: center; border: 1px solid black;\">$"+formatter1.format(Double.parseDouble(audit.getIsTotalUnPaid()))+"</td>"
			+ " </tr> </table> </td> </tr> </table>"
			+ " <table style=\"empty-cells: show;\" cellspacing=0>"
			+ " <tr> <td style=\"width: 450px; text-align: center; border: 1px solid black; height: 20px;\">"
			+ "I certify that I have completed this audit in accordance with the standards and criteria appointed by"
			+ " the quality assurance Department.</td> </tr>"
			+ " <tr><td style=\"border: 1px solid black; height: 20px;\"></td></tr>"
			+ " <tr><td style=\"border: 1px solid black; height: 20px;\"></td></tr> </table>"
			+ " <p><b>Please forward any dispute in this audit within 48 hours of the date listed in th bottom of the sheet.</b></p>"
			+ " <table cellspacing=0> <tr> <td style=\"width:100px\"><b>Processor Signature :</b></td>"
			+ " <td style=\"border-bottom: 1px solid black;width: 50px;\"></td>"
			+ " <td style=\"padding-left:20px ;\"><b>Date:</b></td> <td style=\"border-bottom: 1px solid black;width: 50px;\"></td>"
			+ " <td style=\"padding-left:10px ;\"><b>ADJUSTED?:?YES ?NO</b></td> </tr> </table> </body></html>";
			}//else
		}//for loop
		
		part3="<html><body style=\"font-size:7px\">"+part3;
		return list;
	}
}
