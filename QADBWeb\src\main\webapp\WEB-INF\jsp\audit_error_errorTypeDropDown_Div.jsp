<!DOCTYPE html><%@page language="java" contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>

<html>	

                               <select id="errorTypes" name="errorType" >
                               	<c:if test="${errorTypes eq null}">
                               	  <option value=""><spring:message code="audit.new.monetary.error.none" /></option>
                               	</c:if>
                               
									<c:forEach items="${errorTypes}" var="errorTypes">
													<option value="${errorTypes.errorTypeId}"
													   ${errorTypes.errorTypeId == eErrTypeId ? 'selected="selected"' : ''}>${errorTypes.errorTypes}</option>
									</c:forEach>
								</select>
								
</html>	