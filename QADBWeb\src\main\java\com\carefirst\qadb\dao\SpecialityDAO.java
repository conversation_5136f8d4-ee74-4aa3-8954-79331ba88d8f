package com.carefirst.qadb.dao;

import java.sql.SQLException;
import java.util.List;

import com.carefirst.audit.model.Speciality;

public interface SpecialityDAO {
	
	public List<Speciality> getSpeciality(Speciality specialtyTO) throws SQLException ;

	public Speciality saveUpdateSpeciality(Speciality specialtyTO) throws SQLException;

	public Speciality getSpecialityById(Speciality specialtyTO) throws SQLException;

}
