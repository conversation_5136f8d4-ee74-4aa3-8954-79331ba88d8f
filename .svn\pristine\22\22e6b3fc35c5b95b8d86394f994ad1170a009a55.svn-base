package com.carefirst.qadb.service;

import java.net.MalformedURLException;
import java.net.URL;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.xml.ws.BindingProvider;

import com.carefirst.*;
import com.carefirst.audit.model.AdjustmentLevel;
import com.carefirst.audit.model.ClaimLevel;
import com.carefirst.audit.model.ClstStatusLines;
import com.carefirst.audit.model.ProductLevel;
import com.carefirst.audit.model.StatusBlock;
import com.carefirst.audit.model.SubscriberLevel;
import com.carefirst.eapmca.controller.PMConnector;
import com.carefirst.facets.claimssampling.ClaimsQASamplingServiceagent;
import com.carefirst.facets.claimssampling.QASamplingPort;
import com.carefirst.facets.claimssampling.details.types.ClstStatusLine;
import com.carefirst.facets.claimssampling.details.types.Levels;
import com.carefirst.facets.claimssampling.details.types.Message;
import com.carefirst.facets.claimssampling.details.types.Products;
import com.carefirst.facets.claimssampling.details.types.QASamplingResponse;
import com.carefirst.facets.claimssampling.types.QASamplingRequest;
import com.carefirst.qadb.constant.QADBConstants;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.xml.DomDriver;
import com.carefirst.qadb.utils.*;
import com.ibm.wps.pb.common.QName;

@Service(value="claimService")
public class ClaimServiceImpl implements ClaimService{
	
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);
	
	
	public com.carefirst.audit.model.QASamplingResponse getClaim(String DCN) throws MalformedURLException {
		// TODO Auto-generated method stub
		logger.debug("***getClaim start***");
		//EAPM Migration Changes Start-Mar 2018
		PMConnector  pmc  = PMConnector. getConnector();
		//EAPM Migration Changes End-Mar 2018	
		
		javax.xml.namespace.QName CLAIMSQASAMPLINGSERVICEAGENT_QNAME = new javax.xml.namespace.QName("http://carefirst.com/facets/claimssampling", "ClaimsQASampling.serviceagent");
		ClaimsQASamplingServiceagent claimsQASamplingServiceagent =  new ClaimsQASamplingServiceagent(new URL(QADBConstants.QA_CLAIMS_WSDL),CLAIMSQASAMPLINGSERVICEAGENT_QNAME);
		QASamplingPort portType = claimsQASamplingServiceagent.getQASamplingPortEndpoint1(); 
		BindingProvider bindingProvider =(BindingProvider) portType;
		//EAPM Migration Changes Start-Mar 2018
		//	bindingProvider.getRequestContext().put(BindingProvider.USERNAME_PROPERTY, QADBConstants.USERNAME_PROPERTY);
	//	bindingProvider.getRequestContext().put(BindingProvider.PASSWORD_PROPERTY, QADBConstants.PASSWORD_PROPERTY);
		bindingProvider.getRequestContext().put(BindingProvider.USERNAME_PROPERTY,pmc. getUserName(QADBConstants.VAULT_QUERY));
	bindingProvider.getRequestContext().put(BindingProvider.PASSWORD_PROPERTY, pmc.getContent(QADBConstants.VAULT_QUERY));
		//EAPM Migration Changes End-Mar 2018
		bindingProvider.getRequestContext().put(BindingProvider.ENDPOINT_ADDRESS_PROPERTY, QADBConstants.QA_CLAIMS_END_POINT);
		QASamplingRequest qaSamplingRequest=new QASamplingRequest();
		QASamplingResponse qaSamplingResponse=new QASamplingResponse();
		com.carefirst.audit.model.QASamplingResponse beanObject = new com.carefirst.audit.model.QASamplingResponse();
		
		logger.info("**WSDL** "+QADBConstants.QA_CLAIMS_WSDL);
		logger.info("**EndPoint** "+QADBConstants.QA_CLAIMS_END_POINT);
		logger.info("**VaultQuery** "+QADBConstants.VAULT_QUERY);
		
		
		
		
		DecimalFormat df = new DecimalFormat("0.00");
		
		try{
			qaSamplingRequest.setClaimNumber(DCN);
			logger.info(new StringBuffer("request xml:\n").append(ServiceUtil.objectToXML(qaSamplingRequest)));
			qaSamplingResponse = portType.qaSamplingDetails(qaSamplingRequest);
			logger.debug(qaSamplingResponse);
			logger.info(new StringBuffer("response xml:\n").append(ServiceUtil.objectToXML(qaSamplingResponse)));
			if(qaSamplingResponse!=null){
				logger.debug("qaSamplingResponse is not null");
				if(qaSamplingResponse.getStatusBlock()!=null){
					logger.debug("getStatusBlock is not null");
					StatusBlock domainStatusBlock = new StatusBlock();
					domainStatusBlock.setStatusCode(qaSamplingResponse.getStatusBlock().getStatusCode());
					logger.debug("StatusCode---->"+domainStatusBlock.getStatusCode());
					List<com.carefirst.audit.model.Message> messageList = new ArrayList<com.carefirst.audit.model.Message>();
					if(qaSamplingResponse.getStatusBlock().getMessage()!=null){
						com.carefirst.audit.model.Message messageObj = new com.carefirst.audit.model.Message();
						for(Message message : qaSamplingResponse.getStatusBlock().getMessage()){
							messageObj.setMessageCode(message.getMessageCode());
							messageObj.setMessageDescription(message.getMessageDescription());
							logger.debug("MessageDescription"+messageObj.getMessageDescription());
							if(message.getMessageDescription() != null ){
								if(("Source:Facets G6").equals(message.getMessageDescription())){
									beanObject.setPlatform("Facets G6");
								}else if(("Source:Facets Legacy").equals(message.getMessageDescription())){
									beanObject.setPlatform("Facets Legacy");
								}
							}
							messageList.add(messageObj);
						}
					}
					domainStatusBlock.setMessage(messageList);
					beanObject.setStatusBlock(domainStatusBlock);
				
				if(qaSamplingResponse.getClaimLevel()!=null){
					logger.debug("qaSamplingResponse.getClaimLevel() is not null");
					ClaimLevel domainClaimLevel = new ClaimLevel();
					domainClaimLevel.setCareFirstExaminerUserID(qaSamplingResponse.getClaimLevel().getCareFirstExaminerUserID());
					domainClaimLevel.setCareFirstExaminerName(qaSamplingResponse.getClaimLevel().getCareFirstExaminerName());
					domainClaimLevel.setProductID(qaSamplingResponse.getClaimLevel().getProductID());
					domainClaimLevel.setProductName(qaSamplingResponse.getClaimLevel().getProductName());
					domainClaimLevel.setProcessDate(ServiceUtil.formatDate(qaSamplingResponse.getClaimLevel().getProcessDate()));
					logger.debug("process date-->"+domainClaimLevel.getProcessDate());
					domainClaimLevel.setPaidDate(ServiceUtil.formatDate(qaSamplingResponse.getClaimLevel().getPaidDate()));
					logger.debug("paid date-->"+domainClaimLevel.getPaidDate());
					if((domainClaimLevel.getPaidDate()).equalsIgnoreCase("01-01-53")){
						logger.debug("paid date conversion");
						domainClaimLevel.setPaidDate("");
					}
					logger.debug("paid date after -->"+domainClaimLevel.getPaidDate());
					domainClaimLevel.setClaimCurrentstatus(qaSamplingResponse.getClaimLevel().getClaimCurrentstatus());
					domainClaimLevel.setPaidAmount(df.format(Double.parseDouble(qaSamplingResponse.getClaimLevel().getPaidAmount())));
					domainClaimLevel.setTotalCharge(df.format(Double.parseDouble(qaSamplingResponse.getClaimLevel().getTotalCharge())));
					beanObject.setClaimLevel(domainClaimLevel);
					logger.debug("qaSamplingResponse.getClaimLevel() end");
				}
				
				
				if(qaSamplingResponse.getSubscriberLevel()!=null){
					logger.debug("getSubscriberLevel not null");
					SubscriberLevel domainSubscriberLevel = new SubscriberLevel();
					domainSubscriberLevel.setAccountID(qaSamplingResponse.getSubscriberLevel().getAccountID());
					domainSubscriberLevel.setAccountName(qaSamplingResponse.getSubscriberLevel().getAccountName());
					domainSubscriberLevel.setCJAJurisdiction(qaSamplingResponse.getSubscriberLevel().getCJAJurisdiction());
					domainSubscriberLevel.setMemberID(qaSamplingResponse.getSubscriberLevel().getMemberID());
					domainSubscriberLevel.setSBU(qaSamplingResponse.getSubscriberLevel().getSBU());
					domainSubscriberLevel.setSubscriber_CK(qaSamplingResponse.getSubscriberLevel().getSubscriberCK());
					beanObject.setSubscriberLevel(domainSubscriberLevel);
					logger.debug("getSubscriberLevel end");
				}
				if(qaSamplingResponse.getProductLevel()!=null){
					char lettr=DCN.charAt(4);
					logger.debug("getProductLevel not null");
					ProductLevel domainProductLevel = new ProductLevel();
					List<com.carefirst.audit.model.Products> productList = new ArrayList<com.carefirst.audit.model.Products>();
					if(!((qaSamplingResponse.getProductLevel().getProducts()).isEmpty())){
						for(Products products : qaSamplingResponse.getProductLevel().getProducts()){
							com.carefirst.audit.model.Products productsobj = new com.carefirst.audit.model.Products();
							productsobj.setBSBSCode(products.getBSBSCode());
							productsobj.setGroupID(products.getGroupID());
							if(lettr=='J'||lettr=='j' || lettr=='K' || lettr=='k'){
								productsobj.setLineofBusiness("Blue Card Home");
							}else{
								productsobj.setLineofBusiness("Commercial");
							}
							productsobj.setProductDescription(products.getProductDescription());
							productsobj.setProductLine(products.getProductLine());
							productList.add(productsobj);
						}
					}else{
						com.carefirst.audit.model.Products productsobj = new com.carefirst.audit.model.Products();
						productsobj.setBSBSCode("");
						productsobj.setGroupID("");
						if(lettr=='J'||lettr=='j' || lettr=='K' || lettr=='k'){
							productsobj.setLineofBusiness("Blue Card Home");
						}else{
							productsobj.setLineofBusiness("Commercial");
						}
						productsobj.setProductDescription("");
						productsobj.setProductLine("");
						productList.add(productsobj);
					}
					domainProductLevel.setProducts(productList);
					beanObject.setProductLevel(domainProductLevel);
					logger.debug("getProductLevel end");
				}else{
					logger.debug("getProductLevel null start");
					char lettr=DCN.charAt(4);
					ProductLevel domainProductLevel = new ProductLevel();
					List<com.carefirst.audit.model.Products> productList = new ArrayList<com.carefirst.audit.model.Products>();
					com.carefirst.audit.model.Products productsobj2 = new com.carefirst.audit.model.Products();
					if(lettr=='J'||lettr=='j' || lettr=='K' || lettr=='k'){
						productsobj2.setLineofBusiness("Blue Card Home");
					}else{
						productsobj2.setLineofBusiness("Commercial");
					}
					productList.add(productsobj2);
					domainProductLevel.setProducts(productList);
					beanObject.setProductLevel(domainProductLevel);
					logger.debug("getProductLevel null end");
				}
				
				if(qaSamplingResponse.getAdjustmentLevel()!=null){
					logger.debug("getAdjustmentLevel not null");
					AdjustmentLevel domainAdjustmentLevel = new AdjustmentLevel();
					List<com.carefirst.audit.model.Levels> levelsList = new ArrayList<com.carefirst.audit.model.Levels>();
					if(null!=qaSamplingResponse.getAdjustmentLevel().getLevels()){
						for(Levels levels : qaSamplingResponse.getAdjustmentLevel().getLevels()){
							com.carefirst.audit.model.Levels levelsobj = new com.carefirst.audit.model.Levels();
							levelsobj.setAdjustmentReasoncode(levels.getAdjustmentReasoncode());
							levelsobj.setInterestPaidAmount(levels.getInterestPaidAmount());
							levelsobj.setPenaltyInterestDate(levels.getPenaltyInterestDate());
							levelsList.add(levelsobj);
						}
						domainAdjustmentLevel.setLevels(levelsList);
						beanObject.setAdjustmentLevel(domainAdjustmentLevel);
					}
					logger.debug("getAdjustmentLevel end");
				}
				if(qaSamplingResponse.getCJAJurisdictionCode()!=null){
					logger.debug("getCJAJurisdictionCode not null");
					beanObject.setCJAJurisdictionCode(qaSamplingResponse.getCJAJurisdictionCode());
					logger.debug("getCJAJurisdictionCode end");
				}
				
				
				if(qaSamplingResponse.getClstStatusLines()!=null){
					logger.debug("getClstStatusLines not null");
					ClstStatusLines domainClstStatusLines = new ClstStatusLines();
					List<com.carefirst.audit.model.ClstStatusLine> clstStatusLineList = new ArrayList<com.carefirst.audit.model.ClstStatusLine>();
					for(ClstStatusLine clstStatusLine : qaSamplingResponse.getClstStatusLines().getClstStatusLine()){
						com.carefirst.audit.model.ClstStatusLine clstStatusLineobj = new com.carefirst.audit.model.ClstStatusLine();
						clstStatusLineobj.setClstStatus(clstStatusLine.getClstStatus());
						clstStatusLineobj.setProcessedDate(clstStatusLine.getProcessedDate());
						clstStatusLineobj.setFormattedDate(ServiceUtil.formattedDate((clstStatusLine.getProcessedDate()).substring(0, 10)));
						clstStatusLineobj.setUserID(clstStatusLine.getUserID());
						clstStatusLineList.add(clstStatusLineobj);
					}
					domainClstStatusLines.setClstStatusLine(clstStatusLineList);
					beanObject.setClstStatusLines(domainClstStatusLines);
					logger.debug("getClstStatusLines end");
				}
			}
		}
		}
				catch(Exception e)
		{
			logger.debug(e.getMessage());
		}
		logger.debug("Exist serviceimpl");
		return beanObject;
	}
	
		
	
	public HashMap<String, String> getEditCodes() {
		// TODO Auto-generated method stub
		return null;
	}
	}