package com.carefirst.qadb.service;

import java.util.Arrays;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.springframework.web.servlet.ModelAndView;

import com.carefirst.qadb.constant.QADBConstants;

public class AuthorizationServiceImpl implements AuthorizationService {

	//private static final Logger logger = new LogWrapper(AuthorizationServiceImpl.class.getName());
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);
	public String getRole(String userId) {
		// Taking Read-Only as the default role.
		String role = "RO";
		
		// Call to db to get the user role based on id
		// Hardcoding it for development purposes.
		
		// Read-Only
		if (userId.equalsIgnoreCase("user_RO")) {
			role = "RO";
		}
		
		// Auditor
		if (userId.equalsIgnoreCase("user_AUD")) {
			role = "AUD";
		}
		
		// Admin
		if (userId.equalsIgnoreCase("user_ADM")) {
			role = "ADM";
		}
		
		// Super Admin
		if (userId.equalsIgnoreCase("user_SADM")) {
			role = "SADM";
		}
		
		logger.debug("Role is " + role);
		return role;
	}
	
	@Override
	public ModelAndView AuthorizeUsersAudit(String jspView,
			HttpServletRequest request) {
		
		ModelAndView mv = null;
		String grp[] = {request.getHeader("iv-groups")};
	 	//String b[] = {"Contractor","PeopleSoft_ELM_User","onestop_users","qadb-samd-readonly_users"};

		if((!(((Arrays.toString(grp)).replace("[", "")).replace("]", "")).equals("null"))||(grp.length != 0)){
			
			if(((Arrays.toString(grp)).contains("qadb-superadmin_users"))    ||
			   ((Arrays.toString(grp)).contains("qadb-samd-admin_users"))    || ((Arrays.toString(grp)).contains("qadb-cd-admin_users"))	||
			   ((Arrays.toString(grp)).contains("qadb-samd-auditor_users"))  || ((Arrays.toString(grp)).contains("qadb-cd-auditor_users"))){
				mv = new ModelAndView(jspView);
			}
			else{
				logger.debug("***unauthorizedUserAudit***");
				mv = new ModelAndView("unauthorized");
			}
		}
		else{
			logger.debug("***unauthorizedUserAudit***");
			mv = new ModelAndView("unauthorized");
		}
		return mv;
	}
	
	@Override
	public ModelAndView AuthorizeUsersReports(String jspView,
			HttpServletRequest request) {
		
		ModelAndView mv = null;
		String grp[] = {request.getHeader("iv-groups")};
	 	//String b[] = {"Contractor","PeopleSoft_ELM_User","onestop_users","qadb-samd-readonly_users"};

		if((!(((Arrays.toString(grp)).replace("[", "")).replace("]", "")).equals("null"))||(grp.length != 0)){
			
			if(((Arrays.toString(grp)).contains("qadb-superadmin_users"))    ||
			   ((Arrays.toString(grp)).contains("qadb-samd-admin_users"))    || ((Arrays.toString(grp)).contains("qadb-cd-admin_users"))	||
			   ((Arrays.toString(grp)).contains("qadb-samd-auditor_users"))  || ((Arrays.toString(grp)).contains("qadb-cd-auditor_users"))	||
			   ((Arrays.toString(grp)).contains("qadb-samd-readonly_users")) || ((Arrays.toString(grp)).contains("qadb-cd-readonly_users"))){
				mv = new ModelAndView(jspView);
			}
			else{
				logger.debug(jspView);
				mv = new ModelAndView("unauthorized");
			}
		}
		else{
			logger.debug("***unauthorizedUserReports***");
			mv = new ModelAndView("unauthorized");
		}
		return mv;
		
	}
	
	@Override
	public ModelAndView AuthorizeUsersAdmin(String jspView,
			HttpServletRequest request) {
		ModelAndView mv = null;
		String grp[] = {request.getHeader("iv-groups")};
	 	//String b[] = {"Contractor","PeopleSoft_ELM_User","onestop_users","qadb-samd-readonly_users"};

		if((!(((Arrays.toString(grp)).replace("[", "")).replace("]", "")).equals("null"))||(grp.length != 0)){
			
			if(((Arrays.toString(grp)).contains("qadb-superadmin_users")) ||
			   ((Arrays.toString(grp)).contains("qadb-samd-admin_users")) || ((Arrays.toString(grp)).contains("qadb-cd-admin_users"))){
				mv = new ModelAndView(jspView);
			}
			else{
				logger.debug("***unauthorizedUserAdmin***");
				mv = new ModelAndView("unauthorized");
			}
		}
		else{
			logger.debug("***unauthorizedUserAdmin***");
			mv = new ModelAndView("unauthorized");
		}
		return mv;
	}
	
	@Override
	public String getUserGroup(HttpServletRequest request) {
		
		String userGrp = "('SAMMD','CD','ALL')";
		
	 	String grp[] = {request.getHeader("iv-groups")};
	 	//String grp[] = {"Contractor","PeopleSoft_ELM_User","onestop_users","qadb-samd-readonly_users"};
        logger.debug("Request Grp "+request.getHeader("iv-groups"));
        logger.debug("Grp "+Arrays.toString(grp));

		if((!(((Arrays.toString(grp)).replace("[", "")).replace("]", "")).equals("null"))||(grp.length != 0)){
 			if(((Arrays.toString(grp)).contains("qadb-superadmin_users")) || (((Arrays.toString(grp)).contains("qadb-samd-auditor_users")) && ((Arrays.toString(grp)).contains("qadb-cd-auditor_users")))
 					||(((Arrays.toString(grp)).contains("qadb-samd-readonly_users")) && ((Arrays.toString(grp)).contains("qadb-cd-readonly_users")))){
 				userGrp = "('SAMMD','CD','ALL')";
				logger.debug("***ALL***");
 			}
 			else if(((Arrays.toString(grp)).contains("qadb-samd-auditor_users"))||((Arrays.toString(grp)).contains("qadb-samd-admin_users"))||((Arrays.toString(grp)).contains("qadb-samd-readonly_users"))){
 				userGrp = "('SAMMD','ALL')";
				logger.debug("***SAMMD***");
 			}
 			else if(((Arrays.toString(grp)).contains("qadb-cd-auditor_users"))||((Arrays.toString(grp)).contains("qadb-cd-admin_users"))||((Arrays.toString(grp)).contains("qadb-cd-readonly_users"))){
 				userGrp = "('CD','ALL')";
				logger.debug("***CD***");
 			}
 		}
		logger.debug("User Grp "+grp + " - "+userGrp);
		return userGrp;
	}
}