<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<head>
<link href="webResources/css/qadb.css" rel="stylesheet">

<script type="text/javascript" src="webResources/js/metro.min.js"></script>
<!-- Local JavaScript -->

<link href="webResources/css/iconFont.css" rel="stylesheet">
<link href="webResources/css/docs.css" rel="stylesheet">
<link href="webResources/js/prettify/prettify.css" rel="stylesheet">

<!-- Load JavaScript Libraries -->
<script src="webResources/js/jquery/jquery.min.js"></script>
<script src="webResources/js/jquery/jquery.widget.min.js"></script>
<script src="webResources/js/jquery/jquery.dataTables.js"></script>
<style>
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
	margin-right: auto;
	height: 210%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}
.hidden
        {
            display: none;
        }
</style>



<title><spring:message code="audit.search.title" /></title>
</head>
<body>
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container2">

		<!-- Sidebar Start-->
		<div id="left-sidebar">
			<h2 style="color: white ;padding-top: 32px;"><spring:message code="audit.search.leftNav.heading" /></h2>
			<h3 style="color: white"><spring:message code="audit.search.leftNav.text" /></h3>

			<div class="grid fluid">
				<div class="row">
					<div class="span4">

						<nav class="sidebar light">
							<ul>

								<li class="active"><a href="searchBasic"><img
										alt="BasicSearch"
										src="webResources/images/Tabs/Search/Icn_Basic_Search.png">&nbsp;&nbsp;&nbsp;&nbsp;
										<spring:message code="audit.search.leftNav.heading1" /></a>
								</li>

								<li><a href="searchAdv"><img alt="AdvanceSearch"
										src="webResources/images/Tabs/Search/Icn_Advanced_Search.png">
										<spring:message code="audit.search.leftNav.heading2" /></a>
								</li>

							</ul>
						</nav>

					</div>


				</div>
			</div>
		</div>
		<!-- Sidebar End-->

		<div id="content-container" style="padding-left: 40px" >

			<form:form id="searchForm" name="searchForm" method="GET"
				commandName="searchBasicForm" action="searchBasicRes" style="width:700px">


				<div style="padding: 10px 0px 0px 0px;">
					<span class="bread1"><spring:message code="audit.new.bread1" /> &gt; </span><span class="bread2">
						<spring:message code="audit.search.leftNav.heading" /></span>
				</div>
				<br>
				<div class="input-control text">
					<spring:message code="qadb.search.message" />
				</div>
				<br>
				<span style="color: #0070c0; font-size: 16px"><spring:message code="audit.new.associate" /></span>
				<div class="line-separator"></div>
				<br>

				<table class="tableForm" style="background-color: #fafafa;">
					<tr>
						<td style="padding-right: 66px;"><spring:message code="audit.search.associate.name" /></td>
						<td><%-- <div class="input-control text">
								<input id="associateName" type="text" name="associateName" value="${basicSearch.associateName}" />
							</div> --%>
							<div class="input-control select" >
							 <select id="associateName" name="associateName" style="width: 250px;">
							 		<option selected value=""><spring:message
																code="audit.search.select.associate" /></option>
									<c:forEach items="${claimProcessors}" var="claimProcessors">
													<option value="${claimProcessors.associateId}"
														 ${claimProcessors.associateId== basicSearch.associateName ? 'selected="selected"' : ''}>${claimProcessors.associateName}</option>
									</c:forEach>
								</select>
							</div>
							</td>
					</tr>
					<tr>
						<td><spring:message code="audit.search.employee" /></td>
						<td><%-- <div class="input-control text">
								<input type="text" id="empNo" name="empNo" value="${basicSearch.empNo}" />
							</div> --%>
							<div class="input-control select" >
							 <select id="empNo" name="empNo" style="width: 200px;">
							 		<option selected value=""><spring:message
																code="audit.search.select.employee" /></option>
									<c:forEach items="${facetsIds}" var="facetsIds">
													<option value="${facetsIds.facetsId}"
														 ${facetsIds.facetsId== basicSearch.empNo ? 'selected="selected"' : ''}>${facetsIds.facetsId}</option>
									</c:forEach>
								</select>
							</div>
							
							</td>
					</tr>

				</table>
				<br>
				<span style="color: #0070c0; font-size: 16px"><spring:message code="audit.new.claim.dcn" /></span>
				<div class="line-separator"></div>
				<br>

				<table class="tableForm" style="background-color: #fafafa;">
					<tr>
						<td><spring:message code="audit.new.claim.dcn" /></td>
						<td><div class="input-control text">
								<input type="text" id="dcn" name="dcn" value="${basicSearch.dcn}" maxlength="12" />
							</div></td>
					</tr>


				</table>

				<div style="width: 750px">
					<div style="float: right">
						<button type="button" onclick="clearSearch();" class="button inverse"><spring:message code="audit.search.reset" /></button>
						<button style="background-color: #298fd8"
							class="button default"><spring:message code="audit.search.search" /></button>
					</div>
				</div>

			</form:form>


			<!-- <div id="welcomeDiv" style="display: none;"> -->
			<c:if test="${searchResult!=null }">
			
			<br> <br> <span style="color: #0070c0; font-size: 16px">
				<spring:message code="audit.search.results" /></span> <br>
			<br>
			<div class="line-separator"></div>
			<table class="table striped hovered dataTable" id="searchDatatable"
				width="700px">
				<thead>
					<tr style="height: 30px">

						<td class="text-left" font-size="25px"><spring:message code="audit.search.results.audit.id" /></td>
						<td class="text-left" font-size="25px" style="padding-left:5px"><spring:message code="audit.new.claim.dcn" /></td>
						<td class="text-left" font-size="25px"><spring:message code="audit.new.associate" /></td>
						<td class="text-left" font-size="25px"><spring:message code="audit.search.results.processed.date" /></td>
						<td class="text-left" font-size="25px"><spring:message code="audit.search.results.auditor" /></td>
						<td class="text-left" font-size="25px"><spring:message code="audit.search.results.audit.date" /></td>


					</tr>
				</thead>

				<tbody>
					
					<c:forEach items="${searchResult}" var="rs">
						<tr style="height: 30px">
							<td>${rs.auditId}</td>
							<td style="padding-left:5px">${rs.dcn}</td>
							<td>${rs.associateName}</td>
							<td>${rs.processedDateFrom}</td>
							<td>${rs.auditorName}</td>
							<td>${rs.auditDateFrom}</td>
						</tr>
					</c:forEach> 

				</tbody>


			</table>
</c:if>
			<!-- </div> -->
			 <script>
				$(function() {
					$('#searchDatatable').dataTable({
										
										
					"columns" : [
					
					{
           
            sClass: "hidden"
        }, 
					{
					"render": function(data, type, full, meta) {
					/* return '<a href="'+full[0]+'"><u>'+data+'<u></a>'; */
					return '<a href="editAuditGeneral?id='+full[0]+'&dcnNo='+data+'"><u>'+data+'<u></a>';
                   
            }
            }, {}, {}, {}, {} ]
									});
				});
			</script> 

			
			
			<script type="text/javascript">
				function clearSearch(){
					document.getElementById("associateName").value="";
					document.getElementById("empNo").value="";
					document.getElementById("dcn").value="";
				}
			</script>



		</div>
	</div>

	<jsp:include page="footer.jsp"></jsp:include>

</body>

</html>
