package com.carefirst.qadb.controller;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import net.sf.jasperreports.engine.JRDataSource;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.carefirst.audit.model.Associate;
import com.carefirst.audit.model.AuditDetails;
import com.carefirst.audit.model.ErrorCodes;
import com.carefirst.audit.model.ErrorDetails;
import com.carefirst.audit.model.JobTitle;
import com.carefirst.audit.model.Mapping;
import com.carefirst.audit.model.OperationalUnit;
import com.carefirst.audit.model.PerformanceGroup;
import com.carefirst.audit.model.RootCause;
import com.carefirst.audit.model.Speciality;
import com.carefirst.qadb.constant.QADBConstants;
import com.carefirst.qadb.dao.AssociateDAO;
import com.carefirst.qadb.dao.AuditDetailsDAO;
import com.carefirst.qadb.dao.ErrorCodesDAO;
import com.carefirst.qadb.dao.ErrorDetailsDAO;
import com.carefirst.qadb.dao.JobTitlesDAO;
import com.carefirst.qadb.dao.MappingDAO;
import com.carefirst.qadb.dao.OperationalUnitDAO;
import com.carefirst.qadb.dao.PerformanceGroupDAO;
import com.carefirst.qadb.dao.RootCauseDAO;
import com.carefirst.qadb.dao.ScoresReportsDAO;
import com.carefirst.qadb.dao.SpecialityDAO;
import com.carefirst.qadb.service.AuthorizationService;

@Controller 
public class AdminController {

	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);
	
	@Autowired
	AssociateDAO associateDAO;
	
	@Autowired
	ErrorCodesDAO errorCodesDAO;
	
	@Autowired
	OperationalUnitDAO operationalUnitDAO;
	
	@Autowired
	PerformanceGroupDAO performanceGroupDAO;
	
	@Autowired
	JobTitlesDAO jobTitlesDAO;
	
	@Autowired
	SpecialityDAO specialityDAO;
	
	@Autowired
	RootCauseDAO rootCauseDAO;
	
	@Autowired  
	AuditDetailsDAO auditDetailsDao;
	
	@Autowired
	ScoresReportsDAO scoresReportsDAO;
	
	@Autowired
	MappingDAO mappingDAO;
	
	@Autowired  
	AuthorizationService authorizationService;
	
	@Autowired
	ErrorDetailsDAO errorDetailsDao;
	
	/***
	 * Method to put the drop down values.
	 * @throws SQLException 
	 */
	@ModelAttribute("associateForm")
	public void values(Map<String, Object> model) throws Exception,SQLException{
		
		 Associate associateForm = new Associate();
	   	 model.put("associateForm", associateForm);
	   	
	   	 List<Associate> jobTitles = associateDAO.getJobTitles();
	     model.put("jobTitles", jobTitles);
	     
	     List<Associate> locations = associateDAO.getLocations();
	     model.put("locations", locations);
	     
	     List<OperationalUnit> workunits = associateDAO.getWorkUnits();
	     model.put("workunits", workunits);
	     
	}
	
	public ModelAndView AuthorizeUser(String jspView,HttpServletRequest request){
		
		ModelAndView mv = null;
		String b[] = {request.getHeader("iv-groups")};
		//String b[] = {"Contractor","PeopleSoft_ELM_User","onestop_users","qadb-samd-admin_users"};
		logger.debug("Grp array "+b);
		if(!(((Arrays.toString(b)).replace("[", "")).replace("]", "")).equals("null")){
			int last = b.length - 1;
			logger.debug("Grp last ele" +last);
			if(((b[last].toString()).contains("qadb-superadmin_users"))||((b[last].toString()).contains("qadb-samd-admin_users"))||((b[last].toString()).contains("qadb-cd-admin_user"))){
				mv = new ModelAndView(jspView);
			}
			else{
				mv = new ModelAndView("unauthorized");
			}
		}
		else{
			mv = new ModelAndView("unauthorized");
		}
		return mv;
	}
	
	
	/***
	 * Method to show the page to add a new Associate.
	 * @return new model and view to enter Associate details.
	 * @throws SQLException 
	 */
	@RequestMapping(value = "/addAssociate", method = RequestMethod.GET)
	private ModelAndView AddAssociate(Map<String, Object> model,HttpServletRequest request) throws Exception,SQLException {
		
		 Associate associateForm = new Associate();
	   	 model.put("associateForm", associateForm);
	   	
	   	 List<Associate> jobTitles = associateDAO.getJobTitles();
	     model.put("jobTitles", jobTitles);
	     
	     List<Associate> locations = associateDAO.getLocations();
	     model.put("locations", locations);
	     
	     List<OperationalUnit> workunits = associateDAO.getWorkUnits();
	     model.put("workunits", workunits);
	     
	     
	     /*Authorization*/
	    // ModelAndView mv = AuthorizeUser("admin_associate", request);
	     ModelAndView mv = null;
		 mv = authorizationService.AuthorizeUsersAdmin("admin_associate", request);
	     return mv;
	}
	
	/**
	 * Method to save new Associate with details.
	 * @param model
	 * @param associateForm the command object for Associate details
	 * @param redirectAttributes
	 * @return 
	 */
	@RequestMapping(value = "/saveAssociate", method = RequestMethod.GET)
	public ModelAndView saveAssociate(Map<String, Object> model,@ModelAttribute("associateForm") Associate associateDetails,final RedirectAttributes redirectAttributesAssociate, HttpSession session) throws Exception {
		logger.debug("*** Entry saveAssociate method ***");
		String userId=(String) session.getAttribute(QADBConstants.USERNAME);
		associateDetails.setUserid(userId);
		associateDetails.setUserActyp(QADBConstants.USER_ACTION_NEW);
		associateDetails.setAuditingStatus(QADBConstants.DEFAULT_FLAG_YES);
		Associate associateSave;
		try {
			associateSave = associateDAO.saveUpdateAssociate(associateDetails);
			redirectAttributesAssociate.addFlashAttribute(QADBConstants.SUCCESS_CODE,associateSave.getSucessCode());
			redirectAttributesAssociate.addFlashAttribute(QADBConstants.SUCCESS_MESSAGE,associateSave.getSuccessMsg());
			redirectAttributesAssociate.addFlashAttribute(QADBConstants.SUCCESS_MESSAGE_ATTRIBUTE, associateSave.getSuccessMsg());
		} catch (Exception e) {
			logger.debug("Exception : "+e.getMessage());
		}
		logger.debug("*** Exit saveAssociate method ***");
		return  new ModelAndView("redirect:addAssociate");
	}
	
	/***
	 * Method to show the page to search Associates.
	 * @return new model and view to search Associates .
	 */
	@RequestMapping(value = "/editAssociate", method = RequestMethod.GET)
	public ModelAndView searchAssociates(HttpServletRequest request){
		//ModelAndView mv = AuthorizeUser("admin_associate_edit_list", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_associate_edit_list", request);
		return mv;
	}
	
	
	/**
	 * Method to fetch the search results for the entered Associate name.
	 * @param associateSearchForm
	 * @return 
	 */
	@RequestMapping(value = "/editAssociateRes", method = RequestMethod.GET)
   	public ModelAndView searchAssociatesRes(@ModelAttribute("associateSearchForm") Associate searchForm,HttpSession session,HttpServletRequest request) throws Exception {
		logger.debug("*** Entry searchAssociates method ***");
		//ModelAndView mv = AuthorizeUser("admin_associate_edit_list", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_associate_edit_list", request);
    	logger.debug("associateName= "+ searchForm.getAssociateName());
    	mv.addObject("associateName", searchForm.getAssociateName());
    	logger.debug("facetsUserId= "+ searchForm.getFacetsId());
    	mv.addObject("facetsUserId", searchForm.getFacetsId());
    	Associate associateSearchTO = new Associate();
    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
    	associateSearchTO.setUserid(userId);
    	associateSearchTO.setSearchType(QADBConstants.USER_ACTION_BASIC);
    	associateSearchTO.setAssociateName(searchForm.getAssociateName());
    	associateSearchTO.setFacetsId(searchForm.getFacetsId());
    	
    	List<Associate> searchResult = null;
    	try {
    		searchResult = associateDAO.getAssociateSearchResults(associateSearchTO);
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
    	mv.addObject("searchResult",searchResult);
    	logger.debug("*** Exit searchAssociates method ***");
    	
    	return mv;
   	}
	
	/**
	 * Method to fetch the Associate details for the selected Associate name to edit the data
	 * @param id is the Associate id selected by the user
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/getAssociate", method = RequestMethod.GET,params = {"id"})
	public ModelAndView getAssociate(@RequestParam(value="id") String id,HttpServletRequest request,HttpSession session) throws Exception {
		logger.debug("Edit Assoc id "+id);
		//ModelAndView mv = AuthorizeUser("admin_associate", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_associate", request);
    	mv.addObject("edit","edit"); // edit EL to identify edit page
    	Associate associateSearchTO = new Associate();
    	associateSearchTO.setSearchType(QADBConstants.EDIT_ASSOCIATE);
    	associateSearchTO.setAssociateId(id);
    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
    	associateSearchTO.setUserid(userId);
    	Associate associateRO =new Associate();
    	
		try {
			associateRO = associateDAO.getAssociate(associateSearchTO);
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
    	mv.addObject(QADBConstants.ASSOCIATE_RES,associateRO);
    	HttpSession sessionE = request.getSession(true);
    	sessionE.setAttribute(QADBConstants.PARAM_ASSOCIATE_ID, id);
		return mv;
	}
	
	/**
	 * Method to save the updated data for the edited Associate
	 * @param associateDetails
	 * @param redirectAttributes
	 * @return
	 */
	@RequestMapping(value = "/updateAssociate", method = RequestMethod.GET)
	public ModelAndView UpdateAssociate(@ModelAttribute("associateForm") Associate associateDetails,RedirectAttributes redirectAttributes, HttpSession session) throws Exception {
		logger.debug("*** Entry UpdateAssociate method ***"+associateDetails.getAssociateId());
		String userId=(String) session.getAttribute(QADBConstants.USERNAME);
		associateDetails.setUserid(userId);
		associateDetails.setUserActyp(QADBConstants.USER_ACTION_EDIT);
		Associate associateUpdate;
		try {
			associateUpdate = associateDAO.saveUpdateAssociate(associateDetails);
			logger.debug("Success msg = " +associateUpdate.getSuccessMsg());
			redirectAttributes.addFlashAttribute(QADBConstants.UPDATE_SUCCESS, associateUpdate.getSuccessMsg());
			redirectAttributes.addFlashAttribute(QADBConstants.SUCCESS_CODE, associateUpdate.getSucessCode());
			redirectAttributes.addFlashAttribute(QADBConstants.SUCCESS_MESSAGE, associateUpdate.getSuccessMsg());
			
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		logger.debug("*** Exit UpdateAssociate method ***");
		return new ModelAndView("redirect:addAssociate");
	}
	
	/**
	 * Method to delete the  Associate
	 * @param associateDetails
	 * @param redirectAttributes
	 * @param session
	 * @return
	 */
	/*@RequestMapping(value = "/deleteAssociate", method = RequestMethod.GET)
	public ModelAndView deleteAssociate(@ModelAttribute("associateForm") Associate associateDetails,RedirectAttributes redirectAttributes,HttpSession session) {
		logger.debug("*** Exit deleteAssociate method ***"+associateDetails.getAssociateId());
		String associateId = (String) session.getAttribute("paramAssociateId");
    	associateDetails.setAssociateId(associateId);
		Associate associateDelete;
		try {
			associateDelete = associateDAO.deleteAssociate(associateDetails);
			logger.debug("Sucess msg = " +associateDelete.getSuccessMsg());
			redirectAttributes.addFlashAttribute(QADBConstants.UPDATE_SUCCESS, associateDelete.getSuccessMsg());
		} catch (Exception e) {
			// TODO Auto-generated catch block
			logger.debug("Exception : "+e.getMessage());
			e.printStackTrace();
		}
		logger.debug("*** Exit deleteAssociate method ***");
		return new ModelAndView("redirect:addAssociate");
	}
	*/
	/**
	 * Associate pdf report generation
	 * @param modelAndView
	 * @param auditingStatus
	 * @return
	 */
	 @RequestMapping(value = "/ListOfAssociates", method = RequestMethod.GET,params = {"auditingStatus"})
	    public ModelAndView listOfAssociates(ModelAndView modelAndView,@RequestParam(value="auditingStatus") String auditingStatus, HttpSession session) throws Exception{
		 logger.debug("*** Entry listOfAssociates method ***"+auditingStatus);
		 Associate associateDetails = new Associate();
		 String userId=(String) session.getAttribute(QADBConstants.USERNAME);
		 associateDetails.setUserid(userId);
		 associateDetails.setSearchType("REPORT");
		 associateDetails.setAuditingStatus(auditingStatus);
		 JRDataSource datasource = null;
			try {
				datasource = associateDAO.getAssociatesListReport(associateDetails);
			} catch (SQLException e) {
				logger.debug("Exception generated"+e.getMessage());
			}
			Map<String,Object> parameterMap = new HashMap<String,Object>();
			parameterMap.put("datasource", datasource);
			modelAndView = new ModelAndView("associateReport", parameterMap);
			logger.debug("*** Exit listOfAssociates method ***");
			return modelAndView;
		}
	/***
	 * Method to put the dropdown values for Operational unit.
	 * @throws SQLException 
	 */
	@ModelAttribute("opUnitForm")
	public void operationalDropdownValues(Map<String, Object> model) throws Exception,SQLException{
		
		 OperationalUnit opUnitForm = new OperationalUnit();
	   	 model.put("opUnitForm", opUnitForm);
	   	
	   	 List<OperationalUnit> operations = operationalUnitDAO.getOperations();
	     model.put("operations", operations);
	     
	     List<OperationalUnit> divisions = operationalUnitDAO.getDivisions();
	     model.put("divisions", divisions);
	     
	     List<OperationalUnit> directors = operationalUnitDAO.getDirectors();
	     model.put("directors", directors);
	     
	     List<OperationalUnit> managers = operationalUnitDAO.getManagers();
	     model.put("managers", managers);
	     
	     List<OperationalUnit> supervisors = operationalUnitDAO.getSupervisors();
	     model.put("supervisors", supervisors);
	     
	     List<Associate> locations = associateDAO.getLocations();
	     model.put("locations", locations);
	}
	
	/**
	 * Method to show the page to add a new Operational Unit.
	 * @return new model and view to enter Operational Unit details.
	 */
	@RequestMapping(value = "/addOperationalUnit", method = RequestMethod.GET)
	public ModelAndView AddOperationalUnit(HttpServletRequest request) {
		
		//ModelAndView mv = AuthorizeUser("admin_operational_unit", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_operational_unit", request);
		return mv;
	}
	
	/**
	 * Method to save new Operational unit with details.
	 * @param model
	 * @param opUnitForm the command object for Operational unit details
	 * @param redirectAttributes
	 * @return 
	 */
	@RequestMapping(value = "/saveOpUnit", method = RequestMethod.GET)
	public ModelAndView saveOperationalUnit(Map<String, Object> model,@ModelAttribute("opUnitForm") OperationalUnit operationalUnitDetails,RedirectAttributes redirectAttributes, HttpSession session) throws Exception {
		logger.debug("*** Entry saveOperationalUnit method ***");
		String userId=(String) session.getAttribute(QADBConstants.USERNAME);
		operationalUnitDetails.setUserId(userId);
		operationalUnitDetails.setUserActyp(QADBConstants.USER_ACTION_NEW);
		operationalUnitDetails.setStatus(QADBConstants.DEFAULT_FLAG_YES);// status enabled on add
		OperationalUnit opUnitSave;
		try {
			opUnitSave = operationalUnitDAO.saveUpdateOpUnit(operationalUnitDetails);
			redirectAttributes.addFlashAttribute(QADBConstants.SUCCESS_CODE,opUnitSave.getSucessCode());
			redirectAttributes.addFlashAttribute(QADBConstants.SUCCESS_MESSAGE,opUnitSave.getSuccessMsg());
			redirectAttributes.addFlashAttribute(QADBConstants.SUCCESS_MESSAGE_ATTRIBUTE, opUnitSave.getSuccessMsg());
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		logger.debug("*** Exit saveOperationalUnit method ***");
		return  new ModelAndView("redirect:addOperationalUnit");
	}
	
	
	/***
	 * Method to show the page to search Operational Units.
	 * @return new model and view to search Operational Units .
	 */
	@RequestMapping(value = "/editOperationalUnit", method = RequestMethod.GET)
   	public ModelAndView searchOpUnits(HttpServletRequest request){
		//ModelAndView mv = AuthorizeUser("admin_operational_unit_edit_list", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_operational_unit_edit_list", request);
		return mv;
	}
	
	/**
	 * Method to fetch the search results for the entered Operational Units.
	 * @param opUnitSearchForm
	 * @return 
	 */
	@RequestMapping(value = "/editOperationalUnitRes", method = RequestMethod.GET)
   	public ModelAndView searchOpUnitsRes(@ModelAttribute("opUnitSearchForm") OperationalUnit searchForm,HttpSession session,HttpServletRequest request) throws Exception {
		//ModelAndView mv = AuthorizeUser("admin_operational_unit_edit_list", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_operational_unit_edit_list", request);
    	logger.debug("OpunitName "+ searchForm.getUnitName());
    	mv.addObject("OpunitName", searchForm.getUnitName());
    	OperationalUnit opUnitSearchTO = new OperationalUnit();
    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
    	opUnitSearchTO.setUserId(userId);
    	opUnitSearchTO.setSearchType(QADBConstants.USER_ACTION_BASIC);
    	opUnitSearchTO.setUnitName(searchForm.getUnitName());
    	List<OperationalUnit> searchResult = null;
    	try {
    		searchResult = operationalUnitDAO.getOpUnitSearchResults(opUnitSearchTO);
    	
    	} catch (Exception e) {
    		logger.debug("Exception generated"+e.getMessage());
    	}
    	mv.addObject("searchResult", searchResult);
    	logger.debug("*** Exit searchOpUnits method ***");
    	return mv;
    	
   	}
	
	/**
	 * Method to fetch the data for the selected Operational Unit name to edit the data
	 * @param id is the Operational Unit id selected by the user
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/getOpUnit", method = RequestMethod.GET,params = {"id"})
	public ModelAndView getOpUnit(@RequestParam(value="id") String id,HttpServletRequest request, HttpSession session) throws Exception {
		logger.debug("*** Entry getOpUnit method ***");
		logger.debug("Edit OpUnit id "+id);
		//ModelAndView mv = AuthorizeUser("admin_operational_unit", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_operational_unit", request);
    	mv.addObject("edit","edit"); // edit EL to identify edit page
    	OperationalUnit opUnitSearchTO = new OperationalUnit();
    	opUnitSearchTO.setSearchType(QADBConstants.EDIT_OP_UNIT);
    	opUnitSearchTO.setUnitId(id);
    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
    	opUnitSearchTO.setUserId(userId);
    	opUnitSearchTO.setPagetype(QADBConstants.OP_PAGE_TYPE);
    	OperationalUnit opUnitRO = null ;
    	List<OperationalUnit> associatesRS = null ;
    	try {
    		opUnitRO = operationalUnitDAO.getOpUnit(opUnitSearchTO);
    		associatesRS = operationalUnitDAO.getAssociatesRS(opUnitSearchTO);
    	} catch (Exception e) {
    		logger.debug("Exception generated"+e.getMessage());
    	}
    	mv.addObject("associatesRS", associatesRS);
    	mv.addObject("opUnitRO",opUnitRO);
    	HttpSession sessionE = request.getSession(true);
    	sessionE.setAttribute("paramOpUnitId", id);
    	logger.debug("*** Exit getOpUnit method ***");
		return mv;
	}
	
	//change for DMND0003323
		@RequestMapping(value = "/listOfOperationalUnit")
	    public ModelAndView listOfOperationalUnit(ModelAndView modelAndView, HttpSession session) throws Exception{
		 logger.debug("*** Entry listOfAssociates method ***");
		 OperationalUnit opUnitSearchTO = new OperationalUnit();
		 String userId=(String) session.getAttribute(QADBConstants.USERNAME);
		 opUnitSearchTO.setUserId(userId);
		 opUnitSearchTO.setSearchType(QADBConstants.USER_ACTION_BASIC);
		 //opUnitSearchTO.setSearchType("REPORT");
		 opUnitSearchTO.setUnitName(null);
		 
		 JRDataSource datasource = null;
			try {
				datasource = operationalUnitDAO.getOpUnitSearchResultsList(opUnitSearchTO);
			} catch (SQLException e) {
				logger.debug("Exception generated"+e.getMessage());
			}
			Map<String,Object> parameterMap = new HashMap<String,Object>();
			parameterMap.put("datasource", datasource);
			modelAndView = new ModelAndView("oprUnitReport", parameterMap);
			logger.debug("*** Exit listOfOperationalUnit method ***");
			return modelAndView;
		}
	
	/**
	 * Method to save the updated data for the edited Op unit
	 * @param opUnitDetails
	 * @param redirectAttributes
	 * @return
	 */
	@RequestMapping(value = "/updateOperationalUnit", method = RequestMethod.GET)
	public ModelAndView UpdateOpUnit(@ModelAttribute("opUnitForm") OperationalUnit opUnitDetails,RedirectAttributes redirectAttributes,HttpSession session) throws Exception {
		logger.debug("In Asso Update "+opUnitDetails.getUnitId());
		String opUnitId = (String) session.getAttribute("paramOpUnitId");
    	logger.debug("Op id" +opUnitId);
    	opUnitDetails.setUnitId(opUnitId);
    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
    	opUnitDetails.setUserId(userId);
		opUnitDetails.setUserActyp(QADBConstants.USER_ACTION_EDIT);
		OperationalUnit opUnitUpdate;
		try {
			opUnitUpdate = operationalUnitDAO.saveUpdateOpUnit(opUnitDetails);
			logger.debug("Sucess msg -- " +opUnitUpdate.getSuccessMsg());
			redirectAttributes.addFlashAttribute("upSucess", opUnitUpdate.getSuccessMsg());
		} catch (Exception e) {
			logger.debug("Exception = "+e.getMessage());
		}
		return new ModelAndView("redirect:addOperationalUnit");
	}
	
	/**
	 * Method to delete the  Delete OP unit
	 * @param associateDetails
	 * @param redirectAttributes
	 * @param session
	 * @return
	 */
	@RequestMapping(value = "/deleteOpUnit", method = RequestMethod.GET)
	public ModelAndView DeleteOpUnit(@ModelAttribute("opUnitForm") OperationalUnit opUnitDetails,RedirectAttributes redirectAttributes,HttpSession session) throws Exception {
		logger.debug("*** Entry DeleteOpUnit method ***");
		String opUnitId = (String) session.getAttribute("paramOpUnitId");
    	logger.debug(" Op id" +opUnitId);
    	opUnitDetails.setUnitId(opUnitId);
		OperationalUnit opUnitDelete;
		try {
			opUnitDelete = operationalUnitDAO.deleteOpUnit(opUnitDetails);
			logger.debug("Sucess msg -- " +opUnitDelete.getSuccessMsg());
			redirectAttributes.addFlashAttribute(QADBConstants.DELETE_SUCCESS, opUnitDelete.getSuccessMsg());
		} catch (Exception e) {
			logger.debug("Exception = "+e.getMessage());
		}
		logger.debug("*** Exit DeleteOpUnit method ***");
		return new ModelAndView("redirect:addOperationalUnit");
	}
	
	/***
	 * Method to show the page to add a new error.
	 * @return new model and view to enter error details.
	 */
	@RequestMapping(value = "/addErrorCodes", method = RequestMethod.GET)
	public ModelAndView addErrorCodes(HttpServletRequest request) {
		logger.debug("*** addErrorCodes method ***");
		//ModelAndView mv = AuthorizeUser("admin_errorCodes", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_errorCodes", request);
		return mv; 
	}
	
	/**
	 * Method to save new error with details.
	 * @param model
	 * @param errorCodes the command object for error codes
	 * @param redirectAttributes
	 * @return 
	 */
	@RequestMapping(value = "/saveErrorCodes", method = RequestMethod.GET)
	public ModelAndView saveErrorCodes(Map<String, Object> model,@ModelAttribute("errorCodeForm") ErrorCodes errorCodesdetails,RedirectAttributes redirectAttributesError,HttpSession session) throws Exception {
		logger.debug("*** Entry addErrorCodes method ***");		
		ErrorCodes errorCodeSave;
		String userId=(String) session.getAttribute(QADBConstants.USERNAME);
		errorCodesdetails.setUserid(userId);
		errorCodesdetails.setUserActyp(QADBConstants.USER_ACTION_NEW);
		try {
			errorCodeSave = errorCodesDAO.saveErrorCodes(errorCodesdetails);
			if(errorCodeSave.getSucessCode().equalsIgnoreCase("000")){
				redirectAttributesError.addFlashAttribute(QADBConstants.SUCCESS_MESSAGE_ATTRIBUTE, "SUCCESS");
			}else if(errorCodeSave.getSucessCode().equalsIgnoreCase("001")){
				redirectAttributesError.addFlashAttribute(QADBConstants.SUCCESS_CODE, errorCodeSave.getSucessCode());
				redirectAttributesError.addFlashAttribute(QADBConstants.SUCCESS_MESSAGE, "Error already exist!");
			}
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		logger.debug("*** Exit addErrorCodes method ***");	
		return new ModelAndView("redirect:addErrorCodes");
	}
	

	/***
	 * Method to show the page to error codes.
	 * @return new model and view to search error codes .
	 */
	@RequestMapping(value = "/editErrorCodes", method = RequestMethod.GET)
   	public ModelAndView editErrorCodes(HttpServletRequest request){
		//ModelAndView mv = AuthorizeUser("admin_errorCodes_edit_list", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_errorCodes_edit_list", request);
		mv.addObject("errorCode","");
		return mv;
	}
	
	/**
	 * Method to fetch the search results for the entered error code.
	 * @param errorCodesSearch
	 * @param session
	 * @return 
	 */
	@RequestMapping(value = "/editErrorCodesRes", method = RequestMethod.GET)
	public ModelAndView editErrorCodesRes(@ModelAttribute("errorSearchForm") ErrorCodes errorCodesSearch, HttpSession session,HttpServletRequest request) throws Exception {
		logger.debug("*** Entry editErrorCodes method ***");
		//ModelAndView mv = AuthorizeUser("admin_errorCodes_edit_list", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_errorCodes_edit_list", request);
    	mv.addObject("edit","edit"); // edit EL to identify edit page
    	mv.addObject("errorCode",errorCodesSearch.getErrorCode());
    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
    	errorCodesSearch.setUserid(userId);
    	errorCodesSearch.setUserActyp(QADBConstants.USER_ACTION_BASIC);
    	errorCodesSearch.setErrorCode(errorCodesSearch.getErrorCode());
    	List<ErrorCodes> errorCodesRO = null;
    	try {
    		errorCodesRO = errorCodesDAO.searchErrorCodes(errorCodesSearch);
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
    	mv.addObject(QADBConstants.ERROR_CODES_RES,errorCodesRO);
    	logger.debug("*** Exit editErrorCodes method ***");
		return mv;
	}
	
	//change for DMND0003323
			@RequestMapping(value = "/listOfErrorCode")
		    public ModelAndView listOfErrorCode(ModelAndView modelAndView, HttpSession session) throws Exception{
			 logger.debug("*** Entry listOfErrorCode method ***");
			 ErrorCodes errorCodesSearch = new ErrorCodes();
			 
			 String userId=(String) session.getAttribute(QADBConstants.USERNAME);
		    	errorCodesSearch.setUserid(userId);
		    	errorCodesSearch.setUserActyp(QADBConstants.USER_ACTION_BASIC);
		    	//errorCodesSearch.setStatus(auditingStatus);
		    	errorCodesSearch.setErrorCode(errorCodesSearch.getErrorCode());
			 
			 
			 JRDataSource datasource = null;
				try {
					datasource = errorCodesDAO.searchErrorCodesList(errorCodesSearch);
				} catch (SQLException e) {
					logger.debug("Exception generated"+e.getMessage());
				}
				Map<String,Object> parameterMap = new HashMap<String,Object>();
				parameterMap.put("datasource", datasource);
				modelAndView = new ModelAndView("errorCodeReport", parameterMap);
				logger.debug("*** Exit listOfErrorCode method ***");
				return modelAndView;
			}
	
	/**
	 * Method to fetch the error details for the selected error to edit the data
	 * @param id is the error code selected by the user
	 * @param session
	 * @return
	 */
	@RequestMapping(value = "/getErrorData", method = RequestMethod.GET,params = {"id"})
	public ModelAndView getErrorData(@RequestParam(value="id") String id, HttpSession session,HttpServletRequest request) throws Exception {
		logger.debug("*** Entry getErrorData method ***");
		session.setAttribute("errorCode", id);
		//ModelAndView mv = AuthorizeUser("admin_errorCodes", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_errorCodes", request);
    	mv.addObject("edit","edit"); // edit EL to identify edit page
    	ErrorCodes errorCodesTO = new ErrorCodes();
    	errorCodesTO.setUserActyp(QADBConstants.EDIT_ERR_CODE);
    	errorCodesTO.setErrorCode(id);
    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
    	errorCodesTO.setUserid(userId);
    	ErrorCodes errorCodesRO = null;
    	
    	try {
			errorCodesRO = errorCodesDAO.getErrorData(errorCodesTO);
		} catch (SQLException e) {
			logger.debug("Exception generated"+e.getMessage());
		}
    	mv.addObject(QADBConstants.ERROR_CODES_RES,errorCodesRO);
    	logger.debug("*** Exit getErrorData method ***");
		return mv;
	}
	
	/**
	 * Method to save the updated data for the edited error
	 * @param errorCodeDetails
	 * @param redirectAttributes
	 * @param session
	 * @return
	 */
	@RequestMapping(value = "/updateErrorCodes", method = RequestMethod.GET)
	public ModelAndView updateErrorCodes(@ModelAttribute("errorCodeForm") ErrorCodes errorCodeDetails,RedirectAttributes redirectAttributes,HttpSession session) throws Exception {
		logger.debug("*** Entry updateErrorCodes method ***");
		errorCodeDetails.setErrorCode(session.getAttribute("errorCode").toString());
		String userId=(String) session.getAttribute(QADBConstants.USERNAME);
		errorCodeDetails.setUserid(userId);
		errorCodeDetails.setUserActyp(QADBConstants.USER_ACTION_EDIT);
		ErrorCodes errorCodeUpdate;
		try {
			errorCodeUpdate = errorCodesDAO.saveErrorCodes(errorCodeDetails);
			logger.debug("Sucess msg -- " +errorCodeUpdate.getSucessCode());
			if(errorCodeUpdate.getSucessCode().equalsIgnoreCase("000")){
				redirectAttributes.addFlashAttribute(QADBConstants.UPDATE_SUCCESS, "SUCCESS");
			}
			
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		logger.debug("*** Exit updateErrorCodes method ***");
		return new ModelAndView("redirect:addErrorCodes");
	}
	
	/**
	 * This method is to soft delete an error for the database.
	 * @param errorCodeDetails
	 * @param redirectAttributes
	 * @param session
	 * @return
	 */
	/*@RequestMapping(value = "/deleteError", method = RequestMethod.GET)
	public ModelAndView deleteError(@ModelAttribute("errorCodeForm") ErrorCodes errorCodeDetails,RedirectAttributes redirectAttributes,HttpSession session) {
		logger.debug("*** Entry deleteError method ***");
		errorCodeDetails.setErrorCode(session.getAttribute("errorCode").toString());
		ErrorCodes errorCodesDelete;
		try {
			errorCodesDelete = errorCodesDAO.deleteError(errorCodeDetails);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			logger.debug("Exception generated"+e.getMessage());
			e.printStackTrace();
		}
		logger.debug("*** Exit deleteError method ***");
		return new ModelAndView("admin_errorCodes","successMsg","Error deleted successfully!");
	}*/
	
//	PerformanceGroup
	
	@RequestMapping(value = "/addPerformanceGroup", method = RequestMethod.GET)
	public ModelAndView AddPerformanceGroup(HttpServletRequest request) throws Exception,SQLException {
		//ModelAndView mv = AuthorizeUser("admin_performance_group", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_performance_group", request);
		
		String userGrp = authorizationService.getUserGroup(request);
		
		List<AuditDetails> priPGAList = auditDetailsDao.getPriPGA(userGrp); //getPriPGA
	    mv.addObject("priPGAList", priPGAList);
		return mv;
	}
	
	
	/**
	 * Method to save new performance group
	 * @param model
	 * @param performanceGroup
	 * @param redirectAttributes
	 * @param session
	 * @return
	 */
	@RequestMapping(value = "/savePerformanceGroup", method = RequestMethod.GET)
	public ModelAndView addPerformanceGroup(Map<String, Object> model,@ModelAttribute("perfGrpForm") PerformanceGroup performanceGroup,RedirectAttributes redirectAttributespg,HttpSession session) throws Exception {
		logger.debug("*** Entry addPerformanceGroup method ***");		
		PerformanceGroup performanceGroupSave;
		String userId=(String) session.getAttribute(QADBConstants.USERNAME);
		performanceGroup.setUserid(userId);
		performanceGroup.setUserActyp(QADBConstants.USER_ACTION_NEW);
		try {
			performanceGroupSave = performanceGroupDAO.savePerformanceGroup(performanceGroup);
			redirectAttributespg.addFlashAttribute(QADBConstants.SUCCESS_CODE,performanceGroupSave.getSuccessCode());
			redirectAttributespg.addFlashAttribute(QADBConstants.SUCCESS_MESSAGE,performanceGroupSave.getSuccessMsg());
			if(performanceGroupSave.getSuccessCode().equalsIgnoreCase("000")){
				redirectAttributespg.addFlashAttribute(QADBConstants.SUCCESS_MESSAGE_ATTRIBUTE, "SUCCESS");
			}
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		logger.debug("*** Exit addPerformanceGroup method ***");
		return new ModelAndView("redirect:addPerformanceGroup");
	}
	
	/***
	 * Method to show the page to search Performance Group.
	 * @return new model and view to search Performance Group .
	 */
	@RequestMapping(value = "/editPerformanceGroup", method = RequestMethod.GET)
   	public ModelAndView editPerformanceGroup(HttpServletRequest request){
		//ModelAndView mv = AuthorizeUser("admin_performance_group_edit_list", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_performance_group_edit_list", request);
		return mv;
	}
	
	/**
	 * Method to fetch performance group search results
	 * @param performanceGroupSearch
	 * @param session
	 * @return
	 */
	@RequestMapping(value = "/editPerformanceGroupRes", method = RequestMethod.GET)
	public ModelAndView editPerformanceGroupRes(@ModelAttribute("perfGrpSearchForm") PerformanceGroup performanceGroupSearch, HttpSession session,HttpServletRequest request) throws Exception {

		logger.debug("*** Entry editPerformanceGroup method ***");
		//ModelAndView mv = AuthorizeUser("admin_performance_group_edit_list", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_performance_group_edit_list", request);
    	mv.addObject("edit","edit"); // edit EL to identify edit page
    	mv.addObject("groupId",performanceGroupSearch.getGroupId());
    	mv.addObject("groupName",performanceGroupSearch.getGroupName());
    	mv.addObject("type",performanceGroupSearch.getType());
    	//logger.debug("Performance Group Details : "+performanceGroupSearch.getType());
    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
    	performanceGroupSearch.setUserid(userId);
    	performanceGroupSearch.setUserActyp(QADBConstants.USER_ACTION_BASIC);
    	List<PerformanceGroup> performanceGroupRO = null;
    	try {
    		performanceGroupRO = performanceGroupDAO.searchperformanceGroup(performanceGroupSearch);
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
    	mv.addObject("performanceGroupRO",performanceGroupRO);
    	logger.debug("*** Exit editPerformanceGroup method ***");
		return mv;
	}
	
	//change for DMND0003323
	@RequestMapping(value = "/listOfPerformanceGroup", method = RequestMethod.GET,params = {"auditingStatus"})
    public ModelAndView listOfPerformanceGroup(ModelAndView modelAndView,@RequestParam(value="auditingStatus") String auditingStatus, HttpSession session) throws Exception{
	 logger.debug("*** Entry listOfPerformanceGroup method ***");
	 PerformanceGroup performanceGroupSearch = new PerformanceGroup();
	 String userId=(String) session.getAttribute(QADBConstants.USERNAME);
 	 performanceGroupSearch.setUserid(userId);
 	 performanceGroupSearch.setUserActyp(QADBConstants.USER_ACTION_BASIC);
 	 performanceGroupSearch.setType(auditingStatus);
	 //associateDetails.setAuditingStatus(auditingStatus);
 	 JRDataSource datasource = null;
		try {
			datasource = performanceGroupDAO.searchperformanceGroupList(performanceGroupSearch);
		} catch (SQLException e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		Map<String,Object> parameterMap = new HashMap<String,Object>();
		parameterMap.put("datasource", datasource);
		modelAndView = new ModelAndView("perfGroupReport", parameterMap);
		logger.debug("*** Exit listOfPerformanceGroup method ***");
		return modelAndView;
	}
	
	/**
	 * Method to fetch the details of the performance group selected for edit
	 * @param id
	 * @param session
	 * @return
	 */
	@RequestMapping(value = "/getPerformanceGroupData", method = RequestMethod.GET,params = {"id"})
	public ModelAndView getPerformanceGroupData(@RequestParam(value="id") String id, HttpSession session,HttpServletRequest request) throws Exception {

		logger.debug("*** Entry getPerformanceGroupData method ***");
		session.setAttribute("grpID", id);
		//ModelAndView mv = AuthorizeUser("admin_performance_group", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_performance_group", request);
    	mv.addObject("edit","edit"); // edit EL to identify edit page
    	String userGrp = authorizationService.getUserGroup(request);
    	List<AuditDetails> priPGAList = auditDetailsDao.getPriPGA(userGrp);
	    mv.addObject("priPGAList", priPGAList);
    	PerformanceGroup performanceGroupTO = new PerformanceGroup();
    	performanceGroupTO.setUserActyp(QADBConstants.EDIT_PERF_GRP);
    	performanceGroupTO.setGrpid(id);
    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
    	performanceGroupTO.setUserid(userId);
    	PerformanceGroup performanceGroupRO = null;
    	
    	try {
    		performanceGroupRO = performanceGroupDAO.getPerformanceGroupData(performanceGroupTO);
		} catch (SQLException e) {
			logger.debug("Exception generated"+e.getMessage());
		}
    	mv.addObject("performanceGroupRO",performanceGroupRO);
    	logger.debug("*** Exit getPerformanceGroupData method ***");
		return mv;
	}
	
	/**
	 * Method to update the performance group details.
	 * @param performanceGroupdetails
	 * @param redirectAttributes
	 * @param session
	 * @return
	 */
	@RequestMapping(value = "/updatePerformanceGroup", method = RequestMethod.GET)
	public ModelAndView updatePerformanceGroup(@ModelAttribute("perfGrpForm") PerformanceGroup performanceGroupdetails,RedirectAttributes redirectAttributespg,HttpSession session) throws Exception {

		logger.debug("*** Entry updatePerformanceGroup method ***");
		performanceGroupdetails.setGrpid(session.getAttribute("grpID").toString());
		String userId=(String) session.getAttribute(QADBConstants.USERNAME);
		performanceGroupdetails.setUserid(userId);
		performanceGroupdetails.setUserActyp(QADBConstants.USER_ACTION_EDIT);
		PerformanceGroup performanceGroupUpdate;
		try {
			performanceGroupUpdate = performanceGroupDAO.savePerformanceGroup(performanceGroupdetails);
			if(performanceGroupUpdate.getSuccessCode().equalsIgnoreCase("000")){
				redirectAttributespg.addFlashAttribute(QADBConstants.UPDATE_SUCCESS, "SUCCESS");
			}
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		logger.debug("*** Exit updatePerformanceGroup method ***");
		return new ModelAndView("redirect:addPerformanceGroup");
	}
	
	/**
	 * 
	 * @return
	 * @throws SQLException 
	 */
	@RequestMapping(value = "/addRootCause", method = RequestMethod.GET)
	public ModelAndView AddRootCause(HttpServletRequest request) throws SQLException {
		
		//ModelAndView mv = AuthorizeUser("admin_rootCause", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_rootCause", request);
		
		List<ErrorDetails> errorTypeList = errorDetailsDao.getAllErrorTypes();
		mv.addObject("errorTypes", errorTypeList);
		return mv;
	}
	
	/**
	 * Method to add a new root cause.
	 * @param model
	 * @param rootCause
	 * @param redirectAttributes
	 * @param session
	 * @return
	 */
	@RequestMapping(value = "/saveRootCause", method = RequestMethod.GET)
	public ModelAndView addRootCause(Map<String, Object> model,@ModelAttribute("rootCauseForm") RootCause rootCause,RedirectAttributes redirectAttributesRt,HttpSession session) throws Exception {
		logger.debug("*** Entry addRootCause method ***");		
		RootCause rootCauseSave;
		String userId=(String) session.getAttribute(QADBConstants.USERNAME);
		rootCause.setUserid(userId);
		rootCause.setUserActyp(QADBConstants.USER_ACTION_NEW);
		try {
			rootCauseSave = rootCauseDAO.saveRootCause(rootCause);
			redirectAttributesRt.addFlashAttribute(QADBConstants.SUCCESS_CODE,rootCauseSave.getSuccessCode());
			redirectAttributesRt.addFlashAttribute(QADBConstants.SUCCESS_MESSAGE,rootCauseSave.getSuccessMsg());
			if(rootCauseSave.getSuccessCode().equalsIgnoreCase("000")){
				redirectAttributesRt.addFlashAttribute(QADBConstants.SUCCESS_MESSAGE_ATTRIBUTE, "SUCCESS");
			}
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		logger.debug("*** Exit addRootCause method ***");	
		return new ModelAndView("redirect:addRootCause");
	}
	
	
	/***
	 * Method to show the page to search Root Cause.
	 * @return new model and view to search Root Cause .
	 */
	@RequestMapping(value = "/editRootCause", method = RequestMethod.GET)
   	public ModelAndView editRootCause(HttpServletRequest request){
		//ModelAndView mv = AuthorizeUser("admin_rootCause_edit_list", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_rootCause_edit_list", request);
		return mv;
	}
	
	/**
	 * Method to fetch the search results for the root cause
	 * @param rootCauseSearch
	 * @param session
	 * @return
	 */
	@RequestMapping(value = "/editRootCauseRes", method = RequestMethod.GET)
	public ModelAndView editRootCauseRes(@ModelAttribute("rootCauseSearchForm") RootCause rootCauseSearch, HttpSession session, HttpServletRequest request) throws Exception {
		logger.debug("*** Entry editRootCause method ***");
		//ModelAndView mv = AuthorizeUser("admin_rootCause_edit_list", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_rootCause_edit_list", request);
    	mv.addObject("edit","edit"); // edit EL to identify edit page
    	mv.addObject("rootCauseName",rootCauseSearch.getRootCauseName());
    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
    	rootCauseSearch.setUserid(userId);
    	rootCauseSearch.setUserActyp(QADBConstants.USER_ACTION_BASIC);
    	List<RootCause> rootCauseRO = null;
    	try {
    		rootCauseRO = rootCauseDAO.searchRootCause(rootCauseSearch);
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
    	mv.addObject(QADBConstants.ROOT_CAUSE_RES,rootCauseRO);
    	logger.debug("*** Exit editRootCause method ***");
		return mv;
	}
	
	//change for DMND0003323
		@RequestMapping(value = "/listOfRootCause")
	    public ModelAndView listOfRootCause(ModelAndView modelAndView, HttpSession session) throws Exception{
		 logger.debug("*** Entry listOfRootCause method ***");
		 RootCause rootCauseSearch = new RootCause();
		 String userId=(String) session.getAttribute(QADBConstants.USERNAME);
		 rootCauseSearch.setUserid(userId);
		 //rootCauseSearch.setSearchType("REPORT");
		 rootCauseSearch.setUserActyp(QADBConstants.USER_ACTION_BASIC);
		 //rootCauseSearch.setRootCauseStatus(auditingStatus);
		 JRDataSource datasource = null;
			try {
				datasource = rootCauseDAO.searchRootCauseList(rootCauseSearch);
			} catch (SQLException e) {
				logger.debug("Exception generated"+e.getMessage());
			}
			Map<String,Object> parameterMap = new HashMap<String,Object>();
			parameterMap.put("datasource", datasource);
			modelAndView = new ModelAndView("rootCauseReport", parameterMap);
			logger.debug("*** Exit listOfRootCause method ***");
			return modelAndView;
		}
	/**
	 * Method to populate the details of the roots cause selected for update
	 * @param id
	 * @param session
	 * @return
	 */
	@RequestMapping(value = "/getRootCause", method = RequestMethod.GET,params = {"id"})
	public ModelAndView getRootCause(@RequestParam(value="id") String id, HttpSession session, HttpServletRequest request) throws Exception {
		logger.debug("*** Entry getRootCause method ***");
		session.setAttribute("rootCause", id);
		//ModelAndView mv = AuthorizeUser("admin_rootCause", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_rootCause", request);
    	mv.addObject("edit","edit"); // edit EL to identify edit page
    	List<ErrorDetails> errorTypeList = errorDetailsDao.getAllErrorTypes();
		mv.addObject("errorTypes", errorTypeList);
    	RootCause rootCauseTO = new RootCause();
    	rootCauseTO.setUserActyp(QADBConstants.EDIT_RT_CAUSE);
    	rootCauseTO.setRootCauseId(id);
    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
    	rootCauseTO.setUserid(userId);
    	RootCause rootCauseRO = null;
    	
    	try {
    		rootCauseRO = rootCauseDAO.getRootCause(rootCauseTO);
		} catch (SQLException e) {
			logger.debug("Exception generated"+e.getMessage());
		}
    	mv.addObject(QADBConstants.ROOT_CAUSE_RES,rootCauseRO);
    	logger.debug("*** Exit getRootCause method ***");
		return mv;
	}
	
	/**
	 * Method to save the updated the details for selected root cause
	 * @param rootCauseDetails
	 * @param redirectAttributes
	 * @param session
	 * @return
	 */
	@RequestMapping(value = "/updateRootCause", method = RequestMethod.GET)
	public ModelAndView updateRootCause(@ModelAttribute("rootCauseForm") RootCause rootCauseDetails,RedirectAttributes redirectAttributes,HttpSession session) throws Exception {
		logger.debug("*** Entry updateRootCause method ***");
		rootCauseDetails.setRootCauseId(session.getAttribute("rootCause").toString());
		String userId=(String) session.getAttribute(QADBConstants.USERNAME);
		rootCauseDetails.setUserid(userId);
		rootCauseDetails.setUserActyp(QADBConstants.USER_ACTION_EDIT);
		RootCause rootCauseUpdate;
		try {
			rootCauseUpdate = rootCauseDAO.saveRootCause(rootCauseDetails);
			logger.debug("message-->"+rootCauseUpdate.getSuccessCode());
			if(rootCauseUpdate.getSuccessCode().equalsIgnoreCase("000")){
				redirectAttributes.addFlashAttribute("upSucess", "SUCCESS");
			}
			
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		logger.debug("*** Exit updateRootCause method ***");
		return new ModelAndView("redirect:addRootCause");
	}
	
	/***
	 * Method to show the page to add a new JobTitle.
	 * @return new model and view to enter JobTitle details and all job titles list
	 */
	@RequestMapping(value = "/addJobTitles", method = RequestMethod.GET)
	public ModelAndView addJobTitles(HttpSession session, HttpServletRequest request) throws Exception {
		logger.debug("*** Entry addjobtitles method ***");
		//ModelAndView mv = AuthorizeUser("admin_jobTitles", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_jobTitles", request);
		JobTitle jobTitleTO = new JobTitle();
		String userId=(String) session.getAttribute(QADBConstants.USERNAME);
		jobTitleTO.setUserId(userId);
		jobTitleTO.setSearchType(QADBConstants.USER_ACTION_BASIC);
		List<JobTitle> jobTitlesRS = null;
    	try {
    		jobTitlesRS = jobTitlesDAO.getJobTitles(jobTitleTO);
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
    	mv.addObject("jobTitlesRS", jobTitlesRS);
    	logger.debug("*** Exit addjobtitles method ***");
    	
    	return mv; 
   	
	}
	
	/**
	 * Method to save new JobTitle with details.
	 * @param model
	 * @param jobTitlesForm the command object for jobTitle
	 * @param redirectAttributes
	 * @return 
	 */
	@RequestMapping(value = "/saveJobTitles", method = RequestMethod.GET)
	public ModelAndView saveJobTitles(Map<String, Object> model,@ModelAttribute("jobTitlesForm") JobTitle jobTitlesTO ,RedirectAttributes redirectAttributes, HttpSession session) throws Exception {
    	logger.debug("value Job title "+ jobTitlesTO.getTitleName());
    	jobTitlesTO.setUserActyp(QADBConstants.USER_ACTION_NEW);
    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
    	jobTitlesTO.setUserId(userId);
    	jobTitlesTO.setStatus(QADBConstants.DEFAULT_FLAG_YES);// status enabled on add
		
    	JobTitle jobTitleSave;
		try {
			jobTitleSave = jobTitlesDAO.saveUpdateJobTitle(jobTitlesTO);
			redirectAttributes.addFlashAttribute(QADBConstants.SUCCESS_CODE,jobTitleSave.getSucessCode());
			redirectAttributes.addFlashAttribute(QADBConstants.SUCCESS_MESSAGE,jobTitleSave.getSuccessMsg());
			redirectAttributes.addFlashAttribute(QADBConstants.SUCCESS_MESSAGE_ATTRIBUTE, jobTitleSave.getSuccessMsg());
		} catch (Exception e) {
			logger.debug("Exception "+e.getMessage());
		}
	
		return new ModelAndView("redirect:addJobTitles");
	}
	
	/**
	 * Method to fetch the JobTitle details for the selected JobTitle id to edit the data
	 * @param id is the JobTitle id selected by the user
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/getJobTitle", method = RequestMethod.GET,params = {"id"})
	public ModelAndView getJobTitle(@RequestParam(value="id") String id,HttpServletRequest request, HttpSession session) throws Exception {
		logger.debug("Edit jobTitle id "+id);
		//ModelAndView mv = AuthorizeUser("admin_jobTitles", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_jobTitles", request);
    	mv.addObject("edit","edit"); // edit EL to identify edit page
    	JobTitle jobTitleSearchTO = new JobTitle();
    	jobTitleSearchTO.setSearchType(QADBConstants.EDIT_JOB_TITLE);
    	jobTitleSearchTO.setTitleId(id);
    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
    	jobTitleSearchTO.setUserId(userId);
    	JobTitle jobTitleRO = null;
    	try{
    		jobTitleRO = jobTitlesDAO.getJobTitleById(jobTitleSearchTO);
    		mv.addObject("jobTitleRO",jobTitleRO);
    		logger.debug("Edit JobTitleName "+jobTitleRO.getTitleName());
    	} catch (Exception e) {
			logger.debug("Exception "+e.getMessage());
		}
    	HttpSession sessionE = request.getSession(true);
    	sessionE.setAttribute("paramJobTitleId", id);
		return mv;
	}
	
	
	/**
	 * Method to save the updated data for the edited JobTitle
	 * @param jobTitlesTO
	 * @param redirectAttributes
	 * @return
	 */
	@RequestMapping(value = "/updateJobTitles", method = RequestMethod.GET)
	public ModelAndView updateJobTitles(@ModelAttribute("jobTitlesForm") JobTitle jobTitlesTO,RedirectAttributes redirectAttributes,HttpSession session) throws Exception {
		logger.debug("In Job Update "+jobTitlesTO.getTitleId());
		String jobTitleId = (String) session.getAttribute("paramJobTitleId");
    	logger.debug(" Op id" +jobTitleId);
    	jobTitlesTO.setTitleId(jobTitleId);
    	jobTitlesTO.setUserActyp(QADBConstants.USER_ACTION_EDIT);
    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
    	jobTitlesTO.setUserId(userId);
    	//jobTitlesTO.setSearchType("EDIT_JOB_TITLE");
    	JobTitle jobTitleUpdate;
		try {
			jobTitleUpdate = jobTitlesDAO.saveUpdateJobTitle(jobTitlesTO);
			logger.debug("Sucess msg -- " +jobTitleUpdate.getSuccessMsg());
			redirectAttributes.addFlashAttribute(QADBConstants.UPDATE_SUCCESS, jobTitleUpdate.getSuccessMsg());
			
		} catch (Exception e) {
			logger.debug("Exception "+e.getMessage());
		}
		return new ModelAndView("redirect:addJobTitles");
	}
	
	
	// Specialty
	/***
	 * Method to show the page to add a new Specialty and all Specialties list.
	 * @return new model and view to enter Specialty details and all Specialties list
	 */
	@RequestMapping(value = "/addSpecialty", method = RequestMethod.GET)
	public ModelAndView addSpecialty(HttpSession session,HttpServletRequest request) throws Exception {
		logger.debug("*** Entry addSpecialty method ***");
		//ModelAndView mv = AuthorizeUser("admin_specialty", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_specialty", request);
		Speciality specialtyTO = new Speciality();
		
		String b[] = {request.getHeader("iv-groups")};
		//String b[] = {"Contractor","PeopleSoft_ELM_User","onestop_users","qadb-superadmin_users"};
		logger.debug("Grp array "+b);
		if(!(((Arrays.toString(b)).replace("[", "")).replace("]", "")).equals("null")){
		int last = b.length - 1;
		logger.debug("Grp last ele" +last);
		if(((b[last].toString()).contains("qadb-superadmin_users"))){
			specialtyTO.setUserGrp("ALL");
		}
		else if(((b[last].toString()).contains("qadb-samd-admin_users"))){
			specialtyTO.setUserGrp("SAMMD");
		}
		else if(((b[last].toString()).contains("qadb-cd-admin_user"))){
			specialtyTO.setUserGrp("CD");
		}
		else{
			mv = new ModelAndView("unauthorized");
		}
		}
		
		String userId=(String) session.getAttribute(QADBConstants.USERNAME);
		specialtyTO.setUserId(userId); //user id from system
		specialtyTO.setSearchType(QADBConstants.USER_ACTION_BASIC);
		List<Speciality> specialityRS = null;
    	try {
    		specialityRS = specialityDAO.getSpeciality(specialtyTO);
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
    	mv.addObject("specialityRS", specialityRS);
    	logger.debug("*** Exit addSpecialty method ***");
    	return mv; 
	}
	
	/**
	 * Method to save new Specialty with details.
	 * @param model
	 * @param specialtyForm the command object for Specialty
	 * @param redirectAttributes
	 * @return 
	 */
	@RequestMapping(value = "/saveSpecialty", method = RequestMethod.GET)
	public ModelAndView saveSpecialty(Map<String, Object> model,@ModelAttribute("specialtyForm") Speciality specialtyTO ,RedirectAttributes redirectAttributes, HttpSession session) throws Exception {
		logger.debug("*** Entry saveSpecialty method ***");
    	logger.debug("value Spec "+ specialtyTO.getSpecialty());
    	specialtyTO.setUserActyp(QADBConstants.USER_ACTION_NEW);
    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
		specialtyTO.setUserId(userId); 
    	specialtyTO.setStatus(QADBConstants.DEFAULT_FLAG_YES);//for default enable
    	Speciality specialitySave;
		try {
			specialitySave = specialityDAO.saveUpdateSpeciality(specialtyTO);
			redirectAttributes.addFlashAttribute(QADBConstants.SUCCESS_CODE,specialitySave.getSucessCode());
			redirectAttributes.addFlashAttribute(QADBConstants.SUCCESS_MESSAGE,specialitySave.getSuccessMsg());
			redirectAttributes.addFlashAttribute(QADBConstants.SUCCESS_MESSAGE_ATTRIBUTE, specialitySave.getSuccessMsg());
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		logger.debug("*** Exit saveSpecialty method ***");
		return new ModelAndView("redirect:addSpecialty");
	}
	
	/**
	 * Method to fetch the Specialty details for the selected Specialty id to edit the data
	 * @param id is the Specialty id selected by the user
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/getSpecialty", method = RequestMethod.GET,params = {"id"})
	public ModelAndView getSpecialty(@RequestParam(value="id") String id,HttpServletRequest request, HttpSession session) throws Exception {
		logger.debug("*** Entry getSpecialty method ***"+id);
		//ModelAndView mv = AuthorizeUser("admin_specialty", request);
		ModelAndView mv = null;
		mv = authorizationService.AuthorizeUsersAdmin("admin_specialty", request);
    	mv.addObject("edit","edit"); // edit EL to identify edit page
    	Speciality specialityTO = new Speciality();
    	specialityTO.setSearchType(QADBConstants.EDIT_SPECIALTY);
    	specialityTO.setId(id);
    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
    	specialityTO.setUserId(userId);
    	
    	Speciality specialityRO = null;
    	try{
    		specialityRO = specialityDAO.getSpecialityById(specialityTO);
    		mv.addObject("specialityRO",specialityRO);
    		logger.debug("Edit Specialty id Exe cm "+specialityRO.getSpecialty());
    	} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
    	HttpSession sessionE = request.getSession(true);
    	sessionE.setAttribute("paramSpecialityId", id);
    	logger.debug("*** Exit getSpecialty method ***");
		return mv;
	}
	
	
	/**
	 * Method to save the updated data for the edited Specialty
	 * @param specialtyTO
	 * @param redirectAttributes
	 * @return
	 */
	@RequestMapping(value = "/updateSpecialty", method = RequestMethod.GET)
	public ModelAndView updateSpecialty(@ModelAttribute("specialtyForm") Speciality specialtyTO,RedirectAttributes redirectAttributes,HttpSession session) throws Exception {
		logger.debug("In Speciality Update "+specialtyTO.getId());
		String specialityId = (String) session.getAttribute("paramSpecialityId");
    	specialtyTO.setId(specialityId);
    	specialtyTO.setUserActyp(QADBConstants.USER_ACTION_EDIT);
    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
    	specialtyTO.setUserId(userId);
    	Speciality specialityUpdate;
		try {
			specialityUpdate = specialityDAO.saveUpdateSpeciality(specialtyTO);
			logger.debug("Sucess msg -- " +specialityUpdate.getSuccessMsg());
			redirectAttributes.addFlashAttribute(QADBConstants.UPDATE_SUCCESS, specialityUpdate.getSuccessMsg());
		} catch (Exception e) {
			logger.debug("Exception = "+e.getMessage());
		}
		return new ModelAndView("redirect:addSpecialty");
	}
	
	
	// New Mapping
		/***
		 * Method to show the page to add a new Mapping .
		 * @return new model and view to enter Mapping details
		 * @throws SQLException 
		 */
		@RequestMapping(value = "/addMapping", method = RequestMethod.GET)
		public ModelAndView addMapping(HttpSession session,HttpServletRequest request) throws Exception,SQLException {

			//ModelAndView mv = AuthorizeUser("admin_mapping", request);
			ModelAndView mv = null;
			mv = authorizationService.AuthorizeUsersAdmin("admin_mapping", request);
			//String userGrp = "('SAMMD','CD','ALL')";
			String userGrp = authorizationService.getUserGroup(request);

		 	   //String b[] = {"Contractor","PeopleSoft_ELM_User","onestop_users","qadb-samd-admin_users"};
		 	   String b[] = {request.getHeader("iv-groups")};
		 	   /*if(!(((Arrays.toString(b)).replace("[", "")).replace("]", "")).equals("null")){
		 			int last = b.length - 1;
		 			if(((b[last].toString()).contains("qadb-superadmin_users"))){
		 				userGrp = "('SAMMD','CD','ALL')";
		 			}
		 			if(((b[last].toString()).contains("qadb-samd-auditor_users"))||((b[last].toString()).contains("qadb-samd-admin_users"))){
		 				userGrp = "('SAMMD','ALL')";
		 			}
		 			if(((b[last].toString()).contains("qadb-cd-auditor_users"))||((b[last].toString()).contains("qadb-cd-admin_users"))){
		 				userGrp = "('CD','ALL')";
		 			}
		 		}*/
		 	   
		 	 logger.debug("Grp Gp "+b + " "+userGrp);
			
			List<AuditDetails> auditorsList = auditDetailsDao.getAuditors(userGrp);
		    mv.addObject("auditorsList", auditorsList);
		    
		    List<Associate> associatesList = scoresReportsDAO.getClaimProcessors("ASSOCIATE","");
		    mv.addObject("associatesList", associatesList);
		    
		    List<String> months = Arrays.asList("January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December");
		    mv.addObject("months", months);
		    
		    List<String> years = mappingDAO.getYears();
		    mv.addObject("years", years);
		    
			return mv; 
		}
		
		
		/**
		 * Method to save new Specialty with details.
		 * @param model
		 * @param specialtyForm the command object for Specialty
		 * @param redirectAttributes
		 * @return 
		 */
		@RequestMapping(value = "/saveMapping", method = RequestMethod.GET)
		public ModelAndView saveMapping(Map<String, Object> model,@ModelAttribute("mappingForm") Mapping mappingTO ,RedirectAttributes redirectAttributes, HttpSession session) throws Exception {
			logger.debug("*** Entry saveMapping method ***");
			
			logger.debug("save map "+mappingTO.getAuditorName()+ " asso: "+mappingTO.getAssociatesIds() +" "+mappingTO.getMonth()+" "+mappingTO.getYear()+""+mappingTO.getUserActTyp());
			String userId=(String) session.getAttribute(QADBConstants.USERNAME);
			mappingTO.setUserId(userId); 
			if((mappingTO.getUserActTyp()).equalsIgnoreCase("EDIT")){
				mappingTO.setOldMonth((String) session.getAttribute("month"));
		    	mappingTO.setOldYear((String) session.getAttribute("year"));
			}
			Mapping mappingSave;
			try{
				mappingSave = mappingDAO.saveMapping(mappingTO);
				redirectAttributes.addFlashAttribute(QADBConstants.SUCCESS_CODE,mappingSave.getSuccessCode());
				redirectAttributes.addFlashAttribute(QADBConstants.SUCCESS_MESSAGE,mappingSave.getSuccessMsg());
				redirectAttributes.addFlashAttribute(QADBConstants.UPDATE_SUCCESS,mappingSave.getSuccessMsg());
				redirectAttributes.addFlashAttribute(QADBConstants.SUCCESS_MESSAGE_ATTRIBUTE, mappingSave.getSuccessMsg());
			}catch (Exception e) {
				logger.debug("Exception = "+e.getMessage());
			}
			logger.debug("*** Exit saveMapping method ***");
			return new ModelAndView("redirect:addMapping");
		}
		
		/***
		 * Method to show the page to error codes.
		 * @return new model and view to search error codes .
		 * @throws SQLException 
		 */
		@RequestMapping(value = "/editMapping", method = RequestMethod.GET)
	   	public ModelAndView editMapping(HttpServletRequest request) throws Exception,SQLException{
			//ModelAndView mv = AuthorizeUser("admin_mapping_edit_list", request);
			ModelAndView mv = null;
			mv = authorizationService.AuthorizeUsersAdmin("admin_mapping_edit_list", request);
			//mv.addObject("errorCode","");
			//String userGrp = "('SAMMD','CD','ALL')";
			String userGrp = authorizationService.getUserGroup(request);
		 	//String b[] = {"Contractor","PeopleSoft_ELM_User","onestop_users","qadb-samd-admin_users"};
		 	String b[] = {request.getHeader("iv-groups")};
		 	/*if(!(((Arrays.toString(b)).replace("[", "")).replace("]", "")).equals("null")){
		 			int last = b.length - 1;
		 			if(((b[last].toString()).contains("qadb-superadmin_users"))){
		 				userGrp = "('SAMMD','CD','ALL')";
		 			}
		 			if(((b[last].toString()).contains("qadb-samd-auditor_users"))||((b[last].toString()).contains("qadb-samd-admin_users"))){
		 				userGrp = "('SAMMD','ALL')";
		 			}
		 			if(((b[last].toString()).contains("qadb-cd-auditor_users"))||((b[last].toString()).contains("qadb-cd-admin_users"))){
		 				userGrp = "('CD','ALL')";
		 			}
		 		}*/
		 	   
		 	 logger.debug("UserGrp "+b + " - "+userGrp);
			
			List<AuditDetails> auditorsList = auditDetailsDao.getAuditors(userGrp);
		    mv.addObject("auditorsList", auditorsList);
		    
		    List<String> months = Arrays.asList("January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December");
		    mv.addObject("months", months);
		    
		    List<String> years = mappingDAO.getYears();
		    mv.addObject("years", years);

			return mv;
		}
		
		/**
		 * Method to fetch the search results for mapping.
		 * @param mappingSearch
		 * @param mappingSearchForm
		 * @return 
		 * @throws SQLException 
		 */
		@RequestMapping(value = "/editMappingResult",method = RequestMethod.GET)
		public ModelAndView editMappingResult(@ModelAttribute("mappingSearchForm") Mapping mappingSearch, HttpSession session,HttpServletRequest request) throws Exception,SQLException {
			logger.debug("*** Entry editMappingResult method ***");
			logger.debug("edit map "+mappingSearch.getAuditorName() +" "+mappingSearch.getMonth()+" "+mappingSearch.getYear());
			
			//ModelAndView mv = AuthorizeUser("admin_mapping_edit_list", request);
			ModelAndView mv = null;
			mv = authorizationService.AuthorizeUsersAdmin("admin_mapping_edit_list", request);
			//String userGrp = "('SAMMD','CD','ALL')";
			String userGrp = authorizationService.getUserGroup(request);
		 	   //String b[] = {"Contractor","PeopleSoft_ELM_User","onestop_users","qadb-samd-admin_users"};
		 	   String b[] = {request.getHeader("iv-groups")};
		 	   /*if(!(((Arrays.toString(b)).replace("[", "")).replace("]", "")).equals("null")){
		 			int last = b.length - 1;
		 			if(((b[last].toString()).contains("qadb-superadmin_users"))){
		 				userGrp = "('SAMMD','CD','ALL')";
		 			}
		 			if(((b[last].toString()).contains("qadb-samd-auditor_users"))||((b[last].toString()).contains("qadb-samd-admin_users"))){
		 				userGrp = "('SAMMD','ALL')";
		 			}
		 			if(((b[last].toString()).contains("qadb-cd-auditor_users"))||((b[last].toString()).contains("qadb-cd-admin_users"))){
		 				userGrp = "('CD','ALL')";
		 			}
		 		}*/
		 	   
		 	logger.debug("UserGrp "+b + " "+userGrp);
			List<AuditDetails> auditorsList = auditDetailsDao.getAuditors(userGrp);
			mv.addObject("auditorsList", auditorsList);
			
			List<String> months = Arrays.asList("January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December");
		    mv.addObject("months", months);
		    
		    List<String> years = mappingDAO.getYears();
		    mv.addObject("years", years);
		    
	    	mv.addObject("edit","edit"); // edit EL to identify edit page
	    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
	    	mappingSearch.setUserId(userId);
	    	mappingSearch.setUserActTyp(QADBConstants.USER_ACTION_SEARCH);
	    	List<Mapping> mappingRO = null;
	    	try {
	    		mappingRO = mappingDAO.searchMapping(mappingSearch);
			} catch (Exception e) {
				logger.debug("Exception generated"+e.getMessage());
			}
	    	mv.addObject("auditorNames",mappingSearch.getAuditorName());
	    	mv.addObject("monthEO",mappingSearch.getMonth());
	    	mv.addObject("yearEO",mappingSearch.getYear());
	    	mv.addObject(QADBConstants.MAPPING_RES,mappingRO);
	    	logger.debug("*** Exit editMappingResult method ***");
	    	session.setAttribute("month", mappingSearch.getMonth());
	    	session.setAttribute("year", mappingSearch.getYear());
			return mv;
		}
		
		/**
		 * Method to populate the details of mapping selected for update
		 * @param id
		 * @param session
		 * @return
		 * @throws SQLException 
		 */
		@RequestMapping(value = "/getMapping", method = RequestMethod.GET,params = {"id"})
		public ModelAndView getMapping(@RequestParam(value="id") String auditorName, HttpSession session, HttpServletRequest request) throws Exception,SQLException {
			logger.debug("*** Entry getMapping method ***"+auditorName);
			//ModelAndView mv = AuthorizeUser("admin_mapping", request);
			ModelAndView mv = null;
			mv = authorizationService.AuthorizeUsersAdmin("admin_mapping", request);
	    	mv.addObject("edit","edit"); // edit EL to identify edit page
	    	//String userGrp = "('SAMMD','CD','ALL')";
			String userGrp = authorizationService.getUserGroup(request);
		 	   //String b[] = {"Contractor","PeopleSoft_ELM_User","onestop_users","qadb-samd-admin_users"};
		 	   String b[] = {request.getHeader("iv-groups")};
		 	   /*if(!(((Arrays.toString(b)).replace("[", "")).replace("]", "")).equals("null")){
		 			int last = b.length - 1;
		 			if(((b[last].toString()).contains("qadb-superadmin_users"))){
		 				userGrp = "('SAMMD','CD','ALL')";
		 			}
		 			if(((b[last].toString()).contains("qadb-samd-auditor_users"))||((b[last].toString()).contains("qadb-samd-admin_users"))){
		 				userGrp = "('SAMMD','ALL')";
		 			}
		 			if(((b[last].toString()).contains("qadb-cd-auditor_users"))||((b[last].toString()).contains("qadb-cd-admin_users"))){
		 				userGrp = "('CD','ALL')";
		 			}
		 		}*/
		 	   
		 	 logger.debug("Grp Gp "+b + " "+userGrp);
	    	List<AuditDetails> auditorsList = auditDetailsDao.getAuditors(userGrp);
		    mv.addObject("auditorsList", auditorsList);
		    
		    List<Associate> associatesList = scoresReportsDAO.getClaimProcessors("ASSOCIATE","");
		    mv.addObject("associatesList", associatesList);
		    
		    List<String> months = Arrays.asList("January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December");
		    mv.addObject("months", months);
		    
		    List<String> years = mappingDAO.getYears();
		    mv.addObject("years", years);
		    
	    	Mapping mappingTO = new Mapping();
	    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
	    	mappingTO.setUserId(userId);
	    	mappingTO.setUserActTyp(QADBConstants.USER_ACTION_SEARCH);
	    	mappingTO.setAuditorName(auditorName);
	    	mappingTO.setMonth((String) session.getAttribute("month"));
	    	mappingTO.setYear((String) session.getAttribute("year"));
	    	Mapping mappingRO = new Mapping();
	    	try {
	    		mappingRO = mappingDAO.getMapping(mappingTO);
			} catch (Exception e) {
				logger.debug("Exception generated"+e.getMessage());
			}
	    	mv.addObject("auditorNames",auditorName);
	    	mv.addObject("monthEO",mappingTO.getMonth());
	    	mv.addObject("yearEO",mappingTO.getYear());
	    	mv.addObject(QADBConstants.MAPPING_RES,mappingRO);
	    	logger.debug("*** Exit editMappingResult method ***");
			return mv;
		}
}

