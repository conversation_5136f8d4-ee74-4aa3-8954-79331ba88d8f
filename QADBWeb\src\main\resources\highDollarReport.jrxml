<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="scriptlet" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" whenNoDataType="NoDataSection" whenResourceMissingType="Empty" >
	<property name="com.jasperassistant.designer.Grid" value="false"/>
	<property name="com.jasperassistant.designer.SnapToGrid" value="false"/>
	<property name="com.jasperassistant.designer.GridWidth" value="12"/>
	<property name="com.jasperassistant.designer.GridHeight" value="12"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="net.sf.jasperreports.awt.ignore.missing.font" value="true"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	
<field name="claimAdjFlag" class="java.lang.String">
	<fieldDescription><![CDATA[claimAdjFlag]]></fieldDescription>
</field>
<field name="serviceAdjFlag" class="java.lang.String">
	<fieldDescription><![CDATA[serviceAdjFlag]]></fieldDescription>
</field>

<field name="dcn" class="java.lang.String">
	<fieldDescription><![CDATA[dcn]]></fieldDescription>
</field>
<field name="examinerName" class="java.lang.String">
	<fieldDescription><![CDATA[examinerName]]></fieldDescription>
</field>
<field name="QAName" class="java.lang.String">
	<fieldDescription><![CDATA[QAName]]></fieldDescription>
</field>
<field name="processDate" class="java.lang.String">
	<fieldDescription><![CDATA[processDate]]></fieldDescription>
</field>
<field name="memberId" class="java.lang.String">
	<fieldDescription><![CDATA[memberId]]></fieldDescription>
</field>
<field name="patientName" class="java.lang.String">
	<fieldDescription><![CDATA[patientName]]></fieldDescription>
</field>
<field name="serviceDates" class="java.lang.String">
	<fieldDescription><![CDATA[serviceDates]]></fieldDescription>
</field>
<field name="typeOfService" class="java.lang.String">
	<fieldDescription><![CDATA[typeOfService]]></fieldDescription>
</field>
<field name="diagnosis" class="java.lang.String">
	<fieldDescription><![CDATA[diagnosis]]></fieldDescription>
</field>
<field name="surgeryDOS" class="java.lang.String">
	<fieldDescription><![CDATA[surgeryDOS]]></fieldDescription>
</field>
<field name="surgery" class="java.lang.String">
	<fieldDescription><![CDATA[surgery]]></fieldDescription>
</field>
<field name="fileReferenced" class="java.lang.String">
	<fieldDescription><![CDATA[fileReferenced]]></fieldDescription>
</field>
<field name="totalCharge" class="java.lang.String">
	<fieldDescription><![CDATA[totalCharge]]></fieldDescription>
</field>
<field name="paid" class="java.lang.String">
	<fieldDescription><![CDATA[paid]]></fieldDescription>
</field>
<field name="interestPaid" class="java.lang.String">
	<fieldDescription><![CDATA[interestPaid]]></fieldDescription>
</field>
<field name="providerName" class="java.lang.String">
	<fieldDescription><![CDATA[providerName]]></fieldDescription>
</field>
<field name="providerNumber" class="java.lang.String">
	<fieldDescription><![CDATA[providerNumber]]></fieldDescription>
</field>
<field name="payee" class="java.lang.String">
	<fieldDescription><![CDATA[payee]]></fieldDescription>
</field>
<field name="notes" class="java.lang.String">
	<fieldDescription><![CDATA[notes]]></fieldDescription>
</field>

<field name="audSignDate" class="java.lang.String">
	<fieldDescription><![CDATA[audSignDate]]></fieldDescription>
</field>
<field name="vpSignDate" class="java.lang.String">
	<fieldDescription><![CDATA[vpSignDate]]></fieldDescription>
</field>
<field name="forwrdToDate" class="java.lang.String">
	<fieldDescription><![CDATA[forwrdToDate]]></fieldDescription>
</field>
<field name="rcvedFrmDate" class="java.lang.String">
	<fieldDescription><![CDATA[rcvedFrmDate]]></fieldDescription>
</field>
<field name="releasedByDate" class="java.lang.String">
	<fieldDescription><![CDATA[releasedByDate]]></fieldDescription>
</field>
	
	
	<group name="dummy"> 		
	<groupExpression><![CDATA["dummy"]]></groupExpression> 
		<groupHeader>
			
		</groupHeader>
	</group>
	
	
	<pageHeader>
		<band height="747" splitType="Stretch">
			<printWhenExpression><![CDATA[$V{PAGE_NUMBER}.intValue() == 1? new Boolean(true) : new Boolean(false)]]></printWhenExpression>
			
			
			<rectangle>
				<reportElement x="290" y="617" width="128" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="240" y="452" width="315" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="452" width="240" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="290" y="666" width="128" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="159" y="700" width="131" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="159" y="683" width="131" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="683" width="159" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="159" y="617" width="131" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="159" y="600" width="131" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="600" width="159" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="554" width="159" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="159" y="554" width="131" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="290" y="537" width="128" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="159" y="537" width="131" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="537" width="159" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="-2" y="538" width="162" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Required Autorization]]></text>
			</staticText>
			<rectangle>
				<reportElement x="0" y="398" width="240" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="218" width="240" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="146" width="240" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="182" width="240" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="164" width="240" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="86" width="280" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="22" width="555" height="3" forecolor="#FFFFFF" backcolor="#000080"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="0" y="2" width="555" height="19" forecolor="#000080"  >
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="16" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[HIGH-DOLLAR CLAIM AUTHORIZATION]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="31" width="555" height="30"  />
				<textElement>
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[High-dollar claims with paid amounts over $ 40,000.00 must have the appropriate sign-offs. Attach this sheet to the claim or screen prints for the appropriate signatures.]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="69" width="555" height="14"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Claim Processing System]]></text>
			</staticText>
			<rectangle>
				<reportElement x="280" y="86" width="275" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="50" y="87" width="230" height="14"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[CLAIMS/CLAIM ADJUSTMENTS]]></text>
			</staticText>
			<staticText>
				<reportElement x="281" y="87" width="274" height="14"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[SERVICE ADJUSTMENTS]]></text>
			</staticText>
			<staticText>
				<reportElement x="122" y="109" width="310" height="14"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[**For CARE BlueCard Home - Examiners to complete form **]]></text>
			</staticText>
			<staticText>
				<reportElement x="148" y="124" width="259" height="14"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[**QA Analyst to complete form for other accounts**]]></text>
			</staticText>
			<staticText>
				<reportElement x="11" y="146" width="219" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[Claim #]]></text>
			</staticText>
			<rectangle>
				<reportElement x="240" y="146" width="315" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="240" y="164" width="315" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="11" y="164" width="219" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[Examiner Name]]></text>
			</staticText>
			<rectangle>
				<reportElement x="240" y="182" width="315" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="11" y="182" width="219" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[QA Analyst Name]]></text>
			</staticText>
			<rectangle>
				<reportElement x="0" y="200" width="240" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="240" y="200" width="315" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="11" y="200" width="219" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[Date]]></text>
			</staticText>
			<rectangle>
				<reportElement x="240" y="218" width="315" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="11" y="218" width="219" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[Membership #]]></text>
			</staticText>
			<rectangle>
				<reportElement x="240" y="254" width="315" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="236" width="240" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="254" width="240" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="240" y="272" width="315" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="272" width="240" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="240" y="236" width="315" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="11" y="254" width="219" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[Service Dates]]></text>
			</staticText>
			<staticText>
				<reportElement x="11" y="236" width="219" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[Patient Name]]></text>
			</staticText>
			<staticText>
				<reportElement x="11" y="272" width="219" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[Type of Service]]></text>
			</staticText>
			<rectangle>
				<reportElement x="0" y="290" width="240" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="308" width="240" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="362" width="240" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="240" y="416" width="315" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement key="" x="0" y="344" width="240" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="11" y="398" width="219" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<text><![CDATA[Interest Paid(estimate at time sent for signoff)]]></text>
			</staticText>
			<staticText>
				<reportElement x="11" y="362" width="219" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[Total Charge]]></text>
			</staticText>
			<rectangle>
				<reportElement x="0" y="380" width="240" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="11" y="308" width="219" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[Surgery Date of Service ]]></text>
			</staticText>
			<rectangle>
				<reportElement x="240" y="398" width="315" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="240" y="290" width="315" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="11" y="290" width="219" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[Diagnosis (Code & Verbiage)]]></text>
			</staticText>
			<rectangle>
				<reportElement x="240" y="326" width="315" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="11" y="380" width="219" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[Total Paid]]></text>
			</staticText>
			<rectangle>
				<reportElement x="240" y="344" width="315" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="326" width="240" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="240" y="362" width="315" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="416" width="240" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="11" y="416" width="219" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[Provider Name]]></text>
			</staticText>
			<rectangle>
				<reportElement x="240" y="380" width="315" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="240" y="308" width="315" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="11" y="326" width="219" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[Surgery (CPT Code & Verbiage)]]></text>
			</staticText>
			<staticText>
				<reportElement x="11" y="344" width="229" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[Auth on File Referenced (if applicable)]]></text>
			</staticText>
			<rectangle>
				<reportElement x="240" y="470" width="315" height="38"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="470" width="240" height="38"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="11" y="452" width="219" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[Payee (Subscriber or Provider)]]></text>
			</staticText>
			<staticText>
				<reportElement x="11" y="470" width="219" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[Notes (if applicable)]]></text>
			</staticText>
			<rectangle>
				<reportElement x="240" y="434" width="315" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="434" width="240" height="18"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="11" y="435" width="219" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<text><![CDATA[Provider #]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="518" width="555" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Inpatient Institutional/Outpatient Institutional and all Professional Claims]]></text>
			</staticText>
			<staticText>
				<reportElement x="159" y="538" width="120" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[If Paid Amount is...]]></text>
			</staticText>
			<staticText>
				<reportElement x="303" y="538" width="102" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Signature]]></text>
			</staticText>
			<rectangle>
				<reportElement x="418" y="537" width="128" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="431" y="538" width="102" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Date]]></text>
			</staticText>
			<rectangle>
				<reportElement x="290" y="556" width="128" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="418" y="556" width="128" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="418" y="571" width="128" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="571" width="159" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="290" y="571" width="128" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="159" y="571" width="131" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="-2" y="555" width="162" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[QA Auditor]]></text>
			</staticText>
			<staticText>
				<reportElement x="162" y="555" width="108" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Over $ 100,000.00]]></text>
			</staticText>
			<staticText>
				<reportElement x="-2" y="601" width="162" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Required Autorization]]></text>
			</staticText>
			<rectangle>
				<reportElement x="418" y="617" width="128" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="290" y="600" width="128" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="159" y="601" width="120" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[If Paid Amount is...]]></text>
			</staticText>
			<rectangle>
				<reportElement x="0" y="617" width="159" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="162" y="618" width="108" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Over $ 100,000.00]]></text>
			</staticText>
			<rectangle>
				<reportElement x="418" y="600" width="128" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="-2" y="618" width="162" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Vice President]]></text>
			</staticText>
			<staticText>
				<reportElement x="303" y="601" width="102" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Signature]]></text>
			</staticText>
			<staticText>
				<reportElement x="431" y="601" width="102" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="-2" y="684" width="162" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Forwarded to]]></text>
			</staticText>
			<staticText>
				<reportElement x="159" y="684" width="120" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Vice President]]></text>
			</staticText>
			<rectangle>
				<reportElement x="290" y="683" width="128" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="700" width="159" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="290" y="717" width="128" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="160" y="702" width="119" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Vice President]]></text>
			</staticText>
			<staticText>
				<reportElement x="-2" y="701" width="162" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Received from]]></text>
			</staticText>
			<rectangle>
				<reportElement x="290" y="700" width="128" height="17"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="159" y="717" width="131" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="717" width="159" height="19"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="303" y="667" width="102" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="-2" y="718" width="162" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Released by]]></text>
			</staticText>
			<staticText>
				<reportElement x="160" y="718" width="118" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[QA]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="648" width="555" height="16"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Times New Roman" size="12"/>
				</textElement>
				<text><![CDATA[Please indicate dates...]]></text>
			</staticText>
			<textField>
				<reportElement mode="Transparent" x="247" y="147" width="299" height="17" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dcn}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="247" y="165" width="299" height="17" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					
				</textElement>
				<textFieldExpression><![CDATA[$F{examinerName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="247" y="182" width="299" height="17" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{QAName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="247" y="200" width="299" height="17" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{processDate}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="247" y="219" width="299" height="17" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{memberId}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="247" y="237" width="299" height="17" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{patientName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="247" y="254" width="299" height="17" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{serviceDates}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="247" y="272" width="299" height="17" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{typeOfService}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="247" y="291" width="299" height="17" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{diagnosis}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="247" y="309" width="299" height="17" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{surgeryDOS}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="247" y="326" width="299" height="17" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{surgery}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="247" y="344" width="299" height="17" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{fileReferenced}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="247" y="363" width="299" height="17" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{totalCharge} != null && $F{totalCharge}.length() > 0 ? Double.valueOf($F{totalCharge}) : 0))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="247" y="381" width="299" height="17" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{paid} != null && $F{paid}.length() > 0 ? Double.valueOf($F{paid}) : 0))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="247" y="398" width="299" height="17" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{interestPaid} != null && $F{interestPaid}.length() > 0 ? Double.valueOf($F{interestPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="247" y="416" width="299" height="17" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{providerName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="247" y="434" width="299" height="17" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{providerNumber}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="247" y="452" width="299" height="17" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{payee}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="247" y="471" width="307" height="37" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{notes}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="300" y="555" width="107" height="16" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="427" y="556" width="107" height="16" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{audSignDate}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="301" y="619" width="107" height="16" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="428" y="619" width="107" height="16" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{vpSignDate}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="300" y="684" width="107" height="16" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{forwrdToDate}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="300" y="701" width="107" height="16" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{rcvedFrmDate}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="300" y="718" width="107" height="16" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="12" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{releasedByDate}]]></textFieldExpression>
			</textField>
			<image>
				<reportElement x="328" y="87" width="16" height="15"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<imageExpression><![CDATA[$F{serviceAdjFlag}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="30" y="87" width="16" height="15"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<imageExpression><![CDATA[$F{claimAdjFlag}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
		</band>
	</pageHeader>
	<columnHeader>
		<band/>
	</columnHeader>
	
	<pageFooter>
		<band height="43" splitType="Stretch">
			<textField>
				<reportElement x="381" y="7" width="100" height="30" forecolor="#000080"  />
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="481" y="7" width="100" height="30" forecolor="#000080"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="EEEE, MMMMM dd, yyyy">
				<reportElement x="4" y="7" width="133" height="17" forecolor="#000080"  />
				<textElement>
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="140" y="7" width="260" height="16" forecolor="#000080"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Quality Assurance Productivity Management]]></text>
			</staticText>
			<rectangle>
				<reportElement x="0" y="3" width="555" height="3" forecolor="#FFFFFF" backcolor="#C0C0C0"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
		</band>
	</pageFooter>
	<lastPageFooter>
		<band height="41" splitType="Stretch">
			<textField>
				<reportElement x="381" y="7" width="100" height="30" forecolor="#000080"  />
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="481" y="7" width="100" height="30" forecolor="#000080"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="EEEE, MMMMM dd, yyyy">
				<reportElement x="4" y="7" width="133" height="17" forecolor="#000080"  />
				<textElement>
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="140" y="7" width="260" height="16" forecolor="#000080"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Quality Assurance Productivity Management]]></text>
			</staticText>
			<rectangle>
				<reportElement x="0" y="3" width="555" height="3" forecolor="#FFFFFF" backcolor="#C0C0C0"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
		</band>
	</lastPageFooter>
	
	<noData>
		<band height="55" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="180" y="0" width="316" height="47" forecolor="#000050">
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="22" isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[No records founds!!]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>
