package com.carefirst.audit.model;



public class AuditSearch {
	
	
	private String successMsg;
	private String sucessCode;
	
	private String auditId;
	private String searchType;
	private String pageType;
	
	
	private String  associateName; // name of the role
	private String  empNo;
	private String  dcn;
	
	private String  processedDateFrom;
	private String  processedDateTo;
	private String  auditorName;
	private String  auditDateFrom;
	private String  auditDateTo;
	private String platform;
	private String  processType;
	private String  errorCode;
	private String  piType;
	private String  priPGA;
	private String  secPGA;
	private String  memberID;
	private String  e2e;
	private String  oos;
	
	private String  userId;
	private String  userGroup;
	
	//Claims assessment report
	private String type;
	
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getPlatform() { 
		return platform;
		}

    public void setPlatform(String platform) { 
		this.platform = platform;
		}
		
	public String getUserGroup() {
		return userGroup;
	}
	public void setUserGroup(String userGroup) {
		this.userGroup = userGroup;
	}
	public String getAuditId() {
		return auditId;
	}
	public void setAuditId(String auditId) {
		this.auditId = auditId;
	}
	public String getSearchType() {
		return searchType;
	}
	public void setSearchType(String searchType) {
		this.searchType = searchType;
	}
	
	public String getAssociateName() {
		return associateName;
	}
	public void setAssociateName(String associateName) {
		this.associateName = associateName;
	}
	public String getEmpNo() {
		return empNo;
	}
	public void setEmpNo(String empNo) {
		this.empNo = empNo;
	}
	public String getDcn() {
		return dcn;
	}
	public void setDcn(String dcn) {
		this.dcn = dcn;
	}
	public String getProcessedDateFrom() {
		return processedDateFrom;
	}
	public void setProcessedDateFrom(String processedDateFrom) {
		this.processedDateFrom = processedDateFrom;
	}
	public String getProcessedDateTo() {
		return processedDateTo;
	}
	public void setProcessedDateTo(String processedDateTo) {
		this.processedDateTo = processedDateTo;
	}
	public String getAuditorName() {
		return auditorName;
	}
	public void setAuditorName(String auditorName) {
		this.auditorName = auditorName;
	}
	public String getAuditDateFrom() {
		return auditDateFrom;
	}
	public void setAuditDateFrom(String auditDateFrom) {
		this.auditDateFrom = auditDateFrom;
	}
	public String getAuditDateTo() {
		return auditDateTo;
	}
	public void setAuditDateTo(String auditDateTo) {
		this.auditDateTo = auditDateTo;
	}
	public String getProcessType() {
		return processType;
	}
	public void setProcessType(String processType) {
		this.processType = processType;
	}
	public String getErrorCode() {
		return errorCode;
	}
	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}
	public String getPiType() {
		return piType;
	}
	public void setPiType(String piType) {
		this.piType = piType;
	}
	public String getPriPGA() {
		return priPGA;
	}
	public void setPriPGA(String priPGA) {
		this.priPGA = priPGA;
	}
	public String getSecPGA() {
		return secPGA;
	}
	public void setSecPGA(String secPGA) {
		this.secPGA = secPGA;
	}
	public String getMemberID() {
		return memberID;
	}
	public void setMemberID(String memberID) {
		this.memberID = memberID;
	}
	
	public String getSuccessMsg() {
		return successMsg;
	}
	public void setSuccessMsg(String successMsg) {
		this.successMsg = successMsg;
	}
	public String getSucessCode() {
		return sucessCode;
	}
	public void setSucessCode(String sucessCode) {
		this.sucessCode = sucessCode;
	}
	public String getPageType() {
		return pageType;
	}
	public void setPageType(String pageType) {
		this.pageType = pageType;
	}
	public String getE2e() {
		return e2e;
	}
	public void setE2e(String e2e) {
		this.e2e = e2e;
	}
	public String getOos() {
		return oos;
	}
	public void setOos(String oos) {
		this.oos = oos;
	}
}
