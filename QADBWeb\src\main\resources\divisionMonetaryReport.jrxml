<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="scriptlet" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" isSummaryWithPageHeaderAndFooter="true" whenNoDataType="NoDataSection" whenResourceMissingType="Empty"  >
	<property name="com.jasperassistant.designer.Grid" value="false"/>
	<property name="com.jasperassistant.designer.SnapToGrid" value="false"/>
	<property name="com.jasperassistant.designer.GridWidth" value="12"/>
	<property name="com.jasperassistant.designer.GridHeight" value="12"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="net.sf.jasperreports.awt.ignore.missing.font" value="true"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	
	<field name="timeframe" class="java.lang.String">
		<fieldDescription><![CDATA[timeframe]]></fieldDescription>
	</field>
	<field name="e2e" class="java.lang.String">
		<fieldDescription><![CDATA[e2e]]></fieldDescription>
	</field>
	<field name="auditType" class="java.lang.String">
		<fieldDescription><![CDATA[auditType]]></fieldDescription>
	</field>
	<field name="subType" class="java.lang.String">
		<fieldDescription><![CDATA[subType]]></fieldDescription>
	</field>
	<field name="month" class="java.lang.String">
		<fieldDescription><![CDATA[month]]></fieldDescription>
	</field>
	<field name="associateName" class="java.lang.String">
		<fieldDescription><![CDATA[associateName]]></fieldDescription>
	</field>
	<field name="empNO" class="java.lang.String">
		<fieldDescription><![CDATA[empNO]]></fieldDescription>
	</field>
	<field name="supervisor" class="java.lang.String">
		<fieldDescription><![CDATA[supervisor]]></fieldDescription>
	</field>
	
	<field name="isSno" class="java.lang.String">
		<fieldDescription><![CDATA[isSno]]></fieldDescription>
	</field>
	<field name="isDcn" class="java.lang.String">
		<fieldDescription><![CDATA[isDcn]]></fieldDescription>
	</field>
	<field name="isMemId" class="java.lang.String">
		<fieldDescription><![CDATA[isMemId]]></fieldDescription>
	</field>
	<field name="isPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isPaid]]></fieldDescription>
	</field>
	<field name="isUnderPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isUnderPaid]]></fieldDescription>
	</field>
	<field name="isOverPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isOverPaid]]></fieldDescription>
	</field>
	<field name="isHighDoller" class="java.lang.String">
		<fieldDescription><![CDATA[isHighDoller]]></fieldDescription>
	</field>
	<field name="isAuditType" class="java.lang.String">
		<fieldDescription><![CDATA[isAuditType]]></fieldDescription>
	</field>
	<field name="isMockAudit" class="java.lang.String">
		<fieldDescription><![CDATA[isMockAudit]]></fieldDescription>
	</field>
	<field name="reportType" class="java.lang.String">
		<fieldDescription><![CDATA[reportType]]></fieldDescription>
	</field>
	<field name="isAdjRequired" class="java.lang.String">
		<fieldDescription><![CDATA[isAdjRequired]]></fieldDescription>
	</field>
	<field name="isProcessOn" class="java.lang.String">
		<fieldDescription><![CDATA[isProcessOn]]></fieldDescription>
	</field>
	<field name="isErrorCode" class="java.lang.String">
		<fieldDescription><![CDATA[isErrorCode]]></fieldDescription>
	</field>
	<field name="isMonetary" class="java.lang.String">
		<fieldDescription><![CDATA[isMonetary]]></fieldDescription>
	</field>
	<field name="isProcedural" class="java.lang.String">
		<fieldDescription><![CDATA[isProcedural]]></fieldDescription>
	</field>
	<field name="isErrorType" class="java.lang.String">
		<fieldDescription><![CDATA[isErrorType]]></fieldDescription>
	</field>
	<field name="isExplanation" class="java.lang.String">
		<fieldDescription><![CDATA[isExplanation]]></fieldDescription>
	</field>
	<field name="isSop" class="java.lang.String">
		<fieldDescription><![CDATA[isSop]]></fieldDescription>
	</field>
	<field name="isPriPerfGroup" class="java.lang.String">
		<fieldDescription><![CDATA[isPriPerfGroup]]></fieldDescription>
	</field>
	<field name="isAuditor" class="java.lang.String">
		<fieldDescription><![CDATA[isAuditor]]></fieldDescription>
	</field>
	<field name="isAuditDate" class="java.lang.String">
		<fieldDescription><![CDATA[isAuditDate]]></fieldDescription>
	</field>
	
	<field name="isTotalClaims" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalClaims]]></fieldDescription>
	</field>
	<field name="isProcClaims" class="java.lang.String">
		<fieldDescription><![CDATA[isProcClaims]]></fieldDescription>
	</field>
	<field name="isMonClaims" class="java.lang.String">
		<fieldDescription><![CDATA[isMonClaims]]></fieldDescription>
	</field>
	<field name="isTotalPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalPaid]]></fieldDescription>
	</field>
	<field name="isTotalOvPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalOvPaid]]></fieldDescription>
	</field>
	<field name="isTotalUnPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalUnPaid]]></fieldDescription>
	</field>
	
	<group name="dummy"> 		
	<groupExpression><![CDATA["dummy"]]></groupExpression> 
		<groupHeader>
			
		</groupHeader>
	</group>
	
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="127" splitType="Stretch">
			<textField>
				<reportElement x="4" y="2" width="797" height="30"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="28"/>
				</textElement>
			 	<textFieldExpression><![CDATA["Monetary Error Sheet for "+$F{timeframe}+" "+"("+$F{e2e}+")"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="92" y="40" width="348" height="21"  />
				<textElement>
					<font size="14" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{associateName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="4" y="38" width="88" height="30"  />
				<box topPadding="4" leftPadding="8">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement>
					<font fontName="Arial" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Division : ]]></text>
			</staticText>
			<rectangle>
				<reportElement x="1" y="72" width="800" height="4" backcolor="#080707"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="117" y="90" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[High Dollar]]></text>
			</staticText>
			<staticText>
				<reportElement x="-12" y="90" width="131" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Membership #]]></text>
			</staticText>
			<staticText>
				<reportElement x="169" y="106" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Out-Of-Sample]]></text>
			</staticText>
			<staticText>
				<reportElement x="342" y="75" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Monetary]]></text>
			</staticText>
			<staticText>
				<reportElement x="392" y="75" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Procedural]]></text>
			</staticText>
			<staticText>
				<reportElement x="228" y="106" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Processed On]]></text>
			</staticText>
			<staticText>
				<reportElement x="67" y="106" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Under Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="66" y="90" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Over Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="169" y="75" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Audit Type]]></text>
			</staticText>
			<staticText>
				<reportElement x="169" y="90" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Mock Audit]]></text>
			</staticText>
			<staticText>
				<reportElement x="279" y="75" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Error]]></text>
			</staticText>
			<staticText>
				<reportElement x="-18" y="75" width="131" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[DCN]]></text>
			</staticText>
			<staticText>
				<reportElement x="630" y="75" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[SOP Reference]]></text>
			</staticText>
			<staticText>
				<reportElement x="228" y="75" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Adj. Required]]></text>
			</staticText>
			<staticText>
				<reportElement x="68" y="75" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="516" y="75" width="114" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Explanation]]></text>
			</staticText>
			<staticText>
				<reportElement x="431" y="75" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Error]]></text>
			</staticText>
			<rectangle>
				<reportElement x="1" y="122" width="800" height="4" backcolor="#080707"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
			
			<staticText>
				<reportElement x="302" y="75" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Codes]]></text>
			</staticText>
			<staticText>
				<reportElement x="451" y="75" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Type]]></text>
			</staticText>
			<staticText>
				<reportElement x="715" y="75" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Manager]]></text>
			</staticText>
			<staticText>
				<reportElement x="715" y="90" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Supervisor]]></text>
			</staticText>
			<staticText>
				<reportElement x="715" y="106" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Associate]]></text>
			</staticText>
			<staticText>
				<reportElement x="440" y="44" width="75" height="14" forecolor="#000080"  />
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Audit Type :]]></text>
			</staticText>
			<textField>
				<reportElement x="499" y="46" width="131" height="13"  />
				<textElement>
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{auditType}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="634" y="45" width="75" height="14" forecolor="#000080"  />
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Sub Type :]]></text>
			</staticText>
			<textField>
				<reportElement x="685" y="46" width="120" height="13"  >
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{subType}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	
	<detail>
		<band height="70" splitType="Stretch">
			<image>
				<reportElement x="154" y="13" width="16" height="15"  >
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<imageExpression><![CDATA[$F{isHighDoller}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="202" y="13" width="16" height="15"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<imageExpression><![CDATA[$F{isMockAudit}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="265" y="2" width="16" height="15"  />
				<imageExpression><![CDATA[$F{isAdjRequired}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="377" y="2" width="16" height="15"  />
				<imageExpression><![CDATA[$F{isMonetary}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="429" y="2" width="16" height="15"  />
				<imageExpression><![CDATA[$F{isProcedural}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="202" y="28" width="16" height="15"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<imageExpression><![CDATA[$F{reportType}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<line>
				<reportElement positionType="Float" x="0" y="1" width="800" height="1"  />
				<graphicElement>
					<pen lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="11" y="2" width="74" height="18"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isDcn}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="-4" y="2" width="31" height="22"  >
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isSno}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="11" y="15" width="74" height="18"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isMemId}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="82" y="2" width="63" height="12"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isPaid} != null && $F{isPaid}.length() > 0 ? Double.valueOf($F{isPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="82" y="15" width="63" height="12"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isOverPaid} != null && $F{isOverPaid}.length() > 0 ? Double.valueOf($F{isOverPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="82" y="29" width="63" height="12"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isUnderPaid} != null && $F{isUnderPaid}.length() > 0 ? Double.valueOf($F{isUnderPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="188" y="2" width="50" height="12"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isAuditType}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="248" y="29" width="51" height="12"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isProcessOn}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="455" y="2" width="61" height="30"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isErrorType}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="520" y="2" width="110" height="68"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isExplanation}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="711" y="2" width="100" height="22"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isAuditor}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="636" y="2" width="73" height="68"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isSop}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="316" y="2" width="31" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isErrorCode}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="711" y="25" width="100" height="22"  />
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{supervisor}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="711" y="48" width="100" height="22"  />
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isAuditDate}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
<pageFooter>
		<band height="34" splitType="Stretch">
			<textField>
				<reportElement x="644" y="1" width="100" height="30"  />
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="744" y="1" width="100" height="30"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="EEEE, MMMMM dd, yyyy">
				<reportElement x="4" y="1" width="146" height="17"  />
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="180" y="2" width="420" height="16"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[Quality Assurance Productivity Management]]></text>
			</staticText>
		</band>
	</pageFooter>
	<lastPageFooter>
		<band height="33" splitType="Stretch">
			<textField>
				<reportElement x="645" y="2" width="100" height="30"  />
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="745" y="2" width="100" height="30"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="EEEE, MMMMM dd, yyyy">
				<reportElement x="4" y="0" width="146" height="17"  />
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="180" y="2" width="420" height="16"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[Quality Assurance Productivity Management]]></text>
			</staticText>
		</band>
	</lastPageFooter>	
	<summary>
		<band height="249" splitType="Stretch">
			<textField>
				<reportElement x="6" y="5" width="415" height="12"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Summary for Division = "+$F{associateName}+" ("+$F{isTotalClaims}+" claims)"]]></textFieldExpression>
			</textField>
			<rectangle>	
				<reportElement x="6" y="20" width="158" height="48"  />
			</rectangle>
			<rectangle>
				<reportElement x="112" y="20" width="52" height="16" backcolor="#CCCCCC"  />
			</rectangle>
			<staticText>
				<reportElement x="9" y="21" width="74" height="15"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Claims]]></text>
			</staticText>
			<staticText>
				<reportElement x="9" y="37" width="133" height="15"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Procedural Error Claims]]></text>
			</staticText>
			<staticText>
				<reportElement x="9" y="53" width="133" height="15"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Monetary Error Claims]]></text>
			</staticText>
			<rectangle>
				<reportElement x="176" y="20" width="153" height="48"  />
			</rectangle>
			<staticText>
				<reportElement x="178" y="37" width="91" height="15"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Over Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="178" y="21" width="74" height="15"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="178" y="53" width="91" height="15"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Under Paid]]></text>
			</staticText>
			
			<rectangle>
				<reportElement x="112" y="36" width="52" height="16" backcolor="#CCCCCC"  />
			</rectangle>
			<rectangle>
				<reportElement x="112" y="52" width="52" height="16" backcolor="#CCCCCC"  />
			</rectangle>
			<rectangle>
				<reportElement x="254" y="20" width="76" height="16" backcolor="#CCCCCC"  />
			</rectangle>
			<rectangle>
				<reportElement x="254" y="36" width="76" height="16" backcolor="#CCCCCC"  />
			</rectangle>
			<rectangle>
				<reportElement x="254" y="52" width="76" height="16" backcolor="#CCCCCC"  />
			</rectangle>
			
			<textField>
				<reportElement x="113" y="23" width="51" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isTotalClaims}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="113" y="40" width="51" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isProcClaims}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="113" y="55" width="51" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isMonClaims}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="254" y="23" width="76" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isTotalPaid} != null && $F{isTotalPaid}.length() > 0 ? Double.valueOf($F{isTotalPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="254" y="40" width="76" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isTotalOvPaid} != null && $F{isTotalOvPaid}.length() > 0 ? Double.valueOf($F{isTotalOvPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="253" y="55" width="76" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isTotalUnPaid} != null && $F{isTotalUnPaid}.length() > 0 ? Double.valueOf($F{isTotalUnPaid}) : 0))]]></textFieldExpression>
			</textField>
		</band>
	</summary>
	<noData>
		<band height="55" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="300" y="0" width="316" height="47" forecolor="#000050">
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="22" isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[No records found!!]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>
