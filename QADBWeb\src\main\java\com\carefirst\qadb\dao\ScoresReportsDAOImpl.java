package com.carefirst.qadb.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.sql.DataSource;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import oracle.jdbc.OracleTypes;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import com.carefirst.audit.model.Associate;
import com.carefirst.audit.model.AuditAssessment;
import com.carefirst.audit.model.AuditDetails;
import com.carefirst.audit.model.ScoresAndTrends;
import com.carefirst.audit.model.ScoresReport;
import com.carefirst.audit.model.TrendsReport;
import com.carefirst.qadb.constant.QADBConstants;

public class ScoresReportsDAOImpl implements ScoresReportsDAO {

	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);
	public static String type = "" ;
	
	@Autowired
	DataSource dataSource;

	@Override
	public JRDataSource getScoresReport(ScoresAndTrends scores)
			throws SQLException {

		logger.debug("*** getScoresReport start ***");
		logger.debug("Subject " + scores.getSubject() + " Period " + scores.getPeriod()+" pPG"+scores.getPriPGA()+" sPG"+scores.getSecPGA());
		String getScoresReport = QADBConstants.REPORTS_SCORES_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getScoresReport);
		List<ScoresReport> scoresList = new ArrayList<ScoresReport>();
		DecimalFormat df = new DecimalFormat("0.00");  /*Enhancement for  Accuracy of 2 decimal places is requested for %age calculations*/  
		
		try {

			callableStatment.setNull(1, java.sql.Types.VARCHAR);
			callableStatment.setString(2, scores.getSubject());
			callableStatment.setInt(3, Integer.parseInt(scores.getName()));
			callableStatment.setString(4, scores.getType());
			callableStatment.setString(5, scores.getSubType());
			callableStatment.setInt(6, Integer.parseInt(scores.getAuditType()));
			callableStatment.setString(7, scores.getE2e() );
			logger.debug("e2e method "+scores.getE2eMethod());
			if((null != (scores.getE2eMethod())) && ((scores.getE2eMethod()).equalsIgnoreCase("Y"))){
				callableStatment.setString(8, scores.getE2eMethod());
			}
			else{
				callableStatment.setString(8, "N");
			}
			
			if((scores.getUse()).equalsIgnoreCase("TF")){
				callableStatment.setString(9, scores.getYear());
				
				if ((scores.getTimeframe()).equalsIgnoreCase("N")
						&& (scores.getPeriod()).equalsIgnoreCase("N")) {
					callableStatment.setString(10, "Y"); // Yearly
					callableStatment.setNull(11, java.sql.Types.VARCHAR);
				} else {
					callableStatment.setString(10, scores.getTimeframe());
					callableStatment.setString(11, scores.getPeriod());
				}
				callableStatment.setNull(12, java.sql.Types.VARCHAR);
				callableStatment.setNull(13, java.sql.Types.VARCHAR);
			}
			else{
				callableStatment.setNull(9, java.sql.Types.VARCHAR);//year
				callableStatment.setNull(10, java.sql.Types.VARCHAR);//TF
				callableStatment.setNull(11, java.sql.Types.VARCHAR);//Period
				logger.debug("from date "+scores.getFromDate());
				callableStatment.setString(12, scores.getFromDate());
				callableStatment.setString(13, scores.getToDate());
			}
			callableStatment.setNull(14, java.sql.Types.VARCHAR);

			if(null!=scores.getPriPGA()){
				if((scores.getPriPGA()).equalsIgnoreCase(" ")){
					callableStatment.setNull(15, java.sql.Types.INTEGER);
				}else{
					callableStatment.setInt(15, Integer.parseInt(scores.getPriPGA()));
				}
			}else{
				callableStatment.setNull(15, java.sql.Types.INTEGER);
			}
			
			if(null!=scores.getSecPGA()){
				if((scores.getSecPGA()).equalsIgnoreCase("")){
					callableStatment.setInt(16, Integer.parseInt("0"));
				}else{
					callableStatment.setInt(16, Integer.parseInt(scores.getSecPGA()));
				}
			}else{
				callableStatment.setNull(16, java.sql.Types.INTEGER);
			}
			
			callableStatment.registerOutParameter(17, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(18, OracleTypes.VARCHAR);
			/* set out parameters from 14 to 37 */
			for (int i = 19; i < 41; i++) {
				callableStatment.registerOutParameter(i, OracleTypes.INTEGER);
			}
			
			callableStatment.registerOutParameter(41, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(42, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(43, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(44, OracleTypes.VARCHAR);
			callableStatment.executeUpdate();

			// IS 1st col header
			String colHeader = "";
			if (scores.getTimeframe() != null) {
				if ((scores.getTimeframe()).equalsIgnoreCase("M")) {
					colHeader = "Processed Date";
				} else {
					colHeader = "Month";
				}
			} else {
				colHeader = "Month";
			}

			if ((scores.getSubject()).equalsIgnoreCase("ASSOCIATE")
					|| (scores.getSubject()).equalsIgnoreCase("SUPERVISOR")
					|| (scores.getSubject()).equalsIgnoreCase("MANAGER")
					|| (scores.getSubject()).equalsIgnoreCase("DIRECTOR")

			) {
				int count = 0;
				if ((null != (scores.getSample()) && (scores.getSample())
						.equalsIgnoreCase("in"))
						|| ((scores.getSubject()).equalsIgnoreCase("LOB"))) {

					/* IN-Sample RS */

					ResultSet rs1 = (ResultSet) callableStatment.getObject(41);
					logger.debug("Row count IS " + rs1.getRow());

					while (rs1.next()) {

						ScoresReport scoresIs = new ScoresReport();

						logger.debug("Scores RS 1 "
								+ rs1.getString("PROCESS_PERIOD") + " claims "
								+ rs1.getString("CLAIMS") + "paid "
								+ rs1.getString("PAID") + "opaid "
								+ rs1.getString("OVER_PAID") + "upaid "
								+ rs1.getString("UNDER_PAID") + "pro "
								+ rs1.getString("PROCEDURAL") + "mon "
								+ rs1.getString("MONETARY") + "total "
								+ rs1.getString("TOTAL"));
						++count;
						// IS col header //only for Associate
						scoresIs.setProcessedColHeader(colHeader);

						// IS counts
						if((scores.getUse()).equalsIgnoreCase("TF")){
							scoresIs.setTimeframe(getTimeframe(scores.getPeriod(), scores.getYear(), scores.getTimeframe()));
						}
						else{
							scoresIs.setTimeframe((scores.getFromDate())+" To "+(scores.getToDate()));
						}
						
						scoresIs.setE2e(getType(scores.getE2e(),"E"));
						scoresIs.setAssociateName(callableStatment.getString(17));
						scoresIs.setEmpId(callableStatment.getString(18));
						scoresIs.setAuditType(getType(scores.getAuditType(),"P"));
						scoresIs.setSubType(getType(scores.getSubType(),"M"));

						scoresIs.setIsRowCount(String.valueOf(count));
						scoresIs.setIsTotalClaims(callableStatment
								.getString(19));
						scoresIs.setIsTotalPaid(callableStatment.getString(20));
						scoresIs.setIsTotalOverPaid(callableStatment
								.getString(21));
						scoresIs.setIsTotalUnderPaid(callableStatment
								.getString(22));
						scoresIs.setIsTotalProcedural(callableStatment
								.getString(23));
						scoresIs.setIsTotalMonetary(callableStatment
								.getString(24));
						scoresIs.setIsSumTotal(callableStatment.getString(25));
						
						/*scoresIs.setIsTotalProceduralAccuracy(callableStatment.getString(24));
						scoresIs.setIsTotalDollarFreq(callableStatment.getString(25));
						scoresIs.setIsTotalDollarAccuracy(callableStatment.getString(26));
						scoresIs.setIsTotalCompositeQuality(callableStatment.getString(27));*/
						
						scoresIs.setIsTotalProceduralAccuracy(df.format(Double.parseDouble(callableStatment.getString(26))));
						scoresIs.setIsTotalDollarFreq(df.format(Double.parseDouble(callableStatment.getString(27))));
						scoresIs.setIsTotalDollarAccuracy(df.format(Double.parseDouble(callableStatment.getString(28))));
						scoresIs.setIsTotalCompositeQuality(df.format(Double.parseDouble(callableStatment.getString(29))));
						
						// RS PROCESS_DT PROCESS_PERIOD
						if (null != (rs1.getString("PROCESS_PERIOD"))) {
							scoresIs.setProcessedDate((rs1
									.getString("PROCESS_PERIOD")).toString());
							type= "is";
						} else {
							scoresIs.setProcessedDate("");
						}

						if (null != (rs1.getString("CLAIMS"))) {
							scoresIs.setIsClaims((rs1.getString("CLAIMS"))
									.toString());
						} else {
							scoresIs.setIsClaims("");
						}

						if (null != (rs1.getString("PAID"))) {
							scoresIs.setIsPaid((rs1.getString("PAID"))
									.toString());
						} else {
							scoresIs.setIsPaid("");
						}
						if (null != (rs1.getString("OVER_PAID"))) {
							scoresIs.setIsOverPaid((rs1.getString("OVER_PAID"))
									.toString());
						} else {
							scoresIs.setIsOverPaid("");
						}
						if (null != (rs1.getString("UNDER_PAID"))) {
							scoresIs.setIsUnderPaid((rs1
									.getString("UNDER_PAID")).toString());
						} else {
							scoresIs.setIsUnderPaid("");
						}
						if (null != (rs1.getString("PROCEDURAL"))) {
							scoresIs.setIsProcedural((rs1
									.getString("PROCEDURAL")).toString());
						} else {
							scoresIs.setIsProcedural("");
						}
						if (null != (rs1.getString("MONETARY"))) {
							scoresIs.setIsMonetary((rs1.getString("MONETARY"))
									.toString());
						} else {
							scoresIs.setIsMonetary("");
						}
						if (null != (rs1.getString("TOTAL"))) {
							scoresIs.setIsTotal((rs1.getString("TOTAL"))
									.toString());
						} else {
							scoresIs.setIsTotal("");
						}
						//accuracy row
					if((scores.getUse()).equalsIgnoreCase("TF")){
						if(!(((scores.getSubject()).equalsIgnoreCase("ASSOCIATE"))&&((scores.getTimeframe()).equalsIgnoreCase("M")))){
							if (null != (rs1.getString("PROCEDURAL_ACCURACY"))) {
							scoresIs.setIsProceduralAccuracy((df.format(Double.parseDouble((rs1.getString("PROCEDURAL_ACCURACY")).toString())))+"%");
							} else {
							scoresIs.setIsProceduralAccuracy("");
							}
							
							if (null != (rs1.getString("DOLLAR_FREQUENCY"))) {
							scoresIs.setIsDollarFreq((df.format(Double.parseDouble((rs1.getString("DOLLAR_FREQUENCY")).toString())))+"%");
							} else {
							scoresIs.setIsDollarFreq("");
							}
							if (null != (rs1.getString("DOLLAR_ACCURACY"))) {
							scoresIs.setIsDollarAccuracy((df.format(Double.parseDouble((rs1.getString("DOLLAR_ACCURACY")).toString())))+"%");
							} else {
							scoresIs.setIsDollarAccuracy("");
							}
							if (null != (rs1.getString("COMPOSITE_SCORE"))) {
							scoresIs.setIsCompositeQuality((df.format(Double.parseDouble((rs1.getString("COMPOSITE_SCORE")).toString())))+"%");
							} else {
							scoresIs.setIsCompositeQuality("");
							}
						}
					}
						else{
							if(!(((scores.getSubject()).equalsIgnoreCase("ASSOCIATE")))){
								if (null != (rs1.getString("PROCEDURAL_ACCURACY"))) {
								scoresIs.setIsProceduralAccuracy((df.format(Double.parseDouble((rs1.getString("PROCEDURAL_ACCURACY")).toString())))+"%");
								} else {
								scoresIs.setIsProceduralAccuracy("");
								}
								if (null != (rs1.getString("DOLLAR_FREQUENCY"))) {
								scoresIs.setIsDollarFreq((df.format(Double.parseDouble((rs1.getString("DOLLAR_FREQUENCY")).toString())))+"%");
								} else {
								scoresIs.setIsDollarFreq("");
								}
								if (null != (rs1.getString("DOLLAR_ACCURACY"))) {
								scoresIs.setIsDollarAccuracy((df.format(Double.parseDouble((rs1.getString("DOLLAR_ACCURACY")).toString())))+"%");
								} else {
								scoresIs.setIsDollarAccuracy("");
								}
								if (null != (rs1.getString("COMPOSITE_SCORE"))) {
								scoresIs.setIsCompositeQuality((df.format(Double.parseDouble((rs1.getString("COMPOSITE_SCORE")).toString())))+"%");
								} else {
								scoresIs.setIsCompositeQuality("");
								}
							}
						}
						
						scoresList.add(scoresIs);
					}
					logger.debug("IS count " + count);
					rs1.close();

				}
				else {

					/* Out-of-Sample RS */
					ResultSet rs2 = (ResultSet) callableStatment.getObject(42);
					logger.debug("Row count OOS" + rs2.getRow());

					while (rs2.next()) {

						ScoresReport scoresOos = new ScoresReport();

						logger.debug("Scores RS 2 "
								+ rs2.getString("PROCESS_PERIOD") + " claims "
								+ rs2.getString("CLAIMS") + "paid "
								+ rs2.getString("PAID") + "opaid "
								+ rs2.getString("OVER_PAID") + "upaid "
								+ rs2.getString("UNDER_PAID") + "pro "
								+ rs2.getString("PROCEDURAL") + "mon "
								+ rs2.getString("MONETARY") + "total "
								+ rs2.getString("TOTAL"));
						++count;
						// IS col header //Only for Associate
						scoresOos.setProcessedColHeader(colHeader);

						// OOS count
						if((scores.getUse()).equalsIgnoreCase("TF")){
							scoresOos.setTimeframe(getTimeframe(scores.getPeriod(), scores.getYear(), scores.getTimeframe()));
						}
						else{
							scoresOos.setTimeframe((scores.getFromDate())+" To "+(scores.getToDate()));
						}
						scoresOos.setE2e(getType(scores.getE2e(),"E"));
						scoresOos.setAssociateName(callableStatment.getString(17));
						scoresOos.setEmpId(callableStatment.getString(18));
						scoresOos.setAuditType(getType(scores.getAuditType(),"P"));
						scoresOos.setSubType(getType(scores.getSubType(),"M"));
						
						scoresOos.setIsRowCount(String.valueOf(count));
						scoresOos.setIsTotalClaims(callableStatment
								.getString(30));
						scoresOos
								.setIsTotalPaid(callableStatment.getString(31));
						scoresOos.setIsTotalOverPaid(callableStatment
								.getString(32));
						scoresOos.setIsTotalUnderPaid(callableStatment
								.getString(33));
						scoresOos.setIsTotalProcedural(callableStatment
								.getString(34));
						scoresOos.setIsTotalMonetary(callableStatment
								.getString(35));
						scoresOos.setIsSumTotal(callableStatment.getString(36));
						
						scoresOos.setIsTotalProceduralAccuracy(df.format(Double.parseDouble(callableStatment.getString(37))));
						scoresOos.setIsTotalDollarFreq(df.format(Double.parseDouble(callableStatment.getString(38))));
						scoresOos.setIsTotalDollarAccuracy(df.format(Double.parseDouble(callableStatment.getString(39))));
						scoresOos.setIsTotalCompositeQuality(df.format(Double.parseDouble(callableStatment.getString(40))));
						
						// RS DT PROCESS_PERIOD
						if (null != (rs2.getString("PROCESS_PERIOD"))) {
							scoresOos.setProcessedDate((rs2
									.getString("PROCESS_PERIOD")).toString());
							type= "oos";
						} else {
							scoresOos.setProcessedDate("");
						}
						if (null != (rs2.getString("CLAIMS"))) {
							scoresOos.setIsClaims((rs2.getString("CLAIMS"))
									.toString());
						} else {
							scoresOos.setIsClaims("");
						}
						if (null != (rs2.getString("PAID"))) {
							scoresOos.setIsPaid((rs2.getString("PAID"))
									.toString());
						} else {
							scoresOos.setIsPaid("");
						}
						if (null != (rs2.getString("OVER_PAID"))) {
							scoresOos
									.setIsOverPaid((rs2.getString("OVER_PAID"))
											.toString());
						} else {
							scoresOos.setIsOverPaid("");
						}
						if (null != (rs2.getString("UNDER_PAID"))) {
							scoresOos.setIsUnderPaid((rs2
									.getString("UNDER_PAID")).toString());
						} else {
							scoresOos.setIsUnderPaid("");
						}
						if (null != (rs2.getString("PROCEDURAL"))) {
							scoresOos.setIsProcedural((rs2
									.getString("PROCEDURAL")).toString());
						} else {
							scoresOos.setIsProcedural("");
						}
						if (null != (rs2.getString("MONETARY"))) {
							scoresOos.setIsMonetary((rs2.getString("MONETARY"))
									.toString());
						} else {
							scoresOos.setIsMonetary("");
						}
						if (null != (rs2.getString("TOTAL"))) {
							scoresOos.setIsTotal((rs2.getString("TOTAL"))
									.toString());
						} else {
							scoresOos.setIsTotal("");
						}
						
						//accuracy row
					if((scores.getUse()).equalsIgnoreCase("TF")){
						if(!(((scores.getSubject()).equalsIgnoreCase("ASSOCIATE"))&&((scores.getTimeframe()).equalsIgnoreCase("M")))){
							if (null != (rs2.getString("PROCEDURAL_ACCURACY"))) {
							scoresOos.setIsProceduralAccuracy((df.format(Double.parseDouble((rs2.getString("PROCEDURAL_ACCURACY")).toString())))+"%");
							} else {
							scoresOos.setIsProceduralAccuracy("");
							}
							if (null != (rs2.getString("DOLLAR_FREQUENCY"))) {
							scoresOos.setIsDollarFreq((df.format(Double.parseDouble((rs2.getString("DOLLAR_FREQUENCY")).toString())))+"%");
							} else {
							scoresOos.setIsDollarFreq("");
							}
							if (null != (rs2.getString("DOLLAR_ACCURACY"))) {
							scoresOos.setIsDollarAccuracy((df.format(Double.parseDouble((rs2.getString("DOLLAR_ACCURACY")).toString())))+"%");
							} else {
							scoresOos.setIsDollarAccuracy("");
							}
							if (null != (rs2.getString("COMPOSITE_SCORE"))) {
							scoresOos.setIsCompositeQuality((df.format(Double.parseDouble((rs2.getString("COMPOSITE_SCORE")).toString())))+"%");
							} else {
							scoresOos.setIsCompositeQuality("");
							}
						}
					}
					else{
						if(!(((scores.getSubject()).equalsIgnoreCase("ASSOCIATE")))){
							if (null != (rs2.getString("PROCEDURAL_ACCURACY"))) {
							scoresOos.setIsProceduralAccuracy((df.format(Double.parseDouble((rs2.getString("PROCEDURAL_ACCURACY")).toString())))+"%");
							} else {
							scoresOos.setIsProceduralAccuracy("");
							}
							if (null != (rs2.getString("DOLLAR_FREQUENCY"))) {
							scoresOos.setIsDollarFreq((df.format(Double.parseDouble((rs2.getString("DOLLAR_FREQUENCY")).toString())))+"%");
							} else {
							scoresOos.setIsDollarFreq("");
							}
							if (null != (rs2.getString("DOLLAR_ACCURACY"))) {
							scoresOos.setIsDollarAccuracy((df.format(Double.parseDouble((rs2.getString("DOLLAR_ACCURACY")).toString())))+"%");
							} else {
							scoresOos.setIsDollarAccuracy("");
							}
							if (null != (rs2.getString("COMPOSITE_SCORE"))) {
							scoresOos.setIsCompositeQuality((df.format(Double.parseDouble((rs2.getString("COMPOSITE_SCORE")).toString())))+"%");
							} else {
							scoresOos.setIsCompositeQuality("");
							}
						}
					}
						scoresList.add(scoresOos);

					}
					logger.debug("OOS count " + count);
					rs2.close();

				}

			}// designation loop end

			// division RS

			if ((scores.getSubject()).equalsIgnoreCase("DIVISION")) {

				ResultSet rs1 = (ResultSet) callableStatment.getObject(41);
				logger.debug("Row count IS " + rs1.getRow());

				while (rs1.next()) {

					ScoresReport division = new ScoresReport();

					logger.debug("Scores RS Division - "
							+ rs1.getString("EXAMINERS") + " claims "
							+ rs1.getString("CLAIMS") + "pay"
							+ rs1.getString("PAY_RATIO") + "doll "
							+ rs1.getString("DOLLARS_AUDITED") + "opaid "
							+ rs1.getString("OVERPAYMENTS"));
					if((scores.getUse()).equalsIgnoreCase("TF")){
						division.setTimeframe(getTimeframe(scores.getPeriod(), scores.getYear(), scores.getTimeframe()));
					}
					else{
						division.setTimeframe((scores.getFromDate())+" To "+(scores.getToDate()));
					}
					division.setE2e(getType(scores.getE2e(),"E"));
					division.setAssociateName(callableStatment.getString(17));
					division.setEmpId(callableStatment.getString(18));
					division.setAuditType(getType(scores.getAuditType(),"P"));
					division.setSubType(getType(scores.getSubType(),"M"));
					
					// RS PROCESS_DT
					if (null != (rs1.getString("EXAMINERS"))) {
						division.setExamAudits((rs1.getString("EXAMINERS"))
								.toString());
					} else {
						division.setExamAudits("");
					}
					if (null != (rs1.getString("CLAIMS"))) {
						division.setClaimsAudit((rs1.getString("CLAIMS"))
								.toString());
					} else {
						division.setClaimsAudit("");
					}
					if (null != (rs1.getString("PAY_RATIO"))) {
						division.setPayRatio((rs1.getString("PAY_RATIO"))
								.toString());
					} else {
						division.setPayRatio("");
					}
					if (null != (rs1.getString("DOLLARS_AUDITED"))) {
						division.setDollarAudits((rs1
								.getString("DOLLARS_AUDITED")).toString());
					} else {
						division.setDollarAudits("");
					}
					if (null != (rs1.getString("OVERPAYMENTS"))) {
						division.setOverPay((rs1.getString("OVERPAYMENTS"))
								.toString());
					} else {
						division.setOverPay("");
					}
					if (null != (rs1.getString("UNDERPAYMENTS"))) {
						division.setUnderPay((rs1.getString("UNDERPAYMENTS"))
								.toString());
					} else {
						division.setUnderPay("");
					}
					if (null != (rs1.getString("DOLLARS_IN_ERR"))) {
						division.setTotalDollars((rs1
								.getString("DOLLARS_IN_ERR")).toString());
					} else {
						division.setTotalDollars("");
					}
					if (null != (rs1.getString("ERROR_CLAIMS"))) {
						division.setErrorClaims((rs1.getString("ERROR_CLAIMS"))
								.toString());
					} else {
						division.setErrorClaims("");
					}
					if (null != (rs1.getString("MONETARY_CLAIMS"))) {
						division.setMonErrClaim((rs1
								.getString("MONETARY_CLAIMS")).toString());
					} else {
						division.setMonErrClaim("");
					}
					if (null != (rs1.getString("PROCEDURAL_CLAIMS"))) {
						division.setProcErrClaim((rs1
								.getString("PROCEDURAL_CLAIMS")).toString());
					} else {
						division.setProcErrClaim("");
					}
					
					//Accuracy
						if (null != (rs1.getString("DOLLAR_ACCURACY"))) {
						division.setDollarAccuracy(df.format(Double.parseDouble((rs1.getString("DOLLAR_ACCURACY")).toString())));
						} else {
						division.setDollarAccuracy("");
						} 
						if (null != (rs1.getString("COMPOSITE_SCORE"))) {
							division.setErrorFreq(df.format(Double.parseDouble((rs1.getString("COMPOSITE_SCORE")).toString())));
						} else {
							division.setErrorFreq("");
						}
						if (null != (rs1.getString("DOLLAR_FREQUENCY"))) {
							division.setDollarFreq(df.format(Double.parseDouble((rs1.getString("DOLLAR_FREQUENCY")).toString())));
						} else {
							division.setDollarFreq("");
						}
						if (null != (rs1.getString("PROCEDURAL_ACCURACY"))) {
							division.setProAccuaracy(df.format(Double.parseDouble((rs1.getString("PROCEDURAL_ACCURACY")).toString())));
						} else {
							division.setProAccuaracy("");
						}
						
					
					scoresList.add(division);
				}
				rs1.close();

			}

			// LOB/ RS

			if ((scores.getSubject()).equalsIgnoreCase("LOB")||(scores.getSubject()).equalsIgnoreCase("PPG")
					||(scores.getSubject()).equalsIgnoreCase("SPG")
					) {

				ResultSet rs1 = (ResultSet) callableStatment.getObject(41);
				logger.debug("Row count LOB " + rs1.getRow());

				while (rs1.next()) {

					// scoreData -- includes LOB/PG1/PG2 data

					ScoresReport scoresData = new ScoresReport();

					logger.debug("Scores RS 1 LOB -- "
							+ rs1.getString("DCN_NBR") + "paid "
							+ rs1.getString("PAID") + "opaid "
							+ rs1.getString("OVER_PAID") + "upaid "
							+ rs1.getString("UNDER_PAID") + "pro "
							+ rs1.getString("PROCEDURAL") + "mon "
							+ rs1.getString("MONETARY") + "total "
							+ rs1.getString("TOTAL"));
					
					if((scores.getSubject()).equalsIgnoreCase("PPG")){
						scoresData.setPgType("Primary");
					}else{
						scoresData.setPgType("Secondary");
					}
					
					if((scores.getUse()).equalsIgnoreCase("TF")){
						scoresData.setTimeframe(getTimeframe(scores.getPeriod(), scores.getYear(), scores.getTimeframe()));
					}
					else{
						scoresData.setTimeframe((scores.getFromDate())+" To "+(scores.getToDate()));
					}
					scoresData.setE2e(getType(scores.getE2e(),"E"));
					scoresData.setAssociateName(callableStatment.getString(17));
					scoresData.setEmpId(callableStatment.getString(18));
					scoresData.setAuditType(getType(scores.getAuditType(),"P"));
					scoresData.setSubType(getType(scores.getSubType(),"M"));
					
					scoresData.setIsTotalClaims(callableStatment.getString(19));
					scoresData.setIsTotalPaid(callableStatment.getString(20));
					scoresData.setIsTotalOverPaid(callableStatment.getString(21));
					scoresData.setIsTotalUnderPaid(callableStatment.getString(22));
					scoresData.setIsTotalProcedural(callableStatment.getString(23));
					scoresData.setIsTotalMonetary(callableStatment.getString(24));
					scoresData.setIsSumTotal(callableStatment.getString(25));
					
					scoresData.setIsTotalProceduralAccuracy(df.format(Double.parseDouble(callableStatment.getString(26))));
					scoresData.setIsTotalDollarFreq(df.format(Double.parseDouble(callableStatment.getString(27))));
					scoresData.setIsTotalDollarAccuracy(df.format(Double.parseDouble(callableStatment.getString(28))));
					scoresData.setIsTotalCompositeQuality(df.format(Double.parseDouble(callableStatment.getString(29))));
					
					// RS PROCESS_DT PROCESS_PERIOD
					if (null != (rs1.getString("DCN_NBR"))) {
						scoresData.setProcessedDate((rs1
								.getString("DCN_NBR")).toString());
					} else {
						scoresData.setProcessedDate("");
					}
					if (null != (rs1.getString("PAID"))) {
						scoresData.setIsPaid((rs1.getString("PAID")).toString());
					} else {
						scoresData.setIsPaid("");
					}
					if (null != (rs1.getString("OVER_PAID"))) {
						scoresData.setIsOverPaid((rs1.getString("OVER_PAID"))
								.toString());
					} else {
						scoresData.setIsOverPaid("");
					}
					if (null != (rs1.getString("UNDER_PAID"))) {
						scoresData.setIsUnderPaid((rs1.getString("UNDER_PAID"))
								.toString());
					} else {
						scoresData.setIsUnderPaid("");
					}
					if (null != (rs1.getString("PROCEDURAL"))) {
						scoresData.setIsProcedural((rs1.getString("PROCEDURAL"))
								.toString());
					} else {
						scoresData.setIsProcedural("");
					}
					if (null != (rs1.getString("MONETARY"))) {
						scoresData.setIsMonetary((rs1.getString("MONETARY"))
								.toString());
					} else {
						scoresData.setIsMonetary("");
					}
					if (null != (rs1.getString("TOTAL"))) {
						scoresData.setIsTotal((rs1.getString("TOTAL")).toString());
					} else {
						scoresData.setIsTotal("");
					}
					
					scoresList.add(scoresData);
				}
				rs1.close();

			}

			callableStatment.close();

			conn.close();

		} catch (Exception e) {
			e.printStackTrace();
			logger.debug("Exception generated "+e.getMessage());
		}

		JRDataSource ds = new JRBeanCollectionDataSource(scoresList);

		// Return the wrapped collection
		return ds;

	}
	
	
	//Trends
	
	@Override
	public JRDataSource getTrendsReport(ScoresAndTrends trends)
			throws SQLException {

		logger.debug("***getTrendsReport entry***");
		logger.debug("Trends " + trends.getSubject() + " " + trends.getName());
		String getTrendsReport = QADBConstants.REPORTS_TRENDS_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getTrendsReport);
		List<TrendsReport> scoresList = new ArrayList<TrendsReport>();

		try {

			callableStatment.setNull(1, java.sql.Types.VARCHAR);
			callableStatment.setString(2, trends.getSubject());
			callableStatment.setInt(3, Integer.parseInt(trends.getName()));
			callableStatment.setString(4, trends.getType());
			callableStatment.setString(5, trends.getSubType());
			callableStatment.setInt(6, Integer.parseInt(trends.getAuditType()));
			callableStatment.setString(7, trends.getE2e() );
			if((trends.getUse()).equalsIgnoreCase("TF")){
				callableStatment.setString(8, trends.getYear());
				
				if ((trends.getTimeframe()).equalsIgnoreCase("N")
						&& (trends.getPeriod()).equalsIgnoreCase("N")) {
					callableStatment.setString(9, "Y"); // Yearly
					callableStatment.setNull(10, java.sql.Types.VARCHAR);
				} else {
					callableStatment.setString(9, trends.getTimeframe());
					callableStatment.setString(10, trends.getPeriod());
				}
				callableStatment.setNull(11, java.sql.Types.VARCHAR);
				callableStatment.setNull(12, java.sql.Types.VARCHAR);
			}
			else{
				callableStatment.setNull(8, java.sql.Types.VARCHAR);//year
				callableStatment.setNull(9, java.sql.Types.VARCHAR);//TF
				callableStatment.setNull(10, java.sql.Types.VARCHAR);//Period
				callableStatment.setString(11, trends.getFromDate());
				callableStatment.setString(12, trends.getToDate());
			}
			
			callableStatment.setNull(13, java.sql.Types.VARCHAR);

			callableStatment.registerOutParameter(14, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(15, OracleTypes.VARCHAR);
			/* set out parameters from 14 to 23 */
			for (int i = 16; i < 24; i++) {
				callableStatment.registerOutParameter(i, OracleTypes.INTEGER);
			}

			callableStatment.registerOutParameter(24, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(25, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(26, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(27, OracleTypes.VARCHAR);
			callableStatment.executeUpdate();

			//Associate--Supervisor
			if ((trends.getSubject()).equalsIgnoreCase("ASSOCIATE")||(trends.getSubject()).equalsIgnoreCase("SUPERVISOR")) {
				if ((null != (trends.getSample()) && (trends.getSample())
						.equalsIgnoreCase("in"))
						|| ((trends.getSubject()).equalsIgnoreCase("LOB"))) {

					/* IN-Sample RS */

					ResultSet rs1 = (ResultSet) callableStatment.getObject(24);
					logger.debug("Row count IS " + rs1.getRow());

					while (rs1.next()) {

						TrendsReport trendsIs = new TrendsReport();

						logger.debug("Trends RS 1 "
								+ rs1.getString("ERROR_ID") + " name "
								+ rs1.getString("ERROR_NAME") + "mon "
								+ rs1.getString("MONETARY") + "pro "
								+ rs1.getString("PROCEDURAL"));
						

						// IS counts
						trendsIs.setTitle(getTitle((trends.getSubject())));
						if((trends.getUse()).equalsIgnoreCase("TF")){
							trendsIs.setTimeframe(getTimeframe(trends.getPeriod(), trends.getYear(), trends.getTimeframe()));
						}
						else{
							trendsIs.setTimeframe((trends.getFromDate())+" To "+(trends.getToDate()));
						}
						trendsIs.setE2e(getType(trends.getE2e(),"E"));
						trendsIs.setAssociateName(callableStatment
								.getString(14));
					//	trendsIs.setEmpId(callableStatment.getString(15));
						trendsIs.setAuditType(getType(trends.getAuditType(),"P"));
						trendsIs.setSubType(getType(trends.getSubType(),"M"));
						
						
						trendsIs.setIsTotalClaims(callableStatment
								.getString(16));
						trendsIs.setIsTotalErrClaims(callableStatment.getString(17));
						trendsIs.setIsTotalMonetary(callableStatment
								.getString(18));
						trendsIs.setIsTotalProcedural(callableStatment
								.getString(19));
						trendsIs.setOosTotalClaims(callableStatment
								.getString(20));
						trendsIs.setOosTotalErrClaims(callableStatment.getString(21));
						
						int totalTotalClaims = ((Integer.parseInt((callableStatment
								.getString(16))))+(Integer.parseInt((callableStatment
										.getString(20)))));
						trendsIs.setTotalClaims(String.valueOf(totalTotalClaims));
						
						int totalTotalErrClaims = ((Integer.parseInt((callableStatment
								.getString(17))))+(Integer.parseInt((callableStatment
										.getString(21)))));
						trendsIs.setTotalErrClaims(String.valueOf(totalTotalErrClaims));
						
						int isTotalErrors = ((Integer.parseInt((callableStatment
								.getString(18))))+(Integer.parseInt((callableStatment
										.getString(19)))));
						trendsIs.setIsTotalErrors(String.valueOf(isTotalErrors));
						
						// RS PROCESS_DT PROCESS_PERIOD
						if (null != (rs1.getString("ERROR_ID"))) {
							trendsIs.setIsErrorId((rs1
									.getString("ERROR_ID")).toString());
							type= "is";
						} else {
							trendsIs.setIsErrorId("");
						}

						if (null != (rs1.getString("ERROR_NAME"))) {
							trendsIs.setIsErrorName((rs1.getString("ERROR_NAME"))
									.toString());
						} else {
							trendsIs.setIsErrorName("");
						}

						if (null != (rs1.getString("MONETARY"))) {
							trendsIs.setIsErrorMonetary((rs1.getString("MONETARY"))
									.toString());
						} else {
							trendsIs.setIsErrorMonetary("");
						}
						if (null != (rs1.getString("PROCEDURAL"))) {
							trendsIs.setIsErrorProcedural((rs1.getString("PROCEDURAL"))
									.toString());
						} else {
							trendsIs.setIsErrorProcedural("");
						}
						

						scoresList.add(trendsIs);
					}
					rs1.close();

				} else {

					/* Out-of-Sample RS */
					ResultSet rs2 = (ResultSet) callableStatment.getObject(25);
					logger.debug("Row count OOS" + rs2.getRow());

					while (rs2.next()) {

						TrendsReport trendsIs = new TrendsReport();

						logger.debug("Trends RS 2 "
								+ rs2.getString("ERROR_ID") + " name "
								+ rs2.getString("ERROR_NAME") + "mon "
								+ rs2.getString("MONETARY") + "pro "
								+ rs2.getString("PROCEDURAL"));
						

						// IS counts
						trendsIs.setTitle(getTitle((trends.getSubject())));
						if((trends.getUse()).equalsIgnoreCase("TF")){
							trendsIs.setTimeframe(getTimeframe(trends.getPeriod(), trends.getYear(), trends.getTimeframe()));
						}
						else{
							trendsIs.setTimeframe((trends.getFromDate())+" To "+(trends.getToDate()));
						}
						trendsIs.setE2e(getType(trends.getE2e(),"E"));
						trendsIs.setAssociateName(callableStatment
								.getString(14));
					//	trendsIs.setEmpId(callableStatment.getString(15));
						trendsIs.setAuditType(getType(trends.getAuditType(),"P"));
						trendsIs.setSubType(getType(trends.getSubType(),"M"));

						trendsIs.setIsTotalClaims(callableStatment
								.getString(16));
						trendsIs.setIsTotalErrClaims(callableStatment.getString(17));
						trendsIs.setOosTotalClaims(callableStatment
								.getString(20));
						trendsIs.setOosTotalErrClaims(callableStatment.getString(21));
						trendsIs.setIsTotalMonetary(callableStatment
								.getString(22));
						trendsIs.setIsTotalProcedural(callableStatment
								.getString(23));
						
						
						int totalTotalClaims = ((Integer.parseInt((callableStatment
								.getString(16))))+(Integer.parseInt((callableStatment
										.getString(20)))));
						trendsIs.setTotalClaims(String.valueOf(totalTotalClaims));
						
						int totalTotalErrClaims = ((Integer.parseInt((callableStatment
								.getString(17))))+(Integer.parseInt((callableStatment
										.getString(21)))));
						trendsIs.setTotalErrClaims(String.valueOf(totalTotalErrClaims));
						
						int oosTotalErrors = ((Integer.parseInt((callableStatment
								.getString(22))))+(Integer.parseInt((callableStatment
										.getString(23)))));
						trendsIs.setOosTotalErrors(String.valueOf(oosTotalErrors));
						
						// RS PROCESS_DT PROCESS_PERIOD
						if (null != (rs2.getString("ERROR_ID"))) {
							trendsIs.setIsErrorId((rs2
									.getString("ERROR_ID")).toString());
							type= "oos";
						} else {
							trendsIs.setIsErrorId("");
						}

						if (null != (rs2.getString("ERROR_NAME"))) {
							trendsIs.setIsErrorName((rs2.getString("ERROR_NAME"))
									.toString());
						} else {
							trendsIs.setIsErrorName("");
						}

						if (null != (rs2.getString("MONETARY"))) {
							trendsIs.setIsErrorMonetary((rs2.getString("MONETARY"))
									.toString());
						} else {
							trendsIs.setIsErrorMonetary("");
						}
						if (null != (rs2.getString("PROCEDURAL"))) {
							trendsIs.setIsErrorProcedural((rs2.getString("PROCEDURAL"))
									.toString());
						} else {
							trendsIs.setIsErrorProcedural("");
						}
						

						scoresList.add(trendsIs);

					}
					rs2.close();

				}

			}// designation loop end
			
			//Manager-Division-LOB-PG
			if((trends.getSubject()).equalsIgnoreCase("DIRECTOR")||(trends.getSubject()).equalsIgnoreCase("MANAGER")||(trends.getSubject()).equalsIgnoreCase("DIVISION")||(trends.getSubject()).equalsIgnoreCase("LOB")
					||(trends.getSubject()).equalsIgnoreCase("PPG")||(trends.getSubject()).equalsIgnoreCase("SPG")
					){
				

				ResultSet rs1 = (ResultSet) callableStatment.getObject(24);
				logger.debug("Row count IS Division " + rs1.getRow());
				
				String rcid="";
				String errorId="";
				while (rs1.next()) {

					TrendsReport trendsIs = new TrendsReport();

					logger.debug("Trends RS 1 "
							+ rs1.getString("ROOT_CAUSE_ID") + " name "
							+ rs1.getString("ROOT_CAUSE_NAME") + "mon "
							+ rs1.getString("RTC_MONETARY") + "pro "
							+ rs1.getString("RTC_PROCEDURAL"));
					
					trendsIs.setTitle(getTitle((trends.getSubject())));
					if((trends.getUse()).equalsIgnoreCase("TF")){
						trendsIs.setTimeframe(getTimeframe(trends.getPeriod(), trends.getYear(), trends.getTimeframe()));
					}
					else{
						trendsIs.setTimeframe((trends.getFromDate())+" To "+(trends.getToDate()));
					}
					trendsIs.setE2e(getType(trends.getE2e(),"E"));
					trendsIs.setAssociateName(callableStatment.getString(14));
					trendsIs.setAuditType(getType(trends.getAuditType(),"P"));
					trendsIs.setSubType(getType(trends.getSubType(),"M"));

					// RS 
				if(!(rcid.equalsIgnoreCase(rs1.getString("ROOT_CAUSE_ID")))){
					if (null != (rs1.getString("ROOT_CAUSE_NAME"))) {
						trendsIs.setRootCauseName((rs1
								.getString("ROOT_CAUSE_NAME")).toString());
					} else {
						trendsIs.setRootCauseName("");
					}

					if (null != (rs1.getString("RTC_MONETARY"))) {
						trendsIs.setRootCauseMonetary((rs1.getString("RTC_MONETARY"))
								.toString());
					} else {
						trendsIs.setRootCauseMonetary("");
					}

					if (null != (rs1.getString("RTC_PROCEDURAL"))) {
						trendsIs.setRootCauseProcedural((rs1.getString("RTC_PROCEDURAL"))
								.toString());
					} else {
						trendsIs.setRootCauseProcedural("");
					}
					//Error details
					if(!(errorId.equalsIgnoreCase(rs1.getString("ERROR_ID")))){
						if (null != (rs1.getString("ERROR_ID"))) {
							trendsIs.setErrorId((rs1.getString("ERROR_ID")).toString());
						} else {
							trendsIs.setErrorId("");
						}

						if (null != (rs1.getString("ERROR_NAME"))) {
							trendsIs.setErrorName((rs1.getString("ERROR_NAME")).toString());
						} else {
							trendsIs.setErrorName("");
						}

						if (null != (rs1.getString("ERR_MONETARY"))) {
							trendsIs.setErrorMonetary((rs1.getString("ERR_MONETARY")).toString());
						} else {
							trendsIs.setErrorMonetary("");
						}
						
						if (null != (rs1.getString("ERR_PROCEDURAL"))) {
							trendsIs.setErrorProcedural((rs1.getString("ERR_PROCEDURAL")).toString());
						} else {
							trendsIs.setErrorProcedural("");
						}
					}else{
						if (null != (rs1.getString("SPECIALTY_NAME"))) {
							trendsIs.setSpecialty((rs1.getString("SPECIALTY_NAME")).toString());
						} else {
							trendsIs.setSpecialty("");
						}

						if (null != (rs1.getString("SPCL_MONETARY"))) {
							trendsIs.setSpecialtyMonetary((rs1.getString("SPCL_MONETARY")).toString());
						} else {
							trendsIs.setSpecialtyMonetary("");
						}
						
						if (null != (rs1.getString("SPCL_PROCEDURAL"))) {
							trendsIs.setSpecialtyProcedural((rs1.getString("SPCL_PROCEDURAL")).toString());
						} else {
							trendsIs.setSpecialtyProcedural("");
						}
					}
					
					//specialty
					if (null != (rs1.getString("SPECIALTY_NAME"))) {
						trendsIs.setSpecialty((rs1.getString("SPECIALTY_NAME")).toString());
					} else {
						trendsIs.setSpecialty("");
					}

					if (null != (rs1.getString("SPCL_MONETARY"))) {
						trendsIs.setSpecialtyMonetary((rs1.getString("SPCL_MONETARY")).toString());
					} else {
						trendsIs.setSpecialtyMonetary("");
					}
					
					if (null != (rs1.getString("SPCL_PROCEDURAL"))) {
						trendsIs.setSpecialtyProcedural((rs1.getString("SPCL_PROCEDURAL")).toString());
					} else {
						trendsIs.setSpecialtyProcedural("");
					}
					
				}
				else{
					
					if(!(errorId.equalsIgnoreCase(rs1.getString("ERROR_ID")))){
						if (null != (rs1.getString("ERROR_ID"))) {
							trendsIs.setErrorId((rs1.getString("ERROR_ID")).toString());
						} else {
							trendsIs.setErrorId("");
						}

						if (null != (rs1.getString("ERROR_NAME"))) {
							trendsIs.setErrorName((rs1.getString("ERROR_NAME")).toString());
						} else {
							trendsIs.setErrorName("");
						}

						if (null != (rs1.getString("ERR_MONETARY"))) {
							trendsIs.setErrorMonetary((rs1.getString("ERR_MONETARY")).toString());
						} else {
							trendsIs.setErrorMonetary("");
						}
						
						if (null != (rs1.getString("ERR_PROCEDURAL"))) {
							trendsIs.setErrorProcedural((rs1.getString("ERR_PROCEDURAL")).toString());
						} else {
							trendsIs.setErrorProcedural("");
						}
					}
					
						
						if (null != (rs1.getString("SPECIALTY_NAME"))) {
							trendsIs.setSpecialty((rs1.getString("SPECIALTY_NAME")).toString());
						} else {
							trendsIs.setSpecialty("");
						}

						if (null != (rs1.getString("SPCL_MONETARY"))) {
							trendsIs.setSpecialtyMonetary((rs1.getString("SPCL_MONETARY")).toString());
						} else {
							trendsIs.setSpecialtyMonetary("");
						}
						
						if (null != (rs1.getString("SPCL_PROCEDURAL"))) {
							trendsIs.setSpecialtyProcedural((rs1.getString("SPCL_PROCEDURAL")).toString());
						} else {
							trendsIs.setSpecialtyProcedural("");
						}
						
					
				}
				
					rcid = rs1.getString("ROOT_CAUSE_ID");
					errorId = rs1.getString("ERROR_ID");
					scoresList.add(trendsIs);
				}
				rs1.close();
				
			}
			
			callableStatment.close();

			conn.close();

		} catch (Exception e) {
			e.printStackTrace();
			logger.debug("Exception generated "+e.getMessage());
		}

		JRDataSource ds = new JRBeanCollectionDataSource(scoresList);
		
		logger.debug("***getTrendsReport entry***");
		// Return the wrapped collection
		return ds;

	}
	
	//Monetary
	public JRDataSource getMonetoryReport(ScoresAndTrends monetory) throws SQLException {
		logger.debug("*** start getMonetoryReport method ***");
		String getMonetoryReport = QADBConstants.REPORTS_MONETORY_ERROR_SHEET_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getMonetoryReport);
		List<AuditAssessment> auditAssessmentList = new ArrayList<AuditAssessment>();
		
		try {
			
			callableStatment.setNull(1, java.sql.Types.VARCHAR);
			callableStatment.setString(2, monetory.getSubject());
			callableStatment.setInt(3, Integer.parseInt(monetory.getName()));
			callableStatment.setString(4, monetory.getType());
			callableStatment.setString(5, monetory.getSubType());
			callableStatment.setInt(6, Integer.parseInt(monetory.getAuditType()));
			callableStatment.setString(7, monetory.getE2e() );
		
			if((monetory.getUse()).equalsIgnoreCase("TF")){
				callableStatment.setString(8, monetory.getYear());
				
				if ((monetory.getTimeframe()).equalsIgnoreCase("N")
						&& (monetory.getPeriod()).equalsIgnoreCase("N")) {
					callableStatment.setString(9, "Y"); // Yearly
					callableStatment.setNull(10, java.sql.Types.VARCHAR);
				} else {
					callableStatment.setString(9, monetory.getTimeframe());
					callableStatment.setString(10, monetory.getPeriod());
				}
				callableStatment.setNull(11, java.sql.Types.VARCHAR);
				callableStatment.setNull(12, java.sql.Types.VARCHAR);
			}
			else{
				callableStatment.setNull(8, java.sql.Types.VARCHAR);//year
				callableStatment.setNull(9, java.sql.Types.VARCHAR);//TF
				callableStatment.setNull(10, java.sql.Types.VARCHAR);//Period
				callableStatment.setString(11, monetory.getFromDate());
				callableStatment.setString(12, monetory.getToDate());
			}
			
			callableStatment.setNull(13, java.sql.Types.VARCHAR);
			
			callableStatment.registerOutParameter(14, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(15, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(16, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(17, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(18, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(19, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(20, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(21, OracleTypes.INTEGER);

			callableStatment.registerOutParameter(22, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(23, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(24, OracleTypes.VARCHAR);
			//logger.debug(" Aya "+callableStatment.execute());
			
			callableStatment.executeUpdate();
			
			logger.debug("Associate name == " +callableStatment.getString(14));
			
			ResultSet  res =   (ResultSet) callableStatment.getObject(22);
			
			logger.debug("res== " +res.getFetchSize());
			
			while(res.next()){
				logger.debug("***Entry");
				AuditAssessment auditAssessmentIs = new AuditAssessment();
				
				auditAssessmentIs.setTitle(getTitle((monetory.getSubject())));
				if((monetory.getUse()).equalsIgnoreCase("TF")){
					auditAssessmentIs.setTimeframe(getTimeframe(monetory.getPeriod(), monetory.getYear(), monetory.getTimeframe()));
				}
				else{
					auditAssessmentIs.setTimeframe((monetory.getFromDate())+" To "+(monetory.getToDate()));
				}
				auditAssessmentIs.setE2e(getType(monetory.getE2e(),"E"));
				auditAssessmentIs.setAuditType(getType(monetory.getAuditType(),"P"));
				auditAssessmentIs.setSubType(getType(monetory.getSubType(),"M"));
				
				auditAssessmentIs.setAssociateName(callableStatment.getString(14));
				auditAssessmentIs.setEmpNO(callableStatment.getString(15));
				auditAssessmentIs.setIsTotalClaims(callableStatment.getString(16));
				auditAssessmentIs.setIsProcClaims(callableStatment.getString(17));
				auditAssessmentIs.setIsMonClaims(callableStatment.getString(18));
				auditAssessmentIs.setIsTotalPaid(callableStatment.getString(19));
				auditAssessmentIs.setIsTotalOvPaid(callableStatment.getString(20));
				auditAssessmentIs.setIsTotalUnPaid(callableStatment.getString(21));
				auditAssessmentIs.setIsSno(res.getString("SNO"));
				auditAssessmentIs.setIsDcn(res.getString("DCN_NBR"));
				auditAssessmentIs.setIsMemId(res.getString("MEMB_ID"));;
				auditAssessmentIs.setIsPaid(res.getString("TOTAL_PAID"));
				auditAssessmentIs.setIsOverPaid(res.getString("OVER_PAID"));
				auditAssessmentIs.setIsUnderPaid(res.getString("UNDER_PAID"));
				auditAssessmentIs.setIsHighDoller(res.getString("HIGH_DOLLAR_FLG"));
				auditAssessmentIs.setIsAuditType(res.getString("AUDIT_TYPE"));
				auditAssessmentIs.setIsMockAudit((res.getString("MOCK_FLG")).toString());
				auditAssessmentIs.setReportType((res.getString("OOS_FLG")).toString());
				if(null != (res.getString("REQUIRED_FLG"))){
					auditAssessmentIs.setIsAdjRequired((res.getString("REQUIRED_FLG")).toString());
				}
				else{
					auditAssessmentIs.setIsAdjRequired("");
				}
				auditAssessmentIs.setIsProcessOn((res.getString("PROCESS_DATE")).toString());
				
				if(null != (res.getString("ERROR_ID"))){
					auditAssessmentIs.setIsErrorCode((res.getString("ERROR_ID")).toString());
				}
				else{
					auditAssessmentIs.setIsErrorCode("");
				}
				if(null != (res.getString("MONETARY_FLG"))){
					auditAssessmentIs.setIsMonetary((res.getString("MONETARY_FLG")).toString());
				}
				else{
					auditAssessmentIs.setIsMonetary("");
				}
				if(null != (res.getString("PROCEDURAL_FLG"))){
					auditAssessmentIs.setIsProcedural((res.getString("PROCEDURAL_FLG")).toString());
				}
				else{
					auditAssessmentIs.setIsProcedural("");
				}
				if(null != (res.getString("ERROR_TYPE"))){
					auditAssessmentIs.setIsErrorType((res.getString("ERROR_TYPE")).toString());
				}
				else{
					auditAssessmentIs.setIsErrorType("");
				}
				if(null != (res.getString("ERROR_REASON_DESC"))){
					auditAssessmentIs.setIsExplanation((res.getString("ERROR_REASON_DESC")).toString());
				}
				else{
					auditAssessmentIs.setIsExplanation("");
				}
				if(null != (res.getString("SOP_NEWS_FLASH_REFNC"))){
					auditAssessmentIs.setIsSop((res.getString("SOP_NEWS_FLASH_REFNC")).toString());
				}
				else{
					auditAssessmentIs.setIsSop("");
				}

				auditAssessmentIs.setIsAuditDate((res.getString("ASSOCIATE")).toString());
				auditAssessmentIs.setIsAuditor((res.getString("MANAGER")).toString());
				if((monetory.getSubject()).equalsIgnoreCase("DIVISION")){
					auditAssessmentIs.setSupervisor((res.getString("SUPERVISOR")).toString());
				}
				
				auditAssessmentList.add(auditAssessmentIs);
				logger.debug("*** exit getMonetoryReport method ***");
			}
			
			
			res.close();
			
			callableStatment.close();
			 
			conn.close();
			
		} catch (Exception e) {
			logger.debug("Exception generated "+e.getMessage());
		}
		
		JRDataSource ds = new JRBeanCollectionDataSource(auditAssessmentList);	
		
		// Return the wrapped collection
		return ds;
	
	
	}
	
	
	@Override
	public List<Associate> getClaimProcessors(String jobTitleName, String userGrp) throws SQLException {

		List<Associate> claimProcessors = null;
		Connection conn =  null ;
		CallableStatement callableStatment = null;
		
		if (jobTitleName.equalsIgnoreCase("Supervisor")
				|| jobTitleName.equalsIgnoreCase("Manager")
				|| jobTitleName.equalsIgnoreCase("Director")
				|| jobTitleName.equalsIgnoreCase("Associate")) {
			logger.debug("in job title id ");
			String jobTitleId = "1";

			if (jobTitleName.equalsIgnoreCase("Supervisor")) {
				jobTitleId = "2";
			} else if (jobTitleName.equalsIgnoreCase("Manager")) {
				jobTitleId = "3";
			} else if (jobTitleName.equalsIgnoreCase("Director")) {
				jobTitleId = "4";
			}

			String sql = QADBConstants.REPORTS_SCORES_TRENDS_CLAIM_PROCESSORS_LIST1
					+ jobTitleId
					+ QADBConstants.REPORTS_SCORES_TRENDS_CLAIM_PROCESSORS_LIST2;

			claimProcessors = new ArrayList<Associate>();

			try {
				 conn = dataSource.getConnection();
				 callableStatment = conn.prepareCall(sql);
				 
				 ResultSet rs = callableStatment.executeQuery();
				 
				 while (rs.next()) {
						Associate claimProcessor = new Associate();
						claimProcessor.setAssociateId((rs.getString("ASSOCIATE_ID"))
								.toString());
						claimProcessor.setAssociateName(((String) (rs.getString("ASSOCIATE_LAST_NAME")))
								+ " "
								+ ((String) (rs.getString("ASSOCIATE_FIRST_NAME"))));
						claimProcessors.add(claimProcessor);
					}	
				 rs.close();
			} catch (SQLException e) {
				logger.debug("Exception generated "+e.getMessage());
			} finally{
				callableStatment.close();
				conn.close();
			}		
		}

		// division DD
		if (jobTitleName.equalsIgnoreCase("Division")) {
			String sql = QADBConstants.REPORTS_SCORES_TRENDS_DIVISION;

			claimProcessors = new ArrayList<Associate>();

			try {
				 conn = dataSource.getConnection();
				 callableStatment = conn.prepareCall(sql);
				 
				 ResultSet rs = callableStatment.executeQuery();
				 
				 while (rs.next()) {
						Associate claimProcessor = new Associate();
						claimProcessor.setAssociateId((rs.getString("DIVISION_ID"))
								.toString());
						claimProcessor.setAssociateName((rs.getString("DIVISION_DESC"))
								.toString());
						claimProcessors.add(claimProcessor);
					}	
				 rs.close();
			} catch (SQLException e) {
				logger.debug("Exception generated "+e.getMessage());
			} finally{
				callableStatment.close();
				conn.close();
			}		
			
		}

		// LOB DD
		if (jobTitleName.equalsIgnoreCase("LOB")) {

			claimProcessors = new ArrayList<Associate>();

			Associate claimProcessor = new Associate();
			claimProcessor.setAssociateId("1");
			claimProcessor.setAssociateName("BLUE CARD HOME");

			claimProcessors.add(claimProcessor);

			Associate claimProcessor2 = new Associate();
			claimProcessor2.setAssociateId("2");
			claimProcessor2.setAssociateName("COMMERCIAL");

			claimProcessors.add(claimProcessor2);

		}

		// PG DD
		if (jobTitleName.equalsIgnoreCase("PPG")|| jobTitleName.equalsIgnoreCase("SPG")) {

			String sql = "";
			if (jobTitleName.equalsIgnoreCase("PPG")) {
				sql = QADBConstants.AUDIT_PRIMARY_PGA_LIST1 +userGrp+ QADBConstants.AUDIT_PRIMARY_PGA_LIST2 ;
			} else {
				sql = QADBConstants.REPORTS_SEC_PGA_LIST1 +userGrp+ QADBConstants.REPORTS_SEC_PGA_LIST2 ;
			}

			claimProcessors = new ArrayList<Associate>();
			
			try {
				 conn = dataSource.getConnection();
				 callableStatment = conn.prepareCall(sql);
				 
				 ResultSet rs = callableStatment.executeQuery();
				 
				 while (rs.next()) {
						Associate claimProcessor = new Associate();
						claimProcessor.setAssociateId((rs.getString("PERF_GROUP_ID"))
								.toString());
						claimProcessor.setAssociateName((rs.getString("PERF_GROUP_NAME"))
								.toString());
						claimProcessors.add(claimProcessor);
					}	
				 rs.close();
			} catch (SQLException e) {
				logger.debug("Exception generated "+e.getMessage());
			} finally{
				callableStatment.close();
				conn.close();
			}		
			
		}

		return claimProcessors;

	}
	
	@Override
	public List<Associate> getFacetsIds() throws SQLException {
		String sql = QADBConstants.REPORTS_FACETS_IDS_LIST ;
		
		List<Associate> facetsIds = new ArrayList<Associate>();

		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
					Associate facetId = new Associate();
					facetId.setFacetsId((rs.getString("ASSOCIATE_FACETS_ID")).toString());
					facetsIds.add(facetId);
				}
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}				

		return facetsIds;
	}
	
	@Override
	public String getTimeframe(String period, String year,String timeframeIp) {

		String timeframe = "";
		
		if (period.equalsIgnoreCase("N")) {
			timeframe = year;
		} 
		else if (timeframeIp.equalsIgnoreCase("Q")) {
			
			if (period.equalsIgnoreCase("Q1")) {
				timeframe = "1st Quarter of "+ year ;
			}
			if (period.equalsIgnoreCase("Q2")) {
				timeframe = "2nd Quarter of "+ year ;
			}
			if (period.equalsIgnoreCase("Q3")) {
				timeframe = "3rd Quarter of "+ year ;
			}
			if (period.equalsIgnoreCase("Q4")) {
				timeframe = "4th Quarter of "+ year ;
			}
		} else if (timeframeIp
				.equalsIgnoreCase("S")) {
			if (period.equalsIgnoreCase("S1")) {
				timeframe = "1st Half of "+ year;
			}
			if (period.equalsIgnoreCase("S2")) {
				timeframe = "2nd Half of "+ year;
			}
		} else {
			timeframe = period.substring(1) + " " + (year);
		}
		
		logger.debug("bfr return TF "+timeframe);
		return timeframe;
	}

	@Override
	public String getType(String type,String subType) {
		
		String typeName = "";
		
		if(subType.equalsIgnoreCase("P")){
			if(type.equalsIgnoreCase("1")){
				typeName = "Post-Pay";
			}
			if(type.equalsIgnoreCase("2")){
				typeName = "Pre-Pay";
			}
			if(type.equalsIgnoreCase("0")){
				typeName = "Pre-Pay and Post-Pay";
			}
		}
		if(subType.equalsIgnoreCase("M")){
			if(type.equalsIgnoreCase("Y")){
				typeName = "Mock";
			}
			if(type.equalsIgnoreCase("N")){
				typeName = "Non-Mock";
			}
			if(type.equalsIgnoreCase("ALL")){
				typeName = "Mock and Non-Mock";
			}
		}
		if(subType.equalsIgnoreCase("E")){
			if(type.equalsIgnoreCase("Y")){
				typeName = "E2E";
			}
			if(type.equalsIgnoreCase("N")){
				typeName = "Non-E2E";
			}
			if(type.equalsIgnoreCase("ALL")){
				typeName = "E2E and Non-E2E";
			}
		}
		
		return typeName;
	}

	@Override
	public String getTitle(String subject) {
		String title= "" ;
		
		if(subject.equalsIgnoreCase("ASSOCIATE")){
			title = "Associate";
		}
		if(subject.equalsIgnoreCase("SUPERVISOR")){
			title = "Supervisor";
		}
		if(subject.equalsIgnoreCase("DIRECTOR")){
			title = "Director";
		}
		if(subject.equalsIgnoreCase("MANAGER")){
			title = "Manager";
		}
		if(subject.equalsIgnoreCase("DIVISION")){
			title = "Division";
		}
		if(subject.equalsIgnoreCase("LOB")){
			title = "LOB";
		}
		if(subject.equalsIgnoreCase("PPG")){
			title = "Primary Perf. Group";
		}
		if(subject.equalsIgnoreCase("SPG")){
			title = "Secondary Perf. Group";
		}
		
		
		return title;
	}



}
