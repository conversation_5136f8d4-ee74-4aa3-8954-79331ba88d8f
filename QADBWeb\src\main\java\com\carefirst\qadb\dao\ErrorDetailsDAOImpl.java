package com.carefirst.qadb.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.sql.DataSource;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import com.carefirst.audit.model.Associate;
import com.carefirst.audit.model.AuditDetails;
import com.carefirst.audit.model.ErrorDetails;
import com.carefirst.qadb.constant.QADBConstants;
import com.carefirst.qadb.controller.AuditController;


public class ErrorDetailsDAOImpl implements ErrorDetailsDAO{
	
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);
	
	@Autowired
	DataSource dataSource;   
	
	
	public List<ErrorDetails> getErrorCodes(String pageType,String userGrp) throws SQLException {
		  
		  String sql = QADBConstants.AUDIT_ERROR_CODES_LIST1 +userGrp+ QADBConstants.AUDIT_ERROR_CODES_LIST2;
		  
		  if(pageType.equalsIgnoreCase("edit")){
			   sql = QADBConstants.AUDIT_EDIT_ERROR_CODES_LIST1 +userGrp+ QADBConstants.AUDIT_EDIT_ERROR_CODES_LIST2;
		  }
		 
		  List<ErrorDetails> errDet = new ArrayList<ErrorDetails>();

		  	Connection conn =  null ;
			CallableStatement callableStatment = null;
			try {
				 conn = dataSource.getConnection();
				 callableStatment = conn.prepareCall(sql);
				 
				 ResultSet rs = callableStatment.executeQuery();
				 
				 while (rs.next()) {
						ErrorDetails errDets = new ErrorDetails();
						errDets.setErrorCodeId((rs.getString("ERROR_ID")).toString());
						errDets.setErrorCode((String) (rs.getString("ERROR_DESC")));
						errDets.setErrorActive((String) (rs.getString("ERROR_ACTIVE_FLG")));
						errDet.add(errDets);
					}
				 rs.close();
			} catch (SQLException e) {
				logger.debug("Exception generated "+e.getMessage());
			} finally{
				callableStatment.close();
				conn.close();
			}				  
			return errDet;
	}

	
	public List<ErrorDetails> getSpeciality(String userGrp) throws SQLException {
		  
		  String sql = QADBConstants.AUDIT_SPECIALTY_LIST1 +userGrp+ QADBConstants.AUDIT_SPECIALTY_LIST2;   
		  
		  List<ErrorDetails> errDet = new ArrayList<ErrorDetails>();

		  	Connection conn =  null ;
			CallableStatement callableStatment = null;
			try {
				 conn = dataSource.getConnection();
				 callableStatment = conn.prepareCall(sql);
				 
				 ResultSet rs = callableStatment.executeQuery();
				 
				 while (rs.next()) {
						ErrorDetails errDets = new ErrorDetails();
						errDets.setSpcialityId((rs.getString("SPECIALTY_ID")).toString());
						errDets.setSpciality((String) (rs.getString("SPECIALTY_NAME")));
						errDet.add(errDets);
					}
				 rs.close();
			} catch (SQLException e) {
				logger.debug("Exception generated "+e.getMessage());
			} finally{
				callableStatment.close();
				conn.close();
			}				
			return errDet;
	}

	
	public List<ErrorDetails> getRootCause(String userGrp) throws SQLException {
		 
		String sql = QADBConstants.AUDIT_ROOT_CAUSE_LIST1 +userGrp+ QADBConstants.AUDIT_ROOT_CAUSE_LIST2;   
		
		List<ErrorDetails> errDet = new ArrayList<ErrorDetails>();

		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
					ErrorDetails errDets = new ErrorDetails();
					errDets.setRootCauseId((rs.getString("ROOT_CAUSE_ID")).toString());
					errDets.setRootCause((String) (rs.getString("ROOT_CAUSE_NAME")));
					errDet.add(errDets);
				}	
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}	
		return errDet;
	}


	// dynamic field based on root cause
	
	public List<ErrorDetails> getErrorTypes(String rootCauseId) throws SQLException {

		String sql = QADBConstants.AUDIT_ERROR_TYPES_LIST1 +rootCauseId ;
		
		List<ErrorDetails> errDet = new ArrayList<ErrorDetails>();

		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
					ErrorDetails errDets = new ErrorDetails();
					errDets.setErrorTypeId((rs.getString("ERROR_TYPE_ID")).toString());
					errDets.setErrorTypes((String) (rs.getString("ERROR_TYPE_DESC")));
					errDet.add(errDets);
				}
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}			
		return errDet;
	}


	@Override
	public List<ErrorDetails> getErrorInfo(String auditId) throws SQLException {
		
		int id = Integer.parseInt(auditId);
		
		String sql = QADBConstants.AUDIT_ERROR_INFO_LIST+id;   
		  
		List<ErrorDetails> errDet = new ArrayList<ErrorDetails>();

		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
					ErrorDetails errDets = new ErrorDetails();
					if(null!=(rs.getString("ERROR_ID"))){
						errDets.setErrorCodeId((rs.getString("ERROR_ID")).toString());
					}
					if(null!=(rs.getString("MONETARY_FLG"))){
						errDets.setMonetary((rs.getString("MONETARY_FLG")).toString());
					}
					if(null!=(rs.getString("PROCEDURAL_FLG"))){
						errDets.setProcedural((rs.getString("PROCEDURAL_FLG")).toString());
					}
					if(null!=(rs.getString("SPECIALTY_ID"))){
						errDets.setSpcialityId((rs.getString("SPECIALTY_ID")).toString());
					}
					if(null!=(rs.getString("ROOT_CAUSE_ID"))){
						errDets.setRootCauseId((rs.getString("ROOT_CAUSE_ID")).toString());
					}
					if(null != (rs.getString("EDIT_CODE"))){
						errDets.setEditCodeId((rs.getString("EDIT_CODE")).toString());
					}
					logger.debug("errorRecev"+(rs.getString("ERROR_ID")).toString());
					errDet.add(errDets);
				}	
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}		

			return errDet;
	}


	@Override
	public List<ErrorDetails> getAllErrorTypes() throws SQLException {
		String sql = QADBConstants.AUDIT_ALL_ERROR_TYPES_LIST ;
		
		List<ErrorDetails> errDet = new ArrayList<ErrorDetails>();

		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
					ErrorDetails errDets = new ErrorDetails();
					errDets.setErrorTypes((String) (rs.getString("ERROR_TYPE_DESC")));
					errDet.add(errDets);
				}
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}			
		return errDet;
	}  
}
