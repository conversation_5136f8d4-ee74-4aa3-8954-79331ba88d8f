package com.carefirst.audit.model;

public class ScoresReport {

	private String timeframe;
	private String e2e;
	
	private String associateName;
	private String empId;
	private String auditType;
	private String subType;
	private String processedDate;
	private String sample;
	private String processedColHeader;
	
	//In-sample
	private String isClaims;
	private String isPaid;
	private String isOverPaid;
	private String isUnderPaid;
	private String isProcedural;
	private String isMonetary;
	private String isTotal;
	private String isProceduralAccuracy;
	private String isDollarFreq;
	private String isDollarAccuracy;
	private String isCompositeQuality;
	
	private String isRowCount;
	private String isTotalClaims;
	private String isTotalPaid;
	private String isTotalOverPaid;
	private String isTotalUnderPaid;
	private String isTotalProcedural;
	private String isTotalMonetary;
	private String isSumTotal;
	private String isTotalProceduralAccuracy;
	private String isTotalDollarFreq;
	private String isTotalDollarAccuracy;
	private String isTotalCompositeQuality;
	
	//Out-Sample
	private String oosClaims;
	private String oosPaid;
	private String oosOverPaid;
	private String oosUnderPaid;
	private String oosProcedural;
	private String oosMonetary;
	private String oosTotal;
	private String oosProceduralAccuracy;
	private String oosDollarFreq;
	private String oosDollarAccuracy;
	private String oosCompositeQuality;
	
	private String oosTotalClaims;
	private String oosTotalPaid;
	private String oosTotalOverPaid;
	private String oosTotalUnderPaid;
	private String oosTotalProcedural;
	private String oosTotalMonetary;
	private String oosSumTotal;
	private String oosTotalProceduralAccuracy;
	private String oosTotalDollarFreq;
	private String oosTotalDollarAccuracy;
	private String oosTotalCompositeQuality;
	
	//Division
	private String divisionName;
	private String examAudits;
	private String claimsAudit;
	private String payRatio;
	private String dollarAudits;
	private String overPay;
	private String underPay;
	private String totalDollars;
	private String dollarAccuracy;
	private String errorClaims;
	private String errorFreq;
	private String monErrClaim;
	private String dollarFreq;
	private String procErrClaim;
	private String proAccuaracy;
	
	private String successMsg;
	private String sucessCode;
	
	private String pgType;
	
	
	public String getPgType() {
		return pgType;
	}
	public void setPgType(String pgType) {
		this.pgType = pgType;
	}
	public String getDivisionName() {
		return divisionName;
	}
	public void setDivisionName(String divisionName) {
		this.divisionName = divisionName;
	}
	public String getExamAudits() {
		return examAudits;
	}
	public void setExamAudits(String examAudits) {
		this.examAudits = examAudits;
	}
	public String getClaimsAudit() {
		return claimsAudit;
	}
	public void setClaimsAudit(String claimsAudit) {
		this.claimsAudit = claimsAudit;
	}
	public String getPayRatio() {
		return payRatio;
	}
	public void setPayRatio(String payRatio) {
		this.payRatio = payRatio;
	}
	public String getDollarAudits() {
		return dollarAudits;
	}
	public void setDollarAudits(String dollarAudits) {
		this.dollarAudits = dollarAudits;
	}
	public String getOverPay() {
		return overPay;
	}
	public void setOverPay(String overPay) {
		this.overPay = overPay;
	}
	public String getUnderPay() {
		return underPay;
	}
	public void setUnderPay(String underPay) {
		this.underPay = underPay;
	}
	public String getTotalDollars() {
		return totalDollars;
	}
	public void setTotalDollars(String totalDollars) {
		this.totalDollars = totalDollars;
	}
	public String getDollarAccuracy() {
		return dollarAccuracy;
	}
	public void setDollarAccuracy(String dollarAccuracy) {
		this.dollarAccuracy = dollarAccuracy;
	}
	public String getErrorClaims() {
		return errorClaims;
	}
	public void setErrorClaims(String errorClaims) {
		this.errorClaims = errorClaims;
	}
	public String getErrorFreq() {
		return errorFreq;
	}
	public void setErrorFreq(String errorFreq) {
		this.errorFreq = errorFreq;
	}
	public String getMonErrClaim() {
		return monErrClaim;
	}
	public void setMonErrClaim(String monErrClaim) {
		this.monErrClaim = monErrClaim;
	}
	public String getDollarFreq() {
		return dollarFreq;
	}
	public void setDollarFreq(String dollarFreq) {
		this.dollarFreq = dollarFreq;
	}
	public String getProcErrClaim() {
		return procErrClaim;
	}
	public void setProcErrClaim(String procErrClaim) {
		this.procErrClaim = procErrClaim;
	}
	public String getProAccuaracy() {
		return proAccuaracy;
	}
	public void setProAccuaracy(String proAccuaracy) {
		this.proAccuaracy = proAccuaracy;
	}
	public String getIsRowCount() {
		return isRowCount;
	}
	public void setIsRowCount(String isRowCount) {
		this.isRowCount = isRowCount;
	}
	public String getProcessedColHeader() {
		return processedColHeader;
	}
	public void setProcessedColHeader(String processedColHeader) {
		this.processedColHeader = processedColHeader;
	}
	public String getTimeframe() {
		return timeframe;
	}
	public void setTimeframe(String timeframe) {
		this.timeframe = timeframe;
	}
	public String getE2e() {
		return e2e;
	}
	public void setE2e(String e2e) {
		this.e2e = e2e;
	}
	public String getAssociateName() {
		return associateName;
	}
	public void setAssociateName(String associateName) {
		this.associateName = associateName;
	}
	public String getEmpId() {
		return empId;
	}
	public void setEmpId(String empId) {
		this.empId = empId;
	}
	public String getAuditType() {
		return auditType;
	}
	public void setAuditType(String auditType) {
		this.auditType = auditType;
	}
	public String getSubType() {
		return subType;
	}
	public void setSubType(String subType) {
		this.subType = subType;
	}
	public String getProcessedDate() {
		return processedDate;
	}
	public void setProcessedDate(String processedDate) {
		this.processedDate = processedDate;
	}
	public String getSample() {
		return sample;
	}
	public void setSample(String sample) {
		this.sample = sample;
	}
	public String getIsClaims() {
		return isClaims;
	}
	public void setIsClaims(String isClaims) {
		this.isClaims = isClaims;
	}
	public String getIsPaid() {
		return isPaid;
	}
	public void setIsPaid(String isPaid) {
		this.isPaid = isPaid;
	}
	public String getIsOverPaid() {
		return isOverPaid;
	}
	public void setIsOverPaid(String isOverPaid) {
		this.isOverPaid = isOverPaid;
	}
	public String getIsUnderPaid() {
		return isUnderPaid;
	}
	public void setIsUnderPaid(String isUnderPaid) {
		this.isUnderPaid = isUnderPaid;
	}
	public String getIsProcedural() {
		return isProcedural;
	}
	public void setIsProcedural(String isProcedural) {
		this.isProcedural = isProcedural;
	}
	public String getIsMonetary() {
		return isMonetary;
	}
	public void setIsMonetary(String isMonetary) {
		this.isMonetary = isMonetary;
	}
	public String getIsTotal() {
		return isTotal;
	}
	public void setIsTotal(String isTotal) {
		this.isTotal = isTotal;
	}
	public String getIsProceduralAccuracy() {
		return isProceduralAccuracy;
	}
	public void setIsProceduralAccuracy(String isProceduralAccuracy) {
		this.isProceduralAccuracy = isProceduralAccuracy;
	}
	public String getIsDollarFreq() {
		return isDollarFreq;
	}
	public void setIsDollarFreq(String isDollarFreq) {
		this.isDollarFreq = isDollarFreq;
	}
	public String getIsDollarAccuracy() {
		return isDollarAccuracy;
	}
	public void setIsDollarAccuracy(String isDollarAccuracy) {
		this.isDollarAccuracy = isDollarAccuracy;
	}
	public String getIsCompositeQuality() {
		return isCompositeQuality;
	}
	public void setIsCompositeQuality(String isCompositeQuality) {
		this.isCompositeQuality = isCompositeQuality;
	}
	public String getIsTotalClaims() {
		return isTotalClaims;
	}
	public void setIsTotalClaims(String isTotalClaims) {
		this.isTotalClaims = isTotalClaims;
	}
	public String getIsTotalPaid() {
		return isTotalPaid;
	}
	public void setIsTotalPaid(String isTotalPaid) {
		this.isTotalPaid = isTotalPaid;
	}
	public String getIsTotalOverPaid() {
		return isTotalOverPaid;
	}
	public void setIsTotalOverPaid(String isTotalOverPaid) {
		this.isTotalOverPaid = isTotalOverPaid;
	}
	public String getIsTotalUnderPaid() {
		return isTotalUnderPaid;
	}
	public void setIsTotalUnderPaid(String isTotalUnderPaid) {
		this.isTotalUnderPaid = isTotalUnderPaid;
	}
	public String getIsTotalProcedural() {
		return isTotalProcedural;
	}
	public void setIsTotalProcedural(String isTotalProcedural) {
		this.isTotalProcedural = isTotalProcedural;
	}
	public String getIsTotalMonetary() {
		return isTotalMonetary;
	}
	public void setIsTotalMonetary(String isTotalMonetary) {
		this.isTotalMonetary = isTotalMonetary;
	}
	public String getIsSumTotal() {
		return isSumTotal;
	}
	public void setIsSumTotal(String isSumTotal) {
		this.isSumTotal = isSumTotal;
	}
	public String getIsTotalProceduralAccuracy() {
		return isTotalProceduralAccuracy;
	}
	public void setIsTotalProceduralAccuracy(String isTotalProceduralAccuracy) {
		this.isTotalProceduralAccuracy = isTotalProceduralAccuracy;
	}
	public String getIsTotalDollarFreq() {
		return isTotalDollarFreq;
	}
	public void setIsTotalDollarFreq(String isTotalDollarFreq) {
		this.isTotalDollarFreq = isTotalDollarFreq;
	}
	public String getIsTotalDollarAccuracy() {
		return isTotalDollarAccuracy;
	}
	public void setIsTotalDollarAccuracy(String isTotalDollarAccuracy) {
		this.isTotalDollarAccuracy = isTotalDollarAccuracy;
	}
	public String getIsTotalCompositeQuality() {
		return isTotalCompositeQuality;
	}
	public void setIsTotalCompositeQuality(String isTotalCompositeQuality) {
		this.isTotalCompositeQuality = isTotalCompositeQuality;
	}
	public String getOosClaims() {
		return oosClaims;
	}
	public void setOosClaims(String oosClaims) {
		this.oosClaims = oosClaims;
	}
	public String getOosPaid() {
		return oosPaid;
	}
	public void setOosPaid(String oosPaid) {
		this.oosPaid = oosPaid;
	}
	public String getOosOverPaid() {
		return oosOverPaid;
	}
	public void setOosOverPaid(String oosOverPaid) {
		this.oosOverPaid = oosOverPaid;
	}
	public String getOosUnderPaid() {
		return oosUnderPaid;
	}
	public void setOosUnderPaid(String oosUnderPaid) {
		this.oosUnderPaid = oosUnderPaid;
	}
	public String getOosProcedural() {
		return oosProcedural;
	}
	public void setOosProcedural(String oosProcedural) {
		this.oosProcedural = oosProcedural;
	}
	public String getOosMonetary() {
		return oosMonetary;
	}
	public void setOosMonetary(String oosMonetary) {
		this.oosMonetary = oosMonetary;
	}
	public String getOosTotal() {
		return oosTotal;
	}
	public void setOosTotal(String oosTotal) {
		this.oosTotal = oosTotal;
	}
	public String getOosProceduralAccuracy() {
		return oosProceduralAccuracy;
	}
	public void setOosProceduralAccuracy(String oosProceduralAccuracy) {
		this.oosProceduralAccuracy = oosProceduralAccuracy;
	}
	public String getOosDollarFreq() {
		return oosDollarFreq;
	}
	public void setOosDollarFreq(String oosDollarFreq) {
		this.oosDollarFreq = oosDollarFreq;
	}
	public String getOosDollarAccuracy() {
		return oosDollarAccuracy;
	}
	public void setOosDollarAccuracy(String oosDollarAccuracy) {
		this.oosDollarAccuracy = oosDollarAccuracy;
	}
	public String getOosCompositeQuality() {
		return oosCompositeQuality;
	}
	public void setOosCompositeQuality(String oosCompositeQuality) {
		this.oosCompositeQuality = oosCompositeQuality;
	}
	public String getOosTotalClaims() {
		return oosTotalClaims;
	}
	public void setOosTotalClaims(String oosTotalClaims) {
		this.oosTotalClaims = oosTotalClaims;
	}
	public String getOosTotalPaid() {
		return oosTotalPaid;
	}
	public void setOosTotalPaid(String oosTotalPaid) {
		this.oosTotalPaid = oosTotalPaid;
	}
	public String getOosTotalOverPaid() {
		return oosTotalOverPaid;
	}
	public void setOosTotalOverPaid(String oosTotalOverPaid) {
		this.oosTotalOverPaid = oosTotalOverPaid;
	}
	public String getOosTotalUnderPaid() {
		return oosTotalUnderPaid;
	}
	public void setOosTotalUnderPaid(String oosTotalUnderPaid) {
		this.oosTotalUnderPaid = oosTotalUnderPaid;
	}
	public String getOosTotalProcedural() {
		return oosTotalProcedural;
	}
	public void setOosTotalProcedural(String oosTotalProcedural) {
		this.oosTotalProcedural = oosTotalProcedural;
	}
	public String getOosTotalMonetary() {
		return oosTotalMonetary;
	}
	public void setOosTotalMonetary(String oosTotalMonetary) {
		this.oosTotalMonetary = oosTotalMonetary;
	}
	public String getOosSumTotal() {
		return oosSumTotal;
	}
	public void setOosSumTotal(String oosSumTotal) {
		this.oosSumTotal = oosSumTotal;
	}
	public String getOosTotalProceduralAccuracy() {
		return oosTotalProceduralAccuracy;
	}
	public void setOosTotalProceduralAccuracy(String oosTotalProceduralAccuracy) {
		this.oosTotalProceduralAccuracy = oosTotalProceduralAccuracy;
	}
	public String getOosTotalDollarFreq() {
		return oosTotalDollarFreq;
	}
	public void setOosTotalDollarFreq(String oosTotalDollarFreq) {
		this.oosTotalDollarFreq = oosTotalDollarFreq;
	}
	public String getOosTotalDollarAccuracy() {
		return oosTotalDollarAccuracy;
	}
	public void setOosTotalDollarAccuracy(String oosTotalDollarAccuracy) {
		this.oosTotalDollarAccuracy = oosTotalDollarAccuracy;
	}
	public String getOosTotalCompositeQuality() {
		return oosTotalCompositeQuality;
	}
	public void setOosTotalCompositeQuality(String oosTotalCompositeQuality) {
		this.oosTotalCompositeQuality = oosTotalCompositeQuality;
	}
	public String getSuccessMsg() {
		return successMsg;
	}
	public void setSuccessMsg(String successMsg) {
		this.successMsg = successMsg;
	}
	public String getSucessCode() {
		return sucessCode;
	}
	public void setSucessCode(String sucessCode) {
		this.sucessCode = sucessCode;
	}
	
	
	
}
