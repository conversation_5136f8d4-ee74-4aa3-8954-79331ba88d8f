<!DOCTYPE HTML><%@page language="java"
	contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>

<html>
<head>
</head> 

<!-- <script type="text/javascript" src="webResources/js/jquery.loadmask.js"></script> -->
<script type="text/javascript" src="webResources/js/jquery.min.js"></script>
<script type="text/javascript" src="webResources/js/jquery-ui.js"></script>
<script type="text/javascript" src="webResources/js/qadb.js"></script>
<script src="webResources/js/jquery/jquery.dataTables.js"></script>
<link href="webResources/css/jquery.loadmask.css" rel="stylesheet">
<link href="webResources/css/jquery-ui.css" rel="stylesheet">
<style> 

#dollar {
  position: absolute;
  display: block;
  transform: translate(0, -50%);
  top: 44%;
  pointer-events: none;
  width: 25px;
  text-align: center;
  font-style: normal;
  padding-bottom: 2px;
}

#dollar2 {
  position: absolute;
  display: block;
  transform: translate(0, -50%);
  top: 44%;
  pointer-events: none;
  width: 25px;
  text-align: center;
  font-style: normal;
  padding-bottom: 0px;
}
/*
.input-control.text  > input {
  padding-left: 5px !important;
} */
div.success {
	padding-left: 5px;
	padding-top: 5px;
	height: 30px;
	background: #73AD21;
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	color: white;
}

div.errorInfo {
    padding: 5px 5px 7px 5px ;
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	width:350px;
	background-color:#CE352C;
	color:white;
}

#error {
	padding: 5px 5px 5px 5px;
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	width: 350px;
	font-size: 11pt;
	background-color: #CE352C;
	color: white;
}

#popupPIouter {
	width: 100%;
	height: 100%;
	opacity: 0.5;
	top: 0;
	left: 0;
	display: none;
	position: fixed;
	background-color: #F8F8F8;
	overflow: auto
}

#popupClaimRecords {
	width: 100%;
	height: 100%;
	opacity: 0.5;
	top: 0;
	left: 0;
	display: none;
	position: fixed;
	background-color: #F8F8F8;
	overflow: auto
}

div#popupClaims {
	position: fixed;
	border: 1px solid #383838;
	background-color: #FFFFFF;
	left: 50%;
	top: 17%;
	margin-left: -202px;
	display: none;
	font-family: 'Raleway', sans-serif
}

#popupClaimRecordsD {
	width: 100%;
	height: 100%;
	opacity: 0.5;
	top: 0;
	left: 0;
	display: none;
	position: fixed;
	background-color: #F8F8F8;
	overflow: auto
}

div#popupClaimsD {
	position: fixed;
	border: 1px solid #383838;
	background-color: #FFFFFF;
	left: 55%;
	top: 40%;
	margin-left: -202px;
	display: none;
	font-family: 'Raleway', sans-serif
}

/*Aud count popUp  */
#popupAuditCountD {
	width: 100%;
	height: 100%;
	opacity: 0.5;
	top: 0;
	left: 0;
	display: none;
	position: fixed;
	background-color: #F8F8F8;
	overflow: auto
}

div#popupAuditCount {
	position: fixed;
	border: 1px solid #383838;
	background-color: #FFFFFF;
	left: 55%;
	top: 25%;
	margin-left: -262px;
	display: none;
	font-family: 'Raleway', sans-serif
}

/*DCN check popUp  */
#popupDcnCheckD, #highDollarOverlay {
	width: 100%;
	height: 100%;
	opacity: 0.5;
	top: 0;
	left: 0;
	display: none;
	position: fixed;
	background-color: #F8F8F8;
	overflow: auto
}

div#popupDcnD {
	position: fixed;
	border: 1px solid #383838;
	background-color: #FFFFFF;
	left: 45%;
	top: 10%;
	margin-left: -262px;
	display: none;
	font-family: 'Raleway', sans-serif
}

/* Col */
/* Collapse -Expand */
.headerA {
	background: url('webResources/images/Forms/Icn_Accordion_Collapse.png')
		no-repeat;
	background-position: right 0px;
	cursor: pointer;
}

.collapsed .headerA {
	background-image:
		url('webResources/images/Forms/Icn_Accordion_Expand.png');
}

.contentA {
	height: auto;
	min-height: 100px;
	overflow: hidden;
	transition: all 0.3s linear;
	-webkit-transition: all 0.3s linear;
	-moz-transition: all 0.3s linear;
	-ms-transition: all 0.3s linear;
	-o-transition: all 0.3s linear;
	background-color: #fafafa;
}

.collapsed .contentA {
	min-height: 0px;
	height: 0px;
}

.correct {
	display: none;
	color : red;
} 

div#highDollarPopup {
	position: fixed;
	border: 1px solid #383838;
	background-color: #FFFFFF;
	left: 20%;
	top: 10%;
	right: 25%;
	display: none;
	font-family: 'Raleway', sans-serif;
	height: 80%;
	overflow-y: scroll;
	overflow-x: hidden;
}

#highDollarTable td {
	width: 50%;
	vertical-align: middle;
}

.highDollarTables td {
	vertical-align: middle;
}
</style>

<script type="text/javascript">
	function collapseExpand(divId) {
		if (document.getElementById(divId).style.display == 'none') {
			document.getElementById(divId).style.display = 'inline';
		} else {
			document.getElementById(divId).style.display = 'none';
		}
	}

	/* success msg */
	var a = 1;

	$(document).ready(function() {
		var amt=$("#paids").val();
		
		//console.log("Total Paid amount "+amt);
		
		if (amt>=40000.00){
			//alert("FYI greater");
			$("#fyin").removeAttr("disabled");
			$("#fychk").removeAttr("checked");
			$("#fychky").attr("checked","checked");
		}
		if (a == 1) {
			$('#success').delay(800).fadeIn(400);

		}
		$("button[type='reset']").click(function() {
			this.form.reset(); // reset the form     
			$("input[type='radio']").removeAttr("disabled");
			return false; // prevent reset button from resetting again
		});
	});
	
	
	
	/* success msg */
	/* $(document).ready(function() {
		$(document).keyup(function(e) {
			if (e.keyCode == 13) {
				$("#searchClaim").trigger('click');
			}
		})
	});
	$('#audit').keypress(function(e) {
		if (e.which == 13) { // Checks for the enter key
			e.preventDefault(); // Stops IE from triggering the button to be clicked
		}
	}); */
</script>

<body>

	<%
		String formAction = "";
	%>

	<c:if test="${edit != null}">
		<%
			formAction = "editAuditError";
		%>

	</c:if>
	<c:if test="${edit == null}">
		<%
			formAction = "error";
		%>

	</c:if>

	${eRS.dcn}

	<form:form style="width:700px" id="auditForm" name="auditForm"
		onsubmit="return validateForm()" method="POST" commandName="auditForm"
		action="<%=formAction%>">
		<div id="highContainer">
			<input type="hidden" id="claimAdjFlag" name="claimAdjFlag" value="${editAuditDetails.claimAdjFlag}${claimAdjFlag}" /> 
			<input type="hidden" id="serviceAdjFlag" name="serviceAdjFlag" value="${editAuditDetails.serviceAdjFlag}${serviceAdjFlag}" /> 
			<input type="hidden" id="QAName" name="QAName" value="${editAuditDetails.QAName}${QAName}" /> 
			<input type="hidden" id="patientName" name="patientName" value="${editAuditDetails.patientName}${patientName}" /> 
			<input type="hidden" id="serviceDates" name="serviceDates" value="${editAuditDetails.serviceDates}${serviceDates}" /> 
			<input type="hidden" id="typeOfService" name="typeOfService" value="${editAuditDetails.typeOfService}${typeOfService}" /> 
			<input type="hidden" id="diagnosis" name="diagnosis" value="${editAuditDetails.diagnosis}${diagnosis}" /> 
			<input type="hidden" id="surgeryDOS" name="surgeryDOS" value="${editAuditDetails.surgeryDOS}${surgeryDOS}" />
			<input type="hidden" id="surgery" name="surgery" value="${editAuditDetails.surgery}${surgery}" />
			<input type="hidden" id="fileReferenced" name="fileReferenced" value="${editAuditDetails.fileReferenced}${fileRef}" />
			<input type="hidden" id="interestPaid" name="interestPaid" value="${editAuditDetails.interestPaid}${interestPaid}" />
			<input type="hidden" id="providerName" name="providerName" value="${editAuditDetails.providerName}${providerName}" />
			<input type="hidden" id="providerNumber" name="providerNumber" value="${editAuditDetails.providerNumber}${providerNumber}" />
			<input type="hidden" id="payee" name="payee" value="${editAuditDetails.payee}${payee}" />
			<input type="hidden" id="notes" name="notes" value="${editAuditDetails.notes}${notes}" />
			<input type="hidden" id="audSignDate" name="audSignDate" value="${editAuditDetails.audSignDate}${audSignDate}" />
			<input type="hidden" id="vpSignDate" name="vpSignDate" value="${editAuditDetails.vpSignDate}${vpSignDate}" />
			<input type="hidden" id="forwrdToDate" name="forwrdToDate" value="${editAuditDetails.forwrdToDate}${forwrdToDate}" />
			<input type="hidden" id="rcvedFrmDate" name="rcvedFrmDate" value="${editAuditDetails.rcvedFrmDate}${rcvedFrmDate}" />
			<input type="hidden" id="releasedByDate" name="releasedByDate" value="${editAuditDetails.releasedByDate}${releasedByDate}" />
		</div>
		<div id="content-container" style="padding-left: 40px">

			<input type="hidden" id="numberOfUsers" name="numberOfUsers"
				value="${numberOfUsers }" /> <input type="hidden" id="examinerName"
				name="examinerName"
				value="${claimDetails.claimLevel.careFirstExaminerUserID}" /> <input
				type="hidden" id="dupFlag" name="dupFlag" value="${dupFlag}" /> <input
				type="hidden" id="multicheckFlag" name="multicheckFlag" />

			<div style="padding: 10px 0px 0px 7px;">
				<c:choose>
					<c:when test="${edit != null}">
						<span class="bread1"><spring:message
								code="audit.new.bread1" /> > </span>
						<span class="bread1"> <spring:message
								code="audit.edit.bread1" /> >
						</span>
						<span class="bread2">${eDCN} </span>
					</c:when>
					<c:otherwise>
						<span class="bread1"><spring:message
								code="audit.new.bread1" /> > </span>
						<span class="bread2"><spring:message
								code="audit.new.leftNav.heading" /></span>
					</c:otherwise>
				</c:choose>
			</div>

			<div id="audit" style="width: 750px">
				<div class="containerA" style="padding-left:7px;padding-top: 7px;">
					<table width="100%">
						<tr style="height: 20px; border-bottom-color: gray;">
							<c:choose>
								<c:when test="${edit != null}">
									<td width="40%" style="padding-left: 0px;"><span
										style="font-size: 20px"><spring:message
												code="audit.edit.header" /> ${eDCN} </span></td>
									<td width="60%" style="text-align: right;"><c:set
											var="userRole">
											<%=request.getHeader("iv-groups")%>
										</c:set> <%-- <c:set var="userRole" > "Contractor","PeopleSoft_ELM_User","onestop_users","null" </c:set> --%>
										<c:if
											test="${!((fn:contains(userRole, 'qadb-samd-readonly_user'))||(fn:contains(userRole, 'qadb-cd-readonly_user'))||(fn:contains(userRole, 'null')))}">
											<button title="Cancel" type="reset"
												style="background-color: transparent; border-color: transparent; size: 10px;">
												<img src="webResources/images/Actions/Icn_Cancel.png">
											</button>
											<a onclick="divDelA_show();">
												<button title="Delete" type="button"
													style="background-color: transparent; border-color: transparent; size: 10px;">
													<img alt="Delete Audit"
														src="webResources/images/Actions/Icn_Delete.png">
												</button>
											</a>
											<button title="Save" type="submit"
												style="background-color: transparent; border-color: transparent; size: 10px;">
												<img src="webResources/images/Actions/Icn_Save.png">
											</button>
										</c:if> <a onclick="divCount_show();">
											<button title="Click to view total Audit count" type="button"
												style="background-color: transparent; border-color: transparent; size: 10px;">
												<img alt="Audit Count"
													src="webResources/images/Actions/Icn_Snapshot.png">
											</button>
									</a></td>
								</c:when>

								<c:otherwise>
									<td width="30%" style="padding-left: 0px;"><span
										style="font-size: 20px"><spring:message
												code="audit.new.leftNav.heading"></spring:message> </span></td>
									<td width="70%" style="text-align: right;"><c:if
											test="${audCountRS.processDate!=null}">
											<a href="AuditAssesmentReport">
												<button title="Click for Audit Assesment Report"
													type="button"
													style="background-color: transparent; border-color: transparent; size: 10px;"
													onclick="window.location.href='/QADBWeb/AuditAssesmentReport'">
													<img alt="Audit Count"
														src="webResources/images/Actions/Icn_Preview.png">
												</button>
											</a>
										</c:if> <c:if test="${audCountRS.processDate!=null}">
											<a onclick="divCount_show();">
												<button title="Click to view total Audit count"
													type="button"
													style="background-color: transparent; border-color: transparent; size: 10px;">
													<img alt="Audit Count"
														src="webResources/images/Actions/Icn_Snapshot.png">
												</button>
											</a>
										</c:if> <%-- <c:if test="${assoDetails.associateName != null}"> --%>
										<button title="Reset" type="reset"
											style="background-color: transparent; border-color: transparent; size: 10px;">
											<img src="webResources/images/Actions/Icn_Cancel.png">
										</button>
										<button title="Save" type="submit"
											style="background-color: transparent; border-color: transparent; size: 10px;">
											<img src="webResources/images/Actions/Icn_Save.png">
										</button> <%-- </c:if> --%></td>
								</c:otherwise>
							</c:choose>
						</tr>
					</table>
					<!--Success Msgs  -->
					<div id="error"></div>
					<div>
						<c:if test="${successCode != '000'}">
							<div id="divDuplicate"
								style="width: 240px;color: red;font-weight: bold;">${successMessage}</div>
						</c:if>

						<c:if test="${messageGroup != null}">
							<div id="success" class="success"
								style="width: 490px;background-color:#CE352C;">${messageGroup }</div>
						</c:if>

						<c:if
							test="${claimDetails.statusBlock.statusCode=='00' && assoDetails.associateId != null }">
							<c:if test="${assoDetails.associateName == null}">
								<div id="success" class="success"
									style="width: 400px;background-color:#CE352C;">This
									Associate is not present in Database.Please contact Admin</div>
								<input type="hidden" id="associateInfoName" name="associateInfoName" value="${assoDetails.associateName}" />
							</c:if>
						</c:if>
						<c:if test="${claimDetails.statusBlock.statusCode=='02'}">
							<div id="success" class="success"
								style="width: 490px;background-color:#CE352C;">This claim
								is not exists in FACETS database. Please enter a valid claim
								number!</div>
						</c:if>
						<c:if test="${success == 'SUCCESS'}">
							<div id="success" class="success" style="width: 330px;">Audit for DCN <c:out value="${saveDcn}" ></c:out>
								added successfully!</div>
						</c:if>
						<c:if test="${upSucess == 'SUCCESS'}">
							<div id="success" class="success" style="width: 380px;">Audit details for DCN <c:out value="${updateDcn}" ></c:out>
								 updated successfully!</div>
						</c:if>
						<c:if test="${delSucess == 'SUCCESS'}">
							<div id="success" class="success" style="width: 330px;">Audit for DCN <c:out value="${deleteDcn}" ></c:out>
								deleted successfully!</div>
						</c:if>
						<c:if test="${(fn:contains(success, 'System')) || (fn:contains(upSucess, 'System')) || (fn:contains(delSucess, 'System'))}">
							<div id="success" class="errorInfo" style="width: 300px;">System Error, please try again after some time !</div>
						</c:if>
					</div>

					<hr style="color: gray; size: 2px solid;">

					<table width="100%"
						style="border-bottom: 1 px solid gray; font-size: 14px">
						<tr style="height: 40px;">
							<td width="20%"><spring:message code="audit.new.associate" /></td>
							<td width="20%"><spring:message
									code="audit.new.associate.id" /></td>
							<td width="20%"><spring:message
									code="audit.new.associate.supervisor" /></td>
							<td width="20%"><spring:message
									code="audit.new.associate.manager" /></td>
							<td width="20%"><spring:message
									code="audit.new.associate.director" /></td>
						</tr>
						<tr style="height: 40px">
							<c:choose>
								<c:when test="${(claimDetails.statusBlock.statusCode=='00') && (edit == null)}">
									<td><c:choose>
											<c:when test="${numberOfUsers==1 }">
												<c:out value="${assoDetails.associateName}"></c:out>
												<%-- ${claimDetails.claimLevel.careFirstExaminerUserID } --%>
											</c:when>
											<c:otherwise>${assoDetails.associateName}</c:otherwise>
										</c:choose></td>
									<td><c:choose>
											<c:when test="${numberOfUsers==1 }">
												<%-- <c:out value="${claimDetails.claimLevel.careFirstExaminerName}${assoDetails.associateId}"></c:out> --%> <!-- commented for comma issue -->
												<c:out value="${assoDetails.associateId}"></c:out>
											</c:when>
											<%-- <c:when test="${(numberOfUsers !=1 ) && (claimDetails.claimLevel.careFirstExaminerUserID != null)}">
												<c:out value="${claimDetails.claimLevel.careFirstExaminerUserID}${assoDetails.associateId}"></c:out>
											</c:when> --%>
											<c:otherwise>${assoDetails.associateId}</c:otherwise>
										</c:choose></td>
									<td>${assoDetails.superVisor}</td>
									<td>${assoDetails.manager}</td>
									<td>${assoDetails.director}</td>
								</c:when>
								<c:when test="${edit != null}">
									<td>${assocEditDetails.associateName}</td>
									<input type="hidden" id="associateInfoNameEdit" name="associateInfoNameEdit" value="${assocEditDetails.associateName}" />
									<td>${assocEditDetails.facetsId}</td>
									<td>${assocEditDetails.superVisor}</td>
									<td>${assocEditDetails.manager}</td>
									<td>${assocEditDetails.director}</td>
								</c:when>
								<c:otherwise>
									<td>----------</td>
									<td>----------</td>
									<td>----------</td>
									<td>----------</td>
									<td>----------</td>
								</c:otherwise>
							</c:choose>
						</tr>
					</table>
				</div>
				
				<div class="panelContainer">
					<div id="dcnDetails" class="containerA"
						style="padding-left:7px;padding-top: 7px;">
						<div class="headerA"
							style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 18px"><spring:message
									code="audit.new.claim.details" /></span>
						</div>
						<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
							<table class="table">
								<%-- <tr>
								<td style="color:red;" height="25px" colspan="2">
									<div id="mandClaim"></div> <c:forEach
										items="${claimDetails.statusBlock.message }" var="messagedesc">
										<c:out value="${messagedesc.messageDescription }"></c:out>
									</c:forEach>
								</td>
							</tr> --%>
								<tr>
									<td><spring:message code="audit.new.claim.dcn" /><span
										style="color:red">&nbsp;*</span></td>
									<td class="input-control text">
										<div id="dcnNumberc" class="input-control text">
											<c:choose>
												<c:when test="${edit != null}">
													<form:input path="dcn" value="${eDCN}" readonly="true"
														style="border: 0px" />
												</c:when>
												<c:otherwise>
													<form:input path="dcn" value="${dcn}"
														placeholder="Claim Number" id="dcnNumber" name="dcn"
														maxlength="12" />
													<a id="searchClaim" href="#" onclick="validateDCN('N')"><img
														src="webResources/images/validate.png"> </a>
												</c:otherwise>
											</c:choose>
										</div>
									</td>
								</tr>
								<tr>
									<td><spring:message code="audit.new.claim.type" /><span
										style="color:red">&nbsp;*</span></td>
									<td>
										<div class="input-control select">
											<c:choose>
												<c:when test="${claimType != null}">
													<select id="claimType" name="claimType"
														style="width: 200px; font-size: 14px; border-color: #919191">
														<c:forEach items="${claimTypes}" var="claimTypes">
															<option value="${claimTypes.claimTypeId}"
																${claimTypes.claimTypeId == claimType ? 'selected="selected"' : ''}>${claimTypes.claimType}</option>
														</c:forEach>
													</select>
												</c:when>
												
												<c:when test="${ fn:substring(dcn,4,5)== 'B'|| fn:substring(dcn,4,5)=='Q'|| fn:substring(dcn,4,5)=='S'||  fn:substring(dcn,4,5)=='K'||
													fn:substring(dcn,4,5)=='D' ||fn:substring(dcn,4,5)=='E' ||fn:substring(dcn,4,5)== 'W'||fn:substring(dcn,4,5)== 'Y' ||fn:substring(dcn,4,5)=='1' ||fn:substring(dcn,4,5)== '4' && eDCN==null }">
													
													<select id="claimType" name="claimType"
														style="width: 200px; font-size: 14px; border-color: #919191">
														<option><spring:message
																code="audit.new.error.selectOne" /></option>
													 	<c:forEach items="${claimTypes}" var="claimTypes">
													
													 	<c:if test="${claimTypes.claimType=='OP Professional' || claimTypes.claimType=='IP Professional'  }">
															<option value="${claimTypes.claimTypeId}"
																${claimTypes.claimTypeId ==claimType ? 'selected="selected"' : '' }  >${claimTypes.claimType}</option>
													    </c:if>
													    
														</c:forEach>
													</select>
												</c:when>
												
												<c:when test="${fn:substring(dcn,4,5)== 'A'|| fn:substring(dcn,4,5)=='F'|| fn:substring(dcn,4,5)=='Z'||
													fn:substring(dcn,4,5)=='G' ||fn:substring(dcn,4,5)=='I'  ||fn:substring(dcn,4,5)=='J' ||fn:substring(dcn,4,5)== 'R'||fn:substring(dcn,4,5)== '2' ||fn:substring(dcn,4,5)=='X' ||fn:substring(dcn,4,5)== '3'  }">
													
													<select id="claimType" name="claimType"
														style="width: 200px; font-size: 14px; border-color: #919191">
														<option><spring:message
																code="audit.new.error.selectOne" /></option>
													 	<c:forEach items="${claimTypes}" var="claimTypes">
													 	<c:if test="${claimTypes.claimType=='OP Facility' || claimTypes.claimType=='IP Facility' }">
															<option value="${claimTypes.claimTypeId}"
																${claimTypes.claimTypeId ==claimType ? 'selected="selected"' : '' }  >${claimTypes.claimType}</option>
													    </c:if>														 
														</c:forEach>
													</select>
												</c:when>
												<c:when test="${fn:substring(eDCN,4,5)== 'A'|| fn:substring(eDCN,4,5)=='F'|| fn:substring(eDCN,4,5)=='Z'||
													fn:substring(eDCN,4,5)=='G' ||fn:substring(eDCN,4,5)=='I'  ||fn:substring(eDCN,4,5)=='J' ||fn:substring(eDCN,4,5)== 'R'||fn:substring(eDCN,4,5)== '2' ||fn:substring(eDCN,4,5)=='X' ||fn:substring(eDCN,4,5)== '3'  }">
													
													<select id="claimType" name="claimType"
														style="width: 200px; font-size: 14px; border-color: #919191">
														<option><spring:message
																code="audit.new.error.selectOne" /></option>
													 	<c:forEach items="${claimTypes}" var="claimTypes">
													 	<c:if test="${claimTypes.claimType=='OP Facility' || claimTypes.claimType=='IP Facility' }">
															<option value="${claimTypes.claimTypeId}"
																${claimTypes.claimTypeId ==eClaimType ? 'selected="selected"' : '' }  >${claimTypes.claimType}</option>
													    </c:if>														 
														</c:forEach>
													</select>
												</c:when>
												<c:when test="${fn:substring(eDCN,4,5)== 'B'|| fn:substring(eDCN,4,5)=='Q'|| fn:substring(eDCN,4,5)=='S'||  fn:substring(eDCN,4,5)=='K'||
													fn:substring(eDCN,4,5)=='D' ||fn:substring(eDCN,4,5)=='E' ||fn:substring(eDCN,4,5)== 'W'||fn:substring(eDCN,4,5)== 'Y' ||fn:substring(eDCN,4,5)=='1' ||fn:substring(eDCN,4,5)== '4' && eDCN!=null }">
													
													<select id="claimType" name="claimType"
														style="width: 200px; font-size: 14px; border-color: #919191">
														<option><spring:message
																code="audit.new.error.selectOne" /></option>
													 	<c:forEach items="${claimTypes}" var="claimTypes">
													
													 	<c:if test="${claimTypes.claimType=='Prof (historical only)' || claimTypes.claimType=='IP Professional' || claimTypes.claimType=='OP Professional' }">
															<option value="${claimTypes.claimTypeId}"
																${claimTypes.claimTypeId ==eClaimType ? 'selected="selected"' : '' }  >${claimTypes.claimType}</option>
													    </c:if>
													    
														</c:forEach>
													</select>
												</c:when>
												
												<c:when test="${ edit == null }">
													<select id="claimType" name="claimType"
														style="width: 200px; font-size: 14px; border-color: #919191">
														<option><spring:message
																code="audit.new.error.selectOne" /></option>
														<c:forEach items="${claimTypes}" var="claimTypes">
														<c:if test="${claimTypes.claimType!='Prof (historical only)'}">	
															<option value="${claimTypes.claimTypeId}"
																${claimTypes.claimTypeId == claimType ? 'selected="selected"' : ''}>${claimTypes.claimType}</option>
														</c:if>
														</c:forEach>
													</select>
												</c:when>
									     	 	
												<c:otherwise>
													<select id="claimType" name="claimType"
														style="width: 200px; font-size: 14px; border-color: #919191">
														 <c:forEach items="${claimTypes}" var="claimTypes">	   
															<option value="${claimTypes.claimTypeId}"
																${claimTypes.claimTypeId == eClaimType ? 'selected="selected"' : ''}>${claimTypes.claimType}</option>
														 </c:forEach>
													</select>
												</c:otherwise>
											</c:choose>
											<%-- <form:select path="claimType" items="${claimTypes}" style="width: 200px; font-size: 14px; border-color: #919191" /> --%>
										</div>
									</td>
								</tr>
								<tr>
									<td><spring:message code="audit.new.claim.lob" /></td>
									<td class="input-control text"><c:choose>
											<c:when test="${claimDetails.statusBlock.statusCode=='00' }">
												<c:forEach begin="0" end="0"
													items="${claimDetails.productLevel.products }"
													var="products">
													<form:input path="lob" id="lob"
														value="${products.lineofBusiness} " readonly="true"
														style="border: 0px" />
												</c:forEach>
											</c:when>
											<c:when test="${edit != null}">
												<form:input path="lob" value="${eLob} " readonly="true"
													style="border: 0px" />
											</c:when>
											<c:otherwise>
												<form:input path="lob" value="${products.lineofBusiness}"
													disabled="${true}" />
											</c:otherwise>
										</c:choose></td>
								</tr>
								<tr>
									<td><spring:message code="audit.new.claim.member.id" /></td>
									<td class="input-control text"><c:choose>
											<c:when test="${claimDetails.statusBlock.statusCode=='00' }">
												<%-- <c:out value="${claimDetails.subscriberLevel.memberID }"></c:out> --%>
												<form:input path="memberId"
													value="${claimDetails.subscriberLevel.memberID}"
													readonly="true" style="border: 0px" />
											</c:when>
											<c:when test="${edit != null}">
												<form:input path="memberId" value="${eMemId}"
													readonly="true" style="border: 0px" />
											</c:when>
											<c:otherwise>
												<input type="text" name="memId" disabled>
											</c:otherwise>
										</c:choose></td>
								</tr>
								<tr>
									<td><spring:message code="audit.new.claim.process.date" /></td>
									
									<%--changes for RITM1166550 --%>
									<c:set
											var="userRole">
											<%=request.getHeader("iv-groups")%>
										</c:set> <%-- <c:set var="userRole" > "Contractor","PeopleSoft_ELM_User","onestop_users","null" </c:set> --%>
										
										<c:choose>
										<c:when test="${(fn:contains(userRole, 'qadb-superadmin_users'))}" >
											<td class="input-control text"><c:choose>
											<c:when test="${claimDetails.statusBlock.statusCode=='00' }">
												<%-- <c:out value="${claimDetails.claimLevel.processDate }"></c:out> --%>
												<form:input id="datepick1" path="processDate"
													value="${claimDetails.claimLevel.processDate}"
													readonly="false" style="border: 0px" />
											</c:when>
											<c:when test="${edit != null}">
												<form:input id="datepick1" path="processDate" value="${eProDate}"
													readonly="false" style="border: 0px" />
											</c:when>
											<c:otherwise>
												<input id="datepick1" type="text" name="proDate" disabled>
											</c:otherwise>
										</c:choose></td>
										</c:when>
										<c:otherwise>
											<td class="input-control text"><c:choose>
											<c:when test="${claimDetails.statusBlock.statusCode=='00' }">
												<%-- <c:out value="${claimDetails.claimLevel.processDate }"></c:out> --%>
												<form:input path="processDate"
													value="${claimDetails.claimLevel.processDate}"
													readonly="true" style="border: 0px" />
											</c:when>
											<c:when test="${edit != null}">
												<form:input path="processDate" value="${eProDate}"
													readonly="true" style="border: 0px" />
											</c:when>
											<c:otherwise>
												<input type="text" name="proDate" disabled>
											</c:otherwise></c:choose></td>
										</c:otherwise>
										</c:choose>
								</tr>
								<tr>
									<td><spring:message code="audit.new.claim.paid.date" /></td>
									<td class="input-control text"><c:choose>
											<c:when test="${claimDetails.statusBlock.statusCode=='00' }">
												<%-- <c:out value="${claimDetails.claimLevel.paidDate }"></c:out> --%>
												<form:input path="paidDate"
													value="${claimDetails.claimLevel.paidDate}" readonly="true"
													style="border: 0px" />
											</c:when>
											<c:when test="${edit != null}">
												<form:input path="paidDate" value="${ePaidDate}"
													readonly="true" style="border: 0px" />
											</c:when>
											<c:otherwise>
												<input type="text" name="paidDate" disabled>
											</c:otherwise>
										</c:choose></td>
								</tr>
								<tr>
									<td><spring:message code="audit.new.claim.total.charge" /></td>
									<td class="input-control text">
									<i id="dollar2">$</i>
									<c:choose>
										
											<c:when test="${claimDetails.statusBlock.statusCode=='00' }">
												<%-- <c:out value="${claimDetails.claimLevel.totalCharge }"></c:out> --%>
												<form:input path="totalCharge" 
													value="${claimDetails.claimLevel.totalCharge}"
													readonly="true" style="border: 0px;padding-left:17px;" />
											</c:when>
											<c:when test="${edit != null}">
												
												<form:input path="totalCharge" value="${eTotalCharge}" 
													readonly="true" style="border: 0px;padding-left:17px;" />
											</c:when>
											<c:otherwise>
												<input type="text" name="totalCharge" style="padding-left:17px;" disabled> 
											</c:otherwise>
										</c:choose></td>
								</tr>
								<tr>
									<td width="30%"><spring:message
											code="audit.new.claim.paid" /></td>
									<td class="input-control text">
									<i id="dollar2">$</i>
									<c:choose>
										
											<%-- <c:when test="${claimDetails.statusBlock.statusCode=='00' }">
												<c:out value="${claimDetails.claimLevel.paidAmount }"></c:out>
												<form:input path="paid"
													value="${claimDetails.claimLevel.paidAmount}" 
													readonly="true" style="border: 0px;padding-left:17px;" id="paids" />
												<span id="correct" class="correct"> <spring:message
														code="audit.new.high.dollar.message" /></span>
											</c:when> --%>
											<c:when test="${claimDetails.statusBlock.statusCode=='00' }">
												<div id="paidc" class="input-control text">
												<i id="dollar">$</i>
													<form:input id="paids" path="paid"
														value="${claimDetails.claimLevel.paidAmount}" style="padding-left:17px;"/>
													<span id="correct" class="correct" style="font-weight: bold;"> <spring:message code="audit.new.high.dollar.message" /></span>
												</div>
											</c:when>
											<c:when test="${edit != null}">
												<div id="paidc" class="input-control text">
												<i id="dollar">$</i>
													<form:input id="paids" path="paid"
														value="${ePaid}" style="padding-left:17px;"/>
													<span id="correct" class="correct" style="font-weight: bold;"> <spring:message code="audit.new.high.dollar.message" /></span>
												</div>
												<%-- <form:input path="paid" value="${ePaid} " readonly="true" 
													style="border: 0px;padding-left:17px;" id="paids" />
												<span id="correct" class="correct"> <spring:message
														code="audit.new.high.dollar.message" /></span> --%>
											</c:when>
											<c:otherwise>
												<input type="text" name="paid" style="padding-left:17px;" disabled>
											</c:otherwise>
										</c:choose> <!-- <a id="highDollarLink" href="javascript:void(0);" onclick="showHighDollar();" style="display: none;">High Dollar</a> -->
									</td>
								</tr>
								<tr>
									<td colspan="2"><div id="errorHigh" style="display: none;"></div></td>
								</tr>
								<tr>
									<td></td>
									<td><button id="highDollarLink" type="button"
											style="background-color: #298fd8;float: left;"
											class="button default" onclick="showHighDollar();">High
											Dollar</button></td>
								</tr>
							</table>


						</div>
					</div>
				</div>


				<div class="panelContainer">
					<div class="containerA" style="padding-left:7px;padding-top: 7px;">
						<div class="headerA"
							style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 18px"><spring:message
									code="audit.new.monetary.info" /> </span>
						</div>
						<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
							<table class="table">
								<tr>
									<td style="vertical-align: middle;padding-right:45px;"><spring:message
											code="audit.new.monetary.error" /></td>
									<td>
										<div class="input-control radio default-style margin10"
											data-role="input-control">
											<label class="input-control radio small-check"> <c:choose>
													<c:when test="${eMonErrorO != null}">
														<input type="radio" name="monetaryError"
															onclick="calculateThePaid();" checked="checked" value="O">
														<span class="check"></span>
														<span class="caption"><spring:message
																code="audit.new.monetary.error.overpaid" /></span>
													</c:when>
													<c:when test="${monetaryError == 'O'}">
														<input type="radio" name="monetaryError"
															onclick="calculateThePaid();" checked="checked" value="O">
														<span class="check"></span>
														<span class="caption"><spring:message
																code="audit.new.monetary.error.overpaid" /></span>
													</c:when>
													<c:otherwise>
														<input type="radio" name="monetaryError"
															onclick="calculateThePaid();" value="O">
														<span class="check"></span>
														<span class="caption"><spring:message
																code="audit.new.monetary.error.overpaid" /></span>
													</c:otherwise>
												</c:choose>
											</label>
										</div>
										<div class="input-control radio default-style margin10"
											data-role="input-control">
											<label> <c:choose>
													<c:when test="${eMonErrorU != null}">
														<input type="radio" name="monetaryError"
															onclick="calculateThePaid();" checked="checked" value="U">
														<span class="check"></span>
														<span class="caption"><spring:message
																code="audit.new.monetary.error.underpaid" /></span>
													</c:when>
													<c:when test="${monetaryError == 'U'}">
														<input type="radio" name="monetaryError"
															onclick="calculateThePaid();" checked="checked" value="U">
														<span class="check"></span>
														<span class="caption"><spring:message
																code="audit.new.monetary.error.underpaid" /></span>
													</c:when>
													<c:otherwise>
														<input type="radio" name="monetaryError"
															onclick="calculateThePaid();" value="U">
														<span class="check"></span>
														<span class="caption"><spring:message
																code="audit.new.monetary.error.underpaid" /></span>
													</c:otherwise>
												</c:choose>
											</label>
										</div>
										<div class="input-control radio default-style margin10"
											data-role="input-control">
											<label> <c:if test="${edit != null}">
													<c:choose>
														<c:when test="${eMonErrorN != null ||monetaryError == 'N'}">
															<input type="radio" name="monetaryError"
																onclick="calculateThePaid();" checked="checked"
																value="N">
															<span class="check"></span>
															<span class="caption"><spring:message
																	code="audit.new.monetary.error.none" /></span>
														</c:when>
														<c:otherwise>
															<input type="radio" name="monetaryError"
																onclick="calculateThePaid();" value="N">
															<span class="check"></span>
															<span class="caption"><spring:message
																	code="audit.new.monetary.error.none" /></span>
														</c:otherwise>
													</c:choose>
												</c:if> <c:if test="${ edit == null}">
													<c:choose>
														<c:when
															test="${monetaryError == 'N' || monetaryError==null}">
															<input type="radio" name="monetaryError"
																onclick="calculateThePaid();" checked="checked"
																value="N">
															<span class="check"></span>
															<span class="caption"><spring:message
																	code="audit.new.monetary.error.none" /></span>
														</c:when>
														<c:otherwise>
															<input type="radio" name="monetaryError"
																onclick="calculateThePaid();" value="N">
															<span class="check"></span>
															<span class="caption"><spring:message
																	code="audit.new.monetary.error.none" /></span>
														</c:otherwise>
													</c:choose>
												</c:if>
											</label>
										</div>
									</td>
								</tr>
								<tr>
									<td><spring:message code="audit.new.monetary.amount" /><span
										style="color:red">&nbsp;*</span></td>
									<td><c:choose>
											<c:when test="${amountp!= null }">
												<div id="amountpc" class="input-control text">
												<i id="dollar">$</i>
													<form:input id="amountp" path="amountp"
														value="${amountp}" style="padding-left:17px;"/>
													<%-- <span id="correct" class="correct"> <spring:message code="audit.new.high.dollar.message" /></span> --%>
												</div>
											</c:when>
											<c:otherwise>
												<div id="amountpc" class="input-control text">
												<i id="dollar">$</i>
													<form:input id="amountp" path="amountp" style="padding-left:17px;"
														value="${eAmount}" disabled="true" />
													<%-- <span id="correct" class="correct"> <spring:message code="audit.new.high.dollar.message" /></span> --%>
												</div>
											</c:otherwise>
										</c:choose></td>
								</tr>
								<tr>
									<td><spring:message
											code="audit.new.monetary.theoretical.paid" /> <span
										style="color:red">*</span></td>
									<td><c:choose>
											<c:when test="${theoreticalPaid!= null }">
												<div id="theoreticalPaidc" class="input-control text">
													<i id="dollar">$</i>
													<form:input id="theoreticalPaid" path="theoreticalPaid" style="padding-left:17px;"
														 value="${theoreticalPaid}" onkeydown="event.preventDefault()" readonly="readonly"/>
												</div>
											</c:when>
											<c:otherwise>
												<div id="theoreticalPaidc" class="input-control text">
												<i id="dollar">$</i>
													<form:input id="theoreticalPaid" path="theoreticalPaid" style="padding-left:17px;"
														 value="${eTheoPaid}" disabled="true" onkeydown="event.preventDefault()" readonly="readonly"/>
												</div>
											</c:otherwise>
										</c:choose></td>
								</tr>
								<tr>
									<td><spring:message
											code="audit.new.monetary.penalty.interest" /></td>
									<td><div class="input-control checkbox ">
											<label> <c:choose>
													<c:when test="${ePiChk != null}">
														<form:checkbox path="piChk" id="chkbox" name="piChk"
															value="Y" checked="checked" />
														<span class="check"></span>
													</c:when>
													<c:when test="${isChk != null && isChk=='Y'}">
														<form:checkbox path="piChk" id="chkbox" name="piChk"
															value="Y" checked="checked" />
														<span class="check"></span>
													</c:when>
													<c:otherwise>
														<form:checkbox path="piChk" id="chkbox" name="piChk"
															value="Y" />
														<span class="check"></span>
													</c:otherwise>
												</c:choose>
											</label>
										</div></td>
								</tr>
								<tr>
									<td><spring:message
											code="audit.new.monetary.penalty.interest.type" /></td>
									<td>
										<div class="input-control select">
											<c:choose>
												<c:when test="${ePiChk != null}">
													<select id="pilist" name="penaltyInterestType"
														style="width: 200px; font-size: 14px; border-color: #919191">
														<option value=""><spring:message
																code="audit.new.error.selectOne" /></option>
														<c:forEach items="${piTypes}" var="piTypes">
															<option value="${piTypes.piTypeId}"
																${piTypes.piTypeId == ePItype ? 'selected="selected"' : ''}>${piTypes.penaltyInterestType}</option>
														</c:forEach>
													</select>
												</c:when>
												<c:when test="${penalityInterest != null}">
													<select id="pilist" name="penaltyInterestType"
														style="width: 200px; font-size: 14px; border-color: #919191">
														<option value=""><spring:message
																code="audit.new.error.selectOne" /></option>
														<c:forEach items="${piTypes}" var="piTypes">
															<option value="${piTypes.piTypeId}"
																${piTypes.piTypeId == penalityInterest ? 'selected="selected"' : ''}>${piTypes.penaltyInterestType}</option>
														</c:forEach>
													</select>
												</c:when>
												<c:otherwise>
													<select id="pilist" name="penaltyInterestType"
														disabled="${true}"
														style="width: 200px; font-size: 14px; border-color: #919191">
														<option value=""><spring:message
																code="audit.new.error.selectOne" /></option>
														<c:forEach items="${piTypes}" var="piTypes">
															<option value="${piTypes.piTypeId}">${piTypes.penaltyInterestType}</option>
														</c:forEach>
													</select>
												</c:otherwise>
											</c:choose>
										</div>
									</td>
								</tr>
								<tr>
									<td colspan="2">
										<div
											style="border: 1px solid #919191; width: 60%; height: 30px; padding-top: 2px; padding-right: 2px"
											id="PI">
											<jsp:include page="../jsp/piDiv.jsp"></jsp:include>
										</div>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</div>

				<div class="panelContainer">
					<div class="containerA" style="padding-left:7px;padding-top: 7px;">
						<div class="headerA"
							style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 18px"><spring:message
									code="audit.new.details" /> </span>
						</div>
						<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
							<table class="table">
								<tr>
									<td style="padding-right:60px"><spring:message
											code="audit.new.type" /><span style="color:red">&nbsp;*</span></td>
									<td>
										<div class="input-control select">
											<c:choose>
												<c:when test="${auditType!=null }">
													<select id="auditType" name="auditType"
														style="width: 200px; font-size: 14px; border-color: #919191">
														<c:forEach items="${auditTypes}" var="auditTypes">
															<option value="${auditTypes.audTypeId}"
																${auditTypes.audTypeId == auditType ? 'selected="selected"' : ''}>${auditTypes.auditType}</option>
														</c:forEach>
													</select>
												</c:when>
												<c:otherwise>
													<select id="auditType" name="auditType"
														style="width: 200px; font-size: 14px; border-color: #919191">
														<c:forEach items="${auditTypes}" var="auditTypes">
															<option value="${auditTypes.audTypeId}"
																${auditTypes.audTypeId == eAudType ? 'selected="selected"' : ''}>${auditTypes.auditType}</option>
														</c:forEach>
													</select>
												</c:otherwise>
											</c:choose>
										</div>
									</td>
								</tr>
								<tr>
									<td><spring:message
											code="audit.new.primary.pga.special.audits" /></td>
									<td>
										<div class="input-control select">
											<c:choose>
												<c:when test="${priPGA!=null }">
													<select id="priPGA" name="priPGA" onchange="getSecPGADropdown();"
														style="width: 330px; font-size: 14px; border-color: #919191">
														<option selected value=""><spring:message
																code="audit.new.select.primary.pga" /></option>
														<c:forEach items="${priPGAList}" var="priPGAList">
															<option value="${priPGAList.pPgaId}"
																${priPGAList.pPgaId == priPGA ? 'selected="selected"' : ''}>${priPGAList.priPGA}</option>
														</c:forEach>
													</select>
												</c:when>
												<c:otherwise>
													<select id="priPGA" name="priPGA" onchange="getSecPGADropdown();"
														style="width: 330px; font-size: 14px; border-color: #919191">
														<option selected value=""><spring:message
																code="audit.new.select.primary.pga" /></option>
														<c:forEach items="${priPGAList}" var="priPGAList">
															<option value="${priPGAList.pPgaId}"
																${priPGAList.pPgaId == ePriPGA ? 'selected="selected"' : ''}>${priPGAList.priPGA}</option>
														</c:forEach>
													</select>
												</c:otherwise>
											</c:choose>
										</div>
									</td>
								</tr>
								<tr>
									<td><spring:message
											code="audit.new.secondary.pga.special.audits" /> 
											<%-- <input type="hidden" name="eSecPGA" value="${eSecPGA}"> --%>
											</td>
									<td>
										<div class="input-control select" id="secPGA" >
										
											<jsp:include page="../jsp/audit_general_secPGADropDown_Div.jsp">
											<c:if test="${eSecPGA != null}">
												<c:set var="eSecPGA" value="${eSecPGA}" scope="session"/>
											</c:if>
											<c:if test="${secPGA != null}">
												<c:set var="secPGA" value="${secPGA}" scope="session"/>
											</c:if>
											</jsp:include>
											
											<%-- <c:choose>
												<c:when test="${secPGA!=null }">
													<select id="secPGA" name="secPGA"
														style="width: 330px; font-size: 14px; border-color: #919191">
														<option selected value="None"><spring:message
																code="audit.new.select.secondary.pga" /></option>
														<c:forEach items="${secPGAList}" var="secPGAList">
															<option value="${secPGAList.sPgaId}"
																${secPGAList.sPgaId == secPGA ? 'selected="selected"' : ''}>${secPGAList.secPGA}</option>
														</c:forEach>
													</select>
												</c:when>
												<c:otherwise>
													<select id="secPGA" name="secPGA"
														style="width: 330px; font-size: 14px; border-color: #919191">
														<option selected value="None"><spring:message
																code="audit.new.select.secondary.pga" /></option>
														<c:forEach items="${secPGAList}" var="secPGAList">
															<option value="${secPGAList.sPgaId}"
																${secPGAList.sPgaId == eSecPGA ? 'selected="selected"' : ''}>${secPGAList.secPGA}</option>
														</c:forEach>
													</select>
												</c:otherwise>
											</c:choose> --%>
										</div>
									</td>
								</tr>
								<tr>
									<td style="vertical-align: middle;"><spring:message
											code="audit.new.mock" /></td>
									<td><c:choose>
											<c:when test="${eMock != null}">
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label> <form:radiobutton path="mock" value="N" />
														<span class="check"></span> <spring:message
															code="audit.new.non.mock" />
													</label>
												</div>
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label><form:radiobutton path="mock" value="Y"
															checked="checked" /> <span class="check"></span> <spring:message
															code="audit.new.mock" /> </label>
												</div>
											</c:when>
											<c:when test="${mock != null && mock=='Y'}">
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label> <form:radiobutton path="mock" value="N" />
														<span class="check"></span> <spring:message
															code="audit.new.non.mock" />
													</label>
												</div>
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label><form:radiobutton path="mock" value="Y"
															checked="checked" /> <span class="check"></span> <spring:message
															code="audit.new.mock" /> </label>
												</div>
											</c:when>
											<c:otherwise>
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label> <form:radiobutton path="mock" value="N"
															checked="checked" /> <span class="check"></span> <spring:message
															code="audit.new.non.mock" />
													</label>
												</div>
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label> <form:radiobutton path="mock" value="Y" /><span
														class="check"></span> <spring:message
															code="audit.new.mock" />
													</label>
												</div>
											</c:otherwise>
										</c:choose></td>
								</tr>
								<tr>
									<td style="vertical-align: middle;"><spring:message
											code="audit.new.oos" /></td>
									<td><c:choose>
											<%-- <c:when test="${audCountRS.insCount != null && audCountRS.insCount >= 30}"> <!--modified from 'inSampleCount'  -->
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label> <form:radiobutton path="oos" value="N"
															 /><span class="check"></span> <spring:message
															code="audit.new.oos.in.sample" />
													</label>
												</div>
												<div class="input-control radio default-style margin10"
													data-role="input-control" style="padding-left:7px">
													<label> <form:radiobutton path="oos" value="Y"
															checked="checked" /> <span class="check"></span> <spring:message
															code="audit.new.oos.out.sample" />
													</label>
												</div>
												<div
													style="float: right;width: 330px;padding: 3px; border: 1px solid red;font-size: 12px; font-style: italic;">
													<b>Note: </b>In Sample audit count for this associate has
													reached max limit of 30. <!-- and more audits on his /
													her name will be accounted as Out Of Sample by default -->
												</div>
											</c:when> --%>
											<%-- <c:when test="${eOos != null}"><!--eOos if Oos is N  -->
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label> <form:radiobutton path="oos" value="N" /><span
														class="check"></span> <spring:message
															code="audit.new.oos.in.sample" />
													</label>
												</div>
												<div class="input-control radio default-style margin10"
													data-role="input-control" style="padding-left:7px">
													<label> <form:radiobutton path="oos" value="Y" checked="checked"
															 /> <span class="check"></span> <spring:message
															code="audit.new.oos.out.sample" />
													</label>
												</div>
											</c:when> --%>
											<c:when test="${(oos != null && oos=='Y') || eOos != null }"> <!-- OOS checked -->
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label> <form:radiobutton path="oos" value="N" /><span
														class="check" ></span> <spring:message
															code="audit.new.oos.in.sample" />
													</label>
												</div>
												<div class="input-control radio default-style margin10"
													data-role="input-control" style="padding-left:7px">
													<label> <form:radiobutton path="oos" value="Y" checked="checked"
															 /> <span class="check" ></span> <spring:message
															code="audit.new.oos.out.sample" />
													</label>
												</div>
											</c:when>
											<c:otherwise> <!-- InSample Checked -->
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label> <form:radiobutton path="oos" value="N"
															checked="checked" /> <span class="check"></span> <spring:message
															code="audit.new.oos.in.sample" />
													</label>
												</div>
												<div class="input-control radio default-style margin10"
													data-role="input-control" style="padding-left:7px">
													<label> <form:radiobutton path="oos" value="Y" /><span
														class="check"></span> <spring:message
															code="audit.new.oos.out.sample" />
													</label>
												</div>
											</c:otherwise>
										</c:choose>
										<c:if test="${audCountRS.insCount != null && audCountRS.insCount >= 30}">
											<div style="float: right;width: 230px;padding: 3px; border: 1px solid red;font-size: 12px; font-style: italic;">
													<b>Note: </b>In Sample audit count for this associate has
													reached max limit of 30. <!-- and more audits on his /
													her name will be accounted as Out Of Sample by default -->
												</div>
										</c:if>
										</td>
								</tr>
								<tr>
									<td style="vertical-align: middle;"><spring:message
											code="audit.new.e2e" /></td>
									<td><c:choose>
											<c:when test="${eE2e != null}">
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label> <form:radiobutton path="e2e" value="N" />
														<span class="check"></span> <spring:message
															code="audit.new.non.e2e" />
													</label>
												</div>
												<div class="input-control radio default-style margin10"
													data-role="input-control" style="padding-left:13px">
													<label> <form:radiobutton path="e2e" value="Y"
															checked="checked" /> <span class="check"></span> <spring:message
															code="audit.new.e2e" />
													</label>
												</div>
											</c:when>
											<c:when test="${e2e != null && e2e=='Y'}">
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label> <form:radiobutton path="e2e" value="N" />
														<span class="check"></span> <spring:message
															code="audit.new.non.e2e" />
													</label>
												</div>
												<div class="input-control radio default-style margin10"
													data-role="input-control" style="padding-left:13px">
													<label> <form:radiobutton path="e2e" value="Y"
															checked="checked" /> <span class="check"></span> <spring:message
															code="audit.new.e2e" />
													</label>
												</div>
											</c:when>
											<c:otherwise>
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label> <form:radiobutton path="e2e" value="N"
															checked="checked" /> <span class="check"></span> <spring:message
															code="audit.new.non.e2e" />
													</label>
												</div>
												<div class="input-control radio default-style margin10"
													data-role="input-control" style="padding-left:13px">
													<label> <form:radiobutton path="e2e" value="Y" />
														<span class="check"></span> <spring:message
															code="audit.new.e2e" />
													</label>
												</div>

											</c:otherwise>

										</c:choose></td>
								</tr>
								<tr>
									<td style="vertical-align: middle;"><spring:message
											code="audit.new.risk.account" /></td>
									<td><c:choose>
											<c:when test="${eRiscAcc != null}">
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label><form:radiobutton path="riskAccount"
															value="Y" /> <span class="check"></span> <spring:message
															code="audit.new.risk.account.yes" /> </label>
												</div>
												<div class="input-control radio default-style margin10"
													data-role="input-control" style="padding-left:46px">
													<label> <form:radiobutton path="riskAccount"
															value="N" checked="checked" /> <span class="check"></span>
														<spring:message code="audit.new.risk.account.no" />
													</label>
												</div>
											</c:when>
											<c:when test="${riskAccount != null && riskAccount=='N'}">
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label><form:radiobutton path="riskAccount"
															value="Y" /> <span class="check"></span> <spring:message
															code="audit.new.risk.account.yes" /> </label>
												</div>
												<div class="input-control radio default-style margin10"
													data-role="input-control" style="padding-left:46px">
													<label> <form:radiobutton path="riskAccount"
															value="N" checked="checked" /> <span class="check"></span>
														<spring:message code="audit.new.risk.account.no" />
													</label>
												</div>
											</c:when>
											<c:otherwise>
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label><form:radiobutton path="riskAccount"
															value="Y" checked="checked" /> <span class="check"></span>
														<spring:message code="audit.new.risk.account.yes" /> </label>
												</div>
												<div class="input-control radio default-style margin10"
													data-role="input-control" style="padding-left:46px">
													<label> <form:radiobutton path="riskAccount"
															value="N" /> <span class="check"></span> <spring:message
															code="audit.new.risk.account.no" />
													</label>
												</div>
											</c:otherwise>
										</c:choose></td>
								</tr>
								<tr>
									<td style="padding-top:7px;"><spring:message
											code="audit.new.process.type" /> <span style="color:red">*</span></td>
									<td>
										<div class="input-control select">
											<%-- <form:select path="processtype" items="${processTypes}"
											style="width: 200px; font-size: 14px; border-color: #919191" /> --%>
											<c:choose>
												<c:when test="${processtype !=null }">
													<select id="processtypeId" name="processtype"
														style="width: 200px; font-size: 14px; border-color: #919191"
														onchange="processTypeChanged();">
														<option><spring:message
																code="audit.new.error.selectOne" /></option>
														<c:forEach items="${processTypes}" var="processTypes">
															<option value="${processTypes.processTypId}"
																${processTypes.processTypId == processtype ? 'selected="selected"' : ''} ${disableDropdown=='true'?'disabled':'' }>${processTypes.processtype}</option>
														</c:forEach>
													</select>
												</c:when>
												<c:when test="${disableDropdown =='true'}">
													<select id="processtypeId" name="processtype"
														style="width: 200px; font-size: 14px; border-color: #919191"
														onchange="processTypeChanged();">
														<option><spring:message
																code="audit.new.error.selectOne" /></option>
														<c:forEach items="${processTypes}" var="processTypes">
															<option value="${processTypes.processTypId}"
																${processTypes.processtype=='Regular Claim' ? 'selected="selected"' : ''} ${processTypes.processtype!='Regular Claim'?'disabled':'' }>${processTypes.processtype}</option>
														</c:forEach>
													</select>
												</c:when>
												<c:when test="${fn:endsWith(dcn,'0') && eDCN == null }">
													<select id="processtypeId" name="processtype" 
														style="width: 200px; font-size: 14px; border-color: #919191"
														onchange="processTypeChanged();">
														<option><spring:message
																code="audit.new.error.selectOne" /></option>
														<c:forEach items="${processTypes}" var="processTypes">
															<option value="${processTypes.processTypId}"
																${processTypes.processtype=='Regular Claim' ? 'selected="selected"' : ''} ${processTypes.processtype!='Regular Claim'?'disabled':'' }>${processTypes.processtype}</option>
														</c:forEach>
													</select>
												</c:when>
												<c:otherwise>
													<select id="processtypeId" name="processtype"
														style="width: 200px; font-size: 14px; border-color: #919191"
														onchange="processTypeChanged();">
														<option><spring:message
																code="audit.new.error.selectOne" /></option>
														<c:forEach items="${processTypes}" var="processTypes">
														<c:if test="${processTypes.processtype!='Regular Claim'}">
															<option value="${processTypes.processTypId}"
																${processTypes.processTypId == eProcType ? 'selected="selected"' : ''}>${processTypes.processtype}</option>
														</c:if>
														</c:forEach>
													</select>
												</c:otherwise>
											</c:choose>
										</div>
									</td>
								</tr>
								<%-- <tr>
									<td style="vertical-align: middle;"><spring:message
											code="audit.new.platform" /></td> 
									<td><c:choose>
											<c:when test="${eAdjustment != null }">
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label> <form:radiobutton path="adjustment"
															value="O" /><span class="check"></span> <spring:message
															code="audit.new.platform.facetslegacy" />
													</label>
												</div>
												<div class="input-control radio default-style margin10"
													data-role="input-control" style="padding-left:30px">
													<label> <form:radiobutton path="adjustment"
															value="E" checked="checked" /> <span class="check"></span>
														<spring:message code="audit.new.platform.facetsg6" />
													</label>
												</div>
											</c:when>
											<c:when test="${adjustment != null && adjustment=='E' }">
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label> <form:radiobutton path="adjustment"
															value="O" /><span class="check"></span> <spring:message
															code="audit.new.platform.facetslegacy" />
													</label>
												</div>
												<div class="input-control radio default-style margin10"
													data-role="input-control" style="padding-left:30px">
													<label> <form:radiobutton path="adjustment"
															value="E" checked="checked" /> <span class="check"></span>
														<spring:message code="audit.new.platform.facetsg6" />
													</label>
												</div>
											</c:when>
											<c:otherwise>
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label> <form:radiobutton path="adjustment"
															value="O" checked="checked" /><span class="check"></span>
														<spring:message code="audit.new.platform.facetslegacy" />
													</label>
												</div>
												<div class="input-control radio default-style margin10"
													data-role="input-control" style="padding-left:30px">
													<label> <form:radiobutton path="adjustment" 
															value="E" /> <span class="check"></span> <spring:message
															code="audit.new.platform.facetsg6" />
													</label>
												</div>
											</c:otherwise>

										</c:choose></td>
								</tr> --%>
								<tr>
									<td><spring:message code="audit.new.platform" /></td>
									<td class="input-control text"><c:choose>
											<c:when test="${claimDetails.statusBlock.statusCode=='00' }">
												<form:input path="platform"
													value="${claimDetails.platform}"
													readonly="true" style="border: 0px" />
											</c:when>
											<c:when test="${edit != null}">
												<form:input path="platform" value="${ePlatform}"
													readonly="true" style="border: 0px" />
											</c:when>
											<c:otherwise>
												<input type="text" name="platform" disabled>
											</c:otherwise>
										</c:choose></td>
								</tr>
								
									<td style="vertical-align: middle;"><spring:message
											code="audit.new.fyi" /></td>
									<td><c:choose>
											<c:when test="${eFyi != null}">
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label> <form:radiobutton path="fyicheck"
															id="fychk" value="N" /> <span class="check"></span> <spring:message
															code="audit.new.risk.account.no" />
													</label>
												</div>
												<div class="input-control radio default-style margin10"
													data-role="input-control" style="padding-left:46px">
													<label><form:radiobutton path="fyicheck" id="fychky" value="Y"
															checked="checked" /> <span class="check"></span> <spring:message
															code="audit.new.risk.account.yes" /> </label>
												</div>
											</c:when>
											<c:when test="${fyicheck != null && fyicheck=='Y'}">
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label> <form:radiobutton path="fyicheck"
															id="fychk" value="N" /> <span class="check"></span> <spring:message
															code="audit.new.risk.account.no" />
													</label>
												</div>
												<div class="input-control radio default-style margin10"
													data-role="input-control" style="padding-left:46px">
													<label><form:radiobutton path="fyicheck" id="fychky" value="Y"
															checked="checked" /> <span class="check"></span> <spring:message
															code="audit.new.risk.account.yes" /> </label>
												</div>
											</c:when>
											<c:otherwise>
												<div class="input-control radio default-style margin10"
													data-role="input-control">
													<label> <form:radiobutton path="fyicheck"
															id="fychk" value="N" checked="checked" /> <span
														class="check"></span> <spring:message
															code="audit.new.risk.account.no" />
													</label>
												</div>
												<div class="input-control radio default-style margin10"
													data-role="input-control" style="padding-left:46px">
													<label><form:radiobutton path="fyicheck" id="fychky" value="Y" />
														<span class="check"></span> <spring:message
															code="audit.new.risk.account.yes" /> </label>
												</div>
											</c:otherwise>
										</c:choose></td>
								</tr>
								<tr>
									<td></td>
									<td>
										<div class="input-control textarea">
											<c:choose>
												<c:when test="${fyi!=null }">
													<textarea id="fyin" name="fyi" maxlength="500"
														style="width: 242px; height: 106px;" disabled="disabled">${fyi}</textarea>
												</c:when>
												<c:otherwise>
													<textarea id="fyin" name="fyi" maxlength="500"
														style="width: 242px; height: 106px;" disabled="disabled">${eFyi}</textarea>
												</c:otherwise>
											</c:choose>
										</div>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</div>

				<div class="panelContainer">
					<div class="containerA" style="padding-left:7px;padding-top: 7px;">
						<div class="headerA"
							style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 18px">Additional
								Claim Info</span>
						</div>
						<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
							<table class="table">
								<c:choose>
									<c:when test="${claimDetails.statusBlock.statusCode=='00' }">
										<tr>
											<td width="30%"><spring:message
													code="claim.details.subscriberCK" /></td>
											<td style="text-align: left"><form:input
													path="subscriberCK" id="subscriberCK"
													value="${claimDetails.subscriberLevel.subscriber_CK }"
													readonly="true" style="border: 0px" /></td>
										</tr>
										<tr>
											<td width="30%"><spring:message
													code="claim.details.accountID" /></td>
											<td style="text-align: left"><form:input
													path="AccountID" id="AccountID"
													value="${claimDetails.subscriberLevel.accountID}"
													readonly="true" style="border: 0px" /></td>
										</tr>
										<tr>
											<td width="30%"><spring:message
													code="claim.details.accountName" /></td>
											<td style="text-align: left"><textarea
													name="AccountName" id="AccountName"
													readonly="true" style="border: 0px;width:260px">${claimDetails.subscriberLevel.accountName}</textarea></td>
										</tr>
										<tr>
											<td width="30%"><spring:message
													code="claim.details.cjaJurisdiction" /></td>
											<td style="text-align: left"><form:input
													path="jurisdiction" id="jurisdiction"
													value="${claimDetails.subscriberLevel.CJAJurisdiction }"
													readonly="true" style="border: 0px" /></td>
										</tr>
										<tr>
											<td width="30%"><spring:message code="claim.details.sbu" /></td>
											<td style="text-align: left"><form:input path="Group"
													id="Group" value="${claimDetails.subscriberLevel.SBU }"
													readonly="true" style="border: 0px" /></td>
										</tr>
										<tr>
											<td width="30%"><spring:message
													code="claim.details.claimCurrentstatus" /></td>
											<td style="text-align: left"><form:input
													path="claimCurrentStatus" id="claimCurrentStatus"
													value="${claimDetails.claimLevel.claimCurrentstatus }"
													readonly="true" style="border: 0px" /></td>
										</tr>
										<tr>
											<td width="30%"><spring:message
													code="claim.details.productID" /></td>
											<td style="text-align: left"><form:input
													path="productID" id="productID"
													value="${claimDetails.claimLevel.productID }"
													readonly="true" style="border: 0px" /></td>
										</tr>
										<tr>
											<td width="30%"><spring:message
													code="claim.details.adjustmentReasoncode" /></td>
											<td style="text-align: left"><form:input
													path="adjustmentReasoncode" id="adjustmentReasoncode"
													value="${adjustmentReasoncode}"
													readonly="true" style="border: 0px" /></td>
										</tr>
									</c:when>
									<c:when test="${edit != null}">
										<%-- <tr>
											<td width="30%"><spring:message
													code="claim.details.subscriberCK" /></td>
											<td style="text-align: left"><c:out value="${eSub}"></c:out>
											</td>
										</tr>
										<tr>
											<td width="30%"><spring:message
													code="claim.details.accountID" /></td>
											<td style="text-align: left">${eAccID}</td>
										</tr>
										<tr>
											<td width="30%"><spring:message
													code="claim.details.accountName" /></td>
											<td style="text-align: left">${eAccName}</td>
										</tr>
										<tr>
											<td width="30%"><spring:message
													code="claim.details.cjaJurisdiction" /></td>
											<td style="text-align: left">${eJuri}</td>
										</tr>
										<tr>
											<td width="30%"><spring:message code="claim.details.sbu" /></td>
											<td style="text-align: left">${eGrp}</td>
										</tr>
										<tr>
											<td width="30%"><spring:message
													code="claim.details.claimCurrentstatus" /></td>
											<td style="text-align: left">${eStatus}</td>
										</tr>
										<tr>
											<td width="30%"><spring:message
													code="claim.details.productID" /></td>
											<td style="text-align: left">${ePrId}</td>
										</tr> --%>
										
										<tr>
											<td width="30%"><spring:message
													code="claim.details.subscriberCK" /></td>
											<td style="text-align: left"><form:input
													path="subscriberCK" id="subscriberCK"
													value="${claimDetails.subscriberLevel.subscriber_CK } ${eSub}"
													readonly="true" style="border: 0px" /></td>
										</tr>
										<tr>
											<td width="30%"><spring:message
													code="claim.details.accountID" /></td>
											<td style="text-align: left"><form:input
													path="AccountID" id="AccountID"
													value="${claimDetails.subscriberLevel.accountID} ${eAccID}"
													readonly="true" style="border: 0px" /></td>
										</tr>
										<tr>
											<td width="30%"><spring:message
													code="claim.details.accountName" /></td>
											<td style="text-align: left"><textarea
													name="AccountName" id="AccountName"
													readonly="true" style="border: 0px;width:260px">${claimDetails.subscriberLevel.accountName}${eAccName}</textarea></td>
										</tr>
										<tr>
											<td width="30%"><spring:message
													code="claim.details.cjaJurisdiction" /></td>
											<td style="text-align: left"><form:input
													path="jurisdiction" id="jurisdiction"
													value="${claimDetails.subscriberLevel.CJAJurisdiction } ${eJuri}"
													readonly="true" style="border: 0px" /></td>
										</tr>
										<tr>
											<td width="30%"><spring:message code="claim.details.sbu" /></td>
											<td style="text-align: left"><form:input path="Group"
													id="Group" value="${claimDetails.subscriberLevel.SBU } ${eGrp}"
													readonly="true" style="border: 0px" /></td>
										</tr>
										<tr>
											<td width="30%"><spring:message
													code="claim.details.claimCurrentstatus" /></td>
											<td style="text-align: left"><form:input
													path="claimCurrentStatus" id="claimCurrentStatus"
													value="${claimDetails.claimLevel.claimCurrentstatus } ${eStatus}"
													readonly="true" style="border: 0px" /></td>
										</tr>
										<tr>
											<td width="30%"><spring:message
													code="claim.details.productID" /></td>
											<td style="text-align: left"><form:input
													path="productID" id="productID"
													value="${claimDetails.claimLevel.productID } ${ePrId}"
													readonly="true" style="border: 0px" /></td>
										</tr>
										<tr>
											<td width="30%"><spring:message
													code="claim.details.adjustmentReasoncode" /></td>
											<td style="text-align: left"><form:input
													path="adjustmentReasoncode" id="adjustmentReasoncode"
													value="${adjustmentReasoncode} ${eAdjustmentReasoncode}"
													readonly="true" style="border: 0px" /></td>
										</tr>
										
									</c:when>
									<c:otherwise>
								Please enter a DCN and search for getting the claim details.
							</c:otherwise>
								</c:choose>
							</table>
							Product details: <br>
							<table class="table">
								<c:choose>
									<c:when test="${claimDetails.statusBlock.statusCode=='00' }">
										<tr style="background:#dddfe1;height: 30px">
											<td ><spring:message
													code="claim.details.bsbsCode" /></td>
											<td ><spring:message
													code="claim.details.groupID" /></td>
											<td ><spring:message
													code="claim.details.productLine" /></td>
											<td ><spring:message
													code="claim.details.productDescription" /></td>
										</tr>
										<c:forEach items="${claimDetails.productLevel.products }"
											var="products" varStatus="rowCount">
											<input type="hidden"
												id="products[${rowCount.index }].BSBSCode"
												value="${products.BSBSCode }" />
											<input type="hidden"
												id="products[${rowCount.index }].groupID"
												value="${products.groupID }" />
											<input type="hidden"
												id="products[${rowCount.index }].productLine"
												value="${products.productLine }" />
											<input type="hidden" 
												id="products[${rowCount.index }].productDescription"
												value="${products.productDescription }" />

											<tr>
												<td ><form:input
														path="products[${rowCount.index }].BSBSCode"
														id="productID" value="${products.BSBSCode }"
														readonly="true" style="border: 0px" /></td>
												<td ><form:input
														path="products[${rowCount.index }].groupID" id="productID"
														value="${products.groupID }" readonly="true"
														style="border: 0px" /></td>
												<td ><form:input
														path="products[${rowCount.index }].productLine"
														id="productID" value="${products.productLine }"
														readonly="true" style="border: 0px" /></td>
												<td ><form:input
														path="products[${rowCount.index }].productDescription"
														id="productID" value="${products.productDescription }"
														readonly="true" style="border: 0px;width:295px" /></td>
											</tr>
										</c:forEach>
									</c:when>
									<c:when test="${edit != null}">
										<tr style="background:#dddfe1;height: 30px">
											<td ><spring:message
													code="claim.details.bsbsCode" /></td>
											<td style="text-align: center; "><spring:message
													code="claim.details.groupID" /></td>
											<td ><spring:message
													code="claim.details.productLine" /></td>
											<td ><spring:message
													code="claim.details.productDescription" /></td>
										</tr>
										<%-- <c:forEach items="${eProducts}" var="products">
											<tr>
												<td >${products.BSBSCode }</td>
												<td >${products.groupID }</td>
												<td >${products.productLine }</td>
												<td >${products.productDescription }</td>
											</tr>
										</c:forEach> --%>
										
										<c:forEach items="${eProducts}"
											var="products" varStatus="rowCount">
											<input type="hidden"
												id="products[${rowCount.index }].BSBSCode"
												value="${products.BSBSCode }" />
											<input type="hidden"
												id="products[${rowCount.index }].groupID"
												value="${products.groupID }" />
											<input type="hidden"
												id="products[${rowCount.index }].productLine"
												value="${products.productLine }" />
											<input type="hidden" 
												id="products[${rowCount.index }].productDescription"
												value="${products.productDescription }" />

											<tr>
												<td ><form:input
														path="products[${rowCount.index }].BSBSCode"
														id="productID" value="${products.BSBSCode }"
														readonly="true" style="border: 0px" /></td>
												<td ><form:input
														path="products[${rowCount.index }].groupID" id="productID"
														value="${products.groupID }" readonly="true"
														style="border: 0px" /></td>
												<td ><form:input
														path="products[${rowCount.index }].productLine"
														id="productID" value="${products.productLine }"
														readonly="true" style="border: 0px" /></td>
												<td ><form:input
														path="products[${rowCount.index }].productDescription"
														id="productID" value="${products.productDescription }"
														readonly="true" style="border: 0px;width:295px" /></td>
											</tr>
										</c:forEach>
									</c:when>

									<c:otherwise></c:otherwise>
								</c:choose>
							</table>

						</div>
					</div>
				</div>

				<div>
					<table width="100%" style="padding: 10px 10px 10px 50px">
						<c:choose>
							<c:when test="${edit != null}">
								<td width="30%">
									<div style="float: left;padding-left: 5px;">
										<a style="float: left;" href="#top">Back To Top</a>
									</div>
								</td>
								<td width="70%" style="text-align: right;"><c:set
										var="userRole">
										<%=request.getHeader("iv-groups")%>
									</c:set> <%-- <c:set var="userRole" > "Contractor","PeopleSoft_ELM_User","onestop_users","null" </c:set> --%>
									<c:if test="${!((fn:contains(userRole, 'qadb-samd-readonly_user'))||(fn:contains(userRole, 'qadb-cd-readonly_user'))||(fn:contains(userRole, 'null')))}">
										<button title="Cancel" type="reset"
											style="background-color: transparent; border-color: transparent; size: 10px;">
											<img src="webResources/images/Actions/Icn_Cancel.png">
										</button>
										<a onclick="divDelA_show();">
											<button title="Delete" type="button"
												style="background-color: transparent; border-color: transparent; size: 10px;">
												<img alt="Delete Audit"
													src="webResources/images/Actions/Icn_Delete.png">
											</button>
										</a>
										<button title="Save" type="submit"
											style="background-color: transparent; border-color: transparent; size: 10px;">
											<img src="webResources/images/Actions/Icn_Save.png">
										</button>
									</c:if> <a onclick="divCount_show();">
										<button title="Click to view total Audit count" type="button"
											style="background-color: transparent; border-color: transparent; size: 10px;">
											<img alt="Audit Count"
												src="webResources/images/Actions/Icn_Snapshot.png">
										</button>
								</a></td>
							</c:when>

							<c:otherwise>
								<td width="30%">
									<div style="float: left;padding-left: 5px;">
										<a style="float: left;" href="#top">Back To Top</a>
									</div>
								</td>
								<td width="70%" style="text-align: right;">
									<%-- <c:if test="${assoDetails.associateName != null}"> --%>
									<button title="Reset" type="reset"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Cancel.png">
									</button>
									<button title="Save" type="submit"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png">
									</button> <%-- </c:if> --%>

								</td>
							</c:otherwise>
						</c:choose>
					</table>
				</div>
			</div>
			<jsp:include page="piCalculator.jsp"></jsp:include>
		</div>
	</form:form>
	<div id="popupClaimRecords">
		<!-- popup claim records Starts Here -->
	</div>
	<div id="popupClaims"
		style="text-align: center; padding: 25px 25px 25px 25px;border-radius: 5px;
    box-shadow: 5px 5px 5px #888888;">
		<img id="close" src="webResources/images/Misc/Icn_Close2.png"
			onclick="divclaims_hide()"> <span style="font-size: 12px;"><b>
				<spring:message code="audit.new.dcn.records.header" />
		</b> </span>
		<table style="width: 400px;">
			<tr>
				<td style="height: 30px"></td>
			</tr>
		</table>
		<div align="center">
			<table class="table striped hovered dataTable" id="searchDatatable"
				style="width: 470px;">
				<thead>
					<tr>
						<td width="30%" style="text-align: center;"><spring:message
								code="audit.new.associate.id" /></td>
						<td width="20%" style="text-align: center;"><spring:message
								code="audit.new.dcn.records.column2" /></td>
						<td width="30%" style="text-align: center;"><spring:message
								code="audit.new.dcn.records.column3" /></td>
						<td width="20%" style="text-align: center;"><spring:message
								code="audit.new.dcn.records.column4" /><td>
					</tr>
				</thead>
				<tbody>
					<c:forEach items="${claimDetails.clstStatusLines.clstStatusLine }"
						var="clstStatusLine">
						<tr>
							<td style="text-align: center;"><a href="#"
								style="text-decoration: none;"
								onclick="selectClaim('${clstStatusLine.userID }');validateDCN('Y','${clstStatusLine.userID }','${clstStatusLine.processedDate }','${clstStatusLine.clstStatus }','yes')"><c:out
										value="${clstStatusLine.userID }"></c:out> </a></td>
							<td style="text-align: center;"><c:out
									value="${fn:substring((clstStatusLine.processedDate), 11, 19)} "></c:out>
							</td>
							<td style="text-align: center;"><c:out
									value="${(clstStatusLine.formattedDate)} "></c:out></td>
							<td style="text-align: center;"><c:out
									value="${(clstStatusLine.clstStatus)} "></c:out></td>
						</tr>
					</c:forEach>
				</tbody>
			</table>
		</div>
	</div>
	<script>
		$(function() {
			$('#searchDatatable').dataTable({

				"columns" : [

				{
					"render" : function(data, type, full, meta) {
						/* return '<a href="'+full[0]+'"><u>'+data+'<u></a>'; */
						return '<a href=""><u>' + data + '<u></a>';

					}
				}, {}, {}, {} ]
			});
		});
		
		//added for RITM1166550
	$("#datepick1").datepicker({
	dateFormat: "mm-dd-y",
	showOn:"button",
    /* onSelect: function(selected) {
      $("#datepick2").datepicker("option","minDate", selected) 
    },*/
	});
	$("#datepick2").datepicker({
	showOn:"button",
    onSelect: function(selected) {
      $("#datepick1").datepicker("option","maxDate", selected)
    },
	});
	</script>
	<!-- delete popup -->
	<div id="popupClaimRecordsD">
		<!-- popup claim records Starts Here -->


	</div>
	<div id="popupClaimsD"
		style="text-align: center; padding: 25px 25px 25px 25px;border-radius: 5px;
    box-shadow: 5px 5px 5px #888888;">
		<img id="close" src="webResources/images/Misc/Icn_Close2.png"
			onclick="divDelA_hide()"> <span style="font-size: 15px;"><b>
				<spring:message code="audit.new.delete.message" />
		</b> </span> <br> <br>
		<div align="center" style="width: 450">
			<table>
				<tr>

					<a onclick="divDelA_hide();">
						<button type="reset" class="button inverse">
							<spring:message code="audit.new.risk.account.no" />
						</button>
					</a>
					<a href="deleteAudit">
						<button style="background-color: #298fd8" class="button default">
							<spring:message code="audit.new.risk.account.yes" />
						</button>
					</a>
				</tr>

			</table>

		</div>
	</div>

	<!-- Audit counts popup -->

	<div id="popupAuditCountD">
		<!-- popup claim records Starts Here -->


	</div>
	<div id="popupAuditCount"
		style="text-align: left; padding: 25px 25px 25px 25px;border-radius: 5px;
    box-shadow: 5px 5px 5px #888888;">
		<img id="close" src="webResources/images/Misc/Icn_Close2.png"
			onclick="divCount_hide()"> <span style="font-size: 15px;"><b>
				<spring:message code="audit.new.popup.count.message1" />
				${audCountRS.processDate} ${audCountRS.associateName} <spring:message
					code="audit.new.popup.count.message2" />
		</b>
			<div style="padding-left:10px">
				<ol>
					<li><spring:message
							code="audit.new.popup.count.in.sample.audits" />
						-------------------------- ${audCountRS.insCount}
						<ul style="list-style-type:disc">
							<li>${audCountRS.insNonMockCount} Non-Mocks and
								${audCountRS.insMockCount} mocks</li>
							<li>${audCountRS.insPrePayCount} Pre-pays and
								${audCountRS.insPostPayCount} Post-pays</li>
						</ul></li>
					<br>
					<li><spring:message
							code="audit.new.popup.count.out.sample.audits" />
						--------------------- ${audCountRS.oosCount}
						<ul style="list-style-type:disc">
							<li>${audCountRS.oosNonMockCount} Non-Mocks and
								${audCountRS.oosMockCount} mocks</li>
							<li>${audCountRS.oosPrePayCount} Pre-pays and
								${audCountRS.oosPostPayCount} Post-pays</li>
						</ul></li>
					<br> *****************************************************
					<br>
					<li><spring:message code="audit.new.popup.count.total.audits" />
						-------------------------------- ${audCountRS.totalAuditCount}</li>
				</ol>
			</div> </span>
		<div align="right" style="width: 450">
			<table>
				<tr>

					<a onclick="divCount_hide();">
						<button class="button default">
							<spring:message code="audit.new.popup.count.ok" />
						</button>
					</a>

				</tr>

			</table>

		</div>
	</div>


	<!-- DCN check popup -->

	<div id="popupDcnCheckD">
		<!-- popup claim records Starts Here -->


	</div>
	<div id="popupDcnD"
		style="text-align: left; padding: 25px 25px 25px 25px;border-radius: 5px;
    box-shadow: 5px 5px 5px #888888;">
		<img id="close" src="webResources/images/Misc/Icn_Close2.png"
			onclick="divDcn_hide()"> <span style="font-size: 15px;"><b>
			<c:choose>
    			<c:when test="${empty dupDcnResult}">
       				 Click on Add New to add audit.
    			</c:when>
    			<c:otherwise>
        			Audit for this DCN is already exist ! Please click on dcn no to
				update that audit or click on Add New to add audit.
   			 </c:otherwise>
			</c:choose>
			<%--Audit for this DCN is already exist ! Please click on dcn no to
				update that audit or click on Add New to add audit.
				--%>
				<div style="padding:10px 0px 15px 0px">

					<table class="table striped hovered dataTable"
						id="searchDCNDatatable" width="700px">
						<thead>
							<tr style="height: 30px">

								<td class="text-left" font-size="25px"><spring:message
										code="audit.search.results.audit.id" /></td>
								<td class="text-left" font-size="25px" style="padding-left:5px"></a>><spring:message
										code="audit.new.claim.dcn" /></td>
								<td class="text-left" font-size="25px"><spring:message
										code="audit.new.associate" /></td>
								<td class="text-left" font-size="25px"><spring:message
										code="audit.search.results.processed.date" /></td>
								<td class="text-left" font-size="25px"><spring:message
										code="audit.search.results.auditor" /></td>
								<td class="text-left" font-size="25px"><spring:message
										code="audit.search.results.audit.date" /></td>


							</tr>
						</thead>

						<tbody>

							<c:forEach items="${dupDcnResult}" var="rs">
								<tr style="height: 30px">
									<td>${rs.auditId}</td>
									<td style="padding-left:5px">${rs.dcn}</td>
									<td>${rs.associateName}</td>
									<td>${rs.processedDateFrom}</td>
									<td>${rs.auditorName}</td>
									<td>${rs.auditDateFrom}</td>
								</tr>
							</c:forEach>

						</tbody>


					</table>

				</div>

				<div align="right" style="width: 450">
					<table>
						<tr>

							<a onclick="validateDCN('Y');divDcn_hide();">
								<button class="button default">Add New</button>
							</a>

						</tr>

					</table>

				</div>
	</div>
	
	<jsp:include page="../jsp/highDollar.jsp"></jsp:include>



</body>
<!--Dup DCn JS  -->
<script type="text/javascript">
	$(function() {
		$('#searchDCNDatatable').dataTable(
				{

					"columns" : [

							{

								sClass : "hidden"
							},
							{
								"render" : function(data, type, full, meta) {				
									/* return '<a href="'+full[0]+'"><u>'+data+'<u></a>'; */
									return '<a href="editAuditGeneral?id='
											+ full[0] + '&dcnNo=' + data
											+ '"><u>' + data + '<u></a>';

								}
							}, {}, {}, {}, {} ]
				});
	});
</script>

<script type="text/javascript">
	$(function() {
		$('.headerA').click(function() {
			$(this).closest('.containerA').toggleClass('collapsed');
		});

	});

	/* Script for checkbox toggle */
	var $checkBox = $('#chkbox'), $select = $('#pilist');
	$input = $('#input');
	$button = $('#calPiButton');

	$checkBox.on('change', function(e) {
		if ($(this).is(':checked')) {
			$select.removeAttr('disabled');
			$input.removeAttr('disabled');
			$button.removeAttr('disabled');
		} else {
			$select.attr('disabled', 'disabled');
			$input.attr('disabled', 'disabled');
			$button.attr('disabled', 'disabled');
		}
	});

	var comm = $("input[name='fyicheck']:checked").val();
	if (comm == "Y") {
		$("#fyin").removeAttr("disabled");
	} else {
		$("#fyin").attr("disabled", "disabled");
	}

	/*Script for high doller  */
	$(document)
			.ready(
					function() {
						highDollarValidations();
					});
	if ($("input[name=monetaryError]:checked", "#auditForm").val() == "N") {
		$("#amountp").attr("disabled", "disabled");
		$("#theoreticalPaid").attr("disabled", "disabled");
	} else {
		$("#amountp").removeAttr("disabled");
		$("#theoreticalPaid").removeAttr("disabled");
	}
	/* Script for enable disable amount and theo paid*/
	$("input[name='monetaryError']").click(function() {
		if ($("input[name=monetaryError]:checked", "#auditForm").val() == "N") {
			$("#amountp").attr("disabled", "disabled");
			$("#theoreticalPaid").attr("disabled", "disabled");
		} else {
			$("#amountp").removeAttr("disabled");
			$("#theoreticalPaid").removeAttr("disabled");
		}
	});

	/* Script for enable disable FYI*/
	$("input[name='fyicheck']").click(function() {
		if ($(this).attr("id") == "fychk") {
			$("#fyin").attr("disabled", "disabled");
		} else {
			$("#fyin").removeAttr("disabled");
		}
	});
	
	
	/*Enhancement for DMND0002873*/
	$("#paids").change(function () {
	mandatoryFYI();
	});
	
	function mandatoryFYI(){
	
		var amt=$("#paids").val();
		console.log("Total Paid amount "+amt);
		if (amt>=40000.00){
			//alert("FYI mandatory");
			$("#fyin").removeAttr("disabled");
			$("#fychk").removeAttr("checked");
			$("#fychky").attr("checked","checked");
		}
	}
	
	/*Enhancement as per SVET011597*/
	$("#paids").change(function () {
	highDollarValidations();
	if ($("input[name=monetaryError]:checked", "#auditForm").val() != "N") {	
			calculateThePaid();
		}
	});
	
	function highDollarValidations(){
		if ($("#paids").val() > 4999.99) {
				$("#correct").show("fast"); //Slide Down Effect
			} else {
				$("#correct").hide("fast"); //Slide Up Effect
			}
		if (null != document.getElementById("paids")) {
							if ($("#paids").val() > 39999.99 && $("#paids").val() <100000.00) { //Changes made as per request - RITM0840801 
								console.log("paid > 39999.99 and < 100000.00"
										+ $("#error").html());
								$("#errorHigh")
										.html(
												"<p style='color:red;padding-left:0px'>NOTE:  Claim is between $40000-99999.99, forensic review required. Please Verify!<p>");
								$("#paids").focus();
								$("#errorHigh").css({
									"display" : "block"
								});
								$("#highDollarLink").css({
									"display" : "block"
								});

							}
							else if ($("#paids").val() >= 100000.00) { //Changes made as per request - RITM0840801 
								console.log("paid >= 100000.00"
										+ $("#error").html());
								$("#errorHigh")
										.html(
												"<p style='color:red;padding-left:0px'>NOTE:  Claim is 100k or greater, forensic review and VP signoff required.  Please Verify!<p>");
								$("#paids").focus();
								$("#errorHigh").css({
									"display" : "block"
								});
								$("#highDollarLink").css({
									"display" : "block"
								});

							}
							 else {
								$("#highDollarLink").css({
									"display" : "none"
								});
								$("#errorHigh").css({
									"display" : "none"
								});
							}
						} else {
							$("#highDollarLink").css({
								"display" : "none"
							});
							$("#errorHigh").css({
								"display" : "none"
							});
						}  
	}
	/*Enhancement as per SVET011597*/
	
	/* Script for auto theo paid */
	$("#amountp").change(function() {
		calculateThePaid();
	});

	function calculateThePaid() {
		
		var a = parseFloat(Math.round(($("#amountp").val()) * 100) / 100).toFixed(2);
		var b = parseFloat(Math.round(($("#paids").val()) * 100) / 100).toFixed(2);
		/* var a = $("#amountp").val();
		var b = $("#paids").val(); */

		if ($("input[name=monetaryError]:checked", "#auditForm").val() == "U") {
			console.log("under");
			var sum = (+a + +b);
			var summ = parseFloat(Math.round(sum * 100) / 100).toFixed(2);
			/* var diff = parseFloat((dif).toFixed(2)); */
			$("#theoreticalPaid").val(summ);
		} else if ($("input[name=monetaryError]:checked", "#auditForm").val() == "O") {
			var dif = (+b - +a);
			var diff = parseFloat(Math.round(dif * 100) / 100).toFixed(2);
			$("#theoreticalPaid").val(diff);
		} else if ($("input[name=monetaryError]:checked", "#auditForm").val() == "N") {
			console.log("none");
			$("#theoreticalPaid").val("");
			$("#amountp").val("");
		}
	}
	processTypeChanged();
</script>

<script type="text/javascript">
				function getSecPGADropdown(val) {
					if (val == null) {
						var priId = document.getElementById("priPGA").value;
					} 
					var url = "secPGADropDown";
					console.log("inptA--->" + priId);
					$.ajax({
						type : "GET",
						url : url,
						dataType : "html",
						data : {
							'priId' : priId
						},
						success : function(response) {
							$("#secPGA").html(response);
						},
						error : function() {

						}

					});
				}
				
				
				$(function() {
					getSecPGADropdown(null);
				});
			</script>

<script type="text/javascript" src="webResources/js/jquery.loadmask.js"></script>
</html>


