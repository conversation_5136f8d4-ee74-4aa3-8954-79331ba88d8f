<!DOCTYPE HTML><%@page language="java"
	contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<html>
	<input type="hidden" id="claimAdjFlag" name="claimAdjFlag" value="${claimAdjFlag}" />
	<input type="hidden" id="serviceAdjFlag" name="serviceAdjFlag" value="${serviceAdjFlag}" />
	<input type="hidden" id="QAName" name="QAName" value="${QAName}" />
	<input type="hidden" id="patientName" name="patientName" value="${patientName}" />
	<input type="hidden" id="serviceDates" name="serviceDates" value="${serviceDates}" />
	<input type="hidden" id="typeOfService" name="typeOfService" value="${typeOfService}" />
	<input type="hidden" id="diagnosis" name="diagnosis" value="${diagnosis}" />
	<input type="hidden" id="surgeryDOS" name="surgeryDOS" value="${surgeryDOS}" />
	<input type="hidden" id="surgery" name="surgery" value="${surgery}" />
	<input type="hidden" id="fileReferenced" name="fileReferenced" value="${fileReferenced}" />
	<input type="hidden" id="interestPaid" name="interestPaid" value="${interestPaid}" />
	<input type="hidden" id="providerName" name="providerName" value="${providerName}" />
	<input type="hidden" id="providerNumber" name="providerNumber" value="${providerNumber}" />
	<input type="hidden" id="payee" name="payee" value="${payee}" />
	<input type="hidden" id="notes" name="notes" value="${notes}" />
	<input type="hidden" id="audSignDate" name="audSignDate" value="${audSignDate}" />
	<input type="hidden" id="vpSignDate" name="vpSignDate" value="${vpSignDate}" />
	<input type="hidden" id="forwrdToDate" name="forwrdToDate" value="${forwrdToDate}" />
	<input type="hidden" id="rcvedFrmDate" name="rcvedFrmDate" value="${rcvedFrmDate}" />
	<input type="hidden" id="releasedByDate" name="releasedByDate" value="${releasedByDate}" />
</html>