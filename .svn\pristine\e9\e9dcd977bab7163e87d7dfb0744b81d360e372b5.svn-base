<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="tree-template" pageWidth="595" pageHeight="842"  whenNoDataType="NoDataSection" columnWidth="535"  >
	<property name="com.jasperassistant.designer.Grid" value="false"/>
	<property name="com.jasperassistant.designer.SnapToGrid" value="false"/>
	<property name="com.jasperassistant.designer.GridWidth" value="12"/>
	<property name="com.jasperassistant.designer.GridHeight" value="12"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	
	<parameter name="JasperCustomSubReportLocation1" class="net.sf.jasperreports.engine.JasperReport"/>
	<parameter name="JasperCustomSubReportDatasource1" class="net.sf.jasperreports.engine.data.JRBeanCollectionDataSource"/>
	<parameter name="JasperCustomSubReportLocation2" class="net.sf.jasperreports.engine.JasperReport"/>
	<parameter name="JasperCustomSubReportDatasource2" class="net.sf.jasperreports.engine.data.JRBeanCollectionDataSource"/>
	
	<field name="isTotal" class="java.lang.String">
		<fieldDescription><![CDATA[isTotal]]></fieldDescription>
	</field>
	<field name="isTotalMonetary" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalMonetary]]></fieldDescription>
	</field>
	<field name="isTotalProcedural" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalProcedural]]></fieldDescription>
	</field>
	
	<background>
		<band height="777" splitType="Stretch">
			
			<textField>
				<reportElement x="381" y="745" width="100" height="30" forecolor="#000080"  />
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="481" y="745" width="100" height="30" forecolor="#000080"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="EEEE, MMMMM dd, yyyy">
				<reportElement x="4" y="745" width="133" height="17" forecolor="#000080"  />
				<textElement>
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="140" y="745" width="260" height="16" forecolor="#000080"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Quality Assurance Productivity Management]]></text>
			</staticText>
			<rectangle>
				<reportElement x="0" y="743" width="555" height="3" forecolor="#FFFFFF" backcolor="#C0C0C0"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
			
		</band>
	</background>
	<title>
		<band height="40" splitType="Stretch">
			<printWhenExpression><![CDATA[!($P{JasperCustomSubReportDatasource1}.getData()).isEmpty()]]></printWhenExpression>
			<subreport>
				<reportElement x="-20" y="-35" width="176" height="10">
                </reportElement>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource ($P{JasperCustomSubReportDatasource1}.getData())]]></dataSourceExpression>
				<subreportExpression class="net.sf.jasperreports.engine.JasperReport"><![CDATA[$P{JasperCustomSubReportLocation1}]]></subreportExpression>
			</subreport>
			<break>
				<reportElement x="0" y="0" width="555" isRemoveLineWhenBlank="true" height="1"/>
			</break>
		</band>
	</title>
	<summary>
		<band height="0" splitType="Stretch">
			<printWhenExpression><![CDATA[!($P{JasperCustomSubReportDatasource2}.getData()).isEmpty()]]></printWhenExpression>
			<subreport>
				<reportElement x="-20" y="-35" width="200" height="1"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource ($P{JasperCustomSubReportDatasource2}.getData())]]></dataSourceExpression>
				<subreportExpression class="net.sf.jasperreports.engine.JasperReport"><![CDATA[$P{JasperCustomSubReportLocation2}]]></subreportExpression>
			</subreport>
		</band>
	</summary>
	<noData>
		<band height="55" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="187" y="0" width="250" height="47" forecolor="#000050">
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="22" isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[No records found!!]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>
