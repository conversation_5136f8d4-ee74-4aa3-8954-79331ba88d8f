/**
 * QASamplingResponse.java
 *
 * This file was auto-generated from WSDL
 * by the IBM Web services WSDL2Java emitter.
 * cf011339.01 v10113164338
 */

package com.carefirst.audit.model;

public class QASamplingResponse  {
    private com.carefirst.audit.model.StatusBlock statusBlock;
    private com.carefirst.audit.model.SubscriberLevel subscriberLevel;
    private com.carefirst.audit.model.ClaimLevel claimLevel;
    private com.carefirst.audit.model.ProductLevel productLevel;
    private com.carefirst.audit.model.AdjustmentLevel adjustmentLevel;
    private com.carefirst.audit.model.ClstStatusLines clstStatusLines;
    private String CJAJurisdictionCode;
    private String platform;
    
    public java.lang.String getPlatform() {
        return platform;
    }

    public void setPlatform(java.lang.String platform) {
        this.platform = platform;
    }

    public QASamplingResponse() {
    }

    public com.carefirst.audit.model.StatusBlock getStatusBlock() {
        return statusBlock;
    }

    public void setStatusBlock(com.carefirst.audit.model.StatusBlock statusBlock) {
        this.statusBlock = statusBlock;
    }

    public com.carefirst.audit.model.SubscriberLevel getSubscriberLevel() {
        return subscriberLevel;
    }

    public void setSubscriberLevel(com.carefirst.audit.model.SubscriberLevel subscriberLevel) {
        this.subscriberLevel = subscriberLevel;
    }

    public com.carefirst.audit.model.ClaimLevel getClaimLevel() {
        return claimLevel;
    }

    public void setClaimLevel(com.carefirst.audit.model.ClaimLevel claimLevel) {
        this.claimLevel = claimLevel;
    }

    public com.carefirst.audit.model.ProductLevel getProductLevel() {
        return productLevel;
    }

    public void setProductLevel(com.carefirst.audit.model.ProductLevel productLevel) {
        this.productLevel = productLevel;
    }

    public com.carefirst.audit.model.AdjustmentLevel getAdjustmentLevel() {
        return adjustmentLevel;
    }

    public void setAdjustmentLevel(com.carefirst.audit.model.AdjustmentLevel adjustmentLevel) {
        this.adjustmentLevel = adjustmentLevel;
    }

    public com.carefirst.audit.model.ClstStatusLines getClstStatusLines() {
        return clstStatusLines;
    }

    public void setClstStatusLines(com.carefirst.audit.model.ClstStatusLines clstStatusLines) {
        this.clstStatusLines = clstStatusLines;
    }

    public java.lang.String getCJAJurisdictionCode() {
        return CJAJurisdictionCode;
    }

    public void setCJAJurisdictionCode(java.lang.String CJAJurisdictionCode) {
        this.CJAJurisdictionCode = CJAJurisdictionCode;
    }

}
