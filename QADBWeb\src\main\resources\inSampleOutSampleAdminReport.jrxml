<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="scriptlet" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="30" bottomMargin="30" whenNoDataType="NoDataSection" whenResourceMissingType="Empty" >
	<property name="com.jasperassistant.designer.Grid" value="false"/>
	<property name="com.jasperassistant.designer.SnapToGrid" value="false"/>
	<property name="com.jasperassistant.designer.GridWidth" value="12"/>
	<property name="com.jasperassistant.designer.GridHeight" value="12"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="net.sf.jasperreports.awt.ignore.missing.font" value="true"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="month" class="java.lang.String">
		<fieldDescription><![CDATA[month]]></fieldDescription>
	</field>
	<field name="year" class="java.lang.String">
		<fieldDescription><![CDATA[year]]></fieldDescription>
	</field>
	<field name="auditorName" class="java.lang.String">
		<fieldDescription><![CDATA[auditorName]]></fieldDescription>
	</field>
	<field name="associateName" class="java.lang.String">
		<fieldDescription><![CDATA[associateName]]></fieldDescription>
	</field>
	<field name="isMock" class="java.lang.String">
		<fieldDescription><![CDATA[isMock]]></fieldDescription>
	</field>
	<field name="isNonMock" class="java.lang.String">
		<fieldDescription><![CDATA[isNonMock]]></fieldDescription>
	</field>
	<field name="oosMock" class="java.lang.String">
		<fieldDescription><![CDATA[oosMock]]></fieldDescription>
	</field>
	<field name="oosNonMock" class="java.lang.String">
		<fieldDescription><![CDATA[oosNonMock]]></fieldDescription>
	</field>
	
	<field name="sumIsMock" class="java.lang.String">
		<fieldDescription><![CDATA[sumIsMock]]></fieldDescription>
	</field>
	<field name="sumIsNonMock" class="java.lang.String">
		<fieldDescription><![CDATA[sumIsNonMock]]></fieldDescription>
	</field>
	<field name="sumOosMock" class="java.lang.String">
		<fieldDescription><![CDATA[sumOosMock]]></fieldDescription>
	</field>
	<field name="sumOosNonMock" class="java.lang.String">
		<fieldDescription><![CDATA[sumOosNonMock]]></fieldDescription>
	</field>
	
	<group name="dummy"> 		
	<groupExpression><![CDATA["dummy"]]></groupExpression> 
		<groupHeader>
			
		</groupHeader>
	</group>
	
	<title>
		<band height="40" splitType="Stretch">
			<textField>
				<reportElement x="27" y="5" width="500" height="30" forecolor="#000080"  />
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="20" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["In Sample - Out Sample Report For "+$F{month}+" "+$F{year}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="0" y="35" width="555" height="3" forecolor="#FFFFFF" backcolor="#C0C0C0"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
		</band>
	</title>
	<pageHeader>
		<band height="30" splitType="Stretch">
			<rectangle>
				<reportElement x="0" y="26" width="555" height="3" forecolor="#FFFFFF" backcolor="#C0C0C0"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="4" y="3" width="72" height="18" forecolor="#000080"  />
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Auditor]]></text>
			</staticText>
			<staticText>
				<reportElement x="137" y="3" width="72" height="18" forecolor="#000080"  />
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Associate]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="254" y="0" width="72" height="18" forecolor="#000080" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA["In Sample Mock"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="326" y="0" width="72" height="18" forecolor="#000080" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Out Of Sample Mock"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="404" y="0" width="72" height="18" forecolor="#000080" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA["In Sample Non-Mock"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="482" y="0" width="72" height="18" forecolor="#000080" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Out Of Sample Non-Mock"]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band/>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement x="4" y="1" width="126" height="10"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{auditorName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="130" y="1" width="134" height="10"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{associateName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="256" y="1" width="70" height="10"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isMock}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="326" y="1" width="70" height="10"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosMock}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="404" y="1" width="70" height="10"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isNonMock}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="483" y="1" width="70" height="10"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosNonMock}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="56" splitType="Stretch">
			<textField>
				<reportElement x="381" y="20" width="100" height="30" forecolor="#000080"  />
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="481" y="20" width="100" height="30" forecolor="#000080"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="EEEE, MMMMM dd, yyyy">
				<reportElement x="4" y="20" width="133" height="17" forecolor="#000080"  />
				<textElement>
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="140" y="20" width="260" height="16" forecolor="#000080"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Quality Assurance Productivity Management]]></text>
			</staticText>
			<rectangle>
				<reportElement x="0" y="16" width="555" height="3" forecolor="#FFFFFF" backcolor="#C0C0C0"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
		</band>
	</pageFooter>
	<lastPageFooter>
		<band height="56" splitType="Stretch">
			<textField>
				<reportElement x="381" y="20" width="100" height="30" forecolor="#000080"  />
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="481" y="20" width="100" height="30" forecolor="#000080"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="EEEE, MMMMM dd, yyyy">
				<reportElement x="4" y="20" width="133" height="17" forecolor="#000080"  />
				<textElement>
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="140" y="20" width="260" height="16" forecolor="#000080"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Quality Assurance Productivity Management]]></text>
			</staticText>
			<rectangle>
				<reportElement x="0" y="16" width="555" height="3" forecolor="#FFFFFF" backcolor="#C0C0C0"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
		</band>
	</lastPageFooter>
	<summary>
		<band height="24">
			<staticText>
				<reportElement x="226" y="3" width="38" height="18" forecolor="#000080"  />
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Sum]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement x="256" y="4" width="70" height="12"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{sumIsMock}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="326" y="4" width="70" height="12"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{sumOosMock}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="404" y="4" width="70" height="12"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{sumIsNonMock}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="483" y="4" width="70" height="12"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{sumOosNonMock}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="0" y="0" width="555" height="2" forecolor="#FFFFFF" backcolor="#000080"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
		</band>
	</summary>
	<noData>
		<band height="55" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="180" y="0" width="316" height="47" forecolor="#000050">
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="22" isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[No records found!!]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>
