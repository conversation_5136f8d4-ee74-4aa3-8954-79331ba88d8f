package com.carefirst.qadb.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.sql.DataSource;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import oracle.jdbc.OracleTypes;

import org.apache.log4j.Logger;
import org.osoa.sca.annotations.Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import java.sql.*;

import com.carefirst.audit.model.Associate;
import com.carefirst.audit.model.OperationalUnit;
import com.carefirst.qadb.constant.QADBConstants;

import javax.naming.*;


public class AssociateDAOImpl implements AssociateDAO {

	//private static final Logger logger = Logger.getLogger(AssociateDAOImpl.class);
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);
	
	@Autowired
	DataSource dataSource;

	@Override
	public List<Associate> getJobTitles() throws SQLException {
		String sql = QADBConstants.ADMIN_ASSOCIATE_QUERIES_JOB_TITLES_LIST;
		
		List<Associate> jobTitles = new ArrayList<Associate>();
		
		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
					Associate jobTitle = new Associate();
					jobTitle.setJobTitleId((rs.getString("JOB_TITLE_ID")).toString());
					jobTitle.setJobTitles((String) (rs.getString("JOB_TITLE_NAME")));
					
					jobTitles.add(jobTitle);
				}	
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		}finally{
			callableStatment.close();
			conn.close();
		}
		return jobTitles;
	}

	@Override
	public List<Associate> getLocations() throws SQLException  {
		String sql = QADBConstants.ADMIN_ASSOCIATE_QUERIES_LOCATIONS_LIST;
		
		List<Associate> locations = new ArrayList<Associate>();
		
		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
					Associate location = new Associate();
					location.setLocationId((rs.getString("LOCATION_ID")).toString());
					location.setLocations((String) (rs.getString("LOCATION_DESC")));
					locations.add(location);
				}
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		}finally{
			callableStatment.close();
			conn.close();
		}
		return locations;
	}
	
	
	@Override
	public Associate saveUpdateAssociate(Associate associateDetails) throws Exception {
		logger.debug("Entry saveUpdateAssociate method." + associateDetails.getFirstName());
		String associateSp = QADBConstants.ADMIN_ASSOCIATE_SAVE_SP;
		Connection conn = null;
		CallableStatement callableStatment = null;
		Associate associateRO = new Associate();
		
		try {
			//SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
			conn = dataSource.getConnection();
			callableStatment = conn.prepareCall(associateSp);
			
			if((associateDetails.getUserActyp()).equals("NEW") || (associateDetails.getUserActyp()).equals("EDIT")){
				
				callableStatment.setString(1, associateDetails.getUserid());
				callableStatment.setString(2, associateDetails.getUserActyp());
				if(null != associateDetails.getAssociateId()){
					callableStatment.setInt(3,Integer.parseInt(associateDetails.getAssociateId()));
				}else{
					callableStatment.setNull(3, java.sql.Types.INTEGER);
				}
				callableStatment.setString(4, associateDetails.getFirstName());
				callableStatment.setString(5, associateDetails.getMiddleName());
				callableStatment.setString(6, associateDetails.getLastName());
				callableStatment.setInt(7, Integer.parseInt(associateDetails.getJobTitleId()));
				//callableStatment.setDate(8, new java.sql.Date((sdf.parse(associateDetails.getDateOfHire())).getTime()));
				callableStatment.setString(8, associateDetails.getDateOfHire());
				callableStatment.setInt(9, Integer.parseInt(associateDetails.getLocationId()));
				callableStatment.setInt(10, Integer.parseInt(associateDetails.getWorkUnitId())); //work unit id
				callableStatment.setString(11,associateDetails.getWorkUnitStartDate());
				callableStatment.setString(12, associateDetails.getWorkUnitEndDate());
				callableStatment.setString(13, associateDetails.getComments());
				callableStatment.setString(14, (associateDetails.getWorkPhone()).replaceAll("\\D", ""));
				callableStatment.setString(15,associateDetails.getAuditingStatus());
				if(((associateDetails.getJobTitleId()).equals("1")) && (associateDetails.getAuditingStatus().equals("N"))){
					callableStatment.setString(16, associateDetails.getDisableDate());
				}else{
					callableStatment.setNull(16, java.sql.Types.DATE);
				}
				callableStatment.setString(17, associateDetails.getFacetsId());
				callableStatment.registerOutParameter(18, OracleTypes.VARCHAR);
				callableStatment.registerOutParameter(19, OracleTypes.VARCHAR);
				
				callableStatment.execute();
				
				logger.debug("success code = "+callableStatment.getString(18));
				logger.debug("success message"+callableStatment.getString(19));
				 
				associateRO.setSucessCode(callableStatment.getString(18));
				associateRO.setSuccessMsg(callableStatment.getString(19));
				
			}
			
		} catch (Exception e) {
			logger.debug("exception = "+e.getMessage());
			conn.rollback();
			e.printStackTrace();
			
		}finally {
			if (callableStatment != null) {
				callableStatment.close();
			}

			if (conn != null) {
				conn.close();
			}
		}
		logger.debug("Exit saveUpdateAssociate method.");
		return associateRO;
	}


	@Override
	public List<Associate> getAssociateSearchResults(Associate associateSearchTO) throws SQLException {
		
		logger.debug("*** Entry getAssociateSearchResults method ***");
		logger.debug("user id "+associateSearchTO.getUserid());
		String getAssociates =QADBConstants.ADMIN_ASSOCIATE_SEARCH_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getAssociates);
		List<Associate> associatesList= new ArrayList<Associate>();
		try {
			
			callableStatment.setString(1, associateSearchTO.getUserid());
			callableStatment.setString(2, associateSearchTO.getSearchType());
			callableStatment.setString(3, associateSearchTO.getFacetsId());
			callableStatment.setString(4, associateSearchTO.getAssociateName());
			callableStatment.setNull(5, java.sql.Types.VARCHAR);
			callableStatment.registerOutParameter(6, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(7, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(8, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(8));
			callableStatment.executeUpdate();
			
			ResultSet rs =   (ResultSet) callableStatment.getObject(6);			
			logger.debug("Row count" +rs.getRow());
			
			while (rs.next()) {
				Associate associate = new Associate();
				if(null!=rs.getString("ASSOCIATE_ID")){
					associate.setAssociateId((rs.getString("ASSOCIATE_ID")).toString());
				}
				if(null!=rs.getString("ASSOCIATE_NAME")){
					associate.setAssociateName((rs.getString("ASSOCIATE_NAME")).toString());
				}
				if(null!=rs.getString("TITLE")){
					associate.setJobTitles((rs.getString("TITLE")).toString());
				}
				if(null!=rs.getString("STATUS")){
					associate.setAuditingStatus((rs.getString("STATUS")).toString());
				}
				associatesList.add(associate);
			}
			rs.close();
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}finally{
			callableStatment.close();
			conn.close();
		}
		logger.debug("*** Exit getJobTitles method ***");
		return associatesList;
	}


	@Override
	public List<OperationalUnit> getWorkUnits() throws SQLException {
		logger.debug("Entry getWorkUnits method.");
		String sql =QADBConstants.ADMIN_ASSOCIATE_QUERIES_WORK_UNITS_LIST;
		
		List<OperationalUnit> workUnitsRS = new ArrayList<OperationalUnit>();
		
		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
					
					OperationalUnit workUnit = new OperationalUnit();
					
					if(null != (rs.getString("UNIT_ID"))){
						workUnit.setUnitId((rs.getString("UNIT_ID")).toString());
					}
					else{
						workUnit.setUnitId("");
					}
					if(null != (rs.getString("UNIT_NAME"))){
						workUnit.setUnitName((rs.getString("UNIT_NAME")).toString());
					}
					else{
						workUnit.setUnitName("");
					}
					if(null != (rs.getString("UNIT_SUPERVISOR"))){
						workUnit.setSupervisor((rs.getString("UNIT_SUPERVISOR")).toString());
					}
					else{
						workUnit.setSupervisor("");
					}
					
					workUnitsRS.add(workUnit);
				}
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}
		
		logger.debug("Exit getWorkUnits method.");
		return workUnitsRS;
		
	}

	@Override
	public Associate getAssociate(Associate associateSearchTO) throws SQLException {
		
		String getAssociateDetails = QADBConstants.ADMIN_ASSOCIATE_SEARCH_SP;
		
		Associate associateEO = new Associate();
		Connection conn = null;
		CallableStatement callableStatment = null;		
		try {
			conn = dataSource.getConnection();
			callableStatment = conn.prepareCall(getAssociateDetails);
			
			callableStatment.setString(1,associateSearchTO.getUserid());
			callableStatment.setString(2,associateSearchTO.getSearchType());
			callableStatment.setInt(3,Integer.parseInt(associateSearchTO.getAssociateId()));
			callableStatment.setNull(4, java.sql.Types.VARCHAR);
			callableStatment.setNull(5, java.sql.Types.VARCHAR);
			callableStatment.registerOutParameter(6, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(7, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(8, OracleTypes.VARCHAR);
			
			callableStatment.executeUpdate();
			ResultSet rs =   (ResultSet) callableStatment.getObject(6);			
			logger.debug("RS "+rs);
			logger.debug("Row count" +rs.getRow());
			logger.debug(callableStatment.getString(7));
			
			while (rs.next()) {
				logger.debug("AssoName Edit"+rs.getString("ASSOCIATE_FIRST_NAME")+ " super "+rs.getString("ASSOCIATE_PHONE_NBR")+ "man " +rs.getString("ASSOCIATE_COMMENTS_TEXT"));
				if(null!=rs.getString("ASSOCIATE_ID")){
					associateEO.setAssociateId((rs.getString("ASSOCIATE_ID")).toString());
				}
				if(null!=rs.getString("ASSOCIATE_FIRST_NAME")){
					String name=((rs.getString("ASSOCIATE_FIRST_NAME")).toString()).replaceAll("\"","&#34;");
					associateEO.setFirstName(name);
				}
				if(null!=(rs.getString("ASSOCIATE_MIDDLE_NAME"))){
					associateEO.setMiddleName((rs.getString("ASSOCIATE_MIDDLE_NAME")).toString());
				}
				if(null!=(rs.getString("ASSOCIATE_LAST_NAME"))){
					associateEO.setLastName((rs.getString("ASSOCIATE_LAST_NAME")).toString());
				}
				if(null!=rs.getString("JOB_TITLE_ID")){
					associateEO.setJobTitleId((rs.getString("JOB_TITLE_ID")).toString());
				}
				if(null!=rs.getString("HIRE_DT")){
					associateEO.setDateOfHire((rs.getString("HIRE_DT").toString()));
				}
				if(null!=rs.getString("LOCATION_ID")){
					associateEO.setLocationId((rs.getString("LOCATION_ID")).toString());
				}
				if(null!=rs.getString("ASSOCIATE_PHONE_NBR")){
					associateEO.setWorkPhone((rs.getString("ASSOCIATE_PHONE_NBR")).toString());
				}
				if(null!=rs.getString("UNIT_ID")){
					associateEO.setWorkUnitId((rs.getString("UNIT_ID")).toString());
				}
				if(null!=rs.getString("ASSOCIATE_WORK_START_DT") && null!=rs.getString("ASSOCIATE_WORK_END_DT")){
					associateEO.setWorkUnitStartDate((rs.getString("ASSOCIATE_WORK_START_DT")));
					associateEO.setWorkUnitEndDate((rs.getString("ASSOCIATE_WORK_END_DT").toString()));
				}
				if(null!=(rs.getString("ASSOCIATE_FACETS_ID"))){
					associateEO.setFacetsId((rs.getString("ASSOCIATE_FACETS_ID")).toString());
				}
				if(null!=(rs.getString("ASSOCIATE_COMMENTS_TEXT"))){
					associateEO.setComments((rs.getString("ASSOCIATE_COMMENTS_TEXT")).toString());
				}
				associateEO.setAuditingStatus((rs.getString("ASSOCIATE_AUDITING_STATUS")).toString()); 
				if(null!=(rs.getString("ASSOCIATE_DISABLE_DT"))){
					associateEO.setDisableDate((rs.getString("ASSOCIATE_DISABLE_DT")).toString());
				}
			}	
			rs.close();
		} catch (Exception e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}
		return associateEO;
	}

	@Override
	public Associate deleteAssociate(Associate associateDetails) throws SQLException {
		
		String sql = QADBConstants.ADMIN_ASSOCIATE_DELETE +associateDetails.getAssociateId()+ " " ;
		
		Associate associateRO = new Associate();
		
		Connection conn =  null ;
		CallableStatement callableStatment = null;
		
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 callableStatment.executeQuery();
			 associateRO.setSuccessMsg("SUCCESS");
		} catch (Exception e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}
		logger.debug("***Delete Execution complete***");
		return associateRO;
	}

	@Override
	public JRDataSource getAssociatesListReport(Associate associateDetails) throws SQLException {
		
		logger.debug("***getAssociatesListReport entry***");
		logger.debug("selected status " + associateDetails.getAuditingStatus());
		String getAssociateReportDetails = QADBConstants.ADMIN_ASSOCIATE_SEARCH_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getAssociateReportDetails);
		List<Associate> associatesList= new ArrayList<Associate>();
		try {
			callableStatment.setString(1,associateDetails.getUserid());
			callableStatment.setString(2,associateDetails.getSearchType());
			callableStatment.setNull(3, java.sql.Types.INTEGER);
			callableStatment.setNull(4,java.sql.Types.VARCHAR); // Associate name
			if((associateDetails.getAuditingStatus().equals("A"))){
				callableStatment.setNull(5, java.sql.Types.VARCHAR);
			}
			else{
				callableStatment.setString(5,associateDetails.getAuditingStatus()); // status
			}
			callableStatment.registerOutParameter(6, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(7, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(8, OracleTypes.VARCHAR);
			
			callableStatment.executeUpdate();
			ResultSet rs =   (ResultSet) callableStatment.getObject(6);			
			logger.debug("Row count" +rs.getRow());
			
			while (rs.next()) {
				Associate associateEO = new Associate();
				logger.debug("AssoName Edit"+rs.getString("ASSOCIATE_FIRST_NAME")+ " super "+rs.getString("ASSOCIATE_FACETS_ID")+ "man " +rs.getString("JOB_TITLE_NAME"));
				
				associateEO.setAssociateId((rs.getString("ASSOCIATE_ID")).toString());
				associateEO.setFirstName((rs.getString("ASSOCIATE_FIRST_NAME")).toString());
				associateEO.setLastName((rs.getString("ASSOCIATE_LAST_NAME")).toString());
				associateEO.setJobTitles((rs.getString("JOB_TITLE_NAME")).toString());
				
				if(null != (rs.getString("ASSOCIATE_FACETS_ID"))){
					associateEO.setFacetsId((rs.getString("ASSOCIATE_FACETS_ID")).toString());
				}
				else{
					associateEO.setFacetsId("");
				}
				
				if(associateDetails.getAuditingStatus().equalsIgnoreCase("Y")){
					associateEO.setAuditingStatus("Enabled");
				}
				else if(associateDetails.getAuditingStatus().equalsIgnoreCase("N")){
					associateEO.setAuditingStatus("Disabled");
				}
				else{
					associateEO.setAuditingStatus("All");
				}
				associatesList.add(associateEO);
			}
			rs.close();
		} catch (Exception e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}
		
		JRDataSource ds = new JRBeanCollectionDataSource(associatesList);	
		logger.debug("***getAssociatesListReport exit***");
		// Return the wrapped collection
		return ds;
	}

	
}