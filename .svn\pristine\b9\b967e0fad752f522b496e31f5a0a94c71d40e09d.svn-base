@font-face {
  font-family: 'iconFont';
  src: url('../fonts/iconFont.eot');
  src: url('../fonts/iconFont.eot?#iefix') format('embedded-opentype'), url('../fonts/iconFont.woff') format('woff'), url('../fonts/iconFont.ttf') format('truetype'), url('../fonts/iconFont.svg#iconFont') format('svg');
  font-weight: normal;
  font-style: normal;
}
[data-icon]:before {
  font-family: 'iconFont';
  content: attr(data-icon);
  speak: none;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
}
[class*="icon-"] {
  font-family: 'iconFont' !important;
  speak: none;
  font-style: normal;
  font-weight: normal !important;
  font-variant: normal;
  text-transform: none;
  text-decoration: inherit;
  line-height: 1;
  display: inline-block;
  vertical-align: -8%;
  -webkit-font-smoothing: antialiased;
  font-size: inherit;
}
[class*="icon-"].smaller {
  font-size: .7em;
  vertical-align: 6%;
}
[class*="icon-"].large {
  font-size: 1.2em;
  vertical-align: -10%;
}
.icon-newspaper:before {
  content: "\e001";
}
.icon-pencil:before {
  content: "\e002";
}
.icon-droplet:before {
  content: "\e003";
}
.icon-pictures:before {
  content: "\e004";
}
.icon-camera:before {
  content: "\e005";
}
.icon-music:before {
  content: "\e006";
}
.icon-film:before {
  content: "\e007";
}
.icon-camera-2:before {
  content: "\e008";
}
.icon-spades:before {
  content: "\e009";
}
.icon-clubs:before {
  content: "\e00a";
}
.icon-diamonds:before {
  content: "\e00b";
}
.icon-broadcast:before {
  content: "\e00c";
}
.icon-mic:before {
  content: "\e00d";
}
.icon-book:before {
  content: "\e00e";
}
.icon-file:before {
  content: "\e00f";
}
.icon-new:before {
  content: "\e010";
}
.icon-copy:before {
  content: "\e011";
}
.icon-folder:before {
  content: "\e012";
}
.icon-folder-2:before {
  content: "\e013";
}
.icon-tag:before {
  content: "\e014";
}
.icon-cart:before {
  content: "\e015";
}
.icon-basket:before {
  content: "\e016";
}
.icon-calculate:before {
  content: "\e017";
}
.icon-support:before {
  content: "\e018";
}
.icon-phone:before {
  content: "\e019";
}
.icon-mail:before {
  content: "\e01a";
}
.icon-location:before {
  content: "\e01b";
}
.icon-compass:before {
  content: "\e01c";
}
.icon-history:before {
  content: "\e01d";
}
.icon-clock:before {
  content: "\e01e";
}
.icon-bell:before {
  content: "\e01f";
}
.icon-calendar:before {
  content: "\e020";
}
.icon-printer:before {
  content: "\e021";
}
.icon-mouse:before {
  content: "\e022";
}
.icon-screen:before {
  content: "\e023";
}
.icon-laptop:before {
  content: "\e024";
}
.icon-mobile:before {
  content: "\e025";
}
.icon-cabinet:before {
  content: "\e026";
}
.icon-drawer:before {
  content: "\e027";
}
.icon-drawer-2:before {
  content: "\e028";
}
.icon-box:before {
  content: "\e029";
}
.icon-box-add:before {
  content: "\e02a";
}
.icon-box-remove:before {
  content: "\e02b";
}
.icon-download:before {
  content: "\e02c";
}
.icon-upload:before {
  content: "\e02d";
}
.icon-database:before {
  content: "\e02e";
}
.icon-flip:before {
  content: "\e02f";
}
.icon-flip-2:before {
  content: "\e030";
}
.icon-undo:before {
  content: "\e031";
}
.icon-redo:before {
  content: "\e032";
}
.icon-forward:before {
  content: "\e033";
}
.icon-reply:before {
  content: "\e034";
}
.icon-reply-2:before {
  content: "\e035";
}
.icon-comments:before {
  content: "\e036";
}
.icon-comments-2:before {
  content: "\e037";
}
.icon-comments-3:before {
  content: "\e038";
}
.icon-comments-4:before {
  content: "\e039";
}
.icon-comments-5:before {
  content: "\e03a";
}
.icon-user:before {
  content: "\e03b";
}
.icon-user-2:before {
  content: "\e03c";
}
.icon-user-3:before {
  content: "\e03d";
}
.icon-busy:before {
  content: "\e03e";
}
.icon-loading:before {
  content: "\e03f";
}
.icon-loading-2:before {
  content: "\e040";
}
.icon-search:before {
  content: "\e041";
}
.icon-zoom-in:before {
  content: "\e042";
}
.icon-zoom-out:before {
  content: "\e043";
}
.icon-key:before {
  content: "\e044";
}
.icon-key-2:before {
  content: "\e045";
}
.icon-locked:before {
  content: "\e046";
}
.icon-unlocked:before {
  content: "\e047";
}
.icon-wrench:before {
  content: "\e048";
}
.icon-equalizer:before {
  content: "\e049";
}
.icon-cog:before {
  content: "\e04a";
}
.icon-pie:before {
  content: "\e04b";
}
.icon-bars:before {
  content: "\e04c";
}
.icon-stats-up:before {
  content: "\e04d";
}
.icon-gift:before {
  content: "\e04e";
}
.icon-trophy:before {
  content: "\e04f";
}
.icon-diamond:before {
  content: "\e050";
}
.icon-coffee:before {
  content: "\e051";
}
.icon-rocket:before {
  content: "\e052";
}
.icon-meter-slow:before {
  content: "\e053";
}
.icon-meter-medium:before {
  content: "\e054";
}
.icon-meter-fast:before {
  content: "\e055";
}
.icon-dashboard:before {
  content: "\e056";
}
.icon-fire:before {
  content: "\e057";
}
.icon-lab:before {
  content: "\e058";
}
.icon-remove:before {
  content: "\e059";
}
.icon-briefcase:before {
  content: "\e05a";
}
.icon-briefcase-2:before {
  content: "\e05b";
}
.icon-cars:before {
  content: "\e05c";
}
.icon-bus:before {
  content: "\e05d";
}
.icon-cube:before {
  content: "\e05e";
}
.icon-cube-2:before {
  content: "\e05f";
}
.icon-puzzle:before {
  content: "\e060";
}
.icon-glasses:before {
  content: "\e061";
}
.icon-glasses-2:before {
  content: "\e062";
}
.icon-accessibility:before {
  content: "\e063";
}
.icon-accessibility-2:before {
  content: "\e064";
}
.icon-target:before {
  content: "\e065";
}
.icon-target-2:before {
  content: "\e066";
}
.icon-lightning:before {
  content: "\e067";
}
.icon-power:before {
  content: "\e068";
}
.icon-power-2:before {
  content: "\e069";
}
.icon-clipboard:before {
  content: "\e06a";
}
.icon-clipboard-2:before {
  content: "\e06b";
}
.icon-playlist:before {
  content: "\e06c";
}
.icon-grid-view:before {
  content: "\e06d";
}
.icon-tree-view:before {
  content: "\e06e";
}
.icon-cloud:before {
  content: "\e06f";
}
.icon-cloud-2:before {
  content: "\e070";
}
.icon-download-2:before {
  content: "\e071";
}
.icon-upload-2:before {
  content: "\e072";
}
.icon-upload-3:before {
  content: "\e073";
}
.icon-link:before {
  content: "\e074";
}
.icon-link-2:before {
  content: "\e075";
}
.icon-flag:before {
  content: "\e076";
}
.icon-flag-2:before {
  content: "\e077";
}
.icon-attachment:before {
  content: "\e078";
}
.icon-eye:before {
  content: "\e079";
}
.icon-bookmark:before {
  content: "\e07a";
}
.icon-bookmark-2:before {
  content: "\e07b";
}
.icon-star:before {
  content: "\e07c";
}
.icon-star-2:before {
  content: "\e07d";
}
.icon-star-3:before {
  content: "\e07e";
}
.icon-heart:before {
  content: "\e07f";
}
.icon-heart-2:before {
  content: "\e080";
}
.icon-thumbs-up:before {
  content: "\e081";
}
.icon-thumbs-down:before {
  content: "\e082";
}
.icon-plus:before {
  content: "\e083";
}
.icon-minus:before {
  content: "\e084";
}
.icon-help:before {
  content: "\e085";
}
.icon-help-2:before {
  content: "\e086";
}
.icon-blocked:before {
  content: "\e087";
}
.icon-cancel:before {
  content: "\e088";
}
.icon-cancel-2:before {
  content: "\e089";
}
.icon-checkmark:before {
  content: "\e08a";
}
.icon-minus-2:before {
  content: "\e08b";
}
.icon-plus-2:before {
  content: "\e08c";
}
.icon-enter:before {
  content: "\e08d";
}
.icon-exit:before {
  content: "\e08e";
}
.icon-loop:before {
  content: "\e08f";
}
.icon-arrow-up-left:before {
  content: "\e090";
}
.icon-arrow-up:before {
  content: "\e091";
}
.icon-arrow-up-right:before {
  content: "\e092";
}
.icon-arrow-right:before {
  content: "\e093";
}
.icon-arrow-down-right:before {
  content: "\e094";
}
.icon-arrow-down:before {
  content: "\e095";
}
.icon-arrow-down-left:before {
  content: "\e096";
}
.icon-arrow-left:before {
  content: "\e097";
}
.icon-arrow-up-2:before {
  content: "\e098";
}
.icon-arrow-right-2:before {
  content: "\e099";
}
.icon-arrow-down-2:before {
  content: "\e09a";
}
.icon-arrow-left-2:before {
  content: "\e09b";
}
.icon-arrow-up-3:before {
  content: "\e09c";
}
.icon-arrow-right-3:before {
  content: "\e09d";
}
.icon-arrow-down-3:before {
  content: "\e09e";
}
.icon-arrow-left-3:before {
  content: "\e09f";
}
.icon-menu:before {
  content: "\e0a0";
}
.icon-enter-2:before {
  content: "\e0a1";
}
.icon-backspace:before {
  content: "\e0a2";
}
.icon-backspace-2:before {
  content: "\e0a3";
}
.icon-tab:before {
  content: "\e0a4";
}
.icon-tab-2:before {
  content: "\e0a5";
}
.icon-checkbox:before {
  content: "\e0a6";
}
.icon-checkbox-unchecked:before {
  content: "\e0a7";
}
.icon-checkbox-partial:before {
  content: "\e0a8";
}
.icon-radio-checked:before {
  content: "\e0a9";
}
.icon-radio-unchecked:before {
  content: "\e0aa";
}
.icon-font:before {
  content: "\e0ab";
}
.icon-paragraph-left:before {
  content: "\e0ac";
}
.icon-paragraph-center:before {
  content: "\e0ad";
}
.icon-paragraph-right:before {
  content: "\e0ae";
}
.icon-paragraph-justify:before {
  content: "\e0af";
}
.icon-left-to-right:before {
  content: "\e0b0";
}
.icon-right-to-left:before {
  content: "\e0b1";
}
.icon-share:before {
  content: "\e0b2";
}
.icon-new-tab:before {
  content: "\e0b3";
}
.icon-new-tab-2:before {
  content: "\e0b4";
}
.icon-embed:before {
  content: "\e0b5";
}
.icon-code:before {
  content: "\e0b6";
}
.icon-bluetooth:before {
  content: "\e0b7";
}
.icon-share-2:before {
  content: "\e0b8";
}
.icon-share-3:before {
  content: "\e0b9";
}
.icon-mail-2:before {
  content: "\e0ba";
}
.icon-google:before {
  content: "\e0bb";
}
.icon-google-plus:before {
  content: "\e0bc";
}
.icon-google-drive:before {
  content: "\e0bd";
}
.icon-facebook:before {
  content: "\e0be";
}
.icon-instagram:before {
  content: "\e0bf";
}
.icon-twitter:before {
  content: "\e0c0";
}
.icon-feed:before {
  content: "\e0c1";
}
.icon-youtube:before {
  content: "\e0c2";
}
.icon-vimeo:before {
  content: "\e0c3";
}
.icon-flickr:before {
  content: "\e0c4";
}
.icon-picassa:before {
  content: "\e0c5";
}
.icon-dribbble:before {
  content: "\e0c6";
}
.icon-deviantart:before {
  content: "\e0c7";
}
.icon-github:before {
  content: "\e0c8";
}
.icon-github-2:before {
  content: "\e0c9";
}
.icon-github-3:before {
  content: "\e0ca";
}
.icon-github-4:before {
  content: "\e0cb";
}
.icon-github-5:before {
  content: "\e0cc";
}
.icon-git:before {
  content: "\e0cd";
}
.icon-github-6:before {
  content: "\e0ce";
}
.icon-wordpress:before {
  content: "\e0cf";
}
.icon-joomla:before {
  content: "\e0d0";
}
.icon-blogger:before {
  content: "\e0d1";
}
.icon-tumblr:before {
  content: "\e0d2";
}
.icon-yahoo:before {
  content: "\e0d3";
}
.icon-amazon:before {
  content: "\e0d4";
}
.icon-tux:before {
  content: "\e0d5";
}
.icon-apple:before {
  content: "\e0d6";
}
.icon-finder:before {
  content: "\e0d7";
}
.icon-android:before {
  content: "\e0d8";
}
.icon-windows:before {
  content: "\e0d9";
}
.icon-soundcloud:before {
  content: "\e0da";
}
.icon-skype:before {
  content: "\e0db";
}
.icon-reddit:before {
  content: "\e0dc";
}
.icon-linkedin:before {
  content: "\e0dd";
}
.icon-lastfm:before {
  content: "\e0de";
}
.icon-delicious:before {
  content: "\e0df";
}
.icon-stumbleupon:before {
  content: "\e0e0";
}
.icon-pinterest:before {
  content: "\e0e1";
}
.icon-xing:before {
  content: "\e0e2";
}
.icon-flattr:before {
  content: "\e0e3";
}
.icon-foursquare:before {
  content: "\e0e4";
}
.icon-paypal:before {
  content: "\e0e5";
}
.icon-yelp:before {
  content: "\e0e6";
}
.icon-libreoffice:before {
  content: "\e0e7";
}
.icon-file-pdf:before {
  content: "\e0e8";
}
.icon-file-openoffice:before {
  content: "\e0e9";
}
.icon-file-word:before {
  content: "\e0ea";
}
.icon-file-excel:before {
  content: "\e0eb";
}
.icon-file-powerpoint:before {
  content: "\e0ec";
}
.icon-file-zip:before {
  content: "\e0ed";
}
.icon-file-xml:before {
  content: "\e0ee";
}
.icon-file-css:before {
  content: "\e0ef";
}
.icon-html5:before {
  content: "\e0f0";
}
.icon-html5-2:before {
  content: "\e0f1";
}
.icon-css3:before {
  content: "\e0f2";
}
.icon-chrome:before {
  content: "\e0f3";
}
.icon-firefox:before {
  content: "\e0f4";
}
.icon-IE:before {
  content: "\e0f5";
}
.icon-opera:before {
  content: "\e0f6";
}
.icon-safari:before {
  content: "\e0f7";
}
.icon-IcoMoon:before {
  content: "\e0f8";
}
.icon-sunrise:before {
  content: "\e0f9";
}
.icon-sun:before {
  content: "\e0fa";
}
.icon-moon:before {
  content: "\e0fb";
}
.icon-sun-2:before {
  content: "\e0fc";
}
.icon-windy:before {
  content: "\e0fd";
}
.icon-wind:before {
  content: "\e0fe";
}
.icon-snowflake:before {
  content: "\e0ff";
}
.icon-cloudy:before {
  content: "\e100";
}
.icon-cloud-3:before {
  content: "\e101";
}
.icon-weather:before {
  content: "\e102";
}
.icon-weather-2:before {
  content: "\e103";
}
.icon-weather-3:before {
  content: "\e104";
}
.icon-lines:before {
  content: "\e105";
}
.icon-cloud-4:before {
  content: "\e106";
}
.icon-lightning-2:before {
  content: "\e107";
}
.icon-lightning-3:before {
  content: "\e108";
}
.icon-rainy:before {
  content: "\e109";
}
.icon-rainy-2:before {
  content: "\e10a";
}
.icon-windy-2:before {
  content: "\e10b";
}
.icon-windy-3:before {
  content: "\e10c";
}
.icon-snowy:before {
  content: "\e10d";
}
.icon-snowy-2:before {
  content: "\e10e";
}
.icon-snowy-3:before {
  content: "\e10f";
}
.icon-weather-4:before {
  content: "\e110";
}
.icon-cloudy-2:before {
  content: "\e111";
}
.icon-cloud-5:before {
  content: "\e112";
}
.icon-lightning-4:before {
  content: "\e113";
}
.icon-sun-3:before {
  content: "\e114";
}
.icon-moon-2:before {
  content: "\e115";
}
.icon-cloudy-3:before {
  content: "\e116";
}
.icon-cloud-6:before {
  content: "\e117";
}
.icon-cloud-7:before {
  content: "\e118";
}
.icon-lightning-5:before {
  content: "\e119";
}
.icon-rainy-3:before {
  content: "\e11a";
}
.icon-rainy-4:before {
  content: "\e11b";
}
.icon-windy-4:before {
  content: "\e11c";
}
.icon-windy-5:before {
  content: "\e11d";
}
.icon-snowy-4:before {
  content: "\e11e";
}
.icon-snowy-5:before {
  content: "\e11f";
}
.icon-weather-5:before {
  content: "\e120";
}
.icon-cloudy-4:before {
  content: "\e121";
}
.icon-lightning-6:before {
  content: "\e122";
}
.icon-thermometer:before {
  content: "\e123";
}
.icon-compass-2:before {
  content: "\e124";
}
.icon-none:before {
  content: "\e125";
}
.icon-Celsius:before {
  content: "\e126";
}
.icon-Fahrenheit:before {
  content: "\e127";
}
.icon-forrst:before {
  content: "\e128";
}
.icon-headphones:before {
  content: "\e129";
}
.icon-bug:before {
  content: "\e12a";
}
.icon-cart-2:before {
  content: "\e12b";
}
.icon-earth:before {
  content: "\e12c";
}
.icon-battery:before {
  content: "\e12d";
}
.icon-list:before {
  content: "\e12e";
}
.icon-grid:before {
  content: "\e12f";
}
.icon-alarm:before {
  content: "\e130";
}
.icon-location-2:before {
  content: "\e131";
}
.icon-pointer:before {
  content: "\e132";
}
.icon-diary:before {
  content: "\e133";
}
.icon-eye-2:before {
  content: "\e134";
}
.icon-console:before {
  content: "\e135";
}
.icon-location-3:before {
  content: "\e136";
}
.icon-move:before {
  content: "\e137";
}
.icon-gift-2:before {
  content: "\e138";
}
.icon-monitor:before {
  content: "\e139";
}
.icon-mobile-2:before {
  content: "\e13a";
}
.icon-switch:before {
  content: "\e13b";
}
.icon-star-4:before {
  content: "\e13c";
}
.icon-address-book:before {
  content: "\e13d";
}
.icon-shit:before {
  content: "\e13e";
}
.icon-cone:before {
  content: "\e13f";
}
.icon-credit-card:before {
  content: "\e140";
}
.icon-type:before {
  content: "\e141";
}
.icon-volume:before {
  content: "\e142";
}
.icon-volume-2:before {
  content: "\e143";
}
.icon-locked-2:before {
  content: "\e144";
}
.icon-warning:before {
  content: "\e145";
}
.icon-info:before {
  content: "\e146";
}
.icon-filter:before {
  content: "\e147";
}
.icon-bookmark-3:before {
  content: "\e148";
}
.icon-bookmark-4:before {
  content: "\e149";
}
.icon-stats:before {
  content: "\e14a";
}
.icon-compass-3:before {
  content: "\e14b";
}
.icon-keyboard:before {
  content: "\e14c";
}
.icon-award-fill:before {
  content: "\e14d";
}
.icon-award-stroke:before {
  content: "\e14e";
}
.icon-beaker-alt:before {
  content: "\e14f";
}
.icon-beaker:before {
  content: "\e150";
}
.icon-move-vertical:before {
  content: "\e151";
}
.icon-move-horizontal:before {
  content: "\e152";
}
.icon-steering-wheel:before {
  content: "\e153";
}
.icon-volume-3:before {
  content: "\e154";
}
.icon-volume-mute:before {
  content: "\e155";
}
.icon-play:before {
  content: "\e156";
}
.icon-pause:before {
  content: "\e157";
}
.icon-stop:before {
  content: "\e158";
}
.icon-eject:before {
  content: "\e159";
}
.icon-first:before {
  content: "\e15a";
}
.icon-last:before {
  content: "\e15b";
}
.icon-play-alt:before {
  content: "\e15c";
}
.icon-battery-charging:before {
  content: "\e160";
}
.icon-left-quote:before {
  content: "\e161";
}
.icon-right-quote:before {
  content: "\e162";
}
.icon-left-quote-alt:before {
  content: "\e163";
}
.icon-right-quote-alt:before {
  content: "\e164";
}
.icon-smiley:before {
  content: "\e165";
}
.icon-umbrella:before {
  content: "\e166";
}
.icon-info-2:before {
  content: "\e167";
}
.icon-chart-alt:before {
  content: "\e168";
}
.icon-at:before {
  content: "\e169";
}
.icon-hash:before {
  content: "\e16a";
}
.icon-pilcrow:before {
  content: "\e16b";
}
.icon-fullscreen-alt:before {
  content: "\e16c";
}
.icon-fullscreen-exit-alt:before {
  content: "\e16d";
}
.icon-layers-alt:before {
  content: "\e16e";
}
.icon-layers:before {
  content: "\e16f";
}
.icon-floppy:before {
  content: "\e170";
}
.icon-rainbow:before {
  content: "\e000";
}
.icon-air:before {
  content: "\e171";
}
.icon-home:before {
  content: "\e172";
}
.icon-spin:before {
  content: "\e173";
}
.icon-auction:before {
  content: "\e174";
}
.icon-dollar:before {
  content: "\e175";
}
.icon-dollar-2:before {
  content: "\e176";
}
.icon-coins:before {
  content: "\e177";
}
.icon-file-2:before {
  content: "\e186";
}
.icon-file-3:before {
  content: "\e187";
}
.icon-file-4:before {
  content: "\e188";
}
.icon-files:before {
  content: "\e189";
}
.icon-phone-2:before {
  content: "\e18a";
}
.icon-tablet:before {
  content: "\e18b";
}
.icon-monitor-2:before {
  content: "\e18c";
}
.icon-window:before {
  content: "\e18d";
}
.icon-tv:before {
  content: "\e18e";
}
.icon-camera-3:before {
  content: "\e18f";
}
.icon-image:before {
  content: "\e190";
}
.icon-open:before {
  content: "\e191";
}
.icon-sale:before {
  content: "\e192";
}
.icon-direction:before {
  content: "\e193";
}
.icon-medal:before {
  content: "\e194";
}
.icon-medal-2:before {
  content: "\e195";
}
.icon-satellite:before {
  content: "\e196";
}
.icon-discout:before {
  content: "\e197";
}
.icon-barcode:before {
  content: "\e198";
}
.icon-ticket:before {
  content: "\e199";
}
.icon-shipping:before {
  content: "\e19a";
}
.icon-globe:before {
  content: "\e19b";
}
.icon-anchor:before {
  content: "\e19c";
}
.icon-pop-out:before {
  content: "\e19d";
}
.icon-pop-in:before {
  content: "\e19e";
}
.icon-resize:before {
  content: "\e178";
}
.icon-battery-2:before {
  content: "\e179";
}
.icon-battery-3:before {
  content: "\e17a";
}
.icon-battery-4:before {
  content: "\e17b";
}
.icon-battery-5:before {
  content: "\e17c";
}
.icon-tools:before {
  content: "\e17d";
}
.icon-alarm-2:before {
  content: "\e17e";
}
.icon-alarm-cancel:before {
  content: "\e17f";
}
.icon-alarm-clock:before {
  content: "\e180";
}
.icon-chronometer:before {
  content: "\e181";
}
.icon-ruler:before {
  content: "\e182";
}
.icon-lamp:before {
  content: "\e183";
}
.icon-lamp-2:before {
  content: "\e184";
}
.icon-scissors:before {
  content: "\e185";
}
.icon-volume-4:before {
  content: "\e19f";
}
.icon-volume-5:before {
  content: "\e1a0";
}
.icon-volume-6:before {
  content: "\e1a1";
}
.icon-battery-full:before {
  content: "\e15f";
}
.icon-battery-empty:before {
  content: "\e15d";
}
.icon-battery-half:before {
  content: "\e15e";
}
.icon-zip:before {
  content: "\e1a2";
}
.icon-zip-2:before {
  content: "\e1a3";
}
.icon-play-2:before {
  content: "\e1a4";
}
.icon-pause-2:before {
  content: "\e1a5";
}
.icon-record:before {
  content: "\e1a6";
}
.icon-stop-2:before {
  content: "\e1a7";
}
.icon-next:before {
  content: "\e1a8";
}
.icon-previous:before {
  content: "\e1a9";
}
.icon-first-2:before {
  content: "\e1aa";
}
.icon-last-2:before {
  content: "\e1ab";
}
.icon-arrow-left-4:before {
  content: "\e1ac";
}
.icon-arrow-down-4:before {
  content: "\e1ad";
}
.icon-arrow-up-4:before {
  content: "\e1ae";
}
.icon-arrow-right-4:before {
  content: "\e1af";
}
.icon-arrow-left-5:before {
  content: "\e1b0";
}
.icon-arrow-down-5:before {
  content: "\e1b1";
}
.icon-arrow-up-5:before {
  content: "\e1b2";
}
.icon-arrow-right-5:before {
  content: "\e1b3";
}
.icon-cc:before {
  content: "\e1b4";
}
.icon-cc-by:before {
  content: "\e1b5";
}
.icon-cc-nc:before {
  content: "\e1b6";
}
.icon-cc-nc-eu:before {
  content: "\e1b7";
}
.icon-cc-nc-jp:before {
  content: "\e1b8";
}
.icon-cc-sa:before {
  content: "\e1b9";
}
.icon-cc-nd:before {
  content: "\e1ba";
}
.icon-cc-pd:before {
  content: "\e1bb";
}
.icon-cc-zero:before {
  content: "\e1bc";
}
.icon-cc-share:before {
  content: "\e1bd";
}
.icon-cc-share-2:before {
  content: "\e1be";
}
.icon-cycle:before {
  content: "\e1bf";
}
.icon-stop-3:before {
  content: "\e1c0";
}
.icon-stats-2:before {
  content: "\e1c1";
}
.icon-stats-3:before {
  content: "\e1c2";
}
