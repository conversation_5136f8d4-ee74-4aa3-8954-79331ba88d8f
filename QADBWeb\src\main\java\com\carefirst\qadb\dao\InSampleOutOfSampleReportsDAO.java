package com.carefirst.qadb.dao;

import java.sql.SQLException;

import net.sf.jasperreports.engine.JRDataSource;

import com.carefirst.audit.model.AdjustmentsReport;
import com.carefirst.audit.model.EditCodeReport;
import com.carefirst.audit.model.InSampleOutOfSampleReport;
import com.carefirst.audit.model.OtherReports;

public interface InSampleOutOfSampleReportsDAO {

	JRDataSource getInSampleOutOfSampleReport(InSampleOutOfSampleReport isOosReportForm) throws SQLException;

}
