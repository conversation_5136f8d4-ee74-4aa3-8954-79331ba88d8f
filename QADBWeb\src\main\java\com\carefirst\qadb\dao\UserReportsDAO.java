package com.carefirst.qadb.dao;

import java.sql.SQLException;

import net.sf.jasperreports.engine.JRDataSource;

import com.carefirst.audit.model.AdjustmentsReport;
import com.carefirst.audit.model.OtherReports;

public interface UserReportsDAO {

	JRDataSource getOtherReport(OtherReports otherReport) throws SQLException;

	JRDataSource getAdjustmentsRequiredReport(AdjustmentsReport adjustments) throws SQLException;

}
