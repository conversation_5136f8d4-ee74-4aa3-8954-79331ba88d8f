<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="scriptlet" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" isSummaryWithPageHeaderAndFooter="true" whenNoDataType="NoDataSection" whenResourceMissingType="Empty"  >
	<property name="com.jasperassistant.designer.Grid" value="false"/>
	<property name="com.jasperassistant.designer.SnapToGrid" value="false"/>
	<property name="com.jasperassistant.designer.GridWidth" value="12"/>
	<property name="com.jasperassistant.designer.GridHeight" value="12"/>
	<property name="net.sf.jasperreports.export.xls.remove.empty.space.between.rows" value="true"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="net.sf.jasperreports.awt.ignore.missing.font" value="true"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="title" class="java.lang.String">
		<fieldDescription><![CDATA[title]]></fieldDescription>
	</field>
	<field name="timeframe" class="java.lang.String">
		<fieldDescription><![CDATA[timeframe]]></fieldDescription>
	</field>
	<field name="e2e" class="java.lang.String">
		<fieldDescription><![CDATA[e2e]]></fieldDescription>
	</field>
	<field name="auditType" class="java.lang.String">
		<fieldDescription><![CDATA[auditType]]></fieldDescription>
	</field>
	<field name="subType" class="java.lang.String">
		<fieldDescription><![CDATA[subType]]></fieldDescription>
	</field>
	<field name="month" class="java.lang.String">
		<fieldDescription><![CDATA[month]]></fieldDescription>
	</field>
	<field name="associateName" class="java.lang.String">
		<fieldDescription><![CDATA[associateName]]></fieldDescription>
	</field>
	<field name="empNO" class="java.lang.String">
		<fieldDescription><![CDATA[empNO]]></fieldDescription>
	</field>
	<field name="supervisor" class="java.lang.String">
		<fieldDescription><![CDATA[supervisor]]></fieldDescription>
	</field>
	
	<field name="isSno" class="java.lang.String">
		<fieldDescription><![CDATA[isSno]]></fieldDescription>
	</field>
	<field name="isDcn" class="java.lang.String">
		<fieldDescription><![CDATA[isDcn]]></fieldDescription>
	</field>
	<field name="isMemId" class="java.lang.String">
		<fieldDescription><![CDATA[isMemId]]></fieldDescription>
	</field>
	<field name="isPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isPaid]]></fieldDescription>
	</field>
	<field name="isUnderPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isUnderPaid]]></fieldDescription>
	</field>
	<field name="isOverPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isOverPaid]]></fieldDescription>
	</field>
	<field name="isHighDoller" class="java.lang.String">
		<fieldDescription><![CDATA[isHighDoller]]></fieldDescription>
	</field>
	<field name="isAuditType" class="java.lang.String">
		<fieldDescription><![CDATA[isAuditType]]></fieldDescription>
	</field>
	<field name="isMockAudit" class="java.lang.String">
		<fieldDescription><![CDATA[isMockAudit]]></fieldDescription>
	</field>
	<field name="reportType" class="java.lang.String">
		<fieldDescription><![CDATA[reportType]]></fieldDescription>
	</field>
	<field name="isAdjRequired" class="java.lang.String">
		<fieldDescription><![CDATA[isAdjRequired]]></fieldDescription>
	</field>
	<field name="isProcessOn" class="java.lang.String">
		<fieldDescription><![CDATA[isProcessOn]]></fieldDescription>
	</field>
	<field name="isErrorCode" class="java.lang.String">
		<fieldDescription><![CDATA[isErrorCode]]></fieldDescription>
	</field>
	<field name="isMonetary" class="java.lang.String">
		<fieldDescription><![CDATA[isMonetary]]></fieldDescription>
	</field>
	<field name="isProcedural" class="java.lang.String">
		<fieldDescription><![CDATA[isProcedural]]></fieldDescription>
	</field>
	<field name="isErrorType" class="java.lang.String">
		<fieldDescription><![CDATA[isErrorType]]></fieldDescription>
	</field>
	<field name="isExplanation" class="java.lang.String">
		<fieldDescription><![CDATA[isExplanation]]></fieldDescription>
	</field>
	<field name="isSop" class="java.lang.String">
		<fieldDescription><![CDATA[isSop]]></fieldDescription>
	</field>
	<field name="isPriPerfGroup" class="java.lang.String">
		<fieldDescription><![CDATA[isPriPerfGroup]]></fieldDescription>
	</field>
	<field name="isAuditor" class="java.lang.String">
		<fieldDescription><![CDATA[isAuditor]]></fieldDescription>
	</field>
	<field name="isAuditDate" class="java.lang.String">
		<fieldDescription><![CDATA[isAuditDate]]></fieldDescription>
	</field>
	
	<field name="isTotalClaims" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalClaims]]></fieldDescription>
	</field>
	<field name="isProcClaims" class="java.lang.String">
		<fieldDescription><![CDATA[isProcClaims]]></fieldDescription>
	</field>
	<field name="isMonClaims" class="java.lang.String">
		<fieldDescription><![CDATA[isMonClaims]]></fieldDescription>
	</field>
	<field name="isTotalPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalPaid]]></fieldDescription>
	</field>
	<field name="isTotalOvPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalOvPaid]]></fieldDescription>
	</field>
	<field name="isTotalUnPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalUnPaid]]></fieldDescription>
	</field>
	
	<group name="dummy"> 		
	<groupExpression><![CDATA["dummy"]]></groupExpression> 
		<groupHeader>
			
		</groupHeader>
	</group>
	
	<title>
		<band height="117" splitType="Stretch">
			<staticText>
				<reportElement x="1" y="91" width="77" height="20" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Membership #]]></text>
			</staticText>
			<staticText>
				<reportElement x="85" y="66" width="45" height="15" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="178" y="99" width="67" height="16" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Out-Of-Sample]]></text>
			</staticText>
			<staticText>
				<reportElement x="370" y="66" width="51" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="11" isBold="false" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Monetary]]></text>
			</staticText>
			<staticText>
				<reportElement x="187" y="81" width="58" height="16" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Mock Audit]]></text>
			</staticText>
			<textField>
				<reportElement x="486" y="38" width="111" height="13" />
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{auditType}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="421" y="66" width="45" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="11" isBold="false" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Procedural]]></text>
			</staticText>
			<textField>
				<reportElement mode="Transparent" x="0" y="31" width="92" height="28" forecolor="#000000" backcolor="#FFFFFF" />
				<box padding="0" topPadding="4" leftPadding="8" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="14" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Manager : "]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="475" y="66" width="48" height="20" />
				<textElement textAlignment="Left">
					<font fontName="Haettenschweiler" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Error Type]]></text>
			</staticText>
			<staticText>
				<reportElement x="187" y="66" width="58" height="15" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Audit Type]]></text>
			</staticText>
			<staticText>
				<reportElement x="82" y="99" width="55" height="14" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Under Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="529" y="66" width="108" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Explanation]]></text>
			</staticText>
			<staticText>
				<reportElement x="137" y="81" width="50" height="16" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[High Dollar]]></text>
			</staticText>
			<textField>
				<reportElement x="670" y="37" width="129" height="13" >
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{subType}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="245" y="97" width="64" height="20" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Processed On]]></text>
			</staticText>
			<staticText>
				<reportElement x="637" y="66" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[SOP Reference]]></text>
			</staticText>
			<staticText>
				<reportElement x="729" y="66" width="85" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Manager]]></text>
			</staticText>
			<staticText>
				<reportElement x="729" y="97" width="85" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Associate]]></text>
			</staticText>
			<staticText>
				<reportElement x="82" y="81" width="55" height="16" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Over Paid]]></text>
			</staticText>
			<textField>
				<reportElement x="92" y="31" width="329" height="21" />
				<textElement>
					<font size="14" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{associateName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="313" y="66" width="57" height="20" />
				<textElement textAlignment="Left">
					<font fontName="Haettenschweiler" size="11" isBold="false" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Error Codes]]></text>
			</staticText>
			<staticText>
				<reportElement x="1" y="75" width="77" height="15" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[DCN]]></text>
			</staticText>
			<staticText>
				<reportElement key="" x="421" y="37" width="64" height="14" forecolor="#000080" >
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Audit Type :]]></text>
			</staticText>
			<staticText>
				<reportElement x="245" y="66" width="61" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="11" isBold="false"/>
				</textElement>
				<text><![CDATA[Adj. Required]]></text>
			</staticText>
			<staticText>
				<reportElement x="611" y="36" width="59" height="14" forecolor="#000080" />
				<textElement>
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Sub Type :]]></text>
			</staticText>
			<textField>
				<reportElement x="4" y="1" width="797" height="30" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="28"/>
				</textElement>
			 	<textFieldExpression><![CDATA["Monetary Error Sheet for "+$F{timeframe}+" "+"("+$F{e2e}+")"]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<detail>
		<band height="51" splitType="Stretch">
			<image>
				<reportElement x="154" y="13" width="16" height="15" >
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<imageExpression><![CDATA[$F{isHighDoller}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="202" y="13" width="16" height="15" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<imageExpression><![CDATA[$F{isMockAudit}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="265" y="2" width="16" height="15" />
				<imageExpression><![CDATA[$F{isAdjRequired}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="388" y="2" width="16" height="15" />
				<imageExpression><![CDATA[$F{isMonetary}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="437" y="2" width="16" height="15" />
				<imageExpression><![CDATA[$F{isProcedural}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="203" y="28" width="16" height="15" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<imageExpression><![CDATA[$F{reportType}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<line>
				<reportElement positionType="Float" x="-11" y="2" width="824" height="1" />
				<graphicElement>
					<pen lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="10" y="2" width="68" height="11" />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isDcn}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="-21" y="2" width="31" height="22" >
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isSno}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="10" y="13" width="67" height="18" />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isMemId}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="82" y="2" width="63" height="12" />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isPaid} != null && $F{isPaid}.length() > 0 ? Double.valueOf($F{isPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="82" y="15" width="63" height="12" />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isOverPaid} != null && $F{isOverPaid}.length() > 0 ? Double.valueOf($F{isOverPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="82" y="29" width="63" height="12" />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isUnderPaid} != null && $F{isUnderPaid}.length() > 0 ? Double.valueOf($F{isUnderPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="188" y="2" width="50" height="11" />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isAuditType}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="248" y="29" width="51" height="12" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isProcessOn}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="463" y="2" width="61" height="14" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isErrorType}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="528" y="2" width="110" height="48" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isExplanation}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="729" y="2" width="89" height="27" />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isAuditor}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="640" y="2" width="81" height="48" />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isSop}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="324" y="2" width="31" height="14" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isErrorCode}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="729" y="29" width="89" height="19" />
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isAuditDate}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<summary>				
		<band height="69" splitType="Stretch">
			<staticText>
				<reportElement x="10" y="19" width="74" height="15" />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Claims]]></text>
			</staticText>
			<staticText>
				<reportElement x="10" y="35" width="91" height="15" />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Procedural Error Claims]]></text>
			</staticText>
			<staticText>
				<reportElement x="10" y="51" width="83" height="15" />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Monetary Error Claims]]></text>
			</staticText>
			<staticText>
				<reportElement x="158" y="35" width="81" height="15" />
				<textElement textAlignment="Justified">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Over Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="158" y="19" width="74" height="15" />
				<textElement textAlignment="Justified">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="157" y="51" width="93" height="15" >
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Justified">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Under Paid]]></text>
			</staticText>
			<textField>
				<reportElement x="102" y="21" width="51" height="14" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isTotalClaims}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="102" y="38" width="51" height="14" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isProcClaims}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="102" y="53" width="51" height="14" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isMonClaims}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="251" y="19" width="76" height="14" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isTotalPaid} != null && $F{isTotalPaid}.length() > 0 ? Double.valueOf($F{isTotalPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="251" y="36" width="76" height="14" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isTotalOvPaid} != null && $F{isTotalOvPaid}.length() > 0 ? Double.valueOf($F{isTotalOvPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="251" y="51" width="76" height="14" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="SansSerif" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isTotalUnPaid} != null && $F{isTotalUnPaid}.length() > 0 ? Double.valueOf($F{isTotalUnPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="6" y="3" width="415" height="12" forecolor="#000000" backcolor="#FFFFFF" />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Summary for '"+$F{title}+"'= "+$F{associateName}+" ("+$F{isTotalClaims}+" claims)"]]></textFieldExpression>
			</textField>
		</band>
	</summary>
	<noData>
		<band height="55" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="300" y="0" width="316" height="47" forecolor="#000050">
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="22" isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[No records found!!]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>
