<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>

<head>
<link href="webResources/css/qadb.css" rel="stylesheet">

<script type="text/javascript" src="webResources/js/metro.min.js"></script>
                <!-- Local JavaScript -->
			
	<link href="webResources/css/iconFont.css" rel="stylesheet">
    <link href="webResources/css/docs.css" rel="stylesheet">
    <link href="webResources/js/prettify/prettify.css" rel="stylesheet">
    <link href="webResources/css/jquery-ui.css" rel="stylesheet">

    <!-- Load JavaScript Libraries -->
    <script src="webResources/js/jquery/jquery.min.js"></script>
    <script src="webResources/js/jquery/jquery.widget.min.js"></script>
    <script type="text/javascript" src="webResources/js/jquery-ui.js"></script>
    <script type="text/javascript" src="webResources/js/qadb.js"></script>
 	<script type="text/javascript" src="webResources/js/datep.js"></script>
<style>
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
	margin-right: auto;
	height: 210%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}

.ui-widget {
	font-family: Verdana, Arial, sans-serif;
	font-size: 1.1em;
}

.ui-widget .ui-widget {
	font-size: 1em;
}

.ui-widget input, .ui-widget select, .ui-widget textarea, .ui-widget button
	{
	font-family: Verdana, Arial, sans-serif;
	font-size: 1em;
}

.ui-widget-content {
	border: 1px solid #aaaaaa;
	background: #ffffff url() 50% 50% repeat-x;
	color: #222222;
}

.ui-widget-content a {
	color: #222222;
}

/* .ui-widget-header {
	border: 1px solid #aaaaaa;
	background: #2573ab url(images/ui-bg_highlight-soft_75_cccccc_1x100.png)
		50% 50% repeat-x;
	color: #e6f5fc;
	font-weight: bold;
} */

.ui-widget-header a {
	color: #222222;
}

.ui-datepicker-trigger {
	background-image: url("webResources/images/Forms/Icn_Date_Picker.png");
	height: 36px;
	width: 36px;
	background-color: white;
}

.ui-icon-circle-triangle-e {
	background-image: url("webResources//Navbar/Icn_Right_Arrow.png");
}

.ui-icon-circle-triangle-w {
	background-image: url("webResources/images/skip_backward.png");
}
</style>



<title><spring:message code="reports.performance.title"></spring:message></title>
</head>
<body>
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container2">

		<!-- Sidebar Start-->
		<div id="left-sidebar">
			<h2 style="color: white;padding-top: 32px;"><spring:message code="reports.performance.leftNav.heading1"></spring:message></h2>
			<h3 style="color: white;"><spring:message code="reports.performance.leftNav.heading2"></spring:message></h3>

			
		</div>
		<!-- Sidebar End-->

		<div id="content-container"  style="padding: 10px 0px 0px 40px;">
		<span class="bread1"><spring:message code="reports.bread1"></spring:message></span><span class="bread2"><spring:message code="reports.performance.leftNav.heading1"></spring:message></span>
		
		<form:form id="performance_error_sheet" name="pgErrorSheetForm" method="GET" commandName="pgErrorSheetForm" action="PerformanceGroupMonthlyErrorSheet" style="width:700px">

		<br>
		<span style="color: #0070c0;font-size:16px"><spring:message code="reports.performance.reportParameters"></spring:message></span>
		<div class="line-separator"></div>
		  <div style="padding-top: 10px;padding-left: 7px;background-color: #fafafa;width:750px">
		  <table class="tableForm1" style="width:98%" >
		  <tr>
		  		<td width="30%"><spring:message code="reports.performance.month"></spring:message></td>
		  		<td>
							<div class="input-control select">
								<select id="T3" name="month" style="width:150px">
									<option value="JAN">January</option>
									<option value="FEB">February</option>
									<option value="MAR">March</option>
									<option value="APR">April</option>
									<option value="MAY">May</option>
									<option value="JUN">June</option>
									<option value="JUL">July</option>
									<option value="AUG">August</option>
									<option value="SEP">September</option>
									<option value="OCT">October</option>
									<option value="NOV">November</option>
									<option value="DEC">December</option>
								</select>
							</div>
						</td>
		  </tr>
		   <tr>
		  		<td width="30%"><spring:message code="reports.performance.year"></spring:message></td>
		  		<td>
							<div class="input-control select">
								<select  id= "years" name="year" style="width:150px">
								</select>
							</div>
						</td>
		  </tr>
		 
		  <tr>
		  		<td width="30%"><spring:message code="reports.performance.groupType"></spring:message></td>
		  		<td width="70%">
		  			<div class="input-control radio default-style margin1"
										data-role="input-control">
										<label class="input-control radio small-check">
											<input type="radio" name="grouptype" checked="true" value="P">
												<span class="check"></span>
													<span class="caption"><spring:message
										code="admin.perfGroup.groupDetails.primary" /></span>
										</label>
									</div>
									
									<div class="input-control radio default-style margin10"
										data-role="input-control" >
										<label class="input-control radio small-check">
											<input type="radio" name="grouptype"  value="S">
												<span class="check"></span>
													<span class="caption"><spring:message
										code="admin.perfGroup.groupDetails.secondary" /></span>
										</label>
									</div>
		  		</td>
		  </tr>
		  <tr>
		  		<td width="30%"><spring:message code="reports.performance.groupName"></spring:message></td>
		  		<td width="70%">
		  			 <div class="input-control select">
								<select id="P" name="priGroupId" class="desc" style="width: 330px; font-size: 14px; border-color: #919191">
											<c:forEach items="${priPGAList}" var="priPGAList">
												<option value="${priPGAList.pPgaId}">${priPGAList.priPGA}</option>
											</c:forEach>
										</select>
								<select id="S" name="secGroupId" class="desc" style="width: 330px; font-size: 14px; border-color: #919191">
											<c:forEach items="${secPGAList}" var="secPGAList">
												<option value="${secPGAList.sPgaId}">${secPGAList.secPGA}</option>
											</c:forEach>
										</select>
								</div> 
								
					
					
		  		</td>
		  </tr>
		 </table>
		  </div>
		  
		  <div style="width:750px">
			    <div style="float:right">
    	<button type="reset" class="button inverse">Reset</button> <button type="submit" style="background-color:#298fd8" class="button default">Generate</button> 
   			</div>
			</div>
		  </form:form>
		 
		
		
			
		 
		
<script type="text/javascript">
$(document).ready(function(){ 

/*year values with current year from 2000  */
	var max = new Date().getFullYear(),
	min = 2000,
    
   	select = document.getElementById('years');

	for (var i = max; i>=min; i--){
   		 var opt = document.createElement('option');
   		 opt.value = i;
    	 opt.innerHTML = i;
    	 select.appendChild(opt);
		}


 $("#S").hide();
    $("input[name='grouptype']").click(function() {
       
        var test = $(this).val();
        $(".desc").hide();
        $("#"+test).show();
    }); 
});
</script>  
			

	
	
	
	
	<script type="text/javascript">
				$("#datepick1").datepicker(
						{
							showOn : "button",
							onSelect : function(selected) {
								$("#datepick2").datepicker("option", "minDate",
										selected)
							},
						});
				$("#datepick2").datepicker(
						{
							showOn : "button",
							onSelect : function(selected) {
								$("#datepick1").datepicker("option", "maxDate",
										selected)
							},
						});

				jQuery(function($) {
					$("#datepick1").mask("99/99/9999", {
						placeholder : "MM/DD/YYYY"
					});
					$("#datepick2").mask("99/99/9999", {
						placeholder : "MM/DD/YYYY"
					});
				});
			</script>			
        
       
           
           
		</div>
	</div>

	<jsp:include page="footer.jsp"></jsp:include>

</body>

</html>
