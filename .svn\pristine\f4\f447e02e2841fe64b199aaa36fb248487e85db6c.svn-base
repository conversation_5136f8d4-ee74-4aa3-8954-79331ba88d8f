<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:p="http://www.springframework.org/schema/p" 
	xmlns:util="http://www.springframework.org/schema/util"
	xsi:schemaLocation="
		http://www.springframework.org/schema/beans 
		http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.springframework.org/schema/util 
		http://www.springframework.org/schema/util/spring-util-3.0.xsd">
	<!-- 
		Declare Spring's View Resolvers for Jasper
		
		Based on the bean names we can infer that:
			* pdfReport is for generating PDFs
			* xlsReport is for Excel format reports
			* htmlReport is for HTML reports
			* csvReport is for CSV reports
	-->
	<!-- 
		id: the name to be used as a reference in the controller
		url: the path where the Jasper JRXML file is located
		reportDateKey: the name of the datasource. This is passed by the controller
	-->
	<!-- AuditAssesment -->
	<bean id="auditAssesments"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:auditAssesmentMaster.jrxml"
		p:reportDataKey="datasource" 
		p:subReportUrls-ref="customSubReportUrls"
		p:subReportDataKeys-ref="customSubReportDatasource"/>

	<util:map id="customSubReportUrls">
	    <!-- This entry key must be declared exactly as you declared it in the master JRXML file -->
	    <!-- Here we assigned two sub-report templates -->
	    <entry key="JasperCustomSubReportLocation1" value="classpath:auditAssesmentIn.jrxml"/>
	    <entry key="JasperCustomSubReportLocation2" value="classpath:auditAssesmentOut.jrxml"/>
	</util:map>
	
	<util:list id="customSubReportDatasource">
	    <!-- This value must be declared exactly as you declared it in the master JRXML file -->
	    <!-- Here we assigned two sub-report datasources -->
	    <value>JasperCustomSubReportDatasource1</value>
	    <value>JasperCustomSubReportDatasource2</value>
	</util:list>
	
	<!--Assesement only OOS  -->
	<bean id="auditAssesmentsOos"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:auditAssesmentOutSample.jrxml"
		p:reportDataKey="datasource" />
	
	<!-- Associate Scores -->
	<bean id="associateScores"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:associateScoresReportMaster.jrxml"
		p:reportDataKey="datasource" 
		p:subReportUrls-ref="assoScoresSubReportUrls"
		p:subReportDataKeys-ref="assoScoresSubReportDatasource"/>

	<util:map id="assoScoresSubReportUrls">
	    <!-- This entry key must be declared exactly as you declared it in the master JRXML file -->
	    <!-- Here we assigned two sub-report templates -->
	    <entry key="JasperCustomSubReportLocation1" value="classpath:associateScoresReportIn.jrxml"/>
	    <entry key="JasperCustomSubReportLocation2" value="classpath:associateScoresReportOut.jrxml"/>
	</util:map>
	
	<util:list id="assoScoresSubReportDatasource">
	    <!-- This value must be declared exactly as you declared it in the master JRXML file -->
	    <!-- Here we assigned two sub-report datasources -->
	    <value>JasperCustomSubReportDatasource1</value>
	    <value>JasperCustomSubReportDatasource2</value>
	</util:list>
	
	<!--Associate scores only in sample  -->
	<bean id="associateScoresIn"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:associateScoresReportInSample.jrxml"
		p:reportDataKey="datasource" />
	
	<!-- Supervisor Scores -->
	<bean id="supervisorScores"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:associateScoresReportMaster.jrxml"
		p:reportDataKey="datasource" 
		p:subReportUrls-ref="supervisorScoresSubReportUrls"
		p:subReportDataKeys-ref="supervisorScoresSubReportDatasource"/>

	<util:map id="supervisorScoresSubReportUrls">
	    <!-- This entry key must be declared exactly as you declared it in the master JRXML file -->
	    <!-- Here we assigned two sub-report templates -->
	    <entry key="JasperCustomSubReportLocation1" value="classpath:supervisorScoresReportIn.jrxml"/>
	    <entry key="JasperCustomSubReportLocation2" value="classpath:supervisorScoresReportOut.jrxml"/>
	</util:map>
	
	<util:list id="supervisorScoresSubReportDatasource">
	    <!-- This value must be declared exactly as you declared it in the master JRXML file -->
	    <!-- Here we assigned two sub-report datasources -->
	    <value>JasperCustomSubReportDatasource1</value>
	    <value>JasperCustomSubReportDatasource2</value>
	</util:list>
	
	<!-- Supervisor Scores in sample-->
	<bean id="supervisorScoresIn"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:supervisorScoresReportInSample.jrxml"
		p:reportDataKey="datasource" />
		
		
	<!-- Manager Scores -->
	<bean id="managerScores"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:associateScoresReportMaster.jrxml"
		p:reportDataKey="datasource" 
		p:subReportUrls-ref="managerScoresSubReportUrls"
		p:subReportDataKeys-ref="managerScoresSubReportDatasource"/>

	<util:map id="managerScoresSubReportUrls">
	    <!-- This entry key must be declared exactly as you declared it in the master JRXML file -->
	    <!-- Here we assigned two sub-report templates -->
	    <entry key="JasperCustomSubReportLocation1" value="classpath:managerScoresReportIn.jrxml"/>
	    <entry key="JasperCustomSubReportLocation2" value="classpath:managerScoresReportOut.jrxml"/>
	</util:map>
	
	
	<util:list id="managerScoresSubReportDatasource">
	    <!-- This value must be declared exactly as you declared it in the master JRXML file -->
	    <!-- Here we assigned two sub-report datasources -->
	    <value>JasperCustomSubReportDatasource1</value>
	    <value>JasperCustomSubReportDatasource2</value>
	</util:list>
	
	<!-- Manager Scores in sample-->
	<bean id="managerScoresIn"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:managerScoresReportInSample.jrxml"
		p:reportDataKey="datasource" />
	
	
	
	<!-- Director Scores -->
	<bean id="directorScores"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:associateScoresReportMaster.jrxml"
		p:reportDataKey="datasource" 
		p:subReportUrls-ref="directorScoresSubReportUrls"
		p:subReportDataKeys-ref="directorScoresSubReportDatasource"/>

	<util:map id="directorScoresSubReportUrls">
	    <!-- This entry key must be declared exactly as you declared it in the master JRXML file -->
	    <!-- Here we assigned two sub-report templates -->
	    <entry key="JasperCustomSubReportLocation1" value="classpath:directorScoresReportIn.jrxml"/>
	    <entry key="JasperCustomSubReportLocation2" value="classpath:directorScoresReportOut.jrxml"/>
	</util:map>
	
	<util:list id="directorScoresSubReportDatasource">
	    <!-- This value must be declared exactly as you declared it in the master JRXML file -->
	    <!-- Here we assigned two sub-report datasources -->
	    <value>JasperCustomSubReportDatasource1</value>
	    <value>JasperCustomSubReportDatasource2</value>
	</util:list>
	
	<!-- Director Scores in sample-->
	<bean id="directorScoresIn"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:directorScoresReportInSample.jrxml"
		p:reportDataKey="datasource" />
	
	<!--Division scores  -->
	<bean id="divisionScores"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:divisionScoresReport.jrxml"
		p:reportDataKey="datasource" />
	
	<!--LOB scores  -->
	<bean id="lobScores"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:lobScoresReport.jrxml"
		p:reportDataKey="datasource" />
		
	<!--PG scores  -->
	<bean id="pgScores"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:pgScoresReport.jrxml"
		p:reportDataKey="datasource" />
	
	
	<!--Trends  -->
	<!-- Associate Trends -->
	<bean id="associateTrends"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:associateTrendsReportMaster.jrxml"
		p:reportDataKey="datasource" 
		p:subReportUrls-ref="assoTrendsSubReportUrls"
		p:subReportDataKeys-ref="assoTrendsSubReportDatasource"/>

	<util:map id="assoTrendsSubReportUrls">
	    <!-- This entry key must be declared exactly as you declared it in the master JRXML file -->
	    <!-- Here we assigned two sub-report templates -->
	    <entry key="JasperCustomSubReportLocation1" value="classpath:associateTrendsReportIn.jrxml"/>
	    <entry key="JasperCustomSubReportLocation2" value="classpath:associateTrendsReportOut.jrxml"/>
	</util:map>
	
	<util:list id="assoTrendsSubReportDatasource">
	    <!-- This value must be declared exactly as you declared it in the master JRXML file -->
	    <!-- Here we assigned two sub-report datasources -->
	    <value>JasperCustomSubReportDatasource1</value>
	    <value>JasperCustomSubReportDatasource2</value>
	</util:list>
	
	<!--Associate/Supervisor trends only in sample  -->
	<bean id="associateTrendsIn"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:associateTrendsReportInSample.jrxml"
		p:reportDataKey="datasource" />
	
	
	<!--Division Trends  -->
	<bean id="divisionTrends"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:divisionTrendsReport.jrxml"
		p:reportDataKey="datasource" />
	
	<!--Monetory  -->
	<!-- Manager/Supervisor Monetory -->
	<bean id="ManagerSupervisorMonetary"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:managerSupervisorMonetaryReport.jrxml"
		p:reportDataKey="datasource" />
	
	<bean id="ManagerMonetaryXls"
	   class="com.carefirst.qadb.utils.JasperReportsXlsxView"
		p:url="classpath:managerSupervisorMonetaryXlsReport.jrxml"
		p:reportDataKey="datasource" />
		
	<!-- Division Monetory -->
	<bean id="DivisionMonetary"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:divisionMonetaryReport.jrxml"
		p:reportDataKey="datasource" />
	
	<!--User Reports  -->
	<!--Adj Required report  -->
	<bean id="AdjustmentsRequired"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:adjustmentsRequiredReport.jrxml"
		p:reportDataKey="datasource" />
	
	<!--Other Reports  -->
	<bean id="errorCodesReport"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:errorCodesReport.jrxml"
		p:reportDataKey="datasource" />
	
	<bean id="errorCodesReportAll"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:errorCodesReportAll.jrxml"
		p:reportDataKey="datasource" />	
		
	<bean id="currentProcessorsReport"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:currentProcessorsReport.jrxml"
		p:reportDataKey="datasource" />
		
	<bean id="currentProcessorsReportAll"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:currentProcessorsReportAll.jrxml"
		p:reportDataKey="datasource" />
	
	<bean id="supervisorsReport"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:supervisorsReport.jrxml"
		p:reportDataKey="datasource" />
	
	<bean id="supervisorsReportAll"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:supervisorsReportAll.jrxml"
		p:reportDataKey="datasource" />
		
	
	<!-- Perf Grp Error Sheet -->
	<bean id="PerfGrpErrSheet"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:perfGrpErrorSheet.jrxml"
		p:reportDataKey="datasource" />
	
	<!-- ClaimsAuditAssesment -->
	<bean id="claimsAuditAssesments"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:auditAssesmentMaster.jrxml"
		p:reportDataKey="datasource" 
		p:subReportUrls-ref="claimsAuditAssesmentsSubReportUrls"
		p:subReportDataKeys-ref="claimsAuditAssesmentsSubReportDatasource"/>

	<util:map id="claimsAuditAssesmentsSubReportUrls">
	    <!-- This entry key must be declared exactly as you declared it in the master JRXML file -->
	    <!-- Here we assigned two sub-report templates -->
	    <entry key="JasperCustomSubReportLocation1" value="classpath:claimsAuditAssesmentIn.jrxml"/>
	    <entry key="JasperCustomSubReportLocation2" value="classpath:claimsAuditAssesmentOut.jrxml"/>
	</util:map>
	
	<util:list id="claimsAuditAssesmentsSubReportDatasource">
	    <!-- This value must be declared exactly as you declared it in the master JRXML file -->
	    <!-- Here we assigned two sub-report datasources -->
	    <value>JasperCustomSubReportDatasource1</value>
	    <value>JasperCustomSubReportDatasource2</value>
	</util:list>
	
	<!--claim Assesement only OOS  -->
	<bean id="claimsAuditAssesmentsOos"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:claimsAuditAssesmentOutSample.jrxml"
		p:reportDataKey="datasource" />
	
	<!-- EDIT Code report -->
	<bean id="EDITcodeReport"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:editCodeReport.jrxml"
		p:reportDataKey="datasource" />
	
	<!--InSample OutOfSample Report -->
	<!--Auditor-->
	<bean id="isOosAuditorReport"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:inSampleOutSampleAuditorReport.jrxml"
		p:reportDataKey="datasource" />
	<!--Admin-->
	<bean id="isOosAdminReport"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:inSampleOutSampleAdminReport.jrxml"
		p:reportDataKey="datasource" />
		
	<bean id="errorReport"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:errorReport.jrxml"
		p:reportDataKey="datasource" />
		
	<bean id="associateReport"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:associateReport.jrxml"
		p:reportDataKey="datasource" />
		
	<bean id="errorCodeReport"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:errorCodeReport.jrxml"
		p:reportDataKey="datasource" />
		
	<bean id="oprUnitReport"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:oprUnitReport.jrxml"
		p:reportDataKey="datasource" />
		
	<bean id="rootCauseReport"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:rootCauseReport.jrxml"
		p:reportDataKey="datasource" />
		
	<bean id="perfGroupReport"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:perfGroupReport.jrxml"
		p:reportDataKey="datasource" />
		
	<bean id="auditAssesment"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:auditAssesment.jrxml"
		p:reportDataKey="datasource" />
		
	<bean id="highDollar"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:highDollarReport.jrxml"
		p:reportDataKey="datasource" />
		
	<!--Reports End  -->		
	<bean id="pdfReport"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsPdfView"
		p:url="classpath:tree-template.jrxml"
		p:reportDataKey="datasource" />
			
	<bean id="xlsReport"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsXlsView"
		p:url="classpath:tree-template.jrxml"
		p:reportDataKey="datasource" />
	
	<bean id="htmlReport"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsHtmlView"
		p:url="classpath:tree-template.jrxml" 
		p:reportDataKey="datasource" />
	
	<bean id="csvReport"
	   class="org.springframework.web.servlet.view.jasperreports.JasperReportsCsvView"
		p:url="classpath:tree-template.jrxml"
		p:reportDataKey="datasource" />
	
</beans>