<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@page import="com.carefirst.audit.model.AuditSearch"%>
<head>
<link href="webResources/css/qadb.css" rel="stylesheet">

<script type="text/javascript" src="webResources/js/metro.min.js"></script>
<!-- Local JavaScript -->

<link href="webResources/css/iconFont.css" rel="stylesheet">
<link href="webResources/css/docs.css" rel="stylesheet">
<link href="webResources/js/prettify/prettify.css" rel="stylesheet">

<!-- Load JavaScript Libraries -->
<script type="text/javascript" src="webResources/js/datep.js"></script>
<script src="webResources/js/jquery/jquery.min.js"></script>
<script src="webResources/js/jquery/jquery.widget.min.js"></script>


<script type="text/javascript" src="webResources/js/jquery.min.js"></script>
<script type="text/javascript" src="webResources/js/jquery-ui.js"></script> 
<script type="text/javascript" src="webResources/js/qadb.js"></script>

<script src="webResources/js/jquery/jquery.dataTables.js"></script>
<link href="webResources/css/qadb.css" rel="stylesheet">
<link href="webResources/css/jquery-ui.css" rel="stylesheet">
<style>
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
	margin-right: auto;
	height: 250%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}
.hidden
        {
            display: none;
        }
.ui-widget {
	font-family: Verdana,Arial,sans-serif;
	font-size: 1.1em;
}
.ui-widget .ui-widget {
	font-size: 1em;
}
.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
	font-family: Verdana,Arial,sans-serif;
	font-size: 1em;
}
.ui-widget-content {
	border: 1px solid #aaaaaa;
	background: #ffffff url() 50% 50% repeat-x;
	color: #222222;
}
.ui-widget-content a {
	color: #222222;
}
/* .ui-widget-header {
	border: 1px solid #aaaaaa;
	background:#2573ab  url(images/ui-bg_highlight-soft_75_cccccc_1x100.png) 50% 50% repeat-x;
	color: #e6f5fc;
	font-weight: bold;
} */
.ui-widget-header a {
	color: #222222;
}
.ui-datepicker-trigger{
background-image: url("webResources/images/Forms/Icn_Date_Picker.png");
height: 36px;
width: 36px;
background-color:white;
}
.ui-icon-circle-triangle-e{
background-image: url("webResources//Navbar/Icn_Right_Arrow.png");
}
.ui-icon-circle-triangle-w{
background-image: url("webResources/images/skip_backward.png");
}
.headerA {
	background: url('webResources/images/Forms/Icn_Accordion_Collapse.png')
		no-repeat;
	background-position: right 0px;
	cursor: pointer;
}

.collapsed .headerA {
	background-image:
		url('webResources/images/Forms/Icn_Accordion_Expand.png');
}

.contentA {
	height: auto;
	min-height: 100px;
	overflow: hidden;
	transition: all 0.3s linear;
	-webkit-transition: all 0.3s linear;
	-moz-transition: all 0.3s linear;
	-ms-transition: all 0.3s linear;
	-o-transition: all 0.3s linear;
	background-color: #fafafa;
}

.collapsed .contentA {
	min-height: 0px;
	height: 0px;
}        

#error {
    padding: 5px 5px 5px 5px ;
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	width:350px;
	font-size:11pt;
	background-color:#CE352C;
	color:white;
}
</style>



<title><spring:message code="reports.claimsAuditAssessment.title" /></title>
</head>
<body >
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container2">

		<!-- Sidebar Start-->
		<div id="left-sidebar">
			<h2 style="color: white ;padding-top: 32px;"><spring:message code="reports.claimsAuditAssessment.leftNav.heading1" /></h2>
			<h3 style="color: white"><spring:message code="reports.claimsAuditAssessment.leftNav.heading2" /></h3>
		</div>
		<!-- Sidebar End-->


		<div id="content-container" style="padding-left: 40px">
		<form:form id="auditAssessmentForm" name="auditAssessmentForm" onsubmit="return ValidatesClaimsReport()" method="GET" commandName="auditAssessmentForm" action="Claims_Audit_Assessment_Report" style="width:700px">   
			<div style="padding: 10px 0px 0px 0px;">
				<span class="bread1"><spring:message code="reports.bread1"></spring:message></span><span class="bread2"><spring:message code="reports.claimsAuditAssessment.leftNav.heading1"></spring:message></span>
			</div>
			<br>
			<div id="error" ></div>
			<div id="indicate1" class="line-separator"></div>
			<div class="panelContainer">
				<div id="dcnDetails" class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
			 			<span style="color: #0070c0; font-size: 16px"><spring:message code="audit.new.associate" /></span>
			 		</div>
				<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
				<table class="tableForm" >
					<tr>
						<td style="padding-right: 66px;"><spring:message code="audit.search.associate.name" /></td>
						<td><div class="input-control select" >
							 <select id="associateName" name="associateName" style="width: 250px;">
							 		<option selected value=""><spring:message
																code="audit.search.select.associate" /></option>
									<c:forEach items="${claimProcessors}" var="claimProcessors">
													<option value="${claimProcessors.associateId}"
														 ${claimProcessors.associateId== advSearch.associateName ? 'selected="selected"' : ''}>${claimProcessors.associateName}</option>
									</c:forEach>
								</select>
							</div>
						
						<!-- <div class="input-control text">
								<input type="text" name="associateName" />
							</div> -->
						</td>
					</tr>
					<tr>
						<td><spring:message code="audit.search.employee" /></td>
						<td><div class="input-control select" >
							 <select id="empNo" name="empNo" style="width: 200px;">
							 		<option selected value=""><spring:message
																code="audit.search.select.employee" /></option>
									<c:forEach items="${facetsIds}" var="facetsIds">
													<option value="${facetsIds.facetsId}"
														 ${facetsIds.facetsId== advSearch.empNo ? 'selected="selected"' : ''}>${facetsIds.facetsId}</option>
									</c:forEach>
								</select>
							</div>
						
						<!-- <div class="input-control text">
								<input type="text"  name="empNo" />
							</div> -->
						</td>
					</tr>
					
				</table>
			</div>
			</div>	
			</div>
			
			<div class="panelContainer">
				<div id="dcnDetails" class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
					 <span style="color: #0070c0; font-size: 16px"><spring:message code="audit.search.results.processed.date" /></span>
					 </div>
				<div class="contentA" style="padding-top: 10px;padding-left: 7px;">	 
						<table class="tableForm">
							<tr>
								<td><spring:message code="audit.search.processed.date.from" /></td>
								<td><div class="input-control text" >
			                                 <input id="datepick1" size="50" name="processedDateFrom" />
											
			                                </div>
								</td>
							</tr>
							<tr>
								<td><spring:message code="audit.search.processed.date.to" /></td>
								<td><div class="input-control text" >
			                                 <input id="datepick2" size="50" name="processedDateTo" />
			                              </div>
								</td>
							</tr>
			
						</table>
				</div>
				</div>
				</div>
				<div class="panelContainer">
				<div id="dcnDetails" class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
			 			<span style="color: #0070c0; font-size: 16px"><spring:message code="audit.new.claim.dcn" /></span>
					</div>
					<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
							<table class="tableForm">
								<tr>
									<td><spring:message code="audit.new.claim.dcn" /></td>
									<td><div class="input-control text">
											<input type="text" maxlength="12" name="dcn" id="dcn" />
										</div>
									</td>
								</tr>
				
				
							</table>
					</div>
					</div>
					</div>
					
					<div class="panelContainer">
				<div id="dcnDetails" class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
						<span style="color: #0070c0; font-size: 16px"><spring:message code="audit.search.results.auditor" /></span>
					</div>
					<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
						<table class="tableForm">
							<tr>
								<td><spring:message code="audit.search.auditor.name" /></td>
								<td><div class="input-control select">
										<select name="auditorName" id="auditorName">
											<option selected value=""><spring:message
																code="audit.search.select.auditor" /></option>
											<c:forEach items="${auditorsList}" var="auditorsList">
												<option value="${auditorsList.auditorName}"
																	${auditorsList.auditorName == advSearch.auditorName ? 'selected="selected"' : ''}>${auditorsList.auditorName}</option>
											</c:forEach>
										</select>
									</div>
								</td>
							</tr>
							<tr>
								<td><span style="color: #0070c0; font-size: 16px"><spring:message code="audit.search.results.audit.date" /></span></td>
							</tr>
							<tr>
								<td><spring:message code="audit.search.processed.date.from" /></td>
								<td><div class="input-control text" >
			                                 <input id="datepick3" size="50"  name="auditDateFrom" />
											
			                                </div>
								</td>
							</tr>
							<tr>
								<td><spring:message code="audit.search.processed.date.to" /></td>
								<td><div class="input-control text" >
			                                 <input id="datepick4" size="50" name="auditDateTo" />
											
			                                </div>
								</td>
							</tr>
						</table>
					</div>
					</div>
					</div>
					<br>
					<div style="width:750px">
			    <div style="float:right">
    	<button type="reset" class="button inverse">Reset</button> <button type="submit" style="background-color:#298fd8" class="button default">Generate</button> 
   			</div>
			</div>
			</form:form>
			
		
		
		
		
		<script type="text/javascript">
		$(function() {
		$('.headerA').click(function() {
			$(this).closest('.containerA').toggleClass('collapsed');
		});

	});
		
		
</script>

<script type="text/javascript">
		
	$("#datepick1").datepicker({
	showOn:"button",
    onSelect: function(selected) {
      $("#datepick2").datepicker("option","minDate", selected)
    },
	});
	$("#datepick2").datepicker({
	showOn:"button",
    onSelect: function(selected) {
      $("#datepick1").datepicker("option","maxDate", selected)
    },
	});
	
	$("#datepick3").datepicker({
	showOn:"button",
    onSelect: function(selected) {
      $("#datepick4").datepicker("option","minDate", selected)
    },
	});
	$("#datepick4").datepicker({
	showOn:"button",
    onSelect: function(selected) {
      $("#datepick3").datepicker("option","maxDate", selected)
    },
	});
	jQuery(function($){
		$("#datepick1").mask("99/99/9999",{placeholder:"MM/DD/YYYY"});
		$("#datepick2").mask("99/99/9999",{placeholder:"MM/DD/YYYY"});
		$("#datepick3").mask("99/99/9999",{placeholder:"MM/DD/YYYY"});
		$("#datepick4").mask("99/99/9999",{placeholder:"MM/DD/YYYY"});
	});	
		</script>	
			
		</div>

	</div>

	<jsp:include page="footer.jsp"></jsp:include>

</body>

</html>
