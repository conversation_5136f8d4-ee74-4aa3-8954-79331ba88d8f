
<!DOCTYPE html>
<html style="background-color:#dddfe1;">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<head>
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<style>
.container {
	width: 1040px;
	height: 90%;
	background-color: white;
	padding: 0.4px 15px 0px 25px;
}
</style>
<link
	href="${pageContext.request.contextPath}/webResources/css/qadb.css"
	rel="stylesheet">
<!-- Local JavaScript -->
<script src="${pageContext.request.contextPath}/webResources/js/time.js"></script>
<link href="webResources/css/qadb.css" rel="stylesheet">

<title>QADB-Home</title>
</head>
<body>
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container">
		<div style="padding: 10px 15px 0px 30px;">
			<table width="89%">
				<!-- first block -->
				<tr>
					<p class="date">
						<script>
							document.write(today);
						</script>
					</p>

					<h3>Claims Quality Assurance</h3>

				</tr>
				<!--   headings -->
				<c:set var="userRole">
					<%=request.getHeader("iv-groups")%>
				</c:set>
				<%-- <c:set var="userRole" > "Contractor","picapp_users","PeopleSoft_ELM_User" </c:set>  --%>
				<tr>
					<c:if
						test="${(fn:contains(userRole, 'qadb-samd-auditor_users'))||(fn:contains(userRole, 'qadb-cd-auditor_users'))||(fn:contains(userRole, 'qadb-samd-admin_users'))||(fn:contains(userRole, 'qadb-cd-admin_users'))||(fn:contains(userRole, 'qadb-superadmin_users'))}">
						<td>
							<h3>Audit</h3>
						</td>
						<td></td>
					</c:if>
					<td>
						<h3>Reports</h3>
					</td>
					<td></td>
					<!-- <td>
					<h3>Help</h3>
				</td> -->
				</tr>


				<!-- second block -->
				<c:choose>
					<c:when test="${(fn:contains(userRole, 'qadb-samd-auditor_users'))||(fn:contains(userRole, 'qadb-cd-auditor_users'))||(fn:contains(userRole, 'qadb-samd-admin_users'))||(fn:contains(userRole, 'qadb-cd-admin_users'))||(fn:contains(userRole, 'qadb-superadmin_users'))}">
						<tr>
							<td>
								<div class="tile ">
									<div class="tile-content image">
										<a href="audit"><img
											src="${pageContext.request.contextPath}/webResources/images/Tiles/audits.png">
										</a>
									</div>
								</div>
							</td>
							<td rowspan="2">
								<div class="tile double-vertical">

									<div class="tile-content image">
										<img
											src="${pageContext.request.contextPath}/webResources/images/Tiles/history.png">

										<c:forEach items="${recent}" var="rs">
											<div style="padding-top:12px">
												<span
													style="color:white;font-size:15px;padding:0px 0px 0px 5px">DCN
													# ${rs.dcnNo}</span><br> <span
													style="color:white;font-size:12px;padding:0px 0px 0px 6px">${rs.time}
													&nbsp; ${rs.date} </span>
											</div>
										</c:forEach>


										<c:if test="${empty recent}">
											<div style="padding-top:12px">
												<span
													style="color:white;font-size:15px;padding:0px 0px 0px 13px">No
													recent audits </span><br> <span
													style="color:white;font-size:15px;padding:0px 0px 0px 13px">
													available</span><br>
											</div>

										</c:if>

									</div>

								</div>
							</td>
							<td>
								<div class="tile">
									<div class="tile-content image">
										<a href="scores"> <img
											src="${pageContext.request.contextPath}/webResources/images/Tiles/score.png"></a>

									</div>
								</div>


							</td>
							<td rowspan="2">
								<div class="tile double-vertical2">
									<div class="tile double-vertical2content1">
										<div class="tile-content image">
											<a href="performanceReport"> <img
												src="${pageContext.request.contextPath}/webResources/images/Tiles/other1.png"></a>
										</div>
									</div>
									<div class="tile double-vertical2content2">

										<div class="tile-content image">
											<a href="claimsAuditAssessmentReport"> <img
												src="${pageContext.request.contextPath}/webResources/images/Tiles/other2.png"></a>
										</div>

									</div>
									<div class="tile double-vertical2content3">
										<div class="tile-content image">
											<a href="editCodeReport"> <img
												src="${pageContext.request.contextPath}/webResources/images/Tiles/other3.png"></a>
										</div>
									</div>

								</div>
							</td>

						</tr>
						<tr>
							<td>

								<div class="tile ">
									<div class="tile-content image">
										<a href="searchBasic"> <img
											src="${pageContext.request.contextPath}/webResources/images/Tiles/search.png">
										</a>
									</div>
								</div>

							</td>
							<td>
								<div class="tile ">
									<div class="tile-content image">
										<a href="userReports"> <img
											src="${pageContext.request.contextPath}/webResources/images/Tiles/userR.png">
										</a>
									</div>
								</div>


							</td>
							<td></td>
						</tr>
						

					</c:when>
					<c:otherwise>
						<tr>
							<td style="width: 100px;">
								<div class="tile">
									<div class="tile-content image">
										<a href="scores"> <img
											src="${pageContext.request.contextPath}/webResources/images/Tiles/score.png"></a>

									</div>
								</div>
							</td>
							<!-- Changes made to restrict the view for read-only users -->
							<c:if test="${(!fn:contains(userRole, 'qadb-samd-readonly_users')) && (!fn:contains(userRole, 'qadb-cd-readonly_users'))}">
							<td rowspan="2">
								<div class="tile double-vertical2">
									<div class="tile double-vertical2content1">
										<div class="tile-content image">
											<a href="performanceReport"> <img
												src="${pageContext.request.contextPath}/webResources/images/Tiles/other1.png"></a>
										</div>
									</div>
									<div class="tile double-vertical2content2">

										<div class="tile-content image">
											<a href="claimsAuditAssessmentReport"> <img
												src="${pageContext.request.contextPath}/webResources/images/Tiles/other2.png"></a>
										</div>

									</div>
									<div class="tile double-vertical2content3">
										<div class="tile-content image">
											<a href="editCodeReport"> <img
												src="${pageContext.request.contextPath}/webResources/images/Tiles/other3.png"></a>
										</div>
									</div>

								</div>
							</td>
						</c:if>
						</tr>
							<c:if test="${(!fn:contains(userRole, 'qadb-samd-readonly_users')) && (!fn:contains(userRole, 'qadb-cd-readonly_users'))}">
						<tr>
							<td>
								<div class="tile ">
									<div class="tile-content image">
										<a href="userReports"> <img
											src="${pageContext.request.contextPath}/webResources/images/Tiles/userR.png">
										</a>
									</div>
								</div>


							</td>
						</tr>
							</c:if>
					</c:otherwise>
				</c:choose>

			</table>
			<%-- <c:set var="userRole" > "Contractor","PeopleSoft_ELM_User","onestop_users","qadb-supera_users" </c:set> --%>

			<c:if
				test="${(fn:contains(userRole, 'qadb-superadmin_users'))||(fn:contains(userRole, 'qadb-samd-admin_users'))||(fn:contains(userRole, 'qadb-cd-admin_user'))}">

				<table>
					<td>
						<h3>Admin</h3>
					</td>
					<tr>
						<td>
							<div class="tile ">
								<div class="tile-content image">
									<a href="addAssociate"> <img
										src="${pageContext.request.contextPath}/webResources/images/Tiles/admin2.png">
									</a>
								</div>
							</div>


						</td>
						<td>
							<div class="tile ">
								<div class="tile-content image">

									<a href="addOperationalUnit"> <img
										src="${pageContext.request.contextPath}/webResources/images/Tiles/admin1.png">
									</a>
								</div>
							</div>


						</td>
						<td>
							<div class="tile ">
								<div class="tile-content image">
									<a href="addErrorCodes"> <img
										src="${pageContext.request.contextPath}/webResources/images/Tiles/admin4.png">
									</a>
								</div>
							</div>


						</td>
						<td>
							<div class="tile ">
								<div class="tile-content image">

									<a href="addPerformanceGroup"> <img
										src="${pageContext.request.contextPath}/webResources/images/Tiles/admin3.png">
									</a>
								</div>
							</div>


						</td>
						<td>
							<div class="tile ">
								<div class="tile-content image">
									<a href="addRootCause"> <img
										src="${pageContext.request.contextPath}/webResources/images/Tiles/admin5.png">
									</a>
								</div>
							</div>


						</td>
						<td>
							<div class="tile ">
								<div class="tile-content image">
									<a href="addJobTitles"> <img
										src="${pageContext.request.contextPath}/webResources/images/Tiles/admin6.png">
									</a>
								</div>
							</div>


						</td>
					</tr>
					<tr>
						<td>
							<div class="tile ">
								<div class="tile-content image">
									<a href="editAssociate"> <img
										src="${pageContext.request.contextPath}/webResources/images/Tiles/admin8.png">
									</a>
								</div>
							</div>


						</td>
						<td>
							<div class="tile ">
								<div class="tile-content image">

									<a href="editOperationalUnit"> <img
										src="${pageContext.request.contextPath}/webResources/images/Tiles/admin7.png">
									</a>
								</div>
							</div>


						</td>
						<td>
							<div class="tile ">
								<div class="tile-content image">
									<a href="editErrorCodes"> <img
										src="${pageContext.request.contextPath}/webResources/images/Tiles/admin10.png">
									</a>
								</div>
							</div>


						</td>
						<td>
							<div class="tile ">
								<div class="tile-content image">

									<a href="editPerformanceGroup"> <img
										src="${pageContext.request.contextPath}/webResources/images/Tiles/admin9.png">
									</a>
								</div>
							</div>


						</td>
						<td>
							<div class="tile ">
								<div class="tile-content image">
									<a href="editRootCause"> <img
										src="${pageContext.request.contextPath}/webResources/images/Tiles/admin11.png">
									</a>
								</div>
							</div>


						</td>
						<td>
							<div class="tile ">
								<div class="tile-content image">
									<a href="addSpecialty"> <img
										src="${pageContext.request.contextPath}/webResources/images/Tiles/admin12.png">
									</a>
								</div>
							</div>


						</td>
					</tr>
				</table>
			</c:if>




		</div>
	</div>

	<jsp:include page="footer.jsp"></jsp:include>

</body>

</html>
