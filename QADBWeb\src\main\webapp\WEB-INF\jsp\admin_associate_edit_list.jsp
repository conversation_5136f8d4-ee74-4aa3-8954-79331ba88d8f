<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<head>
<link href="webResources/css/qadb.css" rel="stylesheet">

<script type="text/javascript" src="webResources/js/metro.min.js"></script>
<!-- Local JavaScript -->

<link href="webResources/css/iconFont.css" rel="stylesheet">
<link href="webResources/css/docs.css" rel="stylesheet">
<link href="webResources/js/prettify/prettify.css" rel="stylesheet">

<!-- Load JavaScript Libraries -->
<script src="webResources/js/jquery/jquery.min.js"></script>
<script src="webResources/js/jquery/jquery.widget.min.js"></script>
<script src="webResources/js/jquery/jquery.dataTables.js"></script>
<style>
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
	margin-right: auto;
	height: 150%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}
.hidden
        {
            display: none;
        }
</style>



<title><spring:message code="admin.associate.title" /></title>
</head>
<body>
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container2">

		<!-- Sidebar Start-->
		<div id="left-sidebar">
			<h2 style="color: white;padding-top:32px"><spring:message code="admin.associate.edit.header1" /> </h2>
			<h3 style="color: white;padding-top:10px"><spring:message code="admin.associate.edit.header2" /> </h3>

		
		</div>
		<!-- Sidebar End-->

		<div id="content-container" style="padding-left: 40px">

			<form:form id="searchForm" name="searchForm" method="GET"
				commandName="associateSearchForm" action="editAssociateRes" style="width:700px">


				<div style="padding: 10px 0px 0px 0px;">
					<span class="bread1"><spring:message
							code="admin.associate.bread1" /> </span><span class="bread2"><spring:message
							code="admin.associate.edit.bread2" />
					</span>
				</div>
				<br>
				<span style="color: #0070c0; font-size: 16px"><spring:message code="admin.associate.edit.associate" /></span>
				<div class="line-separator"></div>
				<br>

				<table class="tableForm">
					<tr>
						<td style="padding: 5px 0px 0px 5px;"><spring:message code="admin.associate.name" /></td>
						<td><div class="input-control text">
								<input type="text" id="associateName" name="associateName" value="${associateName}" />
							</div></td>
					</tr>
					<tr>
						<td style="padding: 5px 0px 0px 5px;"><spring:message code="admin.associate.database" /></td>
						<td><div class="input-control text">
								<input type="text" id="facetsId" name="facetsId" value="${facetsUserId}" />
							</div></td>
					</tr>
					

				</table>
				

				<div style="width: 750px">
					<div style="float: right">
						<button type="button" onclick="clearText('associateName');" class="button inverse"><spring:message code="qadb.reset" /></button>
						<button style="background-color: #298fd8"
							class="button default"> <spring:message code="qadb.search" /></button>
					</div>
				</div>

			</form:form>


		<c:if test="${null!=searchResult }">	
		<br> <br> <span style="color: #0070c0; font-size: 16px"> <spring:message code="qadb.searchResults" />
				</span> <br>
			<br>
			<div class="line-separator"></div>	
			<table class="table striped hovered dataTable" id="searchDatatable"
				width="700px">
				<thead>
					<tr style="height: 30px">
						<td class="text-left" font-size="25px"></td> <!-- hiddden column -->
						<td class="text-left" font-size="25px" style="padding-left: 5px"><spring:message code="admin.associate.name" /></td>
						<td class="text-left" font-size="25px"><spring:message code="admin.associate.job.title" /></td>
						<td class="text-left" font-size="25px"><spring:message code="admin.associate.edit.status" /></td>
					</tr>
				</thead>

				<tbody>

					<c:forEach items="${searchResult}" var="rs">
						<tr style="height: 30px">
							<td>${rs.associateId}</td>
							<td style="padding-left: 7px">${rs.associateName}</td>
							<td>${rs.jobTitles}</td>
							<td>${rs.auditingStatus}</td>
							
						</tr>
					</c:forEach> 
					
					
				</tbody>

			</table>
			</c:if>	
			 <script>
				$(function() {
					$('#searchDatatable').dataTable({
										
										
					"columns" : [
					
					{
           				 sClass: "hidden"
      				}, 
					
					{
					"render": function(data, type, full, meta) {
					return '<a href="getAssociate?id='+full[0]+'"><u>'+data+'<u></a>';
                   
            }
            }, {}, {} ]
									});
				});
			</script> 

		</div>
	</div>

	<jsp:include page="footer.jsp"></jsp:include>

</body>

</html>
