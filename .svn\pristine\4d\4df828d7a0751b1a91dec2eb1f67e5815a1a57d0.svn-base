package com.carefirst.qadb.logger;

public class LogContextManager {
	public static ThreadLocal<LogContext> userThreadLocal;

	public static ThreadLocal<LogContext> getThreadlocalObj() {
		if (userThreadLocal == null) {
			userThreadLocal = new InheritableThreadLocal<LogContext>();
		}
		return userThreadLocal;
	}

	public static void set(LogContext user) {

		userThreadLocal.set(user);

	}

	public static void unset() {

		userThreadLocal.remove();

	}

	public static LogContext get() {

		return userThreadLocal.get();

	}

}
