<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/">

<!-- Writes all messages to an application log file. -->
<appender name="ApplicationLogFileAppender" class="org.apache.log4j.RollingFileAppender">
		<param name="File" value="QADBWebApp.log"/>
		<param name="MaxFileSize" value="50MB"/>
		<param name="MaxBackupIndex" value="7"/>
		<layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern" value="%d %-5p [%t] (%F:%L) - %m%n"/>
		</layout>
</appender>
<root>
		<priority value="DEBUG"/>
		<appender-ref ref="ApplicationLogFileAppender"/>
</root>
	
</log4j:configuration>
