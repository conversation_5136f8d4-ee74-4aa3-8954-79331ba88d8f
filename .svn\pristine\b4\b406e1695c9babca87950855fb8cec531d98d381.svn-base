
<!DOCTYPE html>
<%@page language="java"
	contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>

<html style="background-color: #dddfe1;">
<div id="popupAuditCountD">
		<!-- popup claim records Starts Here -->
	</div>
	<div id="popupAuditCount"
		style="text-align: left; padding: 25px 25px 25px 25px;border-radius: 5px;
    box-shadow: 5px 5px 5px #888888;">
		<img id="close" src="webResources/images/Misc/Icn_Close2.png"
			onclick="divCount_hide()"> <span style="font-size: 15px;"><b>
				<spring:message code="audit.new.popup.count.message1" /> ${audCountRS.processDate} ${audCountRS.associateName} 
				<spring:message code="audit.new.popup.count.message2" /></b>
		 <div style="padding-left:10px">
			<ol>
				<li><spring:message code="audit.new.popup.count.in.sample.audits" /> --------------------------  ${audCountRS.insCount}
					<ul style="list-style-type:disc">
						<li>${audCountRS.insNonMockCount} Non-Mocks and ${audCountRS.insMockCount} mocks</li>
						<li>${audCountRS.insPrePayCount} Pre-pays and ${audCountRS.insPostPayCount} Post-pays</li>
					</ul>
				</li>
				<br>
				<li><spring:message code="audit.new.popup.count.out.sample.audits" /> ---------------------  ${audCountRS.oosCount}
					<ul style="list-style-type:disc">
						<li>${audCountRS.oosNonMockCount} Non-Mocks and ${audCountRS.oosMockCount} mocks</li>
						<li>${audCountRS.oosPrePayCount} Pre-pays and ${audCountRS.oosPostPayCount} Post-pays</li>
					</ul>
				</li>
				<br> *****************************************************<br>
				<li><spring:message code="audit.new.popup.count.total.audits" /> --------------------------------  ${audCountRS.totalAuditCount}</li>
			</ol>
			</div>
			 </span>
		<div align="right" style="width: 450">
			<table>
				<tr>
					<a onclick="divCount_hide();">
						<button  class="button default"><spring:message code="audit.new.popup.count.ok" /></button>
					</a>
				</tr>
			</table>
		</div>
	</div>
</html>