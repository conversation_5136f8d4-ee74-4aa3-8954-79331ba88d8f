package com.carefirst.qadb.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.sql.DataSource;

import oracle.jdbc.OracleTypes;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.StringUtils;

import com.carefirst.audit.model.Associate;
import com.carefirst.audit.model.ErrorCodes;
import com.carefirst.qadb.constant.QADBConstants;
import com.carefirst.qadb.converter.CheckConverter;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

public class ErrorCodesDAOImpl implements ErrorCodesDAO{
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);

	@Autowired
	DataSource dataSource;
	
	/**
	 * Method to call a procedure to save new error code.
	 */
	@Override
	public ErrorCodes saveErrorCodes(ErrorCodes errorCodes) throws SQLException{
		logger.debug("*** Entry saveErrorCodes method ***");
		//logger.debug("Error name" + errorCodes.getErrorCode());
		String errorSp = QADBConstants.ADMIN_ERROR_SAVE_SP;
		Connection conn = null;
		CallableStatement callableStatment = null;
		ErrorCodes errorCodesRO = new ErrorCodes();
		if(null!=errorCodes.getErrorCode()){
			errorCodesRO.setErrorCode(errorCodes.getErrorCode());
		}
		try {
			conn = dataSource.getConnection();
			callableStatment = conn.prepareCall(errorSp);
			
			//Set the input parameters for the procedure
			callableStatment.setString(1, errorCodes.getUserid());
			callableStatment.setString(2, errorCodes.getUserActyp());
			if(null!=errorCodes.getErrorCode()){
				callableStatment.setString(3,errorCodes.getErrorCode());
			}else{
				callableStatment.setNull(3, java.sql.Types.INTEGER);
			}
			callableStatment.setString(4, errorCodes.getErrorName());
			callableStatment.setString(5, errorCodes.getErrorDescription());
			logger.debug("grp--- "+ new CheckConverter().getUserGroup(errorCodes.getGrpSammd(), errorCodes.getGrpCd()));
			callableStatment.setString(6,new CheckConverter().getUserGroup(errorCodes.getGrpSammd(), errorCodes.getGrpCd()));
			callableStatment.setString(7, errorCodes.getEffectiveStartDate());
			callableStatment.setString(8, errorCodes.getEffectiveEndDate());
			//callableStatment.setString(9, "Y");
			if("N".equalsIgnoreCase(new CheckConverter().convert(errorCodes.getStatus()))){
				callableStatment.setString(9, "Y");
				callableStatment.setNull(10, java.sql.Types.DATE);
				callableStatment.setNull(11, java.sql.Types.DATE);
			}else{
				if(null!=errorCodes.getDisableStartDate() && null!=errorCodes.getDisableEndDate()){
					callableStatment.setString(9, "N");
					callableStatment.setString(10, errorCodes.getDisableStartDate());
					callableStatment.setString(11, errorCodes.getDisableEndDate());
				}else{
					callableStatment.setString(9, "Y");
					callableStatment.setNull(10, java.sql.Types.DATE);
					callableStatment.setNull(11, java.sql.Types.DATE);
				}
				
			}
			callableStatment.registerOutParameter(12, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(13, OracleTypes.VARCHAR);
			
			callableStatment.execute();
			
			logger.debug("status code 	 = "+callableStatment.getString(12));
			logger.debug("status message = "+callableStatment.getString(13));
			
			errorCodesRO.setSucessCode(callableStatment.getString(12));
			errorCodesRO.setSuccessMsg(callableStatment.getString(13));
			
		} catch (Exception e) {
			conn.rollback();
			e.printStackTrace();
			
		}finally {
			if (callableStatment != null) {
				callableStatment.close();
			}

			if (conn != null) {
				conn.close();
			}
		}
		logger.debug("*** Exit saveErrorCodes method ***"+errorCodesRO.getSucessCode());
		return errorCodesRO;
	}
	
	/**
	 * Method to search the error by error code
	 * @throws SQLException 
	 */
	@Override
	public List<ErrorCodes> searchErrorCodes(ErrorCodes errorCodes) throws SQLException{
		logger.debug("*** Entry searchErrorCodes method ***");
		String getErrorDetails = QADBConstants.ADMIN_ERROR_SEARCH_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getErrorDetails);
		List<ErrorCodes> errorCodesList= new ArrayList<ErrorCodes>();
		try {
			
			callableStatment.setString(1,errorCodes.getUserid());
			callableStatment.setString(2,errorCodes.getUserActyp());
			logger.debug("usrGrp "+errorCodes.getUserGrp());
			callableStatment.setString(3,errorCodes.getUserGrp());
			if(null!=errorCodes.getErrorCode()){
					if(errorCodes.getErrorCode().equalsIgnoreCase("")){
						callableStatment.setNull(4, java.sql.Types.INTEGER);
					}else{
						callableStatment.setString(4,errorCodes.getErrorCode());
					}
			}
			else{
				callableStatment.setNull(4, java.sql.Types.INTEGER);
			}
			if(null!=errorCodes.getErrorName()){
				if(errorCodes.getErrorName()==""){
					callableStatment.setNull(5, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(5,errorCodes.getErrorName());
				}
			}
			else{	
				callableStatment.setNull(5, java.sql.Types.VARCHAR);
			}
			callableStatment.setNull(6, java.sql.Types.VARCHAR);
			callableStatment.setNull(7, java.sql.Types.VARCHAR);
			callableStatment.registerOutParameter(8, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(9, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(10, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(10));
			callableStatment.executeUpdate();
			
			ResultSet  rs =   (ResultSet) callableStatment.getObject(8);			
			logger.debug("Row count" +rs.getRow());
			
			while (rs.next()) {
				ErrorCodes errorEO = new ErrorCodes();
				if(null!=rs.getString("ERROR_ID")){
					errorEO.setErrorCode((rs.getString("ERROR_ID")).toString());
				}
				if(null!=rs.getString("ERROR_NAME")){
					errorEO.setErrorName((rs.getString("ERROR_NAME")).toString());
				}
				if(null!=rs.getString("ERROR_DESC")){
					errorEO.setErrorDescription((rs.getString("ERROR_DESC")).toString());
				}
				if(null!=rs.getString("STATUS")){
					errorEO.setErrorStatus((rs.getString("STATUS")).toString());
				}
				errorCodesList.add(errorEO);
			}	
			rs.close();
			
		} catch (Exception e) {
			logger.debug("Exception : "+e.getMessage());
			// TODO: handle exception
		}finally{
			callableStatment.close();
			conn.close();
		}
		logger.debug("*** Exit searchErrorCodes method ***");
		return errorCodesList;
	}
	
	
	//Change for DMND0003323
	@Override
	public JRDataSource searchErrorCodesList(ErrorCodes errorCodes) throws SQLException{
		logger.debug("*** Entry searchErrorCodes method ***");
		String getErrorDetails = QADBConstants.ADMIN_ERROR_SEARCH_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getErrorDetails);
		List<ErrorCodes> errorCodesList= new ArrayList<ErrorCodes>();
		try {
			
			callableStatment.setString(1,errorCodes.getUserid());
			callableStatment.setString(2,errorCodes.getUserActyp());
			logger.debug("usrGrp "+errorCodes.getUserGrp());
			callableStatment.setString(3,errorCodes.getUserGrp());
			if(null!=errorCodes.getErrorCode()){
					if(errorCodes.getErrorCode().equalsIgnoreCase("")){
						callableStatment.setNull(4, java.sql.Types.INTEGER);
					}else{
						callableStatment.setString(4,errorCodes.getErrorCode());
					}
			}
			else{
				callableStatment.setNull(4, java.sql.Types.INTEGER);
			}
			if(null!=errorCodes.getErrorName()){
				if(errorCodes.getErrorName()==""){
					callableStatment.setNull(5, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(5,errorCodes.getErrorName());
				}
			}
			else{	
				callableStatment.setNull(5, java.sql.Types.VARCHAR);
			}
			callableStatment.setNull(6, java.sql.Types.VARCHAR);
			callableStatment.setNull(7, java.sql.Types.VARCHAR);
			callableStatment.registerOutParameter(8, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(9, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(10, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(10));
			callableStatment.executeUpdate();
			
			ResultSet  rs =   (ResultSet) callableStatment.getObject(8);			
			logger.debug("Row count" +rs.getRow());
			
			while (rs.next()) {
				ErrorCodes errorEO = new ErrorCodes();
				if(null!=rs.getString("ERROR_ID")){
					errorEO.setErrorCode((rs.getString("ERROR_ID")).toString());
				}
				if(null!=rs.getString("ERROR_NAME")){
					errorEO.setErrorName((rs.getString("ERROR_NAME")).toString());
				}
				if(null!=rs.getString("ERROR_DESC")){
					errorEO.setErrorDescription((rs.getString("ERROR_DESC")).toString());
				}
				if(null!=rs.getString("STATUS")){
					errorEO.setErrorStatus((rs.getString("STATUS")).toString());
				}
				errorCodesList.add(errorEO);
			}	
			rs.close();
			
		} catch (Exception e) {
			logger.debug("Exception : "+e.getMessage());
			// TODO: handle exception
		}finally{
			callableStatment.close();
			conn.close();
		}
		JRDataSource ds = new JRBeanCollectionDataSource(errorCodesList);
		logger.debug("*** Exit searchErrorCodes method ***");
		return ds;
	}
	
	/**
	 * Method to fetch the data for the error code selected to edit
	 * @throws SQLException 
	 */
	@Override
	public ErrorCodes getErrorData(ErrorCodes errorCodes) throws SQLException{
		logger.debug("*** Entry getErrorData method ***");
		String getErrorDetails = QADBConstants.ADMIN_ERROR_SEARCH_SP;
		ErrorCodes errorEO = new ErrorCodes();
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getErrorDetails);
		try {
			callableStatment.setString(1,errorCodes.getUserid());
			callableStatment.setString(2,errorCodes.getUserActyp());
			callableStatment.setString(3,errorCodes.getUserGrp());
			callableStatment.setString(4,errorCodes.getErrorCode());
			callableStatment.setNull(5, java.sql.Types.VARCHAR);
			callableStatment.setNull(6, java.sql.Types.VARCHAR);
			callableStatment.setNull(7, java.sql.Types.VARCHAR);
			callableStatment.registerOutParameter(8, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(9, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(10, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(10));
			
			callableStatment.executeUpdate();
			ResultSet  rs =   (ResultSet) callableStatment.getObject(8);			
			logger.debug("Row count" +rs.getRow());

			while (rs.next()) {
				if(null!=rs.getString("ERROR_ID")){
					errorEO.setErrorCode((rs.getString("ERROR_ID")).toString());
				}
				if(null!=rs.getString("ERROR_NAME")){
					errorEO.setErrorName((rs.getString("ERROR_NAME")).toString());
				}
				if(null!=rs.getString("ERROR_DESC")){
					errorEO.setErrorDescription((rs.getString("ERROR_DESC")).toString());
				}
				if(null!=rs.getString("ERROR_EFFECTIVE_START_DT")){
					errorEO.setEffectiveStartDate((rs.getString("ERROR_EFFECTIVE_START_DT")).toString());
				}
				if(null!=rs.getString("ERROR_EFFECTIVE_END_DT")){
					errorEO.setEffectiveEndDate((rs.getString("ERROR_EFFECTIVE_END_DT")).toString());
				}
				if(null!=rs.getString("STATUS")){
					errorEO.setErrorStatus((rs.getString("STATUS")).toString());
				}
				if(null!=rs.getString("ERR_CODE_DISABLE_START_DT")){
					errorEO.setDisableStartDate((rs.getString("ERR_CODE_DISABLE_START_DT")).toString());
				}
				if(null!=rs.getString("ERR_CODE_DISABLE_END_DT")){
					errorEO.setDisableEndDate((rs.getString("ERR_CODE_DISABLE_END_DT")).toString());
				}
				if (null != (rs.getString("ERROR_USER_GROUP"))) {
					errorEO.setUserGrp((rs.getString("ERROR_USER_GROUP")).toString());
				} else {
					errorEO.setUserGrp("");
				}
				logger.debug("Ro grp "+rs.getString("ERROR_USER_GROUP"));
			}	
			rs.close();
			
			
		} catch (Exception e) {
			// TODO: handle exception
		}finally{
			callableStatment.close();
			conn.close();
		}
		
		return errorEO;
	}
	
	/**
	 * Method to set the flag as 'N' for the selected error
	 */
	@Override
	public ErrorCodes deleteError(ErrorCodes errorCodes) {
		logger.debug("*** Entry deleteError method ***");
		String sql = "UPDATE  TBL_LKUP_ERRORS "
				+ " SET  ERROR_ACTIVE_FLG  = 'N' "
				+ " WHERE ERROR_ID          = "
				+errorCodes.getErrorCode()+ " "    ;
		
		ErrorCodes errorCodesRO = new ErrorCodes();
		JdbcTemplate jdbcTemp = new JdbcTemplate(dataSource);
		jdbcTemp.execute(sql);
		
		errorCodesRO.setSuccessMsg("SUCCESS");
		logger.debug("*** Exit deleteError method ***");
		return errorCodesRO;
	}

}
