package com.carefirst.qadb.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

import javax.sql.DataSource;

import oracle.jdbc.OracleTypes;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import com.carefirst.audit.model.Speciality;
import com.carefirst.qadb.constant.QADBConstants;
import com.carefirst.qadb.converter.CheckConverter;


public class SpecialityDAOImpl implements SpecialityDAO {
	
	
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);
	
	@Autowired
	DataSource dataSource;

	@Override
	public List<Speciality> getSpeciality(Speciality specialtyTO) throws SQLException {

		logger.debug("*** Entry getSpecialityImpl method ***");
		logger.debug("user id "+specialtyTO.getUserId()+"UsGrp "+specialtyTO.getUserGrp());
		String getSpeciality = QADBConstants.ADMIN_SPECIALTY_SEARCH_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getSpeciality);
		List<Speciality> specialityList= new ArrayList<Speciality>();
		try {
			
			callableStatment.setString(1, specialtyTO.getUserId());
			callableStatment.setString(2, specialtyTO.getSearchType());
			callableStatment.setString(3, specialtyTO.getUserGrp());
			callableStatment.setNull(4, java.sql.Types.INTEGER);
			callableStatment.registerOutParameter(5, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(6, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(7, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(7));
			callableStatment.executeUpdate();
			
			ResultSet  rs =   (ResultSet) callableStatment.getObject(5);			
			logger.debug("Row count" +rs.getRow());
			
			while (rs.next()) {
				Speciality specialityEO = new Speciality();
				logger.debug("id "+(rs.getString("SPECIALTY_ID")).toString());
				if(null!=rs.getString("SPECIALTY_ID")){
					specialityEO.setId((rs.getString("SPECIALTY_ID")).toString());
				}
				if(null!=rs.getString("SPECIALTY_NAME")){
					specialityEO.setSpecialty((rs.getString("SPECIALTY_NAME")).toString());
				}
				if(null!=rs.getString("SPECIALTY_STATUS")){
					specialityEO.setStatus((rs.getString("SPECIALTY_STATUS")).toString());
				}
				specialityList.add(specialityEO);
			}	
			rs.close();
			
		} catch (Exception e) {
			logger.debug("Exception generated "+e.getMessage());
		}finally{
			callableStatment.close();
			conn.close();
		}
		logger.debug("*** Exit getSpecialityImpl method ***");
		return specialityList;
		
	}

	@Override
	public Speciality saveUpdateSpeciality(Speciality specialtyTO) throws SQLException {
		logger.debug("*** Entry saveUpdateSpeciality method ***");
		logger.debug("Job Title id " + specialtyTO.getId()+ "-"+specialtyTO.getSpecialty());
		String specialitySaveUpdateSp = QADBConstants.ADMIN_SPECIALTY_SAVE_SP;
		Connection conn = null;
		CallableStatement callableStatment = null;
		Speciality specialityRO = new Speciality();
		
		try {
			conn = dataSource.getConnection();
			callableStatment = conn.prepareCall(specialitySaveUpdateSp);
			
			//Set the input parameters for the procedure
			callableStatment.setString(1, "Testusr");
			callableStatment.setString(2, specialtyTO.getUserActyp());
			if(null != specialtyTO.getId()){
				callableStatment.setInt(3,Integer.parseInt(specialtyTO.getId()));
			}else{
				callableStatment.setNull(3, java.sql.Types.INTEGER);
			}
			callableStatment.setString(4, specialtyTO.getSpecialty());
			logger.debug("grp--- "+ new CheckConverter().getUserGroup(specialtyTO.getGrpSammd(), specialtyTO.getGrpCd()));
			callableStatment.setString(5,new CheckConverter().getUserGroup(specialtyTO.getGrpSammd(), specialtyTO.getGrpCd()));
			logger.debug("status "+specialtyTO.getStatus()+ " chk " +new CheckConverter().convert(specialtyTO.getStatus()));
			callableStatment.setString(6, new CheckConverter().convert(specialtyTO.getStatus()));
			callableStatment.registerOutParameter(7, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(8, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(9, OracleTypes.VARCHAR);
			
			callableStatment.execute();
			
			logger.debug("status code = "+callableStatment.getString(8));
			logger.debug("status message = "+callableStatment.getString(9));
			specialityRO.setSucessCode(callableStatment.getString(8).toString());
			specialityRO.setSuccessMsg(callableStatment.getString(9).toString());
			
		} catch (Exception e) {
			conn.rollback();
			e.printStackTrace();
			
		}finally {
			if (callableStatment != null) {
				callableStatment.close();
			}

			if (conn != null) {
				conn.close();
			}
		}
		logger.debug("*** Exit saveUpdateSpeciality method ***");
		return specialityRO;
	}

	@Override
	public Speciality getSpecialityById(Speciality specialtyTO) throws SQLException {
		
		logger.debug("*** Entry getSpecialityById method ***"+specialtyTO.getId()+" "+specialtyTO.getUserGrp());
		String getSpecialtyDetails = QADBConstants.ADMIN_SPECIALTY_SEARCH_SP;
		Speciality specialityEO = new Speciality();
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getSpecialtyDetails);
		try {
			callableStatment.setString(1, specialtyTO.getUserId());
			callableStatment.setString(2, specialtyTO.getSearchType());
			callableStatment.setString(3, specialtyTO.getUserGrp());
			callableStatment.setInt(4,Integer.parseInt(specialtyTO.getId()));
			callableStatment.registerOutParameter(5, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(6, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(7, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(7));
			
			callableStatment.executeUpdate();
			ResultSet  rs =   (ResultSet) callableStatment.getObject(5);			
			logger.debug("Row count" +rs.getRow());

			while (rs.next()) {
				if(null!=rs.getString("SPECIALTY_ID")){
					specialityEO.setId((rs.getString("SPECIALTY_ID")).toString());
				}
				if(null!=rs.getString("SPECIALTY_NAME")){
					specialityEO.setSpecialty((rs.getString("SPECIALTY_NAME")).toString());
				}
				if(null!=rs.getString("SPECIALTY_ACTIVE_FLG")){
					specialityEO.setStatus((rs.getString("SPECIALTY_ACTIVE_FLG")).toString());
				}
				if (null != (rs.getString("SPECIALTY_USER_GROUP"))) {
					specialityEO.setUserGrp((rs.getString("SPECIALTY_USER_GROUP")).toString());
				} else {
					specialityEO.setUserGrp("");
				}
				logger.debug("Ro grp "+rs.getString("SPECIALTY_USER_GROUP"));
			}	
			rs.close();
			
			
		} catch (Exception e) {
			logger.debug("Exception generated "+e.getMessage());
		}finally{
			callableStatment.close();
			conn.close();
		}
		logger.debug("*** Exit getSpecialityById method ***");
		return specialityEO;
	}


}
