<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.0.3.final using JasperReports Library version 6.0.3  -->
<!-- 2015-04-14T15:29:45 -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="Blank_A4_Landscape_1" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" >
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="56" splitType="Stretch">
			<staticText>
				<reportElement x="167" y="10" width="469" height="46" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="31" isBold="false"/>
				</textElement>
				<text><![CDATA[March 2015 Daily Claims Audit Assesment]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band height="82" splitType="Stretch">
			<rectangle>
				<reportElement x="20" y="0" width="348" height="21" forecolor="#0A0909" backcolor="#0A0A0A" />
			</rectangle>
			<staticText>
				<reportElement x="88" y="-3" width="220" height="45" forecolor="#FFFFFF" backcolor="#2DE339" />
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="19" isBold="true"/>
				</textElement>
				<text><![CDATA[IN-SAMPLE AUDITS]]></text>
			</staticText>
			<staticText>
				<reportElement x="22" y="39" width="298" height="43" />
				<textElement>
					<font size="20"/>
				</textElement>
				<text><![CDATA[ASSOCIATE : ]]></text>
			</staticText>
			<staticText>
				<reportElement x="348" y="31" width="131" height="30" "/>
				<textElement>
					<font fontName="Haettenschweiler" size="18"/>
				</textElement>
				<text><![CDATA[Employee #:]]></text>
			</staticText>
			<staticText>
				<reportElement x="348" y="55" width="131" height="27" />
				<textElement>
					<font fontName="Haettenschweiler" size="18"/>
				</textElement>
				<text><![CDATA[Supervisor:]]></text>
			</staticText>
			<rectangle>
				<reportElement x="421" y="34" width="81" height="21" />
			</rectangle>
			<rectangle>
				<reportElement x="421" y="58" width="159" height="21" />
			</rectangle>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="119" splitType="Stretch">
			<rectangle>
				<reportElement x="0" y="2" width="800" height="5" backcolor="#080707" />
			</rectangle>
			<rectangle>
				<reportElement x="0" y="88" width="800" height="5" backcolor="#080707" />
			</rectangle>
			<staticText>
				<reportElement x="-31" y="13" width="131" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[DCN]]></text>
			</staticText>
			<staticText>
				<reportElement x="-24" y="36" width="131" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[Membership #]]></text>
			</staticText>
			<staticText>
				<reportElement x="66" y="13" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="183" y="16" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[Audit Type]]></text>
			</staticText>
			<staticText>
				<reportElement x="63" y="58" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[Under Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="121" y="36" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[High Dollar]]></text>
			</staticText>
			<staticText>
				<reportElement x="64" y="36" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[Over Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="183" y="36" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[Mock Audit]]></text>
			</staticText>
			<staticText>
				<reportElement x="183" y="58" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[Out-Of-Sample]]></text>
			</staticText>
			<staticText>
				<reportElement x="254" y="16" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[Adj. Required]]></text>
			</staticText>
			<staticText>
				<reportElement x="254" y="38" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[HB3 Claim]]></text>
			</staticText>
			<staticText>
				<reportElement x="255" y="58" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[Processed On]]></text>
			</staticText>
			<staticText>
				<reportElement x="321" y="16" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[Error Codes]]></text>
			</staticText>
			<staticText>
				<reportElement x="379" y="16" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[Monetary]]></text>
			</staticText>
			<staticText>
				<reportElement x="434" y="16" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[Procedural]]></text>
			</staticText>
			<staticText>
				<reportElement x="492" y="16" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[Error Type]]></text>
			</staticText>
			<staticText>
				<reportElement x="568" y="16" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[Explanation]]></text>
			</staticText>
			<staticText>
				<reportElement x="650" y="16" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[SOP Reference]]></text>
			</staticText>
			<staticText>
				<reportElement x="651" y="59" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[Performance Group]]></text>
			</staticText>
			<staticText>
				<reportElement x="722" y="17" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[Auditor]]></text>
			</staticText>
			<staticText>
				<reportElement x="724" y="36" width="91" height="20" />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="15"/>
				</textElement>
				<text><![CDATA[Audit Date]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="144" splitType="Stretch"/>
	</detail>
	<columnFooter>
		<band height="45" splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="54" splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band height="42" splitType="Stretch"/>
	</summary>
</jasperReport>
