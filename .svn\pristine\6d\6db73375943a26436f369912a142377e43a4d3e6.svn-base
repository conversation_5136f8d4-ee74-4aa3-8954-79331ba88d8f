package com.carefirst.qadb.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import javax.sql.DataSource;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import oracle.jdbc.OracleTypes;

import com.carefirst.audit.model.AuditDetails;
import com.carefirst.audit.model.ErrorCodes;
import com.carefirst.audit.model.Mapping;
import com.carefirst.audit.model.RootCause;
import com.carefirst.qadb.constant.QADBConstants;
import com.carefirst.qadb.converter.CheckConverter;

public class MappingDAOImpl implements MappingDAO{
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);

	@Autowired
	DataSource dataSource;
	
	@Override
	public List<String> getYears() {
		// TODO Auto-generated method stub
		List<String> years = new ArrayList<String>();
		
		int maxYear = Calendar.getInstance().get(Calendar.YEAR);
		
		int minYear = 2000;
		
		for (int i = maxYear; i>=minYear; i--){
			years.add(String.valueOf(i));
		}
		return years;
	}

	/**
	 * Calling procedure to save a new mapping
	 */ 
	@Override
	public Mapping saveMapping(Mapping mappingTO) throws SQLException {
		logger.debug("*** Entry saveMapping method ***");

		String mappingSp = QADBConstants.ADMIN_MAPPING_SP;
		Connection conn = null;
		CallableStatement callableStatment = null;
		Mapping mappingRO = new Mapping();
		try{
			
			conn = dataSource.getConnection();
			callableStatment = conn.prepareCall(mappingSp);
			
			//Set the input parameters for the procedure
			callableStatment.setString(1, mappingTO.getUserId());
			callableStatment.setString(2, mappingTO.getUserActTyp());
			callableStatment.setString(3, mappingTO.getAuditorName());
			String associateIdsArrString = (mappingTO.getAssociatesIds()).toString().replaceAll("[\\s\\[\\]]", "");
			associateIdsArrString=associateIdsArrString.replace(",", "||");
			logger.debug("--->"+associateIdsArrString);
			callableStatment.setString(4,associateIdsArrString);
			if((mappingTO.getUserActTyp()).equalsIgnoreCase("EDIT")){
				callableStatment.setString(5, mappingTO.getOldMonth());
				callableStatment.setString(6, mappingTO.getOldYear());
			}else{
				callableStatment.setNull(5, java.sql.Types.VARCHAR);
				callableStatment.setNull(6, java.sql.Types.VARCHAR);
			}
			callableStatment.setString(7, mappingTO.getMonth());
			callableStatment.setString(8, mappingTO.getYear());
			callableStatment.registerOutParameter(9, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(10, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(11, OracleTypes.VARCHAR);
			
			callableStatment.execute();
			
			logger.debug("status code = "+callableStatment.getString(10));
			logger.debug("status message = "+callableStatment.getString(11));
			
			mappingRO.setSuccessCode(callableStatment.getString(10));
			mappingRO.setSuccessMsg(callableStatment.getString(11));
			
		}catch (Exception e) {
			logger.debug("Exception = "+e.getMessage());
			conn.rollback();
			e.printStackTrace();
			
		}finally {
			if (callableStatment != null) {
				callableStatment.close();
			}
			if (conn != null) {
				conn.close();
			}
		}
		logger.debug("*** Exit saveMapping method ***");
		return mappingRO;
	}
	
	/**
	 * Method to search the mapping
	 */
	@Override
	public Mapping getMapping(Mapping mappingSearch) throws SQLException {

		logger.debug("*** Entry searchMapping method ***");
		String searchMapping = QADBConstants.ADMIN_MAPPING_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(searchMapping);
		Mapping mappingEO = new Mapping();
		try {
			
			//Set the input parameters for the procedure
			callableStatment.setString(1, mappingSearch.getUserId());
			callableStatment.setString(2, mappingSearch.getUserActTyp());
			if((null!=mappingSearch.getAuditorName()) && (mappingSearch.getAuditorName() != "")){
				callableStatment.setString(3,mappingSearch.getAuditorName());
			}else{
				callableStatment.setNull(3, java.sql.Types.VARCHAR);
			}
			callableStatment.setNull(4, java.sql.Types.VARCHAR);
			callableStatment.setString(5, mappingSearch.getMonth());
			callableStatment.setString(6, mappingSearch.getYear());
			callableStatment.setNull(7, java.sql.Types.VARCHAR);
			callableStatment.setNull(8, java.sql.Types.VARCHAR);
			callableStatment.registerOutParameter(9, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(10, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(11, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(11));
			callableStatment.executeUpdate();
			
			ResultSet  rs =   (ResultSet) callableStatment.getObject(9);			
			logger.debug("Row count" +rs.getRow());
			
			int count = 0;
			String associateIds = "";
			List<String> associatesIdrs = new ArrayList<String>();
			while (rs.next()) {
				 
				logger.debug(""+rs.getString("AUDITOR")+" "+rs.getString("ASSOCIATE_NAME")+" "+rs.getString("MAPPED_MONTH")+" "+rs.getString("MAPPED_YEAR"));
				if(null!=rs.getString("AUDITOR")){
					mappingEO.setAuditorName((rs.getString("AUDITOR")).toString());
				}
				if(null!=rs.getString("ASSOCIATE_NAME")){
					mappingEO.setAssociateName((rs.getString("ASSOCIATE_NAME")).toString());
				}
				if(null!=rs.getString("MAPPED_MONTH")){
					mappingEO.setMonth((rs.getString("MAPPED_MONTH")).toString());
				}
				if(null!=rs.getString("MAPPED_YEAR")){
					mappingEO.setYear((rs.getString("MAPPED_YEAR")).toString());
				}
				
				associatesIdrs.add((rs.getString("ASSOCIATE_ID")).toString());
				if(count > 0){
					associateIds = associateIds+ ","+(rs.getString("ASSOCIATE_ID")).toString();
				}
				else{
					associateIds = (rs.getString("ASSOCIATE_ID")).toString();
				}
				++count;
				logger.debug("map asso id "+associateIds+ "-- "+count+" "+associatesIdrs);
				mappingEO.setAssociateIdRS(associateIds);
				mappingEO.setAssociatesIds(associatesIdrs);
				logger.debug(mappingEO.getAssociatesIds());
				
			}	
			rs.close(); 
			
		} catch (Exception e) {
			logger.debug("Exception = "+e.getMessage());
			e.printStackTrace();
		}finally{
			callableStatment.close();
			conn.close();
		}
		logger.debug("*** Exit searchMapping method ***");
		return mappingEO;
	
	}
	
	
	/**
	 * Method to search the mapping
	 */
	@Override
	public List<Mapping> searchMapping(Mapping mappingSearch) throws SQLException {

		logger.debug("*** Entry searchMapping method ***");
		String searchMapping = QADBConstants.ADMIN_MAPPING_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(searchMapping);
		List<Mapping> mappingList = new ArrayList<Mapping>();
		
		try {
			
			//Set the input parameters for the procedure
			callableStatment.setString(1, mappingSearch.getUserId());
			callableStatment.setString(2, mappingSearch.getUserActTyp());
			if((null!=mappingSearch.getAuditorName()) && (mappingSearch.getAuditorName() != "")){
				callableStatment.setString(3,mappingSearch.getAuditorName());
			}else{
				callableStatment.setNull(3, java.sql.Types.VARCHAR);
			}
			callableStatment.setNull(4, java.sql.Types.VARCHAR);
			callableStatment.setString(5, mappingSearch.getMonth());
			callableStatment.setString(6, mappingSearch.getYear());
			callableStatment.setNull(7, java.sql.Types.VARCHAR);
			callableStatment.setNull(8, java.sql.Types.VARCHAR);
			callableStatment.registerOutParameter(9, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(10, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(11, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(11));
			callableStatment.executeUpdate();
			
			ResultSet  rs =   (ResultSet) callableStatment.getObject(9);			
			logger.debug("Row count" +rs.getRow());
			
			int count = 0;
			String associateIds = "";
			List<String> associatesIdrs = new ArrayList<String>();
			while (rs.next()) {
				Mapping mappingEO = new Mapping();
				logger.debug(""+rs.getString("AUDITOR")+" "+rs.getString("ASSOCIATE_NAME")+" "+rs.getString("MAPPED_MONTH")+" "+rs.getString("MAPPED_YEAR"));
				if(null!=rs.getString("AUDITOR")){
					mappingEO.setAuditorName((rs.getString("AUDITOR")).toString());
				}
				if(null!=rs.getString("ASSOCIATE_NAME")){
					mappingEO.setAssociateName((rs.getString("ASSOCIATE_NAME")).toString());
				}
				if(null!=rs.getString("MAPPED_MONTH")){
					mappingEO.setMonth((rs.getString("MAPPED_MONTH")).toString());
				}
				if(null!=rs.getString("MAPPED_YEAR")){
					mappingEO.setYear((rs.getString("MAPPED_YEAR")).toString());
				}
				
				associatesIdrs.add((rs.getString("ASSOCIATE_ID")).toString());
				if(count > 0){
					associateIds = associateIds+ ","+(rs.getString("ASSOCIATE_ID")).toString();
				}
				else{
					associateIds = (rs.getString("ASSOCIATE_ID")).toString();
				}
				++count;
				logger.debug("map asso id "+associateIds+ "-- "+count+" "+associatesIdrs);
				mappingEO.setAssociateIdRS(associateIds);
				mappingEO.setAssociatesIds(associatesIdrs);
				logger.debug(mappingEO.getAssociatesIds());
				mappingList.add(mappingEO);
			}	
			rs.close(); 
			
		} catch (Exception e) {
			logger.debug("Exception = "+e.getMessage());
			e.printStackTrace();
		}finally{
			callableStatment.close();
			conn.close();
		}
		logger.debug("*** Exit searchMapping method ***");
		return mappingList;
	
	}
	
}
