package com.carefirst.qadb.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.sql.DataSource;

import oracle.jdbc.OracleTypes;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import com.carefirst.audit.model.OperationalUnit;
import com.carefirst.qadb.constant.QADBConstants;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

public class OperationalUnitDAOImpl implements OperationalUnitDAO {

	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);

	@Autowired
	DataSource dataSource;

	@Override
	public List<OperationalUnit> getOperations() throws SQLException {
		String sql = QADBConstants.ADMIN_OP_LIST ;
		
		List<OperationalUnit> operations = new ArrayList<OperationalUnit>();

		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
					OperationalUnit operation = new OperationalUnit();
					operation.setOperationId((rs.getString("OPERATION_ID")).toString());
					operation.setOperation((String) (rs.getString("OPERATION_DESC")));
					operations.add(operation);
				}
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}		
		return operations;
	}

	@Override
	public List<OperationalUnit> getDivisions() throws SQLException {
		String sql = QADBConstants.ADMIN_OP_DIVISION_LIST ;
		List<OperationalUnit> divisions = new ArrayList<OperationalUnit>();

		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
					OperationalUnit  division = new OperationalUnit();
					division.setDivisionId((rs.getString("DIVISION_ID")).toString());
					division.setDivision((String) (rs.getString("DIVISION_DESC")));
					divisions.add(division);
				}	
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}				
		return divisions;
	}

	@Override
	public List<OperationalUnit> getDirectors() throws SQLException {
		String sql = QADBConstants.ADMIN_OP_DIRECTORS_LIST;
		List<OperationalUnit> directors = new ArrayList<OperationalUnit>();
		
		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
					OperationalUnit director = new OperationalUnit();
					director.setDirectorId((rs.getString("ASSOCIATE_ID")).toString());
					director.setDirector((String) ((rs.getString("ASSOCIATE_LAST_NAME"))+" "+(rs.getString("ASSOCIATE_FIRST_NAME"))));
					directors.add(director);
				}
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}				
		return directors;
	}

	@Override
	public List<OperationalUnit> getManagers() throws SQLException {
		String sql = QADBConstants.ADMIN_OP_MANAGERS_LIST ;
		List<OperationalUnit> managers = new ArrayList<OperationalUnit>();

		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
					OperationalUnit manager = new OperationalUnit();
					manager.setManagerId((rs.getString("ASSOCIATE_ID")).toString());
					manager.setManager((String) ((rs.getString("ASSOCIATE_LAST_NAME"))+" "+(rs.getString("ASSOCIATE_FIRST_NAME"))));			
					managers.add(manager);
				}	
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}						
		return managers;
	}

	@Override
	public List<OperationalUnit> getSupervisors() throws SQLException {
		String sql = QADBConstants.ADMIN_OP_SUPERVISORS_LIST ;
		List<OperationalUnit>  supervisors = new ArrayList<OperationalUnit>();
		
		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
					OperationalUnit  supervisor = new OperationalUnit();
					supervisor.setSupervisorId((rs.getString("ASSOCIATE_ID")).toString());
					supervisor.setSupervisor((String) ((rs.getString("ASSOCIATE_LAST_NAME"))+" "+(rs.getString("ASSOCIATE_FIRST_NAME"))));
					supervisors.add(supervisor);
				}
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}					
		return supervisors;
	}
	
	/**
	 * 
	 */
	@Override
	public OperationalUnit saveUpdateOpUnit(OperationalUnit operationalUnitDetails) throws Exception {
		logger.debug("Entry saveUpdateOpUnit method");
		String opUnitSp = QADBConstants.ADMIN_OP_SAVE_SP;
		Connection conn = null;
		CallableStatement callableStatment = null;
		OperationalUnit opUnitRO = new OperationalUnit();
		
		try {
			//SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yy");
			conn = dataSource.getConnection();
			callableStatment = conn.prepareCall(opUnitSp);
			if((operationalUnitDetails.getUserActyp()).equals("NEW") || (operationalUnitDetails.getUserActyp()).equals("EDIT")){
				
				callableStatment.setString(1, operationalUnitDetails.getUserId());//userid
				callableStatment.setString(2, operationalUnitDetails.getUserActyp());
				if(operationalUnitDetails.getUnitId()!= null){
					callableStatment.setInt(3,Integer.parseInt(operationalUnitDetails.getUnitId()));
				}else{
					callableStatment.setNull(3, java.sql.Types.INTEGER);
				}
				callableStatment.setString(4, operationalUnitDetails.getUnitName());
				callableStatment.setString(5,operationalUnitDetails.getEffectiveDateStart());
				callableStatment.setString(6,operationalUnitDetails.getEffectiveDateEnd());
				callableStatment.setInt(7, Integer.parseInt(operationalUnitDetails.getOperationId()));
				callableStatment.setInt(8, Integer.parseInt(operationalUnitDetails.getDivisionId()));
				callableStatment.setString(9, operationalUnitDetails.getDirectorId());
				callableStatment.setString(10,operationalUnitDetails.getManagerId());
				callableStatment.setString(11, operationalUnitDetails.getSupervisorId());
				callableStatment.setInt(12, Integer.parseInt(operationalUnitDetails.getLocationId()));
				callableStatment.setString(13, "Y");
				callableStatment.registerOutParameter(14, OracleTypes.VARCHAR);
				callableStatment.registerOutParameter(15, OracleTypes.VARCHAR);
				
				callableStatment.execute();
				
				logger.debug("Success code"+callableStatment.getString(14));
				logger.debug("Success message"+callableStatment.getString(15));
				 
				opUnitRO.setSucessCode(callableStatment.getString(14));
				opUnitRO.setSuccessMsg(callableStatment.getString(15));
			}
			
		} catch (Exception e) {
			conn.rollback();
			e.printStackTrace();
			logger.debug("Exception generated"+e.getMessage());
		}finally {
			if (callableStatment != null) {
				callableStatment.close();
			}

			if (conn != null) {
				conn.close();
			}
		}
		logger.debug("Exit saveUpdateOpUnit method");
		return opUnitRO;
	}
	
	/**
	 * @throws SQLException 
	 * 
	 */
	@Override
	public List<OperationalUnit> getOpUnitSearchResults(OperationalUnit opUnitSearchTO) throws SQLException {
		logger.debug("*** Entry getOpUnitSearchResults method ***"+opUnitSearchTO.getUnitName());
		logger.debug("user id "+opUnitSearchTO.getUserId());
		String getOpunits = QADBConstants.ADMIN_OP_SEARCH_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getOpunits);
		List<OperationalUnit> opUnitList= new ArrayList<OperationalUnit>();
		try {
			
			callableStatment.setString(1, opUnitSearchTO.getUserId());
			callableStatment.setString(2, opUnitSearchTO.getSearchType());
			callableStatment.setNull(3, java.sql.Types.VARCHAR);
			callableStatment.setNull(4, java.sql.Types.INTEGER);
			if(null!=opUnitSearchTO.getUnitName()){
				if(opUnitSearchTO.getUnitName().equalsIgnoreCase("")){
					callableStatment.setNull(5, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(5, opUnitSearchTO.getUnitName());
				}
			}else{
				callableStatment.setNull(5, java.sql.Types.VARCHAR);
			}
			callableStatment.setNull(6, java.sql.Types.VARCHAR);
			callableStatment.setNull(7, java.sql.Types.VARCHAR);
			callableStatment.registerOutParameter(8, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(9, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(10, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(10));
			callableStatment.executeUpdate();
			
			ResultSet  rs =   (ResultSet) callableStatment.getObject(8);			
			while (rs.next()) {
				OperationalUnit opUnit = new OperationalUnit();
				if(null!=rs.getString("UNIT_ID")){
					opUnit.setUnitId((rs.getString("UNIT_ID")).toString());
				}
				if(null!=rs.getString("UNIT_NAME")){
					opUnit.setUnitName((rs.getString("UNIT_NAME")).toString());
				}
				if(null!=rs.getString("OPERATION_NAME")){
					opUnit.setOperation((rs.getString("OPERATION_NAME")).toString());
				}
				if(null!=rs.getString("DIVISION_NAME")){
					opUnit.setDivision((rs.getString("DIVISION_NAME")).toString());
				}
				if(null!=rs.getString("STATUS")){
					opUnit.setStatus((rs.getString("STATUS")).toString());
				}
				opUnitList.add(opUnit);
				
			}	
			rs.close();
			
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}finally{
			callableStatment.close();
			conn.close();
		}
		logger.debug("*** Exit getJobTitles method ***");
		return opUnitList;
		
	}
	
	@Override
	public JRDataSource getOpUnitSearchResultsList(OperationalUnit opUnitSearchTO) throws SQLException {
		logger.debug("*** Entry getOpUnitSearchResultsList method ***"+opUnitSearchTO.getUnitName());
		logger.debug("user id "+opUnitSearchTO.getUserId());
		String getOpunits = QADBConstants.ADMIN_OP_SEARCH_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getOpunits);
		List<OperationalUnit> opUnitList= new ArrayList<OperationalUnit>();
		try {
			
			callableStatment.setString(1, opUnitSearchTO.getUserId());
			callableStatment.setString(2, opUnitSearchTO.getSearchType());
			callableStatment.setNull(3, java.sql.Types.VARCHAR);
			callableStatment.setNull(4, java.sql.Types.INTEGER);
			if(null!=opUnitSearchTO.getUnitName()){
				if(opUnitSearchTO.getUnitName().equalsIgnoreCase("")){
					callableStatment.setNull(5, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(5, opUnitSearchTO.getUnitName());
				}
			}else{
				callableStatment.setNull(5, java.sql.Types.VARCHAR);
			}
			callableStatment.setNull(6, java.sql.Types.VARCHAR);
			callableStatment.setNull(7, java.sql.Types.VARCHAR);
			callableStatment.registerOutParameter(8, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(9, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(10, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(10));
			callableStatment.executeUpdate();
			
			ResultSet  rs =   (ResultSet) callableStatment.getObject(8);			
			while (rs.next()) {
				OperationalUnit opUnit = new OperationalUnit();
				if(null!=rs.getString("UNIT_ID")){
					opUnit.setUnitId((rs.getString("UNIT_ID")).toString());
				}
				if(null!=rs.getString("UNIT_NAME")){
					opUnit.setUnitName((rs.getString("UNIT_NAME")).toString());
				}
				if(null!=rs.getString("OPERATION_NAME")){
					opUnit.setOperation((rs.getString("OPERATION_NAME")).toString());
				}
				if(null!=rs.getString("DIVISION_NAME")){
					opUnit.setDivision((rs.getString("DIVISION_NAME")).toString());
				}
				if(null!=rs.getString("STATUS")){
					opUnit.setStatus((rs.getString("STATUS")).toString());
				}
				opUnitList.add(opUnit);
				
			}	
			rs.close();
			
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}finally{
			callableStatment.close();
			conn.close();
		}
		JRDataSource ds = new JRBeanCollectionDataSource(opUnitList);
		logger.debug("*** Exit getOpUnitSearchResultsList method ***");
		return ds;
		
	}
	
	/**
	 * @throws SQLException 
	 * 
	 */
	@Override
	public OperationalUnit getOpUnit(OperationalUnit opUnitSearchTO) throws SQLException  {
		logger.debug("Entry getOpUnit method");
		String getOpUnitDetails = QADBConstants.ADMIN_OP_SEARCH_SP;
		OperationalUnit opUnitEO = new OperationalUnit();
		
		Connection conn = null;
		CallableStatement callableStatment = null;
		try {
			conn = dataSource.getConnection();
			callableStatment = conn.prepareCall(getOpUnitDetails);
			callableStatment.setString(1,opUnitSearchTO.getUserId());
			callableStatment.setString(2,opUnitSearchTO.getSearchType());
			callableStatment.setString(3,QADBConstants.OP_GNRL);//Pagetype
			callableStatment.setInt(4,Integer.parseInt(opUnitSearchTO.getUnitId()));
			callableStatment.setString(5,null); 
			callableStatment.setString(6,null); 
			callableStatment.setString(7,null); 
			
			callableStatment.registerOutParameter(8, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(9, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(10, OracleTypes.VARCHAR);
			logger.debug(" Exe "+callableStatment.execute());
			
			logger.debug(callableStatment.getString(9));
			
			callableStatment.executeUpdate();
			ResultSet rs =   (ResultSet) callableStatment.getObject(8);			
			logger.debug("RS "+rs);
			logger.debug("Row count" +rs.getRow());
			while (rs.next()) {
				logger.debug("AssoName Edit"+rs.getString("UNIT_ACTIVE_FLG")+ " super "+rs.getString("UNIT_EFFECTIVE_START_DT")+ " man " +rs.getString("UNIT_MANAGER"));
				if(null!=rs.getString("UNIT_ID")){
					opUnitEO.setUnitId((rs.getString("UNIT_ID")).toString());
				}
				if(null!=rs.getString("UNIT_NAME")){
					opUnitEO.setUnitName((rs.getString("UNIT_NAME")).toString());
				}
				if(null!=rs.getString("STATUS")){
					opUnitEO.setStatus((rs.getString("STATUS")).toString());
				}
				if(null!=rs.getString("UNIT_EFFECTIVE_START_DT")){
					opUnitEO.setEffectiveDateStart((rs.getString("UNIT_EFFECTIVE_START_DT").toString()));
				}
				if(null!=rs.getString("UNIT_EFFECTIVE_END_DT")){
					opUnitEO.setEffectiveDateEnd((rs.getString("UNIT_EFFECTIVE_END_DT").toString()));
				}
				if(null!=rs.getString("LOCATION_ID")){
					opUnitEO.setLocationId((rs.getString("LOCATION_ID")));
				}
				if(null!=rs.getString("OPERATION_ID")){
					opUnitEO.setOperationId((rs.getString("OPERATION_ID")).toString());
				}
				if(null!=rs.getString("DIVISION_ID")){
					opUnitEO.setDivisionId((rs.getString("DIVISION_ID")).toString());
				}
				if(null!=rs.getString("UNIT_DIRECTOR")){
					opUnitEO.setDirectorId((rs.getString("UNIT_DIRECTOR")).toString());
				}
				if(null!=rs.getString("UNIT_MANAGER")){
					opUnitEO.setManagerId((rs.getString("UNIT_MANAGER")).toString());
				}
				if(null!=rs.getString("UNIT_SUPERVISOR")){
					opUnitEO.setSupervisorId((rs.getString("UNIT_SUPERVISOR")).toString());
				}
			}
			rs.close();
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}
		logger.debug("***Exit getOpUnit method***");
		return opUnitEO;
	}
	
	/**
	 * @throws SQLException 
	 * 
	 */
	@Override
	public OperationalUnit deleteOpUnit(OperationalUnit opUnitDetails) throws SQLException {
		logger.debug("***Entry deleteOpUnit method***");
		logger.debug("Op id = "+opUnitDetails.getUnitId());
		String sql = QADBConstants.ADMIN_OP_DELETE +opUnitDetails.getUnitId();
		
		OperationalUnit opUnitRO = new OperationalUnit();
		
		Connection conn =  null ;
		CallableStatement callableStatment = null;
		
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 callableStatment.executeQuery();
			 opUnitRO.setSuccessMsg("SUCCESS");
		} catch (Exception e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}
		logger.debug("***Exit deleteOpUnit method***");
		
		return opUnitRO;
	}
	
	/**
	 * @throws SQLException 
	 * 
	 */
	@Override
	public List<OperationalUnit> getAssociatesRS(OperationalUnit opUnitSearchTO) throws SQLException {
		logger.debug("*** Entry getOpUnitSearchResults method ***");
		logger.debug("user id "+opUnitSearchTO.getUserId());
		String getAssociates = QADBConstants.ADMIN_OP_SEARCH_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getAssociates);
		List<OperationalUnit> associateOpList= new ArrayList<OperationalUnit>();
		try {
			callableStatment.setString(1, opUnitSearchTO.getUserId());
			callableStatment.setString(2, opUnitSearchTO.getSearchType());
			callableStatment.setString(3, opUnitSearchTO.getPagetype());
			callableStatment.setInt(4,Integer.parseInt(opUnitSearchTO.getUnitId()));
			callableStatment.setString(5, null);
			callableStatment.setString(6, null);
			callableStatment.setString(7, null);
			callableStatment.registerOutParameter(8, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(9, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(10, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(10));
			callableStatment.executeUpdate();
			
			ResultSet  rs =   (ResultSet) callableStatment.getObject(8);			
			logger.debug("Row count" +rs.getRow());
			
			while (rs.next()) {
				OperationalUnit associateOp = new OperationalUnit();
				if(null!=rs.getString("ASSOCIATE_ID")){
					associateOp.setAssoId((rs.getString("ASSOCIATE_ID")).toString());
				}
				if(null!=rs.getString("ASSOCIATE_NAME")){
					associateOp.setAssoName((String) (rs.getString("ASSOCIATE_NAME")));
				}
				if(null!=rs.getString("START_DATE")){
					associateOp.setAssoStartDate((String) (rs.getString("START_DATE")));
				}
				if(null!=rs.getString("END_DATE")){
					associateOp.setAssoEndDate((String) (rs.getString("END_DATE")));
				}
				associateOpList.add(associateOp);
				
			}	
			rs.close();
			
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}finally{
			callableStatment.close();
			conn.close();
		}
		logger.debug("*** Exit getJobTitles method ***");
		return associateOpList;
		
	}

	@Override
	public JRDataSource getAssociatesRSList(OperationalUnit opUnitSearchTO) throws SQLException {
		logger.debug("*** Entry getOpUnitSearchResults method ***");
		logger.debug("user id "+opUnitSearchTO.getUserId());
		String getAssociates = QADBConstants.ADMIN_OP_SEARCH_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getAssociates);
		List<OperationalUnit> associateOpList= new ArrayList<OperationalUnit>();
		try {
			callableStatment.setString(1, opUnitSearchTO.getUserId());
			callableStatment.setString(2, opUnitSearchTO.getSearchType());
			callableStatment.setString(3, opUnitSearchTO.getPagetype());
			callableStatment.setInt(4,Integer.parseInt(opUnitSearchTO.getUnitId()));
			callableStatment.setString(5, null);
			callableStatment.setString(6, null);
			callableStatment.setString(7, null);
			callableStatment.registerOutParameter(8, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(9, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(10, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(10));
			callableStatment.executeUpdate();
			
			ResultSet  rs =   (ResultSet) callableStatment.getObject(8);			
			logger.debug("Row count" +rs.getRow());
			
			while (rs.next()) {
				OperationalUnit associateOp = new OperationalUnit();
				if(null!=rs.getString("ASSOCIATE_ID")){
					associateOp.setAssoId((rs.getString("ASSOCIATE_ID")).toString());
				}
				if(null!=rs.getString("ASSOCIATE_NAME")){
					associateOp.setAssoName((String) (rs.getString("ASSOCIATE_NAME")));
				}
				if(null!=rs.getString("START_DATE")){
					associateOp.setAssoStartDate((String) (rs.getString("START_DATE")));
				}
				if(null!=rs.getString("END_DATE")){
					associateOp.setAssoEndDate((String) (rs.getString("END_DATE")));
				}
				associateOpList.add(associateOp);
				
			}	
			rs.close();
			
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}finally{
			callableStatment.close();
			conn.close();
		}
		
		JRDataSource ds = new JRBeanCollectionDataSource(associateOpList);	
		
		logger.debug("*** Exit getJobTitles method ***");
		
		return ds;
	}
	
}