<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib  prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<head>
<link href="webResources/css/qadb.css" rel="stylesheet">

<script type="text/javascript" src="webResources/js/metro.min.js"></script>
<!-- Local JavaScript -->

<link href="webResources/css/iconFont.css" rel="stylesheet">
<link href="webResources/css/docs.css" rel="stylesheet">
<link href="webResources/js/prettify/prettify.css" rel="stylesheet">

<!-- Load JavaScript Libraries -->
<script type="text/javascript" src="webResources/js/qadb.js"></script>
<script src="webResources/js/jquery/jquery.min.js"></script>
<script src="webResources/js/jquery/jquery.widget.min.js"></script>
<script src="webResources/js/jquery/jquery.dataTables.js"></script>
<style>
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
	margin-right: auto;
	height: 150%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}

div.success {
	padding-left: 5px;
	padding-top: 5px;
	height: 30px; 
	background: #73AD21; 
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	color : white;
}

div.errorInfo {
    padding: 5px 5px 5px 5px ;
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	width:350px;
	font-size:10pt;
	background-color:#CE352C;
	color:white;
}

#error {
    padding: 5px 5px 5px 5px ;
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	width:350px;
	font-size:11pt;
	background-color:#CE352C;
	color:white;
}
.hidden
        {
            display: none;
        }
</style>
<script type="text/javascript">

var a = 1;
	

		$(document).ready(function() {
		if (a == 1) {
			$('#success').delay(800).fadeIn(400);
			
		}
		})

</script>


<title><spring:message code="admin.specialty.title" /></title>
</head>
<body>
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container2">

		<!-- Sidebar Start-->
		<%-- <c:choose>
   			 <c:when test="${edit != null}">
   			 	
   			 	<div id="left-sidebar">
					<h2 align="center" style="color: white ; padding-top:32px ">
					002 - Modifier ${eAssociateName} 
					</h2>
					<h3 style="color: white; padding:10px 0px 0px 20px">
						<spring:message code="admin.errorCodes.update.leftNav.heading2" />
					</h3>
				</div>
   			 
    		 </c:when>
    		 <c:otherwise>
    		 		<div id="left-sidebar">
					<h2 style="color: white ; padding-top:32px">
						<spring:message code="admin.jobTitle.leftNav.heading1" />
					</h2>
					<h3 style="color: white; padding-top:10px">
						<spring:message code="admin.jobTitle.leftNav.heading2" />
						</h3>
				</div>
    		 
    		 </c:otherwise>
    		 
    		 
    	</c:choose> --%>
    	
    	<div id="left-sidebar">
					<h2 style="color: white ; padding-top:32px">
						<spring:message code="admin.specialty.leftNav.heading1" />
					</h2>
					<h3 style="color: white; padding-top:10px">
						<spring:message code="admin.specialty.leftNav.heading2" />
						</h3>
				</div>
    		 
		<!-- Sidebar End-->

		<div id="content-container" style="padding-left: 40px">

<!--change form action depending upon save /update action  -->

	<%String formAction=""; %>

 	<c:if test="${edit != null}"> 
  		<% formAction="updateSpecialty" ;%>
    </c:if>
	<c:if test="${edit == null}"> 
  		<% formAction="saveSpecialty" ;%>
    </c:if>
    
			<form:form id="specialtyForm" name="specialtyForm" method="GET" onsubmit="return ValidatesSpecialty();" commandName="specialtyForm" action="<%=formAction%>" style="width:700px">


				<div style="padding: 10px 0px 0px 0px;">
					<span class="bread1"><spring:message
							code="admin.specialty.bread1" /> 
					</span>
					<span class="bread2"><spring:message
							code="admin.specialty.leftNav.heading1" />
					</span>
					<c:if test="${edit != null}">
					<span class="bread2"> > ${specialityRO.specialty} </span>
					</c:if>
				</div>
				
					<table width="98%" style="padding: 10px 10px 10px 50px">
					<tr style="height: 20px; border-bottom-color: gray;">


						<c:choose>
							<c:when test="${edit != null}">
								<td width="40%"><span style="font-size: 20px"> 
										<spring:message code="admin.specialty.updateSpecialty">
										</spring:message> 
										 		</span>
								</td>
								<td width="60%" style="text-align: right;">

									<button title="Cancel" type="reset" onclick="hideErrorDiv();"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Cancel.png" alt="Cancel">
									</button> <!-- <a href="#" onclick="divdel_show();">
										<button title="Delete" type="button"
											style="background-color: transparent; border-color: transparent; size: 10px;">
											<img 
												src="webResources/images/Actions/Icn_Delete.png" >
										</button>
								</a> -->
									<button title="Save" type="submit"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png" alt="Save">
									</button></td>
									
							</c:when>

							<c:otherwise>
								<td width="40%"><span style="font-size: 20px"><spring:message
											code="admin.specialty.addSpecialty"></spring:message> </span></td>
								<td width="60%" style="text-align: right;">
									<button type="reset" title="Cancel" onclick="hideErrorDiv();"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Cancel.png">
									</button>
									<button type="submit" title="Save"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png">
									</button></td>
							</c:otherwise>
						</c:choose>
					</tr>
				</table>
				
				<!--Success Msgs  -->
			<div id="error" ></div><div>
					<c:if test="${(successCode != '000') && (successCode != '201') && (successCode != null)}"> 
						<div id="divDuplicate" style="width: 240px;color: red;font-weight: bold;">${successMessage}</div>
					</c:if>
					<c:if test="${success == 'SUCCESS'}"> 
						<div id="success" class="success" style="width: 240px;">Specialty added successfully!</div>
					</c:if>
					<c:if test="${upSucess=='SUCCESS'}"> 
						<div id="success" class="success" style="width: 280px;">Specialty updated successfully!</div>
					</c:if>	
					<c:if test="${delSucess=='SUCCESS'}"> 
						<div id="success" style="padding-left: 5px; height: 20px; width: 220px; background: #99FF99; display: none;">Specialty deleted successfully!</div>
					</c:if>
					<%-- <c:if test="${(fn:contains(success, 'System')) || (fn:contains(upSucess, 'System')) || (fn:contains(delSucess, 'System'))}">
						<div id="success" class="errorInfo" style="width: 300px;">System Error, please try again after some time !</div>
					</c:if>	 --%>
					<c:if test="${successCode == '201'}">
						<div id="success" class="errorInfo" style="width: 300px;">System Error, please try again after some time !</div>
					</c:if>	
				</div>
				<br>
				
				
				<div class="line-separator"></div>
				<br>
				<div style="color:red">* indicates required</div>
				<br>
				<table class="tableForm">
					<tr>
						<td style="width:100px;padding:5px 0px 0px 10px"><spring:message code="admin.specialty.specialty" />&nbsp;<span style="color:red">*</span> </td>
						<td><div id="specialtyc" class="input-control text">
								<input type="text" id="specialty" name="specialty" value="${specialityRO.specialty}" style="width:360px"/>
								&nbsp;<span id="errmsgSpecialty" style="color:red">
							</div></td>
					</tr>
					
					<c:set var="userRole" > <%=request.getHeader("iv-groups")%> </c:set> 
								
								<c:choose>
	  								<c:when test="${(edit != null) && (fn:contains(userRole, 'qadb-superadmin_users')) && (!(fn:contains(userRole, 'null'))) }">
        							
        								<c:if test="${specialityRO.userGrp == '' }">
        									<tr>
											<td style="padding : 5px 0px 0px 10px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div class="input-control checkbox">
												<label> <input type="checkbox" name="grpSammd" id="grpSammd"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input type="checkbox" name="grpCd" id="grpCd"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>											
											</td>
											</tr>
		   								</c:if>	
		   								<c:if test="${specialityRO.userGrp == 'ALL' }">
        									<tr>
											<td style="padding : 5px 0px 0px 10px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div class="input-control checkbox">
												<label> <input type="checkbox" name="grpSammd" id="grpSammd" checked="checked"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input type="checkbox" name="grpCd" id="grpCd" checked="checked"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>											
											</td>
											</tr>
		   								</c:if>	
		   								<c:if test="${specialityRO.userGrp == 'SAMMD' }">
        									<tr>
											<td style="padding : 5px 0px 0px 10px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div class="input-control checkbox">
												<label> <input type="checkbox" name="grpSammd" id="grpSammd" checked="checked"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input type="checkbox" name="grpCd" id="grpCd"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>											
											</td>
											</tr>
		   								</c:if>
		   								<c:if test="${specialityRO.userGrp == 'CD' }">
        									<tr>
											<td style="padding : 5px 0px 0px 10px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div class="input-control checkbox">
												<label> <input type="checkbox" name="grpSammd" id="grpSammd"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input type="checkbox" name="grpCd" id="grpCd" checked="checked" > <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>											
											</td>
											</tr>
		   								</c:if>			
								    </c:when>
    								<c:otherwise>
	          								<c:if test="${(fn:contains(userRole, 'qadb-superadmin_users')) && (!(fn:contains(userRole, 'null')))}">
 										 	<tr>
											<td style="padding : 5px 0px 0px 10px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div id="area" style="width:200px;">
											<div class="input-control checkbox">
												<label> <input id="grpSammd" type="checkbox" name="grpSammd"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input id="grpCd" type="checkbox" name="grpCd"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>
											</div>											
											</td>
										</tr>
 										</c:if>  
  								</c:otherwise>
								</c:choose>
								
								<c:if test="${(fn:contains(userRole, 'qadb-superadmin_users')) && (!(fn:contains(userRole, 'null')))}">
									<input type="hidden" id="sAdmin" name="sAdmin" value="SADMIN">
								</c:if>
								<c:if test="${(fn:contains(userRole, 'qadb-samd-admin_users')) && (!(fn:contains(userRole, 'null')))}">
 								<input type="hidden" name="grpSammd" value="SAMMD">
								</c:if>
								<c:if test="${(fn:contains(userRole, 'qadb-cd-admin_users')) && (!(fn:contains(userRole, 'null')))}">
									<input type="hidden" name="grpCd" value="CD">
								</c:if>
								
								<!--Get specialty by id  hidden inputs-->
 									<c:if test="${(fn:contains(userRole, 'qadb-superadmin_users')) && (!(fn:contains(userRole, 'null')))}">
 									<input type="hidden" name="userGrp" value="ALL">
									</c:if>
 									<c:if test="${(fn:contains(userRole, 'qadb-samd-admin_users')) && (!(fn:contains(userRole, 'null')))}">
									<input type="hidden" name="userGrp" value="SAMMD">
								</c:if>
								<c:if test="${(fn:contains(userRole, 'qadb-cd-admin_users')) && (!(fn:contains(userRole, 'null')))}">
									<input type="hidden" name="userGrp" value="CD">
								</c:if>
								
					<tr>
						<c:if test="${edit != null}">
						
						<td style="width:125px;padding:5px 0px 0px 10px;"><spring:message code="admin.specialty.enabled" /></td>
						<td><div class="input-control checkbox"> 
							<c:choose>
								<c:when test="${specialityRO.status == 'N'}">
									<label> <input type="checkbox" name="status" value="Y"/> <span class="check"></span>
									 </label>
								</c:when>
								
								<c:otherwise>
									<label> <input type="checkbox" name="status" checked="true" value="Y"/> <span class="check"></span>
									 </label>
								
								</c:otherwise>
							</c:choose>
						
								
							</div></td>
						</c:if>
					</tr>
					
					
				</table>

			</form:form>
			<br>
			<div class="line-separator"></div>
		<c:if test="${edit == null}">  <!-- Hide table for update -->
			<br>
			 <span style="color: #0070c0; font-size: 20px"> <spring:message code="admin.specialty.specialties" />
				</span> <br>
			<br>
			<div class="line-separator"></div>

		
			<table class="table striped hovered dataTable" id="searchDatatable"
				width="700px">
				<thead>
					<tr style="height: 30px">

						<td class="text-left" font-size="25px" style="padding-left: 5px"><spring:message code="admin.specialty.id" /></td>
						<td class="text-left" font-size="25px" style="width: 364px;"><spring:message code="admin.specialty.specialty" /></td>
						<td class="text-left" font-size="25px"><spring:message code="admin.specialty.status" /></td>
						
					</tr>
				</thead>

				<tbody>
						
					<c:forEach items="${specialityRS}" var="rs">
						<tr style="height: 30px">
							<td>${rs.id}</td>
							<td style="padding-left: 7px">${rs.specialty}</td>
							<td>${rs.status}</td> 
						</tr>
					</c:forEach> 
						
				</tbody>


			</table>
		</c:if>
			
			 <script>
				$(function() {
					$('#searchDatatable').dataTable({
					"columns" : [
					{
           				 sClass: "hidden"
      				}, 
					{
					"render": function(data, type, full, meta) {
					return '<a href="getSpecialty?id='+full[0]+'"><u>'+data+'<u></a>';
           					 									}
          			   }, {}]
													});
							});
							
							
							
					$("#specialtyc").keypress(function (e) {
					if ((e.which == 0) || e.which == 8) {}
					else{
						var check = maxLimitForName(e,"errmsgSpecialty","specialty");
						if(check==false){
						return false;
						}
					}
					});
  					$("#specialtyc").keydown(function (e) {
						if(e.ctrlKey && e.keyCode==86){
							if ((e.which == 0) || e.which == 8) {}
							else{
				   				var check = maxLimitForName(e,"errmsgSpecialty","specialty");
				   				if(check==false){
									return false;
								}
							}
						}
					});
					$("#specialtyc").keyup(function (e) {
						if($("#specialty").val().length>49){
						var name = $("#specialty").val().substring(0,50);
						document.getElementById("specialty").value=name;
						$("#errmsgSpecialty").html("Maximum limit of 50 char.").show().fadeOut("slow");
					}
				});
			</script> 

			
		</div>
	</div>

	<jsp:include page="footer.jsp"></jsp:include>

</body>

</html>
