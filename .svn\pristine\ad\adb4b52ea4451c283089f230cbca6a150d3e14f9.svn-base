package com.carefirst.audit.model;

public class TrendsReport {
	
	private String title;
	private String timeframe;
	private String e2e;
	
	private String associateName;
	private String auditType;
	private String subType;
	
	//Associate
	//In-sample
	private String totalClaims;
	private String totalErrClaims;
	private String isTotalClaims;
	private String isTotalErrClaims;
	private String oosTotalClaims;
	private String oosTotalErrClaims;
	
	//IS-rows
	private String isErrorId;
	private String isErrorName;
	private String isErrorMonetary;
	private String isErrorProcedural;
	private String isTotalErrors;
	
	//IS-summary
	private String isTotal;
	private String isTotalMonetary;
	private String isTotalProcedural;
	
	//Out-sample
	//private String oosTotalClaims;
	//private String oosTotalErrClaims;
	private String oosMonetary;
	private String oosProcedural;
	//OOS-rows
	private String oosErrorId;
	private String oosErrorName;
	private String oosErrorMonetary;
	private String oosErrorProcedural;
	private String oosTotalErrors;
	
	//OOS-summary
	private String oosTotal;
	private String oosTotalMonetary;
	private String oosTotalProcedural;
	
	
	//Division
	private String rootCauseId;
	private String rootCauseName;
	private String rootCauseMonetary;
	private String rootCauseProcedural;
	private String errorId;
	private String errorName;
	private String errorMonetary;
	private String errorProcedural;
	private String specialty;
	private String specialtyMonetary;
	private String specialtyProcedural;
	private String totalErrors ;
	private String totalErrorsMonetary ;
	private String totalErrorsProcedural ;
	
	
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getRootCauseId() {
		return rootCauseId;
	}
	public void setRootCauseId(String rootCauseId) {
		this.rootCauseId = rootCauseId;
	}
	public String getRootCauseName() {
		return rootCauseName;
	}
	public void setRootCauseName(String rootCauseName) {
		this.rootCauseName = rootCauseName;
	}
	public String getRootCauseMonetary() {
		return rootCauseMonetary;
	}
	public void setRootCauseMonetary(String rootCauseMonetary) {
		this.rootCauseMonetary = rootCauseMonetary;
	}
	public String getRootCauseProcedural() {
		return rootCauseProcedural;
	}
	public void setRootCauseProcedural(String rootCauseProcedural) {
		this.rootCauseProcedural = rootCauseProcedural;
	}
	public String getErrorId() {
		return errorId;
	}
	public void setErrorId(String errorId) {
		this.errorId = errorId;
	}
	public String getErrorName() {
		return errorName;
	}
	public void setErrorName(String errorName) {
		this.errorName = errorName;
	}
	public String getErrorMonetary() {
		return errorMonetary;
	}
	public void setErrorMonetary(String errorMonetary) {
		this.errorMonetary = errorMonetary;
	}
	public String getErrorProcedural() {
		return errorProcedural;
	}
	public void setErrorProcedural(String errorProcedural) {
		this.errorProcedural = errorProcedural;
	}
	public String getSpecialty() {
		return specialty;
	}
	public void setSpecialty(String specialty) {
		this.specialty = specialty;
	}
	public String getSpecialtyMonetary() {
		return specialtyMonetary;
	}
	public void setSpecialtyMonetary(String specialtyMonetary) {
		this.specialtyMonetary = specialtyMonetary;
	}
	public String getSpecialtyProcedural() {
		return specialtyProcedural;
	}
	public void setSpecialtyProcedural(String specialtyProcedural) {
		this.specialtyProcedural = specialtyProcedural;
	}
	public String getTotalErrors() {
		return totalErrors;
	}
	public void setTotalErrors(String totalErrors) {
		this.totalErrors = totalErrors;
	}
	public String getTotalErrorsMonetary() {
		return totalErrorsMonetary;
	}
	public void setTotalErrorsMonetary(String totalErrorsMonetary) {
		this.totalErrorsMonetary = totalErrorsMonetary;
	}
	public String getTotalErrorsProcedural() {
		return totalErrorsProcedural;
	}
	public void setTotalErrorsProcedural(String totalErrorsProcedural) {
		this.totalErrorsProcedural = totalErrorsProcedural;
	}
	public String getIsTotalErrors() {
		return isTotalErrors;
	}
	public void setIsTotalErrors(String isTotalErrors) {
		this.isTotalErrors = isTotalErrors;
	}
	public String getOosTotalErrors() {
		return oosTotalErrors;
	}
	public void setOosTotalErrors(String oosTotalErrors) {
		this.oosTotalErrors = oosTotalErrors;
	}
	public String getTotalClaims() {
		return totalClaims;
	}
	public void setTotalClaims(String totalClaims) {
		this.totalClaims = totalClaims;
	}
	public String getTotalErrClaims() {
		return totalErrClaims;
	}
	public void setTotalErrClaims(String totalErrClaims) {
		this.totalErrClaims = totalErrClaims;
	}
	public String getTimeframe() {
		return timeframe;
	}
	public void setTimeframe(String timeframe) {
		this.timeframe = timeframe;
	}
	public String getE2e() {
		return e2e;
	}
	public void setE2e(String e2e) {
		this.e2e = e2e;
	}
	public String getAssociateName() {
		return associateName;
	}
	public void setAssociateName(String associateName) {
		this.associateName = associateName;
	}
	public String getAuditType() {
		return auditType;
	}
	public void setAuditType(String auditType) {
		this.auditType = auditType;
	}
	public String getSubType() {
		return subType;
	}
	public void setSubType(String subType) {
		this.subType = subType;
	}
	public String getIsTotalClaims() {
		return isTotalClaims;
	}
	public void setIsTotalClaims(String isTotalClaims) {
		this.isTotalClaims = isTotalClaims;
	}
	public String getIsTotalErrClaims() {
		return isTotalErrClaims;
	}
	public void setIsTotalErrClaims(String isTotalErrClaims) {
		this.isTotalErrClaims = isTotalErrClaims;
	}
	public String getIsErrorId() {
		return isErrorId;
	}
	public void setIsErrorId(String isErrorId) {
		this.isErrorId = isErrorId;
	}
	public String getIsErrorName() {
		return isErrorName;
	}
	public void setIsErrorName(String isErrorName) {
		this.isErrorName = isErrorName;
	}
	public String getIsErrorMonetary() {
		return isErrorMonetary;
	}
	public void setIsErrorMonetary(String isErrorMonetary) {
		this.isErrorMonetary = isErrorMonetary;
	}
	public String getIsErrorProcedural() {
		return isErrorProcedural;
	}
	public void setIsErrorProcedural(String isErrorProcedural) {
		this.isErrorProcedural = isErrorProcedural;
	}
	public String getIsTotal() {
		return isTotal;
	}
	public void setIsTotal(String isTotal) {
		this.isTotal = isTotal;
	}
	public String getIsTotalMonetary() {
		return isTotalMonetary;
	}
	public void setIsTotalMonetary(String isTotalMonetary) {
		this.isTotalMonetary = isTotalMonetary;
	}
	public String getIsTotalProcedural() {
		return isTotalProcedural;
	}
	public void setIsTotalProcedural(String isTotalProcedural) {
		this.isTotalProcedural = isTotalProcedural;
	}
	public String getOosTotalClaims() {
		return oosTotalClaims;
	}
	public void setOosTotalClaims(String oosTotalClaims) {
		this.oosTotalClaims = oosTotalClaims;
	}
	public String getOosTotalErrClaims() {
		return oosTotalErrClaims;
	}
	public void setOosTotalErrClaims(String oosTotalErrClaims) {
		this.oosTotalErrClaims = oosTotalErrClaims;
	}
	public String getOosMonetary() {
		return oosMonetary;
	}
	public void setOosMonetary(String oosMonetary) {
		this.oosMonetary = oosMonetary;
	}
	public String getOosProcedural() {
		return oosProcedural;
	}
	public void setOosProcedural(String oosProcedural) {
		this.oosProcedural = oosProcedural;
	}
	public String getOosErrorId() {
		return oosErrorId;
	}
	public void setOosErrorId(String oosErrorId) {
		this.oosErrorId = oosErrorId;
	}
	public String getOosErrorName() {
		return oosErrorName;
	}
	public void setOosErrorName(String oosErrorName) {
		this.oosErrorName = oosErrorName;
	}
	public String getOosErrorMonetary() {
		return oosErrorMonetary;
	}
	public void setOosErrorMonetary(String oosErrorMonetary) {
		this.oosErrorMonetary = oosErrorMonetary;
	}
	public String getOosErrorProcedural() {
		return oosErrorProcedural;
	}
	public void setOosErrorProcedural(String oosErrorProcedural) {
		this.oosErrorProcedural = oosErrorProcedural;
	}
	public String getOosTotal() {
		return oosTotal;
	}
	public void setOosTotal(String oosTotal) {
		this.oosTotal = oosTotal;
	}
	public String getOosTotalMonetary() {
		return oosTotalMonetary;
	}
	public void setOosTotalMonetary(String oosTotalMonetary) {
		this.oosTotalMonetary = oosTotalMonetary;
	}
	public String getOosTotalProcedural() {
		return oosTotalProcedural;
	}
	public void setOosTotalProcedural(String oosTotalProcedural) {
		this.oosTotalProcedural = oosTotalProcedural;
	}
	
	
	
	
	
	
	
	
	
}
