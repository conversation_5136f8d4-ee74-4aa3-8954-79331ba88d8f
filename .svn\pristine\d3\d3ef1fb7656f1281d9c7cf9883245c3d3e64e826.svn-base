<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>

<head>
<link href="webResources/css/qadb.css" rel="stylesheet">

<script type="text/javascript" src="webResources/js/metro.min.js"></script>
                <!-- Local JavaScript -->
			
	<link href="webResources/css/iconFont.css" rel="stylesheet">
    <link href="webResources/css/docs.css" rel="stylesheet">
    <link href="webResources/js/prettify/prettify.css" rel="stylesheet">
    <link href="webResources/css/jquery-ui.css" rel="stylesheet">

    <!-- Load JavaScript Libraries -->
    <script src="webResources/js/jquery/jquery.min.js"></script>
    <script src="webResources/js/jquery/jquery.widget.min.js"></script>
    <script type="text/javascript" src="webResources/js/jquery-ui.js"></script>
    <script type="text/javascript" src="webResources/js/qadb.js"></script>
<style>
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
	margin-right: auto;
	height: 210%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}

</style>



<title><spring:message code="reports.isOosReport.title"></spring:message></title>
</head>
<body>
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container2">

		<!-- Sidebar Start-->
		<div id="left-sidebar">
			<h2 style="color: white;padding-top: 32px;"><spring:message code="reports.isOosReport.leftNav.heading1"></spring:message></h2>
			<h3 style="color: white;"><spring:message code="reports.isOosReport.leftNav.heading2"></spring:message></h3>
		</div>
		<!-- Sidebar End-->

		<div id="content-container"  style="padding: 10px 0px 0px 40px;">
		<span class="bread1"><spring:message code="reports.bread1"></spring:message></span><span class="bread2"><spring:message code="reports.isOosReport.leftNav.heading1"></spring:message></span>
		
		<form:form id="isOos_report" name="isOosReportForm" method="GET" commandName="isOosReportForm" action="InSample_OutOfSample_Report" style="width:700px">

		<br>
		<span style="color: #0070c0;font-size:16px"><spring:message code="reports.performance.reportParameters"></spring:message></span>
		<div class="line-separator"></div>
		  <div style="padding-top: 10px;padding-left: 7px;background-color: #fafafa;width:750px">
		  <table class="tableForm1" style="width:98%" >
		  <tr>
		  		<td width="30%"><spring:message code="reports.performance.month"></spring:message></td>
		  		<td>
							<div class="input-control select">
								<select id="T3" name="month" style="width:150px">
									<option value="January">January</option>
									<option value="February">February</option>
									<option value="March">March</option>
									<option value="April">April</option>
									<option value="May">May</option>
									<option value="June">June</option>
									<option value="July">July</option>
									<option value="August">August</option>
									<option value="September">September</option>
									<option value="October">October</option>
									<option value="November">November</option>
									<option value="December">December</option>
								</select>
							</div>
						</td>
		  </tr>
		   <tr>
		  		<td width="30%"><spring:message code="reports.performance.year"></spring:message></td>
		  		<td>
							<div class="input-control select">
								<select  id="years" name="year" style="width:150px">
								</select>
							</div>
						</td>
		  </tr>
		 
		 </table>
		  </div>
		  
		  <div style="width:750px">
			    <div style="float:right">
    	<button type="reset" class="button inverse">Reset</button> <button type="submit" style="background-color:#298fd8" class="button default">Generate</button> 
   			</div>
			</div>
		  </form:form>
		 
		
<script type="text/javascript">
$(document).ready(function(){ 

/*year values with current year from 2000  */
	var max = new Date().getFullYear(),
	min = 2000,
    
   	select = document.getElementById('years');

	for (var i = max; i>=min; i--){
   		 var opt = document.createElement('option');
   		 opt.value = i;
    	 opt.innerHTML = i;
    	 select.appendChild(opt);
		}
 
});
</script>  
		</div>
	</div>

	<jsp:include page="footer.jsp"></jsp:include>

</body>

</html>
