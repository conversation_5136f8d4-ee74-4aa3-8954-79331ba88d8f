package com.carefirst.qadb.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import javax.sql.DataSource;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import oracle.jdbc.OracleTypes;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import com.carefirst.audit.model.Associate;
import com.carefirst.audit.model.AuditAssessment;
import com.carefirst.audit.model.AuditDetails;
import com.carefirst.audit.model.AuditSave;
import com.carefirst.audit.model.RecentAudits;
import com.carefirst.qadb.constant.QADBConstants;
import com.carefirst.qadb.converter.CheckConverter;

public class AuditDetailsDAOImpl implements AuditDetailsDAO {

	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);
	public static String type = "" ;

	@Autowired
	DataSource dataSource;
	
	@Override
	public List<AuditDetails> getAuditors(String userGrp) throws SQLException {
		
		String sql = QADBConstants.AUDIT_AUDITORS_LIST1 +userGrp+ QADBConstants.AUDIT_AUDITORS_LIST2;
		
		List<AuditDetails> auditors = new ArrayList<AuditDetails>();

		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
				 	AuditDetails auditor = new AuditDetails();
					auditor.setAuditorName((String) (rs.getString("USER_ID")));
					auditors.add(auditor);
				}
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}

		return auditors;
	}
	
	public List<AuditDetails> getClaimType() throws SQLException {
		
		String sql = QADBConstants.AUDIT_CLAIM_TYPES_LIST;

		List<AuditDetails> audDet = new ArrayList<AuditDetails>();
		
		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
					AuditDetails audDets = new AuditDetails();
					audDets.setClaimTypeId((rs.getString("CLAIM_TYPE_ID")).toString());
					audDets.setClaimType((String) (rs.getString("CLAIM_TYPE_DESC")));
					audDet.add(audDets);
				}
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}
		
		return audDet;
	}

	public List<AuditDetails> getPenaltyInterestType() throws SQLException {

		String sql = QADBConstants.AUDIT_PENALTY_INTEREST_TYPES_LIST;

		List<AuditDetails> audDet = new ArrayList<AuditDetails>();
		
		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
					AuditDetails audDets = new AuditDetails();
					audDets.setPiTypeId((rs.getString("PI_TYPE_ID")).toString());
					audDets.setPenaltyInterestType((String) (rs.getString("PI_TYPE_DESC")));
					audDet.add(audDets);
				}
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}	

		return audDet;
	}

	public List<AuditDetails> getAuditType() throws SQLException {

		String sql = QADBConstants.AUDIT_AUDIT_TYPES_LIST;

		List<AuditDetails> audDet = new ArrayList<AuditDetails>();

		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 ResultSet rs = callableStatment.executeQuery();
			 while (rs.next()) {
					AuditDetails audDets = new AuditDetails();
					audDets.setAudTypeId((rs.getString("AUDIT_TYPE_ID")).toString());
					audDets.setAuditType((String) (rs.getString("AUDIT_TYPE_DESC")));
					audDet.add(audDets);
				}
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}	
		return audDet;
	}

	public List<AuditDetails> getPriPGA(String userGrp) throws SQLException {
		
		String sql = QADBConstants.AUDIT_PRIMARY_PGA_LIST1 +userGrp+ QADBConstants.AUDIT_PRIMARY_PGA_LIST2 ;

		List<AuditDetails> audDet = new ArrayList<AuditDetails>();
		
		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
					AuditDetails audDets = new AuditDetails();
					audDets.setpPgaId((rs.getString("PERF_GROUP_ID")).toString());
					audDets.setPriPGA((String) (rs.getString("PERF_GROUP_NAME")));
					audDet.add(audDets);
				}	
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}
		
		return audDet;
	}

	public List<AuditDetails> getSecPGA(String userGrp,String priPGAId) throws SQLException {

		String sql = QADBConstants.AUDIT_SEC_PGA_LIST1 +priPGAId+QADBConstants.AUDIT_SEC_PGA_LIST2 +userGrp+ QADBConstants.AUDIT_SEC_PGA_LIST3 ;
		List<AuditDetails> audDet = new ArrayList<AuditDetails>();
		
		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
					AuditDetails audDets = new AuditDetails();
					audDets.setsPgaId((rs.getString("PERF_GROUP_ID")).toString());
					audDets.setSecPGA((String) (rs.getString("PERF_GROUP_NAME")));
					audDet.add(audDets);
				}
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}		
		return audDet;
	}
	
	@Override
	public List<AuditDetails> getSecPGAList(String userGrp) throws SQLException {
		
		String sql = QADBConstants.REPORTS_SEC_PGA_LIST1 +userGrp+ QADBConstants.REPORTS_SEC_PGA_LIST2 ;
		List<AuditDetails> audDet = new ArrayList<AuditDetails>();

		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
					AuditDetails audDets = new AuditDetails();
					audDets.setsPgaId((rs.getString("PERF_GROUP_ID")).toString());
					audDets.setSecPGA((String) (rs.getString("PERF_GROUP_NAME")));
					audDet.add(audDets);
				}	
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}		
		return audDet;
		
	}

	public List<AuditDetails> getProcessType() throws SQLException {

		String sql = QADBConstants.AUDIT_PROCESS_TYPE_LIST;

		List<AuditDetails> audDet = new ArrayList<AuditDetails>();
		
		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			 conn = dataSource.getConnection();
			 callableStatment = conn.prepareCall(sql);
			 
			 ResultSet rs = callableStatment.executeQuery();
			 
			 while (rs.next()) {
				 	AuditDetails audDets = new AuditDetails();
					audDets.setProcessTypId((rs.getString("PROCESS_TYPE_ID")).toString());
					audDets.setProcesstype((String) (rs.getString("PROCESS_TYPE_DESC")));
					audDet.add(audDets);
				}	
			 rs.close();
		} catch (SQLException e) {
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}
		
		return audDet;
	}
	
	/*public List<Statistics> getStatisticsBK(String dcn) {

		
		String sql	= "SELECT TBL_USER_DETAILS.USER_FIRST_NAME,TBL_AUDIT_STATISTICS.USER_INSRT_TMSTP FROM TBL_AUDIT_STATISTICS INNER JOIN TBL_USER_DETAILS ON TBL_USER_DETAILS.USER_ID = TBL_AUDIT_STATISTICS.USER_ID WHERE DCN_NBR = "+ "'"+dcn+"'"
				+ " ORDER BY TBL_AUDIT_STATISTICS.USER_INSRT_TMSTP Desc";
		
		List<Statistics> audDet = new ArrayList<Statistics>();

		JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);

		List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);
		for (
		Map row : rows) {
			Statistics stat = new Statistics();
			stat.setSavedBy((row.get("USER_FIRST_NAME")).toString());
			stat.setSavedDate(new SimpleDateFormat("dd MMMMM yyyy").format((row.get("USER_INSRT_TMSTP"))));
			stat.setSavedTime(new SimpleDateFormat("HH:mm:ss").format((row.get("USER_INSRT_TMSTP"))));
			audDet.add(stat);
		}
		
		return audDet;
	}*/

//	Save Update Audit
	/*
	 * (non-Javadoc)
	 * @see com.carefirst.qadb.dao.AuditDetailsDAO#saveAudit(com.carefirst.audit.model.AuditSave)
	 */
	/**
	 * @throws NoSuchMethodException 
	 * @throws SecurityException 
	 * 
	 */
	
	
	/* (non-Javadoc)
	 * @see com.carefirst.qadb.dao.AuditDetailsDAO#saveAudit(com.carefirst.audit.model.AuditSave)
	 */
	public AuditSave saveAudit(AuditSave aud) throws SecurityException, NoSuchMethodException {

		logger.debug("Save DCN" + aud.getDcn());
		logger.debug("***** Save/Update Audit Start ****");

		String saveAuditSp = QADBConstants.AUDIT_SAVE_SP;

		Connection conn =  null ;
		CallableStatement callableStatment = null;
		
		AuditSave auds = new AuditSave();
		
		try {
			
			SimpleDateFormat sdf1 = new SimpleDateFormat("MM-dd-yy");

			SimpleDateFormat sdf2 = new SimpleDateFormat("MM/dd/yy");
			conn=dataSource.getConnection();
            
			callableStatment = conn.prepareCall(saveAuditSp);
				
			   callableStatment.setString(1, aud.getUserid());//userid==aac2838
			   callableStatment.setString(2, aud.getUserActyp());
				
				logger.debug("audIdSave"+aud.getAuditId());
				
				if(null!=aud.getAuditId()){
					callableStatment.setInt(3,Integer.parseInt(aud.getAuditId()));
				}else{
					callableStatment.setNull(3, java.sql.Types.INTEGER);
				}				
				logger.debug("Associate Id Audit SP "+ aud.getAssociID()+"Dcn--> "+ aud.getDcn()+ "LOB--> "+ aud.getLob()+"Member Id---> "+aud.getMemberId()+"Process Date--> "+aud.getProcessDate());	
				callableStatment.setString(4, aud.getAssociID());// asso id
				callableStatment.setString(5, aud.getDcn());
				callableStatment.setInt(6, Integer.parseInt(aud.getClaimType()));// claim type
				callableStatment.setString(7, aud.getLob());
				callableStatment.setString(8, aud.getMemberId());
				callableStatment.setString(9, aud.getProcessDate());
				if((aud.getPaidDate().trim()).equals("")){
					callableStatment.setNull(10,java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(10,aud.getPaidDate());
				}
				//callableStatment.setString(10,aud.getPaidDate());
				callableStatment.setDouble(11,Double.parseDouble(aud.getTotalCharge()));
				callableStatment.setDouble(12,Double.parseDouble(aud.getPaid()));
				
				callableStatment.setString(13, aud.getJurisdiction());
				callableStatment.setString(14, aud.getMonetaryError());
				if((aud.getMonetaryError()).equalsIgnoreCase("N")){
					callableStatment.setDouble(15, 0);
					callableStatment.setDouble(16, 0);
				}
				else{
					callableStatment.setDouble(15, Double.parseDouble(aud.getAmountp()));
					callableStatment.setDouble(16, Double.parseDouble(aud.getTheoreticalPaid()));
				}
				
				callableStatment.setString(17,new CheckConverter().convert((aud.getPiChk())));
				logger.debug("Pichk "+new CheckConverter().convert(aud.getPiChk()));
				if(new CheckConverter().convert(aud.getPiChk()).equals("N")){
					callableStatment.setNull(18, java.sql.Types.INTEGER);
					callableStatment.setNull(19, java.sql.Types.INTEGER);
				}else{
					callableStatment.setInt(18, Integer.parseInt(aud.getPenaltyInterestType()));
					if(null != aud.getPi()){
				    callableStatment.setDouble(19, Double.parseDouble(aud.getPi()));
					}
					else{
					callableStatment.setDouble(19, 0); //default value 0 for PI rate
					}
				}
				
			    callableStatment.setInt(20,Integer.parseInt(aud.getAuditType()));
				logger.debug("pg1 save "+aud.getPriPGA());
			    if((aud.getPriPGA()).equals("")){
			    	callableStatment.setNull(21, java.sql.Types.INTEGER);
			    	
			    }else{
			    	callableStatment.setInt(21,Integer.parseInt(aud.getPriPGA()));
			    }
			    logger.debug("pg2 save "+aud.getSecPGA());
			    if((null == aud.getSecPGA() )||(aud.getSecPGA()).equals("")){
			    	callableStatment.setNull(22, java.sql.Types.INTEGER);
			    	
			    }else{
			    	callableStatment.setInt(22,Integer.parseInt(aud.getSecPGA()));
			    }
				callableStatment.setString(23, aud.getMock());
				callableStatment.setString(24, aud.getOos());
				callableStatment.setString(25, aud.getE2e());
				callableStatment.setString(26, aud.getRiskAccount());
				callableStatment.setInt(27,Integer.parseInt(aud.getProcesstype()));
				callableStatment.setString(28, aud.getPlatform());
				if(null!=aud.getFyiChk()){
					if((aud.getFyiChk()).equalsIgnoreCase("N")){
						callableStatment.setNull(29,java.sql.Types.VARCHAR);
					}
					else{
					callableStatment.setString(29, aud.getFyi());
					}
				}else{
					callableStatment.setNull(29,java.sql.Types.VARCHAR);
				}
				
				logger.debug("before error "+aud.getErrorCodesId());
				
				// error
				if(null!=aud.getErrorCodesId()){
					
					if((aud.getErrorCodesId()).isEmpty()){
						logger.debug("error No "+java.sql.Types.ARRAY);
						callableStatment.setString(30,"N");
						callableStatment.setNull(31, java.sql.Types.VARCHAR);
						callableStatment.setNull(32, java.sql.Types.VARCHAR);
						callableStatment.setNull(33, java.sql.Types.VARCHAR);
						callableStatment.setNull(34, java.sql.Types.VARCHAR);
						callableStatment.setNull(35, java.sql.Types.VARCHAR);
						callableStatment.setNull(36, java.sql.Types.VARCHAR);
						
						callableStatment.setNull(37, java.sql.Types.VARCHAR);
						callableStatment.setNull(38, java.sql.Types.VARCHAR);
						callableStatment.setNull(39, java.sql.Types.VARCHAR);
						callableStatment.setNull(40, java.sql.Types.VARCHAR);
						callableStatment.setString(41, "N");
						callableStatment.setString(42, "N");
						callableStatment.setString(43, "N"); 
						callableStatment.setNull(44, java.sql.Types.DATE);
						
					}else{
						logger.debug("error Yes ");
						callableStatment.setString(30,"Y");
						String ecArrString = (aud.getErrorCodesId()).toString().replaceAll("[\\s\\[\\]]", "");
						ecArrString=ecArrString.replace(",", "||");
						logger.debug("--->"+ecArrString);
						callableStatment.setString(31,ecArrString);
						String monArrString = (aud.getMonetarys()).toString().replaceAll("[\\s\\[\\]]", "");
						monArrString=monArrString.replace(",", "||");
						logger.debug("Monetary --->"+monArrString);
						callableStatment.setString(32,monArrString);
						String proArrString = (aud.getProcedurals()).toString().replaceAll("[\\s\\[\\]]", "");
						proArrString=proArrString.replace(",", "||");
						logger.debug("Procedural --->"+proArrString);
						callableStatment.setString(33,proArrString);
						String speArrString = (aud.getSpecialitysId()).toString().replaceAll("[\\s\\[\\]]", "");
						speArrString=speArrString.replace(",", "||");
						callableStatment.setString(34,speArrString);
						String rcArrString = (aud.getRootCausesId()).toString().replaceAll("[\\s\\[\\]]", "");
						rcArrString=rcArrString.replace(",", "||");
						callableStatment.setString(35,rcArrString);
						
						if(null != aud.getEditCodesId()){
							logger.debug("--->"+aud.getEditCodesId());
						  if(!(aud.getEditCodesId()).isEmpty()){
							if(org.springframework.util.StringUtils.hasText(aud.getEditCodesId().get(0))){
								
							}else{
								aud.getEditCodesId().add(0, "");
							}
						  }
							logger.debug("--->"+aud.getEditCodesId());
							//logger.debug("--->"+aud.getEditCodesId().get(0));
							String editArrString = (aud.getEditCodesId()).toString().replaceAll("[\\[\\]]", "");
							editArrString=editArrString.replace(",", "||");
							callableStatment.setString(36,editArrString);
						}
						else{
							logger.debug("--->"+aud.getEditCodesId());
							callableStatment.setNull(36, java.sql.Types.VARCHAR);
						}
						callableStatment.setString(37, aud.getErrorType());
						
						logger.debug("sop "+aud.getSop());
						if(null != (aud.getSop())){
							callableStatment.setString(38,aud.getSop());
						}
						else{
							callableStatment.setNull(38, java.sql.Types.VARCHAR);
						}
						callableStatment.setString(39, aud.getReason());
						logger.debug("verbiage issue get reason for error===debug on 08/08/201"+aud.getReason());
						callableStatment.setString(40, aud.getComments());
						callableStatment.setString(41, new CheckConverter().convert(aud.getRequired()));
						callableStatment.setString(42, new CheckConverter().convert(aud.getCompleted()));
						callableStatment.setString(43, new CheckConverter().convert(aud.getAppeal()));
						
						if(null!=aud.getDateAdj()){
							if((aud.getDateAdj()).equalsIgnoreCase("")){
								callableStatment.setNull(44, java.sql.Types.DATE);
							}else{
								callableStatment.setString(44,aud.getDateAdj());
							}
						}else{
							callableStatment.setNull(44, java.sql.Types.DATE);
						}
					}
					
				}else{
					logger.debug("error N");
					callableStatment.setString(30,"N");
					callableStatment.setNull(31, java.sql.Types.VARCHAR);
					callableStatment.setNull(32, java.sql.Types.VARCHAR);
					callableStatment.setNull(33, java.sql.Types.VARCHAR);
					callableStatment.setNull(34, java.sql.Types.VARCHAR);
					callableStatment.setNull(35, java.sql.Types.VARCHAR);
					callableStatment.setNull(36, java.sql.Types.VARCHAR);
					
					callableStatment.setNull(37, java.sql.Types.VARCHAR);
					callableStatment.setNull(38, java.sql.Types.VARCHAR);
					callableStatment.setNull(39, java.sql.Types.VARCHAR);
					callableStatment.setNull(40, java.sql.Types.VARCHAR);
					callableStatment.setString(41, "N");
					callableStatment.setString(42, "N");
					callableStatment.setString(43, "N"); 
					callableStatment.setNull(44, java.sql.Types.DATE);
				}
			logger.debug("Additional claim details "+aud.getSubscriberCK()+ "AccountID--> "+aud.getAccountID()+ "Account Name---> "+aud.getAccountName()+"Group-->"+aud.getGroup()+"Claim Current Status---> "+aud.getClaimCurrentStatus()+"Product ID---> "+aud.getProductID()+" Adjustment Reasoncode"+aud.getAdjustmentReasoncode() );
				
				//Additional Claim details
				if(null!=aud.getAuditId()){
					callableStatment.setInt(45,0);
					callableStatment.setString(46, " ");
					callableStatment.setString(47, " ");
					callableStatment.setString(48, " ");
					callableStatment.setString(49, " ");
					callableStatment.setString(50, " ");
					callableStatment.setString(51, " ");
					callableStatment.setString(52, " ");
					callableStatment.setString(53, " ");
					callableStatment.setString(54, " ");
				}else{
					callableStatment.setInt(45,Integer.parseInt(aud.getSubscriberCK()));
					callableStatment.setString(46, aud.getAccountID());
					callableStatment.setString(47, aud.getAccountName());
					callableStatment.setString(48, aud.getGroup());
					callableStatment.setString(49, aud.getClaimCurrentStatus());
					callableStatment.setString(50, aud.getProductID());
					
					logger.debug("bsbc bfr "+aud.getBSBSCode());
					logger.debug("bsbc bfr "+(aud.getBSBSCode()).toString());
					String bsbcString = (aud.getBSBSCode()).toString().replaceAll("[\\s\\[\\]]", "");
					bsbcString=bsbcString.replace(",", "||");
					callableStatment.setString(51,bsbcString);
					logger.debug("bsbc aftr "+bsbcString);
					String grpIdString = (aud.getGroupID()).toString().replaceAll("[\\s\\[\\]]", "");
					grpIdString=grpIdString.replace(",", "||");
					callableStatment.setString(52,grpIdString);

					String proLineString = (aud.getProductLine()).toString().replaceAll("[\\s\\[\\]]", "");
					proLineString=proLineString.replace(",", "||");
					callableStatment.setString(53,proLineString);

					String prodDesString = (aud.getProductDescription()).toString().replaceAll("[\\[\\]]", "");
					prodDesString=prodDesString.replace(",", "||");
					callableStatment.setString(54,prodDesString);
				}
				
				logger.debug("Errors array "+aud.getErrorCodesId());
				
				//logger.debug("Update Error inputs "+aud.getErrorType()+" "+aud.getSop()+ " "+aud.getReason()+" "+aud.getComments()+ " "+aud.getRequired()+" "+aud.getCompleted()+ " "+aud.getDateAdj()+" "+aud.getAppeal() );
				
				
				// High Dollar Sheet
			logger.debug("aud.getClaimAdjFlag()-->"+aud.getClaimAdjFlag());
				logger.debug("HD inputs");
				callableStatment.setString(55, aud.getClaimAdjFlag());
				callableStatment.setString(56, aud.getServiceAdjFlag());
				callableStatment.setString(57, aud.getExaminerName());
				callableStatment.setString(58, aud.getQAName());
				callableStatment.setString(59, aud.getPatientName());
				callableStatment.setString(60, aud.getServiceDates());
				callableStatment.setString(61, aud.getTypeOfService());
				callableStatment.setString(62, aud.getDiagnosis());
				 if((null == aud.getSurgeryDOS() )||(aud.getSurgeryDOS().trim()).equals("")){
				    	callableStatment.setNull(63, java.sql.Types.DATE);
				    }else{
				    	callableStatment.setString(63,aud.getSurgeryDOS());
				    }
				 if((null == aud.getSurgery() )||(aud.getSurgery().trim()).equals("")){
				    	callableStatment.setNull(64, java.sql.Types.VARCHAR);
				    }else{
				    	callableStatment.setString(64,aud.getSurgery());
				    }
				 if((null == aud.getFileReferenced() )||(aud.getFileReferenced().trim()).equals("")){
				    	callableStatment.setNull(65, java.sql.Types.VARCHAR);
				    }else{
				    	callableStatment.setString(65,aud.getFileReferenced());
				    }
				//callableStatment.setString(63, aud.getSurgeryDOS());
				//callableStatment.setString(64, aud.getSurgery());
				//callableStatment.setString(65, aud.getFileReferenced());
				
				callableStatment.setString(66, aud.getProviderName());
				callableStatment.setString(67, aud.getProviderNumber());
				callableStatment.setString(68, aud.getPayee());
				 if((null == aud.getNotes() )||(aud.getNotes().trim()).equals("")){
				    	callableStatment.setNull(69, java.sql.Types.VARCHAR);
				    }else{
				    	callableStatment.setString(69,aud.getNotes());
				    }
				//callableStatment.setString(69, aud.getNotes());
				callableStatment.setString(70, aud.getAudSignDate());
				if((null == aud.getVpSignDate())||(aud.getVpSignDate().trim()).equalsIgnoreCase("")||(aud.getVpSignDate()).isEmpty()){
			    	callableStatment.setNull(71, java.sql.Types.DATE);
			    }else{
			    	callableStatment.setString(71,aud.getVpSignDate());
			    }
				//callableStatment.setString(71, aud.getVpSignDate());
				callableStatment.setString(72, aud.getForwrdToDate());
				if((null == aud.getRcvedFrmDate() )||(aud.getRcvedFrmDate().trim()).equals("")){
			    	callableStatment.setNull(73, java.sql.Types.DATE);
			    }else{
			    	callableStatment.setString(73,aud.getRcvedFrmDate());
			    }
				if((null == aud.getReleasedByDate() )||(aud.getReleasedByDate().trim()).equals("")){
			    	callableStatment.setNull(74, java.sql.Types.DATE);
			    }else{
			    	callableStatment.setString(74,aud.getReleasedByDate());
			    }
				//callableStatment.setString(73, aud.getRcvedFrmDate());
				//callableStatment.setString(74, aud.getReleasedByDate());
				
				callableStatment.setString(75, aud.getAdjustmentReasoncode());
				logger.debug("interPaid "+ aud.getInterestPaid());
				callableStatment.setString(76, aud.getInterestPaid());
				
				callableStatment.registerOutParameter(77, OracleTypes.VARCHAR);
				callableStatment.registerOutParameter(78, OracleTypes.VARCHAR);
				//logger.debug(new java.sql.Date(today.getTime()));
				logger.debug("SQlDate input procs date "+ new java.sql.Date((sdf1.parse(aud.getProcessDate())).getTime()));
				//logger.debug("SQlDate input paid date "+ new java.sql.Date((sdf1.parse(aud.getPaidDate())).getTime()));
				//logger.debug("SQlDate input err "+ new java.sql.Date((sdf2.parse(aud.getDateAdj())).getTime()));

				callableStatment.execute();
				logger.debug("EXEC complete");
				logger.debug("Success Code "+callableStatment.getString(77));
				logger.debug("Success Message "+callableStatment.getString(78));
				 
				auds.setSucessCode(callableStatment.getString(77));
				auds.setSuccessMsg(callableStatment.getString(78));
				 
				callableStatment.close();
				conn.close();
			
			 
		} catch (SQLException e) {
			e.printStackTrace();
			logger.debug("SQL excp "+(e.getStackTrace()));
		} catch (ParseException e) {
			e.printStackTrace();
			logger.debug("Parse excp "+(e.getStackTrace()));
		}
		logger.debug("***** Save/Update Audit end ****");
		return auds;
	}

	
	@Override
	public AuditSave deleteAudit(AuditSave auditIdDetails) {
		
		logger.debug("del Impl id "+auditIdDetails.getAuditId());
		String sql = QADBConstants.AUD_DEL_QUERY
				+ " SET  AUDIT_DELETE_FLG  = 'Y' "
				+ " WHERE AUDIT_ID          = "
				+auditIdDetails.getAuditId()+ " "    ;
		
		AuditSave auditDetailsRO = new AuditSave();
		
		JdbcTemplate jdbcTemp = new JdbcTemplate(dataSource);
		
		jdbcTemp.execute(sql);
		
		logger.debug("Delete Exe complte");
		
		auditDetailsRO.setSuccessMsg("SUCCESS");
		
		return auditDetailsRO;
		
	}
	
	@Override
	public Associate getAssociateDetails(Associate assoDetails) throws SQLException{

		String getAssociateDetails = QADBConstants.AUDIT_ASSOCIATE_DETAILS_SP;
		String userGrp = assoDetails.getUserGroup();
		
		Associate associateEO = new Associate();
		Connection conn =  null ;
		CallableStatement callableStatment = null;
		try {
			
			conn = dataSource.getConnection();
			
			callableStatment = conn.prepareCall(getAssociateDetails);
			logger.debug("dcn " +assoDetails.getDcnCheck());
			logger.debug("FacetsId " +assoDetails.getFacetsId());
			
			if((null != userGrp)&&(userGrp.equalsIgnoreCase("('SAMMD','CD','ALL')"))){
				userGrp = "ALL";
			}
			else if((null != userGrp)&&(userGrp.equalsIgnoreCase("('SAMMD','ALL')"))){
				userGrp = "SAMMD";
			}
			else if((null != userGrp)&&(userGrp.equalsIgnoreCase("('CD','ALL')"))){
				userGrp = "CD";
			}
			
			if(null != (assoDetails.getDcnCheck())){
				callableStatment.setString(1, assoDetails.getDcnCheck());//dcn for dup check
				callableStatment.setNull(2, java.sql.Types.VARCHAR);
				callableStatment.setString(3, userGrp);
			}
			else{
				callableStatment.setNull(1, java.sql.Types.VARCHAR);
				callableStatment.setString(2, assoDetails.getFacetsId());
				callableStatment.setNull(3, java.sql.Types.VARCHAR);
			}
			//callableStatment.setNull(1, java.sql.Types.VARCHAR);
			//callableStatment.setString(2, assoId);
			//callableStatment.setInt(1, 2);
			
			callableStatment.registerOutParameter(4, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(5, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(6, OracleTypes.VARCHAR);
			logger.debug(" Exe "+callableStatment.execute());
			
			logger.debug("Success Code "+callableStatment.getString(5));
			
			callableStatment.executeUpdate();
			
			if(null != (assoDetails.getFacetsId())){
				
				ResultSet rs =   (ResultSet) callableStatment.getObject(4);			
				
				while (rs.next()) {
				
					logger.debug("AssoName "+rs.getString("DIRECTOR_NAME")+ " super "+rs.getString("MANAGER_NAME")+ "man " +rs.getString("SUPERVISOR_NAME"));
					
					associateEO.setAssociateDBId((rs.getString("ASSOCIATE_ID")).toString());
					associateEO.setAssociateName((rs.getString("ASSOCIATE_NAME")).toString());
					associateEO.setDirector((rs.getString("DIRECTOR_NAME")).toString());
					associateEO.setSuperVisor((rs.getString("SUPERVISOR_NAME")).toString());
					associateEO.setManager((rs.getString("MANAGER_NAME")).toString());
				}	
				rs.close();
			}
			
			associateEO.setSucessCode(callableStatment.getString(5));
			associateEO.setSuccessMsg(callableStatment.getString(6));
			
		} catch (Exception e) {
			e.printStackTrace();
			logger.debug("Exception generated "+e.getMessage());
		} finally{
			callableStatment.close();
			conn.close();
		}
		
		return associateEO;
	}

	@Override
	public List<RecentAudits> getRecentAudits(RecentAudits rcTO) throws SQLException {
      
		logger.debug("*** Entry getRecentAudits method ***");
		String getRecentAudits = QADBConstants.AUDIT_RECENT_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getRecentAudits);
		List<RecentAudits> rcEOs = new ArrayList<RecentAudits>();
		try {
			
            callableStatment.setInt(1, Integer.parseInt(rcTO.getRecords())); // two records
            callableStatment.setString(2, rcTO.getOrder());
            callableStatment.registerOutParameter(3, OracleTypes.CURSOR);
            callableStatment.registerOutParameter(4, OracleTypes.VARCHAR);
            callableStatment.registerOutParameter(5, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(5));
			callableStatment.executeUpdate();
			
			ResultSet rs =   (ResultSet) callableStatment.getObject(3);			
			while (rs.next()) {
				RecentAudits rcEO = new RecentAudits();
				
				rcEO.setOrder((rs.getString("AUDIT_ID")).toString());
				rcEO.setDcnNo((rs.getString("DCN_NBR")).toString());
				rcEO.setTime((rs.getString("TIME")).toString());
				rcEO.setDate((rs.getString("DATE")).toString());
				
				logger.debug("Recent audit Records "+(rs.getString("AUDIT_ID")).toString());
				rcEOs.add(rcEO);
			}	
			rs.close();
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}finally{
			callableStatment.close();
			conn.close();
		}
		logger.debug("*** Exit getRecentAudits method ***");
		return rcEOs;
		
	}

	@Override
	public JRDataSource getAssesmentReport(AuditAssessment auditAssessmentReport) throws SQLException {
		
		logger.debug("*** start getAssesmentReport *** ");
		logger.debug("stutsTo " + auditAssessmentReport.getProcessDateIn());
		String getAssesmentReport = QADBConstants.AUDIT_ASSESMENT_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getAssesmentReport);
		List<AuditAssessment> auditAssessmentList = new ArrayList<AuditAssessment>();
		
		try {
			
			callableStatment.setString(1,auditAssessmentReport.getAssociateId());
			callableStatment.setString(2,auditAssessmentReport.getProcessDateIn());
			callableStatment.setNull(3,java.sql.Types.VARCHAR);
			callableStatment.setNull(4,java.sql.Types.VARCHAR); 
			
			/*set out parameters from 5 to 20*/
			 for(int i=5 ; i < 21 ; i++)
			    {
			            callableStatment.registerOutParameter(i, OracleTypes.VARCHAR);
			    }
			
			callableStatment.registerOutParameter(21, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(22, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(23, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(24, OracleTypes.VARCHAR);
			
			callableStatment.executeUpdate();
			logger.debug(" type "+auditAssessmentReport.getType());
			
			if(null!= (auditAssessmentReport.getType()) && (auditAssessmentReport.getType()).equalsIgnoreCase("in")){
					
					/*IN-Sample RS*/
					
					ResultSet  rs2 =   (ResultSet) callableStatment.getObject(22);			
					logger.debug("Row count IS " +rs2.getRow());
					
					while (rs2.next()) {
						
						AuditAssessment auditAssessmentIs = new AuditAssessment();
						
						logger.debug("Assesment RS "+rs2.getString("DCN_NBR")+ " memb "+rs2.getString("SNO")+ "total " +rs2.getString("TOTAL_PAID"));
						
						auditAssessmentIs.setMonth(callableStatment.getString(5));
						auditAssessmentIs.setAssociateName(callableStatment.getString(6));
						auditAssessmentIs.setEmpNO(callableStatment.getString(7));
						auditAssessmentIs.setSupervisor(callableStatment.getString(8));
						
						//IS counts
						auditAssessmentIs.setIsTotalClaims(callableStatment.getString(15));
						auditAssessmentIs.setIsProcClaims(callableStatment.getString(16));
						auditAssessmentIs.setIsMonClaims(callableStatment.getString(17));
						auditAssessmentIs.setIsTotalPaid(callableStatment.getString(18));
						auditAssessmentIs.setIsTotalOvPaid(callableStatment.getString(19));
						auditAssessmentIs.setIsTotalUnPaid(callableStatment.getString(20));
						
						auditAssessmentIs.setIsSno((rs2.getString("SNO")).toString());
						auditAssessmentIs.setIsDcn((rs2.getString("DCN_NBR")).toString());
						auditAssessmentIs.setIsMemId((rs2.getString("MEMB_ID")).toString());
						auditAssessmentIs.setIsPaid((rs2.getString("TOTAL_PAID")).toString());
						auditAssessmentIs.setIsOverPaid((rs2.getString("OVER_PAID")).toString());
						auditAssessmentIs.setIsUnderPaid((rs2.getString("UNDER_PAID")).toString());
						auditAssessmentIs.setIsHighDoller((rs2.getString("HIGH_DOLLAR_FLG")).toString());
						auditAssessmentIs.setIsAuditType((rs2.getString("AUDIT_TYPE")).toString());
						auditAssessmentIs.setIsMockAudit((rs2.getString("MOCK_FLG")).toString());
						//auditAssessmentIs.setReportType((rs2.getString("OOS_FLG")).toString());
						type= "is";
						
						if(null != (rs2.getString("REQUIRED_FLG"))){
							auditAssessmentIs.setIsAdjRequired((rs2.getString("REQUIRED_FLG")).toString());
						}
						else{
							auditAssessmentIs.setIsAdjRequired("");
						}
						auditAssessmentIs.setIsProcessOn((rs2.getString("PROCESS_DT")).toString());
						
						if(null != (rs2.getString("ERROR_ID"))){
							auditAssessmentIs.setIsErrorCode((rs2.getString("ERROR_ID")).toString());
						}
						else{
							auditAssessmentIs.setIsErrorCode("");
						}
						if(null != (rs2.getString("MONETARY_FLG"))){
							auditAssessmentIs.setIsMonetary((rs2.getString("MONETARY_FLG")).toString());
						}
						else{
							auditAssessmentIs.setIsMonetary("");
						}
						if(null != (rs2.getString("PROCEDURAL_FLG"))){
							auditAssessmentIs.setIsProcedural((rs2.getString("PROCEDURAL_FLG")).toString());
						}
						else{
							auditAssessmentIs.setIsProcedural("");
						}
						if(null != (rs2.getString("ERROR_TYPE"))){
							auditAssessmentIs.setIsErrorType((rs2.getString("ERROR_TYPE")).toString());
						}
						else{
							auditAssessmentIs.setIsErrorType("");
						}
						if(null != (rs2.getString("ERROR_REASON_DESC"))){
							auditAssessmentIs.setIsExplanation((rs2.getString("ERROR_REASON_DESC")).toString());
						}
						else{
							auditAssessmentIs.setIsExplanation("");
						}
						if(null != (rs2.getString("FYI"))){
							auditAssessmentIs.setIsFyi("FYI - "+(rs2.getString("FYI")).toString());
						}
						else{
							auditAssessmentIs.setIsFyi("");
						}
						if(null != (rs2.getString("SOP_NEWS_FLASH_REFNC"))){
							auditAssessmentIs.setIsSop((rs2.getString("SOP_NEWS_FLASH_REFNC")).toString());
						}
						else{
							auditAssessmentIs.setIsSop("");
						}
						
						if(null != (rs2.getString("PRIMARY_PERF_GROUP_ID"))){
							auditAssessmentIs.setIsPriPerfGroup("P-"+((rs2.getString("PRIMARY_PERF_GROUP_ID")).toString()));
						}
						else{
							auditAssessmentIs.setIsPriPerfGroup("");
						}
						if(null != (rs2.getString("SECONDARY_PERF_GROUP_ID"))){
							auditAssessmentIs.setIsSecPerfGroup("S-"+(rs2.getString("SECONDARY_PERF_GROUP_ID")).toString());
						}
						else{
							auditAssessmentIs.setIsSecPerfGroup("");
						}
						auditAssessmentIs.setIsAuditDate((rs2.getString("AUDIT_DT")).toString());
						auditAssessmentIs.setIsAuditor((rs2.getString("USER_ID")).toString());
						
						auditAssessmentList.add(auditAssessmentIs);
					}	
					
					rs2.close();
					
					
				}else{
			
			/*Out-of-Sample RS*/
			ResultSet  rs =   (ResultSet) callableStatment.getObject(21);			
			logger.debug("Row count OOS" +rs.getRow());
			
			while (rs.next()) {
				
				AuditAssessment auditAssessmentOos = new AuditAssessment();
				
				logger.debug("Assesment RS "+rs.getString("DCN_NBR")+ " memb "+rs.getString("SNO")+ "total " +rs.getString("TOTAL_PAID"));
				
				auditAssessmentOos.setMonth(callableStatment.getString(5));
				auditAssessmentOos.setAssociateName(callableStatment.getString(6));
				auditAssessmentOos.setEmpNO(callableStatment.getString(7));
				auditAssessmentOos.setSupervisor(callableStatment.getString(8));
				
				//OOS
				auditAssessmentOos.setOosTotalClaims(callableStatment.getString(9));
				logger.debug("OOS_total_claims "+callableStatment.getString(9));
				auditAssessmentOos.setOosProcClaims(callableStatment.getString(10));
				auditAssessmentOos.setOosMonClaims(callableStatment.getString(11));
				auditAssessmentOos.setOosTotalPaid(callableStatment.getString(12));
				auditAssessmentOos.setOosTotalOvPaid(callableStatment.getString(13));
				auditAssessmentOos.setOosTotalUnPaid(callableStatment.getString(14));
				
				auditAssessmentOos.setOosSno((rs.getString("SNO")).toString());
				auditAssessmentOos.setOosDcn((rs.getString("DCN_NBR")).toString());
				auditAssessmentOos.setOosMemId((rs.getString("MEMB_ID")).toString());
				auditAssessmentOos.setOosPaid((rs.getString("TOTAL_PAID")).toString());
				auditAssessmentOos.setOosOverPaid((rs.getString("OVER_PAID")).toString());
				auditAssessmentOos.setOosUnderPaid((rs.getString("UNDER_PAID")).toString());
				auditAssessmentOos.setOosHighDoller((rs.getString("HIGH_DOLLAR_FLG")).toString());
				auditAssessmentOos.setOosAuditType((rs.getString("AUDIT_TYPE")).toString());
				auditAssessmentOos.setOosMockAudit((rs.getString("MOCK_FLG")).toString());
				auditAssessmentOos.setReportType((rs.getString("OOS_FLG")).toString());
				type= "oos";
				if(null != (rs.getString("REQUIRED_FLG"))){
					auditAssessmentOos.setOosAdjRequired((rs.getString("REQUIRED_FLG")).toString());
				}
				else{
					auditAssessmentOos.setOosAdjRequired("");
				}
				auditAssessmentOos.setOosProcessOn((rs.getString("PROCESS_DT")).toString());
				
				if(null != (rs.getString("ERROR_ID"))){
					auditAssessmentOos.setOosErrorCode((rs.getString("ERROR_ID")).toString());
				}
				else{
					auditAssessmentOos.setOosErrorCode("");
				}
				if(null != (rs.getString("MONETARY_FLG"))){
					auditAssessmentOos.setOosMonetary((rs.getString("MONETARY_FLG")).toString());
				}
				else{
					auditAssessmentOos.setOosMonetary("");
				}
				if(null != (rs.getString("PROCEDURAL_FLG"))){
					auditAssessmentOos.setOosProcedural((rs.getString("PROCEDURAL_FLG")).toString());
				}
				else{
					auditAssessmentOos.setOosProcedural("");
				}
				if(null != (rs.getString("ERROR_TYPE"))){
					auditAssessmentOos.setOosErrorType((rs.getString("ERROR_TYPE")).toString());
				}
				else{
					auditAssessmentOos.setOosErrorType("");
				}
				if(null != (rs.getString("ERROR_REASON_DESC"))){
					auditAssessmentOos.setOosExplanation((rs.getString("ERROR_REASON_DESC")).toString());
				}
				else{
					auditAssessmentOos.setOosExplanation("");
				}
				if(null != (rs.getString("FYI"))){
					auditAssessmentOos.setOosFyi("FYI - "+(rs.getString("FYI")).toString());
				}
				else{
					auditAssessmentOos.setOosFyi("");
				}
				if(null != (rs.getString("SOP_NEWS_FLASH_REFNC"))){
					auditAssessmentOos.setOosSop((rs.getString("SOP_NEWS_FLASH_REFNC")).toString());
				}
				else{
					auditAssessmentOos.setOosSop("");
				}
				
				if(null != (rs.getString("PRIMARY_PERF_GROUP_ID"))){
					auditAssessmentOos.setOosPriPerfGroup("P-"+((rs.getString("PRIMARY_PERF_GROUP_ID")).toString()));
				}
				else{
					auditAssessmentOos.setOosPriPerfGroup("");
				}
				if(null != (rs.getString("SECONDARY_PERF_GROUP_ID"))){
					auditAssessmentOos.setOosSecPerfGroup("S-"+((rs.getString("SECONDARY_PERF_GROUP_ID")).toString()));
				}
				else{
					auditAssessmentOos.setOosSecPerfGroup("");
				}
				auditAssessmentOos.setOosAuditDate((rs.getString("AUDIT_DT")).toString());
				auditAssessmentOos.setOosAuditor((rs.getString("USER_ID")).toString());
				
				auditAssessmentList.add(auditAssessmentOos);
			}	
			
			rs.close();
			
			}
				
			callableStatment.close();
			 
			conn.close();
			
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		
		JRDataSource ds = new JRBeanCollectionDataSource(auditAssessmentList);	
		
		logger.debug("*** end getAssesmentReport *** ");
		// Return the wrapped collection
		return ds;
	}

	public int getInSampleCount(String associateId){
		int inSampleCount;
		String sql =QADBConstants.IN_SAMPLE_COUNT_ONE+associateId+QADBConstants.IN_SAMPLE_COUNT_TWO;
		JdbcTemplate jdbcTemp = new JdbcTemplate(dataSource);
		inSampleCount=jdbcTemp.queryForObject(sql,Integer.class);
		return inSampleCount;
	}


}
