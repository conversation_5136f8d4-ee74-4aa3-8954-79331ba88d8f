package com.carefirst.qadb.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.sql.DataSource;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import oracle.jdbc.OracleTypes;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import com.carefirst.audit.model.EditCodeReport;
import com.carefirst.audit.model.InSampleOutOfSampleReport;
import com.carefirst.qadb.constant.QADBConstants;

public class InSampleOutOfSampleReportsDAOImpl implements
		InSampleOutOfSampleReportsDAO {

	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);

	@Autowired
	DataSource dataSource;

	@Override
	public JRDataSource getInSampleOutOfSampleReport(
			InSampleOutOfSampleReport isOosReportForm) throws SQLException {

		logger.debug("***getInSampleOutOfSampleReport entry***");
		String getInSampleOutOfSampleReport = QADBConstants.REPORTS_IS_OOS_REPORT_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getInSampleOutOfSampleReport);
		
		List<InSampleOutOfSampleReport> inSampleOutOfSampleList = new ArrayList<InSampleOutOfSampleReport>();

		try {

			callableStatment.setNull(1, java.sql.Types.VARCHAR);
			callableStatment.setString(2, isOosReportForm.getReportType());
			
			
			if((isOosReportForm.getReportType()).equalsIgnoreCase("ADMIN")){
				callableStatment.setNull(3, java.sql.Types.VARCHAR);
			}
			else{
				callableStatment.setString(3, isOosReportForm.getAuditorName());
			}
			
			callableStatment.setString(4, isOosReportForm.getMonth());
			callableStatment.setString(5, isOosReportForm.getYear());

			callableStatment.registerOutParameter(6, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(7, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(8, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(9, OracleTypes.VARCHAR);

			callableStatment.registerOutParameter(10, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(11, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(12, OracleTypes.VARCHAR);

			callableStatment.executeUpdate();

			ResultSet rs = (ResultSet) callableStatment.getObject(10);

			while (rs.next()) {
				logger.debug("***Entry" + (rs.getString("ASSOCIATE_NAME")));

				InSampleOutOfSampleReport inSampleOutOfSampleEO = new InSampleOutOfSampleReport();

				inSampleOutOfSampleEO.setMonth(isOosReportForm.getMonth());
				inSampleOutOfSampleEO.setYear(isOosReportForm.getYear());
				
				if(null !=(isOosReportForm.getReportType())){
					if((isOosReportForm.getReportType()).equalsIgnoreCase("ADMIN")){
						inSampleOutOfSampleEO.setAuditorName(rs.getString("AUDITOR"));
					}
				}
				
				inSampleOutOfSampleEO.setAssociateName(rs.getString("ASSOCIATE_NAME"));
				inSampleOutOfSampleEO.setIsMock(rs.getString("INS_MOCK"));
				inSampleOutOfSampleEO.setOosMock(rs.getString("OOS_MOCK"));
				inSampleOutOfSampleEO.setIsNonMock(rs.getString("INS_NMOCK"));
				inSampleOutOfSampleEO.setOosNonMock(rs.getString("OOS_NMOCK"));
				
				inSampleOutOfSampleEO.setSumIsMock(callableStatment.getString(6));
				inSampleOutOfSampleEO.setSumOosMock(callableStatment.getString(7));
				inSampleOutOfSampleEO.setSumIsNonMock(callableStatment.getString(8));
				inSampleOutOfSampleEO.setSumOosNonMock(callableStatment.getString(9));
				
				inSampleOutOfSampleList.add(inSampleOutOfSampleEO);
				logger.debug("***Exit");
			}

			rs.close();

			callableStatment.close();

			conn.close();

		} catch (Exception e) {
			e.printStackTrace();
			logger.debug("Exception generated" + e.getMessage());
		}

		JRDataSource ds = new JRBeanCollectionDataSource(inSampleOutOfSampleList);

		logger.debug("***getInSampleOutOfSampleReport exit***");
		// Return the wrapped collection
		return ds;
	}

}
