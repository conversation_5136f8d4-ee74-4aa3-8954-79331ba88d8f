<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="tree-template" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="NoDataSection" columnWidth="802" isSummaryWithPageHeaderAndFooter="true">
	<property name="com.jasperassistant.designer.Grid" value="false"/>
	<property name="com.jasperassistant.designer.SnapToGrid" value="false"/>
	<property name="com.jasperassistant.designer.GridWidth" value="12"/>
	<property name="com.jasperassistant.designer.GridHeight" value="12"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	
	<field name="timeframe" class="java.lang.String">
		<fieldDescription><![CDATA[timeframe]]></fieldDescription>
	</field>
	<field name="e2e" class="java.lang.String">
		<fieldDescription><![CDATA[e2e]]></fieldDescription>
	</field>
	<field name="associateName" class="java.lang.String">
		<fieldDescription><![CDATA[associateName]]></fieldDescription>
	</field>
	<field name="auditType" class="java.lang.String">
		<fieldDescription><![CDATA[auditType]]></fieldDescription>
	</field>
	<field name="subType" class="java.lang.String">
		<fieldDescription><![CDATA[subType]]></fieldDescription>
	</field>
	<field name="processedDate" class="java.lang.String">
		<fieldDescription><![CDATA[processedDate]]></fieldDescription>
	</field>
	<field name="isPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isPaid]]></fieldDescription>
	</field>
	<field name="isOverPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isOverPaid]]></fieldDescription>
	</field>
	<field name="isUnderPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isUnderPaid]]></fieldDescription>
	</field>
	<field name="isProcedural" class="java.lang.String">
		<fieldDescription><![CDATA[isProcedural]]></fieldDescription>
	</field>
	<field name="isMonetary" class="java.lang.String">
		<fieldDescription><![CDATA[isMonetary]]></fieldDescription>
	</field>
	<field name="isTotal" class="java.lang.String">
		<fieldDescription><![CDATA[isTotal]]></fieldDescription>
	</field>
	<field name="isProceduralAccuracy" class="java.lang.String">
		<fieldDescription><![CDATA[isProceduralAccuracy]]></fieldDescription>
	</field>
	<field name="isDollarFreq" class="java.lang.String">
		<fieldDescription><![CDATA[isDollarFreq]]></fieldDescription>
	</field>
	<field name="isDollarAccuracy" class="java.lang.String">
		<fieldDescription><![CDATA[isDollarAccuracy]]></fieldDescription>
	</field>
	<field name="isCompositeQuality" class="java.lang.String">
		<fieldDescription><![CDATA[isCompositeQuality]]></fieldDescription>
	</field>
	
	<field name="isTotalClaims" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalClaims]]></fieldDescription>
	</field>
	<field name="isTotalPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalPaid]]></fieldDescription>
	</field>
	<field name="isTotalOverPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalOverPaid]]></fieldDescription>
	</field>
	<field name="isTotalUnderPaid" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalUnderPaid]]></fieldDescription>
	</field>
	<field name="isTotalProcedural" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalProcedural]]></fieldDescription>
	</field>
	<field name="isTotalMonetary" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalMonetary]]></fieldDescription>
	</field>
	<field name="isSumTotal" class="java.lang.String">
		<fieldDescription><![CDATA[isSumTotal]]></fieldDescription>
	</field>
	<field name="isTotalProceduralAccuracy" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalProceduralAccuracy]]></fieldDescription>
	</field>
	<field name="isTotalDollarFreq" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalDollarFreq]]></fieldDescription>
	</field>
	<field name="isTotalDollarAccuracy" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalDollarAccuracy]]></fieldDescription>
	</field>
	<field name="isTotalCompositeQuality" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalCompositeQuality]]></fieldDescription>
	</field>
	
	<background>
		<band height="10" splitType="Stretch">
		</band>
	</background>
	
	<pageHeader>
		<band height="141" splitType="Stretch">
			<staticText>
				<reportElement mode="Transparent" x="4" y="3" width="797" height="30" forecolor="#000080" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="26" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<text><![CDATA[ Claims Audit Report]]></text>
			</staticText>
			<textField>
				<reportElement x="4" y="34" width="797" height="30" forecolor="#000080"  />
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="20" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["LOB Audit Scores For "+$F{timeframe}+" "+"("+$F{e2e}+")"]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="7" y="32" width="793" height="3" forecolor="#FFFFFF" backcolor="#C0C0C0"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="8" y="61" width="793" height="3" forecolor="#FFFFFF" backcolor="#C0C0C0"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="2" y="71" width="799" height="23"  />
			</rectangle>
			<staticText>
				<reportElement mode="Transparent" x="1" y="70" width="94" height="30"  />
				<box topPadding="4" leftPadding="8">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[LOB : ]]></text>
			</staticText>
			<textField>
				<reportElement x="84" y="72" width="392" height="21"  />
				<textElement>
					<font size="14" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{associateName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="66" y="100" width="54" height="20" forecolor="#1A2D99"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[DCN]]></text>
			</staticText>
			<staticText>
				<reportElement x="187" y="100" width="54" height="20" forecolor="#1A2D99"  >
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="255" y="100" width="54" height="20" forecolor="#1A2D99"  >
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Overpaid]]></text>
			</staticText>
			<staticText>
				<reportElement x="320" y="100" width="54" height="20" forecolor="#1A2D99"  >
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Underpaid]]></text>
			</staticText>
			<rectangle>
				<reportElement x="374" y="100" width="155" height="32" backcolor="#99CCFF"  />
			</rectangle>
			<rectangle>
				<reportElement x="374" y="116" width="52" height="16" backcolor="#99CCFF"  />
			</rectangle>
			<rectangle>
				<reportElement x="426" y="116" width="52" height="16" backcolor="#99CCFF"  />
			</rectangle>
			<rectangle>
				<reportElement x="478" y="116" width="51" height="16" backcolor="#99CCFF"  >
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="374" y="100" width="155" height="20" forecolor="#1A2D99"  >
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box>
					<topPen lineColor="#0000FF"/>
					<leftPen lineColor="#0000FF"/>
					<bottomPen lineColor="#0000FF"/>
					<rightPen lineColor="#0000FF"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Number of Error Claims]]></text>
			</staticText>
			<staticText>
				<reportElement x="374" y="116" width="54" height="20" forecolor="#1A2D99"  >
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Procedural]]></text>
			</staticText>
			<staticText>
				<reportElement x="426" y="116" width="54" height="20" forecolor="#1A2D99"  >
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="local_mesure_unity" value="pixel"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Monetary]]></text>
			</staticText>
			<staticText>
				<reportElement x="478" y="116" width="54" height="20" forecolor="#1A2D99"  >
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Total]]></text>
			</staticText>
			<staticText>
				<reportElement x="541" y="100" width="54" height="20" forecolor="#1A2D99"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Procedural]]></text>
			</staticText>
			<staticText>
				<reportElement x="613" y="100" width="54" height="20" forecolor="#1A2D99"  >
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Dollar]]></text>
			</staticText>
			<staticText>
				<reportElement x="681" y="100" width="54" height="20" forecolor="#1A2D99"  >
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="749" y="100" width="54" height="20" forecolor="#1A2D99"  >
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Composite]]></text>
			</staticText>
			<staticText>
				<reportElement x="541" y="111" width="54" height="20" forecolor="#1A2D99"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Accuracy]]></text>
			</staticText>
			<staticText>
				<reportElement x="613" y="111" width="54" height="20" forecolor="#1A2D99"  >
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Frequency]]></text>
			</staticText>
			<staticText>
				<reportElement x="681" y="111" width="54" height="20" forecolor="#1A2D99"  >
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Accuracy]]></text>
			</staticText>
			<staticText>
				<reportElement x="749" y="111" width="54" height="20" forecolor="#1A2D99"  >
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="10" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Quality]]></text>
			</staticText>
			<staticText>
				<reportElement x="449" y="75" width="75" height="14" forecolor="#000080"  />
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Audit Type :]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="507" y="76" width="131" height="13"  />
				<textElement>
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{auditType}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="634" y="76" width="75" height="14" forecolor="#000080"  />
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Sub Type :]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="685" y="77" width="120" height="13"  />
				<textElement>
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{subType}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="15" splitType="Stretch">
			<rectangle>
				<reportElement x="374" y="-1" width="52" height="16" backcolor="#99CCFF"  />
			</rectangle>
			<line>
				<reportElement positionType="Float" x="49" y="-2" width="325" height="1"  >
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineStyle="Solid"/>
				</graphicElement>
			</line>
			<rectangle>
				<reportElement x="478" y="-1" width="51" height="16" backcolor="#99CCFF"  >
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
			</rectangle>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="255" y="1" width="56" height="14" forecolor="#030303" backcolor="#FFFFFF"  >
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isOverPaid} != null && $F{isOverPaid}.length() > 0 ? Double.valueOf($F{isOverPaid}) : 0))]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="426" y="-1" width="52" height="16" backcolor="#99CCFF"  />
			</rectangle>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="187" y="1" width="56" height="14" forecolor="#030303" backcolor="#FFFFFF"  >
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isPaid} != null && $F{isPaid}.length() > 0 ? Double.valueOf($F{isPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="320" y="1" width="56" height="14" forecolor="#030303" backcolor="#FFFFFF"  >
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isUnderPaid} != null && $F{isUnderPaid}.length() > 0 ? Double.valueOf($F{isUnderPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="374" y="1" width="54" height="14" forecolor="#050505" backcolor="#FFFFFF"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isProcedural}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="426" y="1" width="54" height="14" forecolor="#050505" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isMonetary}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="478" y="1" width="54" height="14" forecolor="#050505" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isTotal}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="67" y="1" width="103" height="14" forecolor="#030303" backcolor="#FFFFFF"  >
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{processedDate}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="541" y="0" width="54" height="14" forecolor="#030303" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isProceduralAccuracy}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="613" y="0" width="54" height="14" forecolor="#030303" backcolor="#FFFFFF"  >
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isDollarFreq}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="681" y="0" width="54" height="14" forecolor="#030303" backcolor="#FFFFFF"  >
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isDollarAccuracy}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="749" y="0" width="54" height="14" forecolor="#030303" backcolor="#FFFFFF"  >
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isCompositeQuality}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
		<pageFooter>
		<band height="54" splitType="Stretch">
			<textField>
				<reportElement x="619" y="22" width="100" height="30"  />
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="719" y="22" width="100" height="30"  />
				<textElement textAlignment="Left"/>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="EEEE, MMMMM dd, yyyy">
				<reportElement x="4" y="20" width="146" height="17"  />
				<textFieldExpression class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="295" y="21" width="212" height="16"  />
				<text><![CDATA[Quality Assurance Productivity Management]]></text>
			</staticText>
			<rectangle>
				<reportElement x="2" y="10" width="799" height="3" forecolor="#FFFFFF" backcolor="#C0C0C0"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
		</band>
	</pageFooter>
	<lastPageFooter>
		<band height="52" splitType="Stretch">
			<textField>
				<reportElement x="619" y="22" width="100" height="30"  />
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="719" y="22" width="100" height="30"  />
				<textElement textAlignment="Left"/>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="EEEE, MMMMM dd, yyyy">
				<reportElement x="4" y="20" width="146" height="17"  />
				<textFieldExpression class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="295" y="21" width="212" height="16"  />
				<text><![CDATA[Quality Assurance Productivity Management]]></text>
			</staticText>
			<rectangle>
				<reportElement x="2" y="10" width="799" height="3" forecolor="#FFFFFF" backcolor="#C0C0C0"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
		</band>
	</lastPageFooter>
	<summary>
		<band height="81" splitType="Stretch">
			<rectangle>
				<reportElement x="374" y="36" width="155" height="15" backcolor="#99CCFF"  />
			</rectangle>
			<rectangle>
				<reportElement x="478" y="36" width="51" height="16" backcolor="#99CCFF"  >
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="local_mesure_unitx" value="pixel"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="374" y="36" width="52" height="16" backcolor="#99CCFF"  />
			</rectangle>
			<staticText>
				<reportElement x="5" y="36" width="38" height="18"  />
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Sum]]></text>
			</staticText>
			<rectangle>
				<reportElement x="426" y="36" width="52" height="16" backcolor="#99CCFF"  />
			</rectangle>
			<textField>
				<reportElement mode="Transparent" x="373" y="38" width="54" height="20" forecolor="#050505" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isTotalProcedural}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="476" y="39" width="54" height="20" forecolor="#050505" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isSumTotal}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="253" y="38" width="62" height="15" forecolor="#030303" backcolor="#FFFFFF"  >
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isTotalOverPaid} != null && $F{isTotalOverPaid}.length() > 0 ? Double.valueOf($F{isTotalOverPaid}) : 0))]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="5" y="54" width="38" height="18"  />
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Total]]></text>
			</staticText>
			<textField>
				<reportElement mode="Transparent" x="67" y="38" width="54" height="14" forecolor="#030303" backcolor="#FFFFFF"  >
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isTotalClaims}]]></textFieldExpression>
			</textField>
			<textField> 
				<reportElement mode="Transparent" x="426" y="38" width="54" height="20" forecolor="#050505" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isTotalMonetary}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="183" y="38" width="62" height="15" forecolor="#030303" backcolor="#FFFFFF"  >
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isTotalPaid} != null && $F{isTotalPaid}.length() > 0 ? Double.valueOf($F{isTotalPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="314" y="38" width="62" height="15" forecolor="#030303" backcolor="#FFFFFF"  >
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{isTotalUnderPaid} != null && $F{isTotalUnderPaid}.length() > 0 ? Double.valueOf($F{isTotalUnderPaid}) : 0))]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="2" y="4" width="799" height="3" forecolor="#FFFFFF" backcolor="#0A0909"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="542" y="56" width="52" height="16" backcolor="#030303"  >
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
			</rectangle>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="541" y="57" width="54" height="14" forecolor="#FFFFFF" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isTotalProceduralAccuracy}+"%"]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="614" y="56" width="52" height="16" backcolor="#030303"  >
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="680" y="56" width="52" height="16" backcolor="#030303"  >
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="750" y="57" width="52" height="16" backcolor="#030303"  >
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
			</rectangle>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="749" y="57" width="54" height="14" forecolor="#FFFFFF" backcolor="#FFFFFF"  >
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isTotalCompositeQuality}+"%"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="681" y="57" width="54" height="14" forecolor="#FFFFFF" backcolor="#FFFFFF"  >
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isTotalDollarAccuracy}+"%"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement mode="Transparent" x="613" y="57" width="54" height="14" forecolor="#FFFFFF" backcolor="#FFFFFF"  >
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isTotalDollarFreq}+"%"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="4" y="10" width="472" height="18" forecolor="#000000" backcolor="#FFFFFF"  />
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Times New Roman" size="14" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA["Summary For "+$F{associateName}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
	<noData>
		<band height="55" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="300" y="0" width="316" height="47" forecolor="#000050">
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="22" isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[No records found!!]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>
