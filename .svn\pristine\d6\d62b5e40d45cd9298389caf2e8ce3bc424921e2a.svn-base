package com.carefirst.qadb.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.List;

import javax.sql.DataSource;

import oracle.jdbc.OracleTypes;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import com.carefirst.audit.model.JobTitle;
import com.carefirst.qadb.constant.QADBConstants;
import com.carefirst.qadb.converter.CheckConverter;


public class JobTitlesDAOImpl implements JobTitlesDAO {
	
	
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);
	
	@Autowired
	DataSource dataSource;

	@Override
	public List<JobTitle> getJobTitles(JobTitle jobTitleTO) throws SQLException {

		logger.debug("*** Entry getJobTitles method ***");
		logger.debug("user id "+jobTitleTO.getUserId());
		String getJobTitles = QADBConstants.ADMIN_JOB_TITLE_SEARCH_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getJobTitles);
		List<JobTitle> jobTitlesList= new ArrayList<JobTitle>();
		try {
			
			callableStatment.setString(1, jobTitleTO.getUserId());
			callableStatment.setString(2, jobTitleTO.getSearchType());
			callableStatment.setNull(3, java.sql.Types.INTEGER);
			callableStatment.registerOutParameter(4, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(5, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(6, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(6));
			callableStatment.executeUpdate();
			
			ResultSet  rs =   (ResultSet) callableStatment.getObject(4);			
			logger.debug("Row count" +rs.getRow());
			
			while (rs.next()) {
				JobTitle jobTitleEO = new JobTitle();
				if(null!=rs.getString("JOB_TITLE_ID")){
					jobTitleEO.setTitleId((rs.getString("JOB_TITLE_ID")).toString());
				}
				if(null!=rs.getString("JOB_TITLE_NAME")){
					jobTitleEO.setTitleName((rs.getString("JOB_TITLE_NAME")).toString());
				}
				if(null!=rs.getString("JOB_TITLE_STATUS")){
					jobTitleEO.setStatus((rs.getString("JOB_TITLE_STATUS")).toString());
				}
				jobTitlesList.add(jobTitleEO);
			}	
			rs.close();
			
		} catch (Exception e) {
			// TODO: handle exception
		}finally{
			callableStatment.close();
			conn.close();
		}
		logger.debug("*** Exit getJobTitles method ***");
		return jobTitlesList;
		
	}

	@Override
	public JobTitle saveUpdateJobTitle(JobTitle jobTitlesTO) throws SQLException {
		logger.debug("*** Entry saveUpdateJobTitle method ***");
		logger.debug("Job Title id " + jobTitlesTO.getTitleId()+ "-"+jobTitlesTO.getTitleName());
		String jobTitleSaveUpdateSp = QADBConstants.ADMIN_JOB_TITLE_SAVE_SP;
		Connection conn = null;
		CallableStatement callableStatment = null;
		JobTitle jobTitlesRO = new JobTitle();
		
		try {
			conn = dataSource.getConnection();
			callableStatment = conn.prepareCall(jobTitleSaveUpdateSp);
			
			//Set the input parameters for the procedure
			callableStatment.setString(1, jobTitlesTO.getUserId());
			callableStatment.setString(2, jobTitlesTO.getUserActyp());
			if(null != jobTitlesTO.getTitleId()){
				callableStatment.setInt(3,Integer.parseInt(jobTitlesTO.getTitleId()));
			}else{
				callableStatment.setNull(3, java.sql.Types.INTEGER);
			}
			callableStatment.setString(4, jobTitlesTO.getTitleName());
			logger.debug("status "+jobTitlesTO.getStatus()+ " chk " +new CheckConverter().convert(jobTitlesTO.getStatus()));
			callableStatment.setString(5, new CheckConverter().convert(jobTitlesTO.getStatus()));
			callableStatment.registerOutParameter(6, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(7, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(8, OracleTypes.VARCHAR);
			
			callableStatment.execute();
			
			logger.debug("status code = "+callableStatment.getString(7));
			logger.debug("status message = "+callableStatment.getString(8));
			
			jobTitlesRO.setSucessCode(callableStatment.getString(7).toString());
			jobTitlesRO.setSuccessMsg(callableStatment.getString(8).toString());
			
		} catch (Exception e) {
			conn.rollback();
			e.printStackTrace();
			
		}finally {
			if (callableStatment != null) {
				callableStatment.close();
			}

			if (conn != null) {
				conn.close();
			}
		}
		logger.debug("*** Exit saveUpdateJobTitle method ***");
		return jobTitlesRO;
	}

	@Override
	public JobTitle getJobTitleById(JobTitle jobTitleSearchTO) throws SQLException {
		
		logger.debug("*** Entry getJobTitleById method ***"+jobTitleSearchTO.getTitleId());
		String getJobTitleDetails = QADBConstants.ADMIN_JOB_TITLE_SEARCH_SP;
		JobTitle jobTitleEO = new JobTitle();
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getJobTitleDetails);
		try {
			callableStatment.setString(1, jobTitleSearchTO.getUserId());
			callableStatment.setString(2, jobTitleSearchTO.getSearchType());
			callableStatment.setInt(3,Integer.parseInt(jobTitleSearchTO.getTitleId()));
			callableStatment.registerOutParameter(4, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(5, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(6, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(6));
			
			callableStatment.executeUpdate();
			ResultSet  rs =   (ResultSet) callableStatment.getObject(4);			
			logger.debug("Row count" +rs.getRow());

			while (rs.next()) {
				if(null!=rs.getString("JOB_TITLE_ID")){
					jobTitleEO.setTitleId((rs.getString("JOB_TITLE_ID")).toString());
				}
				if(null!=rs.getString("JOB_TITLE_NAME")){
					jobTitleEO.setTitleName((rs.getString("JOB_TITLE_NAME")).toString());
				}
				if(null!=rs.getString("JOB_TITLE_ACTIVE_FLG")){
					jobTitleEO.setStatus((rs.getString("JOB_TITLE_ACTIVE_FLG")).toString());
				}
			}	
			rs.close();
			
			
		} catch (Exception e) {
			logger.debug("Exception :"+e.getMessage());
		}finally{
			callableStatment.close();
			conn.close();
		}
		
		return jobTitleEO;
	}
	  

}
