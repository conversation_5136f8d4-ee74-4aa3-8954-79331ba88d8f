package com.carefirst.qadb.controller;

import org.apache.log4j.Logger;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.servlet.ModelAndView;

import com.carefirst.qadb.constant.QADBConstants;

@ControllerAdvice
public class GlobalExceptionController {
	
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);

	@ExceptionHandler(Exception.class)
	public ModelAndView handleAllException(Exception ex) {

		ModelAndView model = new ModelAndView("error/error");
		model.addObject("errMsg", ex.getMessage());
		logger.debug("Global exception Thrown..");
		logger.debug("File   : "+ex.getStackTrace()[0].getFileName());
		logger.debug("Class  : "+ex.getStackTrace()[0].getClassName());
		logger.debug("Method : "+ex.getStackTrace()[0].getMethodName());
		logger.debug("Line   : "+ex.getStackTrace()[0].getLineNumber());
		logger.debug("Message: "+ex.getLocalizedMessage());
		logger.debug("Message: "+ex.getMessage());
		logger.debug("Trace  : "+ex.getStackTrace());
		ex.printStackTrace();
		return model;

	}

}
