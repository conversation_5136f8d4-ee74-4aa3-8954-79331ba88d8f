<!DOCTYPE html><%@page language="java"
	contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>

<html>

<select id="secPGALists" name="secPGA">
	
		<option selected value=""><spring:message
				code="audit.new.select.secondary.pga" /></option>
	

	<%-- <c:forEach items="${secPGAList}" var="secPGAList">
		<option value="${secPGAList.sPgaId}"
			${secPGAList.sPgaId == secPGA ? 'selected="selected"' : ''}>${secPGAList.secPGA}</option>
	</c:forEach> --%>
	
	<c:choose>
		<c:when test="${secPGA != null }">
			<c:forEach items="${secPGAList}" var="secPGAList">
				<option value="${secPGAList.sPgaId}"
					${secPGAList.sPgaId == secPGA ? 'selected="selected"' : ''}>${secPGAList.secPGA}</option>
			</c:forEach>
		</c:when>
		<c:otherwise>
			<c:forEach items="${secPGAList}" var="secPGAList">
				<option value="${secPGAList.sPgaId}"
					${secPGAList.sPgaId == eSecPGA ? 'selected="selected"' : ''}>${secPGAList.secPGA}</option>
			</c:forEach>
		</c:otherwise>
	</c:choose>
	
</select>

</html>
