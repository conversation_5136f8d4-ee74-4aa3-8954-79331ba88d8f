package com.carefirst.audit.model;


public class InSampleOutOfSampleReport {
	
	private String month;
	private String year;
	private String reportType;
	
	//Report
	private String auditorName;
	private String associateName;
	private String isMock;
	private String isNonMock;
	private String oosMock;
	private String oosNonMock;
	private String sumIsMock;
	private String sumIsNonMock;
	private String sumOosMock;
	private String sumOosNonMock;
	
	public String getSumIsMock() {
		return sumIsMock;
	}
	public void setSumIsMock(String sumIsMock) {
		this.sumIsMock = sumIsMock;
	}
	public String getSumIsNonMock() {
		return sumIsNonMock;
	}
	public void setSumIsNonMock(String sumIsNonMock) {
		this.sumIsNonMock = sumIsNonMock;
	}
	public String getSumOosMock() {
		return sumOosMock;
	}
	public void setSumOosMock(String sumOosMock) {
		this.sumOosMock = sumOosMock;
	}
	public String getSumOosNonMock() {
		return sumOosNonMock;
	}
	public void setSumOosNonMock(String sumOosNonMock) {
		this.sumOosNonMock = sumOosNonMock;
	}
	public String getReportType() {
		return reportType;
	}
	public void setReportType(String reportType) {
		this.reportType = reportType;
	}
	public String getMonth() {
		return month;
	}
	public void setMonth(String month) {
		this.month = month;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	public String getAuditorName() {
		return auditorName;
	}
	public void setAuditorName(String auditorName) {
		this.auditorName = auditorName;
	}
	public String getAssociateName() {
		return associateName;
	}
	public void setAssociateName(String associateName) {
		this.associateName = associateName;
	}
	public String getIsMock() {
		return isMock;
	}
	public void setIsMock(String isMock) {
		this.isMock = isMock;
	}
	public String getIsNonMock() {
		return isNonMock;
	}
	public void setIsNonMock(String isNonMock) {
		this.isNonMock = isNonMock;
	}
	public String getOosMock() {
		return oosMock;
	}
	public void setOosMock(String oosMock) {
		this.oosMock = oosMock;
	}
	public String getOosNonMock() {
		return oosNonMock;
	}
	public void setOosNonMock(String oosNonMock) {
		this.oosNonMock = oosNonMock;
	}
	
}
