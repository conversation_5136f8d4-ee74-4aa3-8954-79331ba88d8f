<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:mvc="http://www.springframework.org/schema/mvc" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"

	xsi:schemaLocation="
        http://www.springframework.org/schema/beans     
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://www.springframework.org/schema/mvc 
        http://www.springframework.org/schema/mvc/spring-mvc-3.0.xsd
        http://www.springframework.org/schema/context 
        http://www.springframework.org/schema/context/spring-context-3.0.xsd">


	<context:component-scan base-package="com.carefirst.qadb.controller" />

	<!-- path for sources css,images,js -->
	<mvc:resources mapping="/webResources/**" location="/webResources/" />
	<mvc:default-servlet-handler />

	<mvc:annotation-driven />

	<!-- XMl view for jasper views.xml -->
	<bean class="org.springframework.web.servlet.view.XmlViewResolver" />


	<bean
		class="org.springframework.web.servlet.view.InternalResourceViewResolver">
		<property name="prefix" value="/WEB-INF/jsp/" />
		<property name="suffix" value=".jsp" />

	</bean>





</beans>



