package com.carefirst.qadb.service;

import java.net.MalformedURLException;
import java.util.HashMap;

import com.carefirst.*;

public interface ClaimService {
    /**
     * QADB is used for auditing the claims from the FACETS and it caters to mainly 2 areas i.e. 
     * Small & Medium group and Consumer Direct. FEP claims are not audited using this system.
     * @param DCN.
     * Version can be blank. In this case FACETs should return all versions.
     * If Specified FACETS should return asked version.
     */
    public com.carefirst.audit.model.QASamplingResponse getClaim(String DCN)throws MalformedURLException ;
    
    public HashMap<String, String> getEditCodes();
}
