package com.carefirst.qadb.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import javax.sql.DataSource;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import oracle.jdbc.OracleTypes;

import com.carefirst.audit.model.ErrorCodes;
import com.carefirst.audit.model.RootCause;
import com.carefirst.qadb.constant.QADBConstants;
import com.carefirst.qadb.converter.CheckConverter;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

public class RootCauseDAOImpl implements RootCauseDAO{
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);

	@Autowired
	DataSource dataSource;
	
	/**
	 * Calling procedure to save a new root cause
	 */
	@Override
	public RootCause saveRootCause(RootCause rootCause) throws SQLException {
		logger.debug("*** Entry saveRootCause method ***");
		//logger.debug("Error name" + errorCodes.getErrorCode());
		String rootCauseSp = QADBConstants.ADMIN_ROOT_CAUSE_SAVE_SP;
		Connection conn = null;
		CallableStatement callableStatment = null;
		RootCause rootCauseRO = new RootCause();
		try {
			//SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yy");
			conn = dataSource.getConnection();
			callableStatment = conn.prepareCall(rootCauseSp);
			
			//Set the input parameters for the procedure
			callableStatment.setString(1, rootCause.getUserid());
			callableStatment.setString(2, rootCause.getUserActyp());
			if(null!=rootCause.getRootCauseId()){
				callableStatment.setInt(3,Integer.parseInt(rootCause.getRootCauseId()));
			}else{
				callableStatment.setNull(3, java.sql.Types.INTEGER);
			}
			callableStatment.setString(4, rootCause.getRootCauseName());
			callableStatment.setString(5, rootCause.getRootCauseDescription());
			logger.debug("grp--- "+ new CheckConverter().getUserGroup(rootCause.getGrpSammd(), rootCause.getGrpCd()));
			callableStatment.setString(6,new CheckConverter().getUserGroup(rootCause.getGrpSammd(), rootCause.getGrpCd()));
			callableStatment.setString(7, new CheckConverter().convert(rootCause.getApplicableAssociate()));
			callableStatment.setString(8, new CheckConverter().convert(rootCause.getApplicableSupervisor()));
			callableStatment.setString(9, new CheckConverter().convert(rootCause.getApplicableManager()));
			callableStatment.setString(10, new CheckConverter().convert(rootCause.getApplicableDirector()));
			callableStatment.setString(11, new CheckConverter().convert(rootCause.getApplicableDivision()));
			callableStatment.setString(12, new CheckConverter().convert(rootCause.getApplicablePriPG()));
			callableStatment.setString(13, new CheckConverter().convert(rootCause.getApplicableSecPG()));
			callableStatment.setString(14, rootCause.getEffectiveStartDate());
			callableStatment.setString(15, rootCause.getEffectiveEndDate());
			if(null!=rootCause.getDisableFrom() && null!=rootCause.getDisableTo()){
				callableStatment.setString(16, rootCause.getDisableFrom());
				callableStatment.setString(17, rootCause.getDisableTo());
			}else{
				callableStatment.setNull(16, java.sql.Types.DATE);
				callableStatment.setNull(17, java.sql.Types.DATE);
			}
			
			callableStatment.setString(18, "Y");
			/*Error type mapping enhancement SVET012196 N7*/
			callableStatment.setString(19, rootCause.getErrorTypeMapping());
			callableStatment.registerOutParameter(20, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(21, OracleTypes.VARCHAR);
			
			callableStatment.execute();
			
			logger.debug("status code = "+callableStatment.getString(20));
			logger.debug("status message = "+callableStatment.getString(21));
			
			rootCauseRO.setSuccessCode(callableStatment.getString(20));
			rootCauseRO.setSuccessMsg(callableStatment.getString(21));
			
		} catch (Exception e) {
			logger.debug("Exception = "+e.getMessage());
			conn.rollback();
			e.printStackTrace();
			
		}finally {
			if (callableStatment != null) {
				callableStatment.close();
			}

			if (conn != null) {
				conn.close();
			}
		}
		logger.debug("*** Exit saveRootCause method ***");
		return rootCauseRO;
	}
	
	/**
	 * Method to search the root cause
	 */
	@Override
	public List<RootCause> searchRootCause(RootCause rootCause) throws SQLException {

		logger.debug("*** Entry searchRootCause method ***");
		String searchRootCause = QADBConstants.ADMIN_ROOT_CAUSE_SEARCH_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(searchRootCause);
		List<RootCause> rootCauseList= new ArrayList<RootCause>();
		try {
			callableStatment.setString(1,rootCause.getUserid());
			callableStatment.setString(2,rootCause.getUserActyp());
			logger.debug("usrGrp "+rootCause.getUserGrp());
			callableStatment.setString(3,rootCause.getUserGrp());
			if(null!=rootCause.getRootCauseId()){
				callableStatment.setInt(4,Integer.parseInt(rootCause.getRootCauseId()));
			}else{
				callableStatment.setNull(4, java.sql.Types.INTEGER);
			}
			if(null!=rootCause.getRootCauseName()){
				callableStatment.setString(5,rootCause.getRootCauseName());
			}else{
				callableStatment.setNull(5, java.sql.Types.VARCHAR);
			}
			callableStatment.setNull(6, java.sql.Types.VARCHAR);
			callableStatment.setNull(7, java.sql.Types.VARCHAR);
			callableStatment.registerOutParameter(8, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(9, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(10, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(10));
			callableStatment.executeUpdate();
			
			ResultSet  rs =   (ResultSet) callableStatment.getObject(8);			
			logger.debug("Row count" +rs.getRow());
			
			while (rs.next()) {
				RootCause rootCauseEO = new RootCause();
				if(null!=rs.getString("ROOT_CAUSE_ID")){
					rootCauseEO.setRootCauseId((rs.getString("ROOT_CAUSE_ID")).toString());
				}
				if(null!=rs.getString("ROOT_CAUSE_NAME")){
					rootCauseEO.setRootCauseName((rs.getString("ROOT_CAUSE_NAME")).toString());
				}
				if(null!=rs.getString("ROOT_CAUSE_DESC")){
					rootCauseEO.setRootCauseDescription((rs.getString("ROOT_CAUSE_DESC")).toString());
				}
				if(null!=rs.getString("STATUS")){
					rootCauseEO.setRootCauseStatus((rs.getString("STATUS")).toString());
				}
				rootCauseList.add(rootCauseEO);
				logger.debug("***"+rootCauseEO.getRootCauseName());
			}	
			rs.close();
			
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}finally{
			callableStatment.close();
			conn.close();
		}
		logger.debug("*** Exit searchRootCause method ***");
		return rootCauseList;
	
	}
	
	//Change for DMND0003323
	@Override
	public JRDataSource searchRootCauseList(RootCause rootCause) throws SQLException {

		logger.debug("*** Entry searchRootCause method ***");
		String searchRootCause = QADBConstants.ADMIN_ROOT_CAUSE_SEARCH_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(searchRootCause);
		List<RootCause> rootCauseList= new ArrayList<RootCause>();
		try {
			callableStatment.setString(1,rootCause.getUserid());
			callableStatment.setString(2,rootCause.getUserActyp());
			logger.debug("usrGrp "+rootCause.getUserGrp());
			callableStatment.setString(3,rootCause.getUserGrp());
			if(null!=rootCause.getRootCauseId()){
				callableStatment.setInt(4,Integer.parseInt(rootCause.getRootCauseId()));
			}else{
				callableStatment.setNull(4, java.sql.Types.INTEGER);
			}
			if(null!=rootCause.getRootCauseName()){
				callableStatment.setString(5,rootCause.getRootCauseName());
			}else{
				callableStatment.setNull(5, java.sql.Types.VARCHAR);
			}
			callableStatment.setNull(6, java.sql.Types.VARCHAR);
			callableStatment.setNull(7, java.sql.Types.VARCHAR);
			callableStatment.registerOutParameter(8, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(9, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(10, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(10));
			callableStatment.executeUpdate();
			
			ResultSet  rs =   (ResultSet) callableStatment.getObject(8);			
			logger.debug("Row count" +rs.getRow());
			
			while (rs.next()) {
				RootCause rootCauseEO = new RootCause();
				if(null!=rs.getString("ROOT_CAUSE_ID")){
					rootCauseEO.setRootCauseId((rs.getString("ROOT_CAUSE_ID")).toString());
				}
				if(null!=rs.getString("ROOT_CAUSE_NAME")){
					rootCauseEO.setRootCauseName((rs.getString("ROOT_CAUSE_NAME")).toString());
				}
				if(null!=rs.getString("ROOT_CAUSE_DESC")){
					rootCauseEO.setRootCauseDescription((rs.getString("ROOT_CAUSE_DESC")).toString());
				}
				if(null!=rs.getString("STATUS")){
					rootCauseEO.setRootCauseStatus((rs.getString("STATUS")).toString());
				}
				rootCauseList.add(rootCauseEO);
				logger.debug("***"+rootCauseEO.getRootCauseName());
			}	
			rs.close();
			
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}finally{
			callableStatment.close();
			conn.close();
		}
		JRDataSource ds = new JRBeanCollectionDataSource(rootCauseList);
		logger.debug("*** Exit searchRootCauseList method ***");
		return ds;
	
	}
	
	/**
	 * Method to populate the details to edit root cause
	 */
	@Override
	public RootCause getRootCause(RootCause rootCause) throws SQLException {
		logger.debug("*** Entry getRootCause method ***");
		String getRootCause = QADBConstants.ADMIN_ROOT_CAUSE_SEARCH_SP;
		RootCause rootCauseEO = new RootCause();
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getRootCause);
		try {
			callableStatment.setString(1,rootCause.getUserid());
			callableStatment.setString(2,rootCause.getUserActyp());
			callableStatment.setString(3,rootCause.getUserGrp());
			if(null!=rootCause.getRootCauseId()){
				callableStatment.setInt(4,Integer.parseInt(rootCause.getRootCauseId()));
			}else{
				callableStatment.setNull(4, java.sql.Types.INTEGER);
			}
			callableStatment.setNull(5, java.sql.Types.VARCHAR);
			callableStatment.setNull(6, java.sql.Types.VARCHAR);
			callableStatment.setNull(7, java.sql.Types.VARCHAR);
			callableStatment.registerOutParameter(8, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(9, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(10, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(10));
			
			callableStatment.executeUpdate();
			ResultSet  rs =   (ResultSet) callableStatment.getObject(8);			
			logger.debug("Row count" +rs.getRow());

			while (rs.next()) {
				if(null!=rs.getString("ROOT_CAUSE_NAME")){
					rootCauseEO.setRootCauseName((rs.getString("ROOT_CAUSE_NAME")).toString());
				}
				if(null!=rs.getString("ROOT_CAUSE_DESC")){
					rootCauseEO.setRootCauseDescription((rs.getString("ROOT_CAUSE_DESC")).toString());
				}
				if(null!=rs.getString("ROOT_CAUSE_RESPONSIBLE")){
					rootCauseEO.setErrorTypeMapping((rs.getString("ROOT_CAUSE_RESPONSIBLE")).toString());
				}
				if(null!=(rs.getString("ROOT_CAUSE_DISABLE_START_DT")) && null!=(rs.getString("ROOT_CAUSE_DISABLE_START_DT"))){
					rootCauseEO.setDisableFrom((rs.getString("ROOT_CAUSE_DISABLE_START_DT")).toString());
					rootCauseEO.setDisableTo((rs.getString("ROOT_CAUSE_DISABLE_END_DT")).toString());
				}
				rootCauseEO.setApplicableAssociate((rs.getString("ASSOCIATE_APPLICABLE_FLG")).toString());
				rootCauseEO.setApplicableSupervisor((rs.getString("SUPERVISOR_APPLICABLE_FLG")).toString());
				rootCauseEO.setApplicableManager((rs.getString("MANAGER_APPLICABLE_FLG")).toString());
				rootCauseEO.setApplicableDirector((rs.getString("DIRECTOR_APPLICABLE_FLG")).toString());
				rootCauseEO.setApplicableDivision((rs.getString("DIVISION_APPLICABLE_FLG")).toString());
				rootCauseEO.setApplicablePriPG((rs.getString("PRIMARY_PG_APPLICABLE_FLG")).toString());
				rootCauseEO.setApplicableSecPG((rs.getString("SECONDARY_PG_APPLICABLE_FLG")).toString());
				if(null!=(rs.getString("ROOT_CAUSE_EFFECTIVE_START_DT")) && null!=(rs.getString("ROOT_CAUSE_EFFECTIVE_END_DT"))){
					rootCauseEO.setEffectiveStartDate((rs.getString("ROOT_CAUSE_EFFECTIVE_START_DT")).toString());
					rootCauseEO.setEffectiveEndDate((rs.getString("ROOT_CAUSE_EFFECTIVE_END_DT")).toString());
				}
				if(null!=rs.getString("STATUS")){
					rootCauseEO.setDisable((rs.getString("STATUS")).toString());
				}
				if (null != (rs.getString("ROOT_CAUSE_USER_GROUP"))) {
					rootCauseEO.setUserGrp((rs.getString("ROOT_CAUSE_USER_GROUP")).toString());
				} else {
					rootCauseEO.setUserGrp("");
				}
				logger.debug("Ro grp "+rs.getString("ROOT_CAUSE_USER_GROUP"));
			}	
			rs.close();
			
			
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());			
		}finally{
			callableStatment.close();
			conn.close();
		}
		
		return rootCauseEO;
	}
	
}
