body {
  height: 100%;
  margin: 0;
  padding: 0;
}
.metro .page {
  width: 980px;
  margin: 0 auto;
  padding: 0 20px;
}
.description {
  margin: 10px 0;
}
.show-grid .row [class*="span"] {
  line-height: 40px;
  background: rgba(193, 179, 123, 0.52);
  text-align: center;
  min-height: 10px !important;
}
.color-stack li,
#icon-list li {
  margin-bottom: 5px;
}
.example > img {
  margin: 5px;
  display: inline-block;
}
.example .image-container {
  margin: 10px;
  display: inline-block;
  vertical-align: middle;
}
.layout {
  min-height: 100%;
  position: relative;
}
header {
  height: 45px;
  position: relative;
}
footer {
  width: 100%;
  height: 25px;
}
@media only screen and (max-width: 640px) {
  .prettyprint,
  .example {
    display: none;
  }
}
