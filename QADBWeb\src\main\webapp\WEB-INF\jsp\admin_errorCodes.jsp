<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib  prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<head>
<link href="webResources/css/qadb.css" rel="stylesheet">

<script type="text/javascript" src="webResources/js/metro.min.js"></script>
<!-- Local JavaScript -->

<link href="webResources/css/iconFont.css" rel="stylesheet">
<link href="webResources/css/docs.css" rel="stylesheet">
<link href="webResources/js/prettify/prettify.css" rel="stylesheet">

<!-- Load JavaScript Libraries -->
<script type="text/javascript" src="webResources/js/datep.js"></script>
<script src="webResources/js/jquery/jquery.min.js"></script>
<script src="webResources/js/jquery/jquery.widget.min.js"></script>

<script type="text/javascript" src="webResources/js/jquery.min.js"></script>
<script type="text/javascript" src="webResources/js/jquery-ui.js"></script> 
<script type="text/javascript" src="webResources/js/qadb.js"></script>
<link href="webResources/css/qadb.css" rel="stylesheet">
<link href="webResources/css/jquery-ui.css" rel="stylesheet">

<style>
/* Collapse -Expand */
.headerA {
	background: url('webResources/images/Forms/Icn_Accordion_Collapse.png')
		no-repeat;
	background-position: right 0px;
	cursor: pointer;
}

.collapsed .headerA {
	background-image:
		url('webResources/images/Forms/Icn_Accordion_Expand.png');
}

.contentA {
	height: auto;
	min-height: 100px;
	overflow: hidden;
	transition: all 0.3s linear;
	-webkit-transition: all 0.3s linear;
	-moz-transition: all 0.3s linear;
	-ms-transition: all 0.3s linear;
	-o-transition: all 0.3s linear;
	background-color:#fafafa;
}

.collapsed .contentA {
	min-height: 0px;
	height: 0px;
}

.correct {
	display: none;
}
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
	margin-right: auto;
	height: 130%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}

.hidden {
	display: none;
}
div.success {
	padding-left: 5px;
	padding-top: 5px;
	height: 30px; 
	background: #73AD21; 
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	color : white;
}

div.errorInfo {
    padding: 5px 5px 5px 5px ;
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	width:350px;
	font-size:10pt;
	background-color:#CE352C;
	color:white;
}

#error {
    padding: 5px 5px 5px 5px ;
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	width:350px;
	font-size:11pt;
	background-color:#CE352C;
	color:white;
}
.ui-widget {
	font-family: Verdana,Arial,sans-serif;
	font-size: 1.1em;
}
.ui-widget .ui-widget {
	font-size: 1em;
}
.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
	font-family: Verdana,Arial,sans-serif;
	font-size: 1em;
}
.ui-widget-content {
	border: 1px solid #aaaaaa;
	background: #ffffff url() 50% 50% repeat-x;
	color: #222222;
}
.ui-widget-content a {
	color: #222222;
}
/* .ui-widget-header {
	border: 1px solid #aaaaaa;
	background:#2573ab  url(images/ui-bg_highlight-soft_75_cccccc_1x100.png) 50% 50% repeat-x;
	color: #e6f5fc;
	font-weight: bold;
} */
.ui-widget-header a {
	color: #222222;
}
.ui-datepicker-trigger{
background-image: url("webResources/images/Forms/Icn_Date_Picker.png");
height: 36px;
width: 36px;
background-color:white;
}
.ui-icon-circle-triangle-e{
background-image: url("webResources//Navbar/Icn_Right_Arrow.png");
}
.ui-icon-circle-triangle-w{
background-image: url("webResources/images/skip_backward.png");
}

</style>

<script type="text/javascript">
	var a = 1;
	$(document).ready(function() {
	if (a == 1) {
		$('#success').delay(800).fadeIn(400);
			
	}
	})

</script>

<title><spring:message code="admin.errorCodes.title" /></title>
</head>
<body>
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container2">

		<!-- Sidebar Start-->
		<c:choose>
   			 <c:when test="${edit != null}">
   			 	
   			 	<div id="left-sidebar">
					<h2 align="center" style="color: white ; padding-top:32px ">
					${errorCodesRO.errorCode} ${errorCodesRO.errorName} 
					</h2>
					<h3 style="color: white; padding:10px 0px 0px 20px">
						<spring:message code="admin.errorCodes.update.leftNav.heading2" />
					</h3>
				</div>
   			 
    		 </c:when>
    		 <c:otherwise>
    		 		<div id="left-sidebar">
					<h2 style="color: white ; padding-top:32px">
						<spring:message code="admin.errorCodes.leftNav.heading1" />
					</h2>
					<h3 style="color: white; padding-top:10px">
						<spring:message code="admin.errorCodes.leftNav.heading2" />
						</h3>
				</div>
    		 
    		 </c:otherwise>
    	</c:choose>
		<!-- Sidebar End-->

		<div id="content-container" style="padding-left: 40px">
<!--change form action depending upon add /update action  -->

	 <%String formAction=""; %>

 	<c:if test="${edit != null}"> 
  		<% formAction="updateErrorCodes" ;%>
    </c:if>
	<c:if test="${edit == null}"> 
  		<% formAction="saveErrorCodes" ;%>
    </c:if> 
			<form:form id="errorCodeForm" name="errorCodeForm" method="GET" onsubmit="return ValidatesErr();" commandName="errorCodeForm" action="<%=formAction%>" style="width:700px">

	<input id="errStatus"  	  type="hidden" value="${errorCodesRO.errorStatus}"/>
	<input id="errStartDt" 	  type="hidden" value="${errorCodesRO.effectiveStartDate}"/>
	<input id="errEndDt"      type="hidden" value="${errorCodesRO.effectiveEndDate}"/>
	<input id="errDisStartDt" type="hidden" value="${errorCodesRO.disableStartDate}"/>
	<input id="errDisEndDt"   type="hidden" value="${errorCodesRO.disableEndDate}"/>
	
				<div style="padding: 10px 0px 0px 0px;">
					<span class="bread1"><spring:message code="admin.associate.bread1" /> </span>
					<c:if test="${edit == null}">
					<span class="bread2"><spring:message code="admin.errorCodes.leftNav.heading1" /></span>
					</c:if>
					<c:if test="${edit != null}">
					<span class="bread2"><spring:message code="admin.errorCodes.update.bread2" /></span>
					</c:if>
				</div>
				<table width="98%" style="padding: 10px 10px 10px 50px">
					<tr style="height: 20px; border-bottom-color: gray;">
						<c:choose>
							<c:when test="${edit != null}">
								<td width="40%"><span style="font-size: 20px">${errorCodesRO.errorCode} - ${errorCodesRO.errorName} 
										</span></td>
								<td width="60%" style="text-align: right;">

									<button title="Cancel" type="reset" onclick="hideErrorDiv();"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Cancel.png" alt="Cancel">
									</button> 
									<button title="Save" type="submit"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png" alt="Save">
									</button></td>
									<tr>
									</tr>
							</c:when>
							<c:otherwise>
								<td width="40%"><span style="font-size: 20px"><spring:message
											code="admin.errorCodes.leftNav.heading1"></spring:message> </span></td>
								<td width="60%" style="text-align: right;">
									<button type="reset" title="Cancel" onclick="hideErrorDiv();"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Cancel.png">
									</button>
									<button type="submit" title="Save"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png">
									</button></td>
							</c:otherwise>
						</c:choose>
					</tr>
				</table>
				
<div >
				<br>
				<div id="error"></div><div>
					<c:if test="${(successCode != '000') && (successCode != '201') && (successCode != null)}"> 
						<div id="divDuplicate" style="width: 240px;color: red;font-weight: bold;">${successMessage}</div>
					</c:if>
					<c:if test="${success == 'SUCCESS'}"> 
						<div id="success" class="success" style="width: 240px;">Error added successfully!</div>
					</c:if>
					<c:if test="${upSucess=='SUCCESS'}"> 
						<div id="success" class="success" style="width: 280px;">Error details updated successfully!</div>
					</c:if>	
					<c:if test="${delSucess=='SUCCESS'}"> 
						<div id="success" style="padding-left: 5px; height: 20px; width: 220px; background: #99FF99; display: none;">Error deleted successfully!</div>
					</c:if>	
					<%-- <c:if test="${(fn:contains(success, 'System')) || (fn:contains(upSucess, 'System')) || (fn:contains(delSucess, 'System'))}">
						<div id="success" class="errorInfo" style="width: 300px;">System Error, please try again after some time !</div>
					</c:if> --%>
					<c:if test="${successCode == '201'}">
						<div id="success" class="errorInfo" style="width: 300px;">System Error, please try again after some time !</div>
					</c:if>	
				</div>
				<br>
				<div class="line-separator"></div>
				<br><div style="color:red">* indicates required</div><br>
				<div class="panelContainer">
					<div class="containerA" style="padding-left:7px;padding-top: 7px;">
						<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 16px"><spring:message
									code="admin.errorCodes.errorDetails" />
							</span>
						</div>
						<div class="contentA" style="padding-top: 10px">
							<table class="tableForm" style="padding-left: 50px;">
								<tr>
									<td><spring:message code="admin.errorCodes.name" />&nbsp;<span style="color:red">*</span>
									</td>
									<td><div id="errorNamec" class="input-control text">
											<input type="text" id="errorName" name="errorName" autofocus="autofocus" placeholder="Error Name" value="${errorCodesRO.errorName}" />
											&nbsp;<span id="errmsgErrname" style="color:red">
										</div>
									</td>
								</tr>
								<tr>
									<td style="width:33%"><spring:message code="admin.errorCodes.errorDescription" />&nbsp;<span style="color:red">*</span></td>
									<td><div id="errorDescriptionc" class="input-control textarea">
												<textarea id="errorDescription" name="errorDescription" style="width: 200px; height: 106px;font-family: Segoe UI_, Open Sans, Verdana, Arial, Helvetica, sans-serif" >${errorCodesRO.errorDescription}</textarea>
												&nbsp;<span id="errmsgDesc" style="color:red">
											</div>
									</td>
								</tr>
								
								<c:set var="userRole" > <%=request.getHeader("iv-groups")%> </c:set> 
								
								<c:choose>
   								<c:when test="${(edit != null) && (fn:contains(userRole, 'qadb-superadmin_users')) && (!(fn:contains(userRole, 'null'))) }">
        							
        								<c:if test="${errorCodesRO.userGrp == '' }">
        									<tr>
											<td style="padding-top : 5px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div class="input-control checkbox">
												<label> <input id="grpSammd" type="checkbox" name="grpSammd"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input id="grpCd" type="checkbox" name="grpCd"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>											
											</td>
											</tr>
		   								</c:if>	
		   								<c:if test="${errorCodesRO.userGrp == 'ALL' }">
        									<tr>
											<td style="padding-top : 5px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div class="input-control checkbox">
												<label> <input id="grpSammd" type="checkbox" name="grpSammd" checked="checked"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input id="grpCd" type="checkbox" name="grpCd" checked="checked"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>											
											</td>
											</tr>
		   								</c:if>	
		   								<c:if test="${errorCodesRO.userGrp == 'SAMMD' }">
        									<tr>
											<td style="padding-top : 5px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div class="input-control checkbox">
												<label> <input id="grpSammd" type="checkbox" name="grpSammd" checked="checked"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input id="grpCd" type="checkbox" name="grpCd" > <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>											
											</td>
											</tr>
		   								</c:if>
		   								<c:if test="${errorCodesRO.userGrp == 'CD' }">
        									<tr>
											<td style="padding-top : 5px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div class="input-control checkbox">
												<label> <input id="grpSammd" type="checkbox" name="grpSammd" > <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input id="grpCd" type="checkbox" name="grpCd" checked="checked" > <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>											
											</td>
											</tr>
		   								</c:if>			
								    </c:when>
    								<c:otherwise>
         								<c:if test="${(fn:contains(userRole, 'qadb-superadmin_users')) && (!(fn:contains(userRole, 'null')))}">
 										<tr>
											<td style="padding-top : 5px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div id="area" style="width:200px;">
											<div class="input-control checkbox">
												<label> <input id="grpSammd" type="checkbox" name="grpSammd"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input id="grpCd" type="checkbox" name="grpCd"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>	
											</div>										
											</td>
										</tr>
										</c:if>  
    								</c:otherwise>
								</c:choose>
								
								<c:if test="${(fn:contains(userRole, 'qadb-superadmin_users')) && (!(fn:contains(userRole, 'null')))}">
									<input type="hidden" id="sAdmin" name="sAdmin" value="SADMIN">
								</c:if>
								<c:if test="${(fn:contains(userRole, 'qadb-samd-admin_users')) && (!(fn:contains(userRole, 'null')))}">
 								<input type="hidden" name="grpSammd" value="SAMMD">
								</c:if>
								<c:if test="${(fn:contains(userRole, 'qadb-cd-admin_users')) && (!(fn:contains(userRole, 'null')))}">
									<input type="hidden" name="grpCd" value="CD">
								</c:if>
								
								<tr style="display: none;">
									<c:if test="${edit != null}">
									
									<td style="width:33.2%"><spring:message code="admin.opUnit.generalInformation.enabled" /></td>
									<td><div class="input-control checkbox">
											<label> <input id="errorStatus" type="checkbox" name="errorStatus"/> <span class="check"></span>
												 </label>
										</div></td>
									</c:if>
								</tr>
							</table>
						</div>
					</div>
				</div>
				
				<div class="panelContainer">
					<div class="containerA" style="padding-left:7px;padding-top: 7px;">
						<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 16px"><spring:message
									code="admin.errorCodes.effective" />
							</span>
						</div>
						<div class="contentA" style="padding-top: 10px">
							<table class="tableForm" style="padding-left: 50px;">
								
								<tr>
									<td><spring:message code="admin.errorCodes.start" />&nbsp;<span style="color:red">*</span></td>
									
									<td><div id="datepick1c" class="input-control text">
												<input id="datepick1" size="50" class="form-control" name="effectiveStartDate" value="${errorCodesRO.effectiveStartDate}"/>
												&nbsp;<span id="errmsgdatepick1" style="color:red">
											</div>
									</td>
								</tr>
								
								<tr>
									<td><spring:message code="admin.errorCodes.end" />&nbsp;<span style="color:red">*</span></td>
									
									<td><div id="datepick2c" class="input-control text">
												<input id="datepick2" size="50" class="form-control" name="effectiveEndDate" value="${errorCodesRO.effectiveEndDate}"/>
												&nbsp;<span id="errmsgdatepick2" style="color:red">
											</div>
									</td>
								</tr>
								<c:if test="${edit != null}"> <!--SVET012196-N13-->
								<tr>
									<td style="width:33.2%"><spring:message code="admin.perfGroup.update.disable" /></td>
									
									<c:choose>
										<c:when test="${null!=errorCodesRO.disableStartDate}">
											<td><div class="input-control checkbox">
											<label> <input type="checkbox" id="status" checked="checked" onclick="enableDisDate()" name="status" value="${errorCodesRO.status}"/> <span class="check"></span>
												 </label>
											</div></td>
										</c:when>
										<c:otherwise>
											<td><div class="input-control checkbox">
											<label> <input type="checkbox" id="status" onclick="enableDisDate()" name="status" value="${errorCodesRO.status}"/> <span class="check"></span>
												 </label>
											</div></td>
										</c:otherwise>
									</c:choose>
								</tr>
								<tr>
									<td><spring:message code="admin.errorCodes.start" />&nbsp;<span style="color:red">*</span></td>
									
									<td><div id="datepick1c" class="input-control text">
												<input id="datepick3" size="50" class="form-control" name="disableStartDate" value="${errorCodesRO.disableStartDate}"/>
												&nbsp;<span id="errmsgdatepick1" style="color:red">
											</div>
									</td>
								</tr>
								
								<tr>
									<td><spring:message code="admin.errorCodes.end" />&nbsp;<span style="color:red">*</span></td>
									
									<td><div id="datepick2c" class="input-control text">
												<input id="datepick4" size="50" class="form-control" name="disableEndDate" value="${errorCodesRO.disableEndDate}"/>
												&nbsp;<span id="errmsgdatepick2" style="color:red">
											</div>
									</td>
								</tr>
								</c:if>
							</table>
						</div>
					</div>
				</div>
							
	</div>
	
	<div class="line-separator"></div>
				<table width="98%" style="padding: 10px 10px 10px 50px">
					<tr style="height: 20px; border-bottom-color: gray;">

								<td width="30%">
									<div style="float: left">
						<a href="#top" style="float: left;"><spring:message code="qadb.backToTop" /></a>
					</div>
								</td>
								<td width="70%" style="text-align: right;">
									<button title="Reset" type="reset" onclick="hideErrorDiv();"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Cancel.png">
									</button>
									<button title="Save" type="submit"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png">
									</button></td>
					</tr>
				</table>
	
	
			</form:form>


		</div>
	<script type="text/javascript">
		
	$("#datepick1").datepicker({
		showOn:"button",
	    onSelect: function(selected) {
      	$("#datepick2").datepicker("option","minDate", selected)
    	},
	});
	$("#datepick2").datepicker({
		showOn:"button",
   		onSelect: function(selected) {
      	$("#datepick1").datepicker("option","maxDate", selected)
   	 	},
	});
	$("#datepick3").datepicker({
		showOn:"button",
	    onSelect: function(selected) {
      	$("#datepick4").datepicker("option","minDate", selected)
    	},
	});
	$("#datepick4").datepicker({
		showOn:"button",
   		onSelect: function(selected) {
      	$("#datepick3").datepicker("option","maxDate", selected)
   	 	},
	});
	
	
	$( document ).ready(function() {	
		if(document.getElementById("errStatus").value == "Enabled"){
			$("#errorStatus").prop("checked", true);
		}else{
			$("#errorStatus").removeAttr("checked")
		}
	});
	
  /*  
   $("#errorNamec").keypress(function (e) {
		if ((e.which == 0) || e.which == 8) {}
			else{
				var check = maxLimitForName(e,"errmsgErrname","errorName");
				if(check==false){
				return false;
			}
		}
	});
  	$("#errorNamec").keydown(function (e) {
		if(e.ctrlKey && e.keyCode==86){
		if ((e.which == 0) || e.which == 8) {}
		else{
			   	var check = maxLimitForName(e,"errmsgErrname","errorName");
				if(check==false){
				return false;
				}
			}
		}
	});
	 */
	
	
	$("#errorNamec").keypress(function (e) {
	 	if ((e.which == 0) || e.which == 8) {}
			else if($("#errorName").val().length>199){
	   		$("#errmsgErrname").html("Maximum limit of 200 char.").show().fadeOut("slow");
	   		return false;
   		}
 		else{}
	});
  	$("#errorNamec").keydown(function (e) {
		if(e.ctrlKey && e.keyCode==86){
			if ((e.which == 0) || e.which == 8) {}
			else if($("#errorName").val().length>199){
   			$("#errmsgErrname").html("Maximum limit of 200 char.").show().fadeOut("slow");
   			return false;
   			}
 		else{}
		}
	});
	$("#errorNamec").keyup(function (e) {
		if($("#errorName").val().length>199){
		var name = $("#errorName").val().substring(0,200);
		document.getElementById("errorName").value=name;
		$("#errmsgErrname").html("Maximum limit of 200 char.").show().fadeOut("slow");
		}
	}); 
	
	 $("#errorDescriptionc").keypress(function (e) {
	 	if ((e.which == 0) || e.which == 8) {}
			else if($("#errorDescription").val().length>199){
	   		$("#errmsgDesc").html("Maximum limit of 200 char.").show().fadeOut("slow");
	   		return false;
   		}
 		else{}
	});
  	$("#errorDescriptionc").keydown(function (e) {
		if(e.ctrlKey && e.keyCode==86){
			if ((e.which == 0) || e.which == 8) {}
			else if($("#errorDescription").val().length>199){
   			$("#errmsgDesc").html("Maximum limit of 200 char.").show().fadeOut("slow");
   			return false;
   			}
 		else{}
		}
	});
   	$("#errorDescriptionc").keyup(function (e) {
		if($("#errorDescription").val().length>199){
			var name = $("#errorDescription").val().substring(0,200);
			document.getElementById("errorDescription").value=name;
			$("#errmsgDesc").html("Maximum limit of 200 char.").show().fadeOut("slow");
		}
	});
	
	jQuery(function($){
  
  	 $("#datepick1").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("errStartDt").value);
  	 $("#datepick2").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("errEndDt").value);
  	 $("#datepick3").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("errDisStartDt").value);
  	 $("#datepick4").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("errDisEndDt").value);
   
});

$(document).ready(function() {
		
		if(document.getElementById("errEndDt").value==""){
			$("#datepick2").datepicker( "setDate" , "12/31/9999" );
		}
		
		if(document.getElementById("errDisEndDt").value==""){
			document.getElementById("status").checked = false;
			document.getElementById("datepick3").disabled = true;
			$("#datepick3").datepicker( "option", "showOn", "focus" );
			document.getElementById("datepick4").disabled = true;
			$("#datepick4").datepicker( "option", "showOn", "focus" );
			$("#datepick4").datepicker( "setDate" , "12/31/9999" );
		}
		enableDisDate();
	})	
	
   $(function() {
		$('.headerA').click(function() {
			$(this).closest('.containerA').toggleClass('collapsed');
		});

	});
	
	function enableDisDate(){
	if($("#status").is(":checked")){
		document.getElementById("datepick3").disabled = false;
		$("#datepick3").datepicker( "option", "showOn", "button" );
		document.getElementById("datepick4").disabled = false;
		$("#datepick4").datepicker( "option", "showOn", "button" );
	}else{
		document.getElementById("datepick3").disabled = true;
		$("#datepick3").datepicker( "option", "showOn", "focus" );
		document.getElementById("datepick4").disabled = true;
		$("#datepick4").datepicker( "option", "showOn", "focus" );
	}
	}
	</script>
		
</div>

	<jsp:include page="footer.jsp"></jsp:include>

</body>

</html>
