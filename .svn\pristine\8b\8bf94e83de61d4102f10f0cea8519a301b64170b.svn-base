//Function to validate the DCN number entered by an auditor
var flag=false;
var flagop=false;
var checkHire=false;
var checkwuStart=false;
var checkwuEnd=false;
var checkdis=false;
var checkoustart=false;
var checkouend=false;

function updateLink(){
	$('#stat').hide();
	$('#err').show();
	var correct=validateForm();
	var error = $("#error");
	if (correct) {
		var error = $("#error");
	    error.css("display", "none");
	    document.getElementById("auditForm").submit();
	    }
	else {
		console.log("error div");
	    error.css("display", "block");
	    $("#success").css("display", "none");
	}
	/* scroll to top */
	var formElement = document.getElementById("error");
	  if(formElement){
	      window.scrollTo(0, formElement.offsetTop);
	  }

}
function validateDCN(flag,userId,processedDate,clstStatus,multicheckFlag)
{
	// alert("inside the validate dcn function");
	var actionType="validateDCN";
	var newFlag=flag;
	var multicheckFlag=multicheckFlag;
	var userId=userId;
	var processedDate=processedDate;
	var clstStatus=clstStatus;
	var dcn = (document.getElementById('dcnNumber').value).toUpperCase();
	var monErr = $('input[name=monetaryError]:checked').val();
	var amountp =document.getElementById('amountp').value;
	var theoPaid =document.getElementById('theoreticalPaid').value;
	var url="validateDCN.html";
	$("#dcnDetails").maskload("Loading..");
	// alert(dcn);
	$.ajax({
		type:"POST",
		url: url,
		dataType: "html", 
		data: {'actionType':actionType,'dcn':dcn,'newFlag':newFlag,'userId':userId,'processedDate':processedDate,'clstStatus':clstStatus},
		success: function(response){
			$("#content-container").html(response);
			/* console.log(document.getElementById('dupFlag').value) */
			if(null!=document.getElementById('dupFlag')){
				if(document.getElementById('dupFlag').value== "Y"){
					// Show duplicate audit div();
					console.log(document.getElementById('dupFlag').value)
					divDcn_show();
				}
			}
			
			document.getElementById('dcnNumber').value=dcn;
			document.getElementById('theoreticalPaid').value = theoPaid;
			// document.getElementById('amountp').value = amountp;
			console.log(document.getElementById('numberOfUsers').value);
			console.log("numberOfUsers"+document.getElementById('numberOfUsers').value);
			if(document.getElementById('numberOfUsers').value>1 && multicheckFlag!="yes"){
				divclaims_show();
			}
			else{
				console.log("one record only");
			}
			if(null!=document.getElementById("paids")){
				if($("#paids").val()>39999.99 && $("#paids").val() <100000.00){		
						console.log("paid > 39999.99 and < 100000.00"+$("#error").html());
						$("#errorHigh").html("<p style='color:white;padding-left:0px'>NOTE:  Claim is between $40000-99999.99, forensic review required. Please Verify!<p>");
						$("#paids").focus();	
						$("#errorHigh").css({"display":"block"});
						$("#highDollarLink").css({"display":"block"});
					
			    }
				else if($("#paids").val()>=100000.00){		
					console.log("paid >= 100000.00"+$("#error").html());
					$("#errorHigh").html("<p style='color:white;padding-left:0px'>NOTE:  Claim is 100k or greater, forensic review and VP signoff required.  Please Verify!<p>");
					$("#paids").focus();	
					$("#errorHigh").css({"display":"block"});
					$("#highDollarLink").css({"display":"block"});
				
				}else{
			    	$("#highDollarLink").css({"display":"none"});
			    	$("#errorHigh").css({"display":"none"});
			    }
			}
		
			//validateDCNPrcsfld();
			//validatePrcdFldWith0();
		},
		error: function(){
			alert("There is an issue, please contact your administrator");
		}

	}) ;

}

function validatePrcdFldWith0(){
	var regex=/(.*)([0]+$)(.*)/;
	var dcn = $("#dcnNumber").val();
	if((dcn).match(regex) && document.getElementById('processtypeId').value != "Regular Claims"){
	    document.getElementById('processtypeId').setAttribute('disabled', 'disabled');
	}
}

//Changes Done by Kartik For SVET013821
function validateDCNPrcsfld(){
//alert("insidevalidateDCN1");
var regex=/(.*)([0]+$)(.*)/;
var dcn = $("#dcnNumber").val();
var dcnNew = dcn.substr(dcn.length - 1);
//window.alert(dcnNew);
if(dcnNew == '0'){
//window.alert(dcn);
var optionToSelect = '1';
    var processtypsel = document.getElementById('processtypeId');
    		window.alert("Inside "+processtypsel.value());
        	processtypsel.selectedIndex = 1;
        	processtypsel.options[0].disabled=true;
        	processtypsel.options[2].disabled=true;
        	processtypsel.options[3].disabled=true;
          
    }

}

// Function on click of a button to calculate the Penalty interest
function check_empty(jurisdiction) {
	
	var dateEv2=datevalidation("datepick2",$("#datepick2").val());
	var dateEv3=datevalidation("datepick3",$("#datepick3").val());
	
	if (document.getElementById('days').value == "" || document.getElementById('amount').value == "") {
		alert("Fill All Fields !");
	} 
	else if(dateEv2==false && dateEv3==false){
		$("#datepick2").css("border-color","red");
		$("#datepick3").css("border-color","red");
		$("#errmsg").html("Invalid date").show();
	}else if(dateEv2==false){
		$("#datepick2").css("border-color","red");
		$("#datepick3").css("border-color","#919191");
		$("#errmsg").html("Invalid date").show();
	}else if(dateEv3==false){
		$("#datepick3").css("border-color","red");
		$("#datepick2").css("border-color","#919191");
		$("#errmsg").html("Invalid date").show();
	}else if((new Date($("#datepick2").val()))>(new Date($("#datepick3").val()))){
		$("#datepick2").css("border-color","red");
		$("#datepick3").css("border-color","red");
		$("#errmsg").html("Start date cannot be greater than end date").show();
	}
	else {
		$("#datepick2").css("border-color","#919191");
		$("#datepick3").css("border-color","#919191");
		$("#errmsg").hide();
		// document.getElementById('form').submit();
		var e = document.getElementById("processTypedd");
		var processTypevalue = e.options[e.selectedIndex].text;
		var url="piDiv";
		var days=document.getElementById('days').value;
		var monErr = $('input[name=monetaryError]:checked').val();
		var amountp =document.getElementById('amountp').value;
		var theoPaid =document.getElementById('theoreticalPaid').value;
		var amount=document.getElementById('amount').value;
		var isChk = $('#chkbox').is(':checked');
		var drpDwn = document.getElementById('pilist').value;
		console.log("inpt--->" +monErr + "amountp" +amountp+ ""+theoPaid+ " chk "+isChk);
		$.ajax({
			type:"POST",
			url: url,
			dataType: "html", 
			data: {'days':days,'amount':amount,'jurisdiction':jurisdiction,'processTypevalue':processTypevalue},
			success: function(response){
				$("#PI").html(response);
				$("#calPiButton").removeAttr('disabled');
				$('#popupPIouter').css('display','none');
				$('#popupPI').css('display','none');
			},
			error: function(){
				alert("There is an issue, please contact your administrator");
			}
	
		}) ;
	}
}

function clickGeneral(){
	console.log("clickGen");
	$('#stat').hide();
	$('#aud').show();
	$('#statsLi').removeClass('active');
	$('#errorleftLi').removeClass('active');
	$('#auditLeftLi').addClass('active');
	var serializedForm=$("#retainForm").serialize();
	var URL = "auditRedirect?"+serializedForm;
	$.ajax({
		type:"POST",
		url: URL,
		dataType: "html", 
		data:{},
		success: function(response){
			$("#content-container").html(response);			
    	}
		
	}); 
	// $("#auditLeft").attr('href',URL);
}

function submitHighDollar(){
	var correct="";
	var correct1=true;
	var dateEv1=datevalidation("datePicksurgeryDOS",$("#datePicksurgeryDOS").val());
	var dateEv2=datevalidation("datePickaudSignDate",$("#datePickaudSignDate").val());
	var dateEv3=datevalidation("datePickvpSignDate",$("#datePickvpSignDate").val());
	var dateEv4=datevalidation("datePickforwrdToDate",$("#datePickforwrdToDate").val());
	var dateEv5=datevalidation("datePickrcvedFrmDate",$("#datePickrcvedFrmDate").val());
	var dateEv6=datevalidation("datePickreleasedByDate",$("#datePickreleasedByDate").val());
	if($("#QANameId").val()==""){
		$("#QANamec").removeClass().addClass("input-control text error-state");
		$("#QANameId").focus();
		correct1=false;
	}else{
		$("#QANamec").attr("class","input-control text");
	}
	
	if($("#patientNameId").val()==""){
		if(correct1!=false){
			$("#patientNameId").focus();
		}
		$("#patientNamec").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else{
		$("#patientNamec").attr("class","input-control text");
	}
	
	if($("#serviceDatesId").val()==""){
		if(correct1!=false){
			$("#serviceDatesId").focus();
		}
		$("#serviceDatesc").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else{
		$("#serviceDatesc").attr("class","input-control text");
	}
	
	if($("#typeOfServiceId").val()==""){
		if(correct1!=false){
			$("#typeOfServiceId").focus();
		}
		$("#typeOfServicec").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else{
		$("#typeOfServicec").attr("class","input-control text");
	}
	
	if($("#diagnosisId").val()==""){
		if(correct1!=false){
			$("#diagnosisId").focus();
		}
		$("#diagnosisc").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else{
		$("#diagnosisc").attr("class","input-control text");
	}
	
	/*if($("#datePicksurgeryDOS").val()==""){
		if(correct1!=false){
			$("#datePicksurgeryDOS").focus();
		}
		$("#surgeryDOSc").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else if(dateEv1==false){
		if(correct1!=false){
			$("#datePicksurgeryDOS").focus();
		}
		$("#surgeryDOSc").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else{
		$("#errmsgdatePicksurgeryDOS").hide();
		$("#surgeryDOSc").attr("class","input-control text");
	}*/
	
	
	/*if($("#surgeryId").val()==""){
		if(correct1!=false){
			$("#surgeryId").focus();
		}
		$("#surgeryc").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else{
		$("#surgeryc").attr("class","input-control text");
	}*/
	
	/*if($("#fileReferencedId").val()==""){
		if(correct1!=false){
			$("#fileReferencedId").focus();
		}
		$("#fileReferencedc").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else{
		$("#fileReferencedc").attr("class","input-control text");
	}*/
	
	if($("#interestPaidId").val()==""){
		if(correct1!=false){
			$("#interestPaidId").focus();
		}
		$("#interestPaidc").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else{
		$("#interestPaidc").attr("class","input-control text");
	}
	
	if($("#providerNameId").val()==""){
		if(correct1!=false){
			$("#providerNameId").focus();
		}
		$("#providerNamec").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else{
		$("#providerNamec").attr("class","input-control text");
	}
	
	if($("#providerNumberId").val()==""){
		if(correct1!=false){
			$("#providerNumberId").focus();
		}
		$("#providerNumberc").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else{
		$("#providerNumberc").attr("class","input-control text");
	}
	
	if($("#payeeId").val()==""){
		if(correct1!=false){
			$("#payeeId").focus();
		}
		$("#payeec").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else{
		$("#payeec").attr("class","input-control text");
	}
	
	/*if($("#notesId").val()==""){
		if(correct1!=false){
			$("#notesId").focus();
		}
		$("#notesc").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else{
		$("#notesc").attr("class","input-control text");
	}*/
	
	if($("#datePickaudSignDate").val()==""){
		if(correct1!=false){
			$("#datePickaudSignDate").focus();
		}
		$("#audSignDatec").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else if(dateEv2==false){
		if(correct1!=false){
			$("#datePickaudSignDate").focus();
		}
		$("#audSignDatec").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else{
		$("#audSignDatec").attr("class","input-control text");
		$("#errmsgdatePickaudSignDate").hide();
	}
	
	/*if($("#datePickvpSignDate").val()==""){
		if(correct1!=false){
			$("#datePickvpSignDate").focus();
		}
		$("#vpSignDatec").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else if(dateEv3==false){
		if(correct1!=false){
			$("#datePickvpSignDate").focus();
		}
		$("#vpSignDatec").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else{
		$("#vpSignDatec").attr("class","input-control text");
		$("#errmsgdatePickvpSignDate").hide();
	}*/
	
	if($("#datePickforwrdToDate").val()==""){
		if(correct1!=false){
			$("#datePickforwrdToDate").focus();
		}
		$("#forwrdToDatec").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else if(dateEv4==false){
		if(correct1!=false){
			$("#datePickforwrdToDate").focus();
		}
		$("#forwrdToDatec").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else{
		$("#forwrdToDatec").attr("class","input-control text");
		$("#errmsgdatePickforwrdToDate").hide();
	}
	
	
	/*if($("#datePickrcvedFrmDate").val()==""){
		if(correct1!=false){
			$("#datePickrcvedFrmDate").focus();
		}
		$("#rcvedFrmDatec").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else if(dateEv5==false){
		if(correct1!=false){
			$("#datePickrcvedFrmDate").focus();
		}
		$("#rcvedFrmDatec").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else{
		$("#rcvedFrmDatec").attr("class","input-control text");
		$("#errmsgdatePickrcvedFrmDate").hide();
	}*/
	
	/*if($("#datePickreleasedByDate").val()==""){
		if(correct1!=false){
			$("#datePickreleasedByDate").focus();
		}
		$("#releasedByDatec").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else if(dateEv6==false){
		if(correct1!=false){
			$("#datePickreleasedByDate").focus();
		}
		$("#releasedByDatec").removeClass().addClass("input-control text error-state");
		correct1=false;
	}else{
		$("#releasedByDatec").attr("class","input-control text");
		$("#errmsgdatePickreleasedByDate").hide();
	}*/
	
	console.log("here");
	if(correct1==false){
		console.log("false");
		$("#errmsgHigh").html("Please fill the highlighted mandatory details").show();
		var formElement = document.getElementById("errmsgHigh");
		window.scrollTo(0, formElement.offsetTop);
	}else{
		var actionType='submitHighDollar';
		var url='submitHighDollar';
		var serializedForm = $("#highDollarForm").serialize();
		$.ajax({
			type:"POST",
			url: url,
			dataType: "html", 
			data: serializedForm,
			success: function(response){
				$("#highContainer").html(response);			
			},
			error: function(){
				alert("Failure");
			}
		}) ;
		// document.getElementById("highDollarForm").submit();
		hideHighDollar();
		$("#errmsgHigh").hide();
	}
}

function showHighDollar() {
	document.getElementById('highDollarPopup').style.display = "block";
	document.getElementById('highDollarOverlay').style.display = "block";
}

function hideHighDollar(){
	document.getElementById('highDollarPopup').style.display = "none";
	document.getElementById('highDollarOverlay').style.display = "none";
}

// Function To Display PI Popup
function div_show() {
	document.getElementById('popupPIouter').style.display = "block";
	document.getElementById('popupPI').style.display = "block";
}
// Function to Hide PI Popup
function div_hide() {
	document.getElementById('popupPIouter').style.display = "none";
	document.getElementById('popupPI').style.display = "none";
}

function divclaims_show() {
	document.getElementById('popupClaimRecords').style.display = "block";
	document.getElementById('popupClaims').style.display = "block";
}

function divclaims_hide(){
	document.getElementById('popupClaimRecords').style.display = "none";
	document.getElementById('popupClaims').style.display = "none";
}

function selectClaim(selectedId){
	divclaims_hide();	
}

  



function validateFormNav(){
	var correct=validateForm();
	var error = $("#error");
	if (correct) {
		var error = $("#error");
	    error.css("display", "none");
	    document.getElementById("auditForm").submit();
	    }
	else {
		console.log("error div");
	    error.css("display", "block");
	    $("#success").css("display", "none");
	}
	/* scroll to top */
	var formElement = document.getElementById("error");
	  if(formElement){
	      window.scrollTo(0, formElement.offsetTop);
	  }
	
}

function validateForm() {
	var regex=/^\d+([.]?\d{0,2})?$/g;
	var error = $("#error");
	var correct = true;
	
	$('#statsLi').removeClass('active');
	$('#errorleftLi').addClass('active');
	
	console.log("dcn value"+$("#dcnNumber").val());
	if(null!=document.getElementById("dcnNumber")){
		if ($("#dcnNumber").val() == "" || $("#dcnNumber").val()==null) {
			console.log("inside if");
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a claim number to see the claim data.<p>";
			console.log("message set");
		    $("#dcnNumberc").removeClass().addClass("input-control text error-state");
		    console.log("class removed");
		    $("#dcnNumber").focus();
		    console.log("focused");
		    correct = false;
		}
		else{
			 $("#dcnNumberc").attr("class","input-control text");
		}
	}
	
	if(null!=document.getElementById("associateInfoName")){
		if($("#associateInfoName").val()==null || $("#associateInfoName").val()==""){
			if(correct==false){}
			else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>This Associate is not present in Database.Please contact Admin<p>";
				correct=false;
			}
	    }else{
	    	 $("#dcnNumberc").attr("class","input-control text");
	    }
	}
	
	//Change added For DMND0002873
	if(null!=document.getElementById("fyin")){
		if(($("#paids").val()>= 40000.00 && $("#fyin").val() == null) ||
				($("#paids").val()>= 40000.00 && $("#fyin").val() == "")){
			if(correct == false){}
			else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>" +
						"FYI/HIDO Review Notes cannot be empty when total amount paid is greater than or equal to $40000.00<p>";
				correct=false;
			}
			$("#fyin").css("border-color","red");
		}
	}
	
	
	if(null!=document.getElementById("associateInfoNameEdit")){
		if($("#associateInfoNameEdit").val()==null || $("#associateInfoNameEdit").val()==""){
			if(correct==false){}
			else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>This Associate is not present in Database.Please contact Admin<p>";
				correct=false;
			}
	    }else{
	    	 $("#dcnNumberc").attr("class","input-control text");
	    }
	}
 	
	/*if(null!=document.getElementById("lob")){
		if($("#lob").val()==null || $("#lob").val()==""){
			if(correct==false){}
			else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please fill the claim details.<p>";
				$("#dcnNumber").focus();
				correct=false;
			}
			$("#dcnNumberc").removeClass().addClass("input-control text error-state");
	    }else{
	    	 $("#dcnNumberc").attr("class","input-control text");
	    }
	}*/
	
	
	if($("input[name=monetaryError]:checked", "#auditForm").val()!="N"){
		if(null!=document.getElementById("amountp")){
			if($("#amountp").val()==null || $("#amountp").val()==""){
				if(correct==false){}
				else{
					document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter an amount.<p>";
					$("#amountp").focus();
					correct=false;
				}
				$("#amountpc").removeClass().addClass("input-control text error-state");
		    }else if(!($("#amountp").val()).match(regex)){
		    	if(correct==false){}
				else{
					document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Amount must be a numeric.<p>";
					$("#amountp").focus();
					correct=false;
				}
				$("#amountpc").removeClass().addClass("input-control text error-state");
		    }else{
		    	 $("#amountpc").attr("class","input-control text");
		    }
		}
		
		if(null!=document.getElementById("theoreticalPaid")){
			if($("#theoreticalPaid").val()==null || $("#theoreticalPaid").val()==""){
				if(correct==false){}
				else{
					document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Theoretical Paid.<p>";
					$("#theoreticalPaid").focus();
					correct=false;
				}
				$("#theoreticalPaidc").removeClass().addClass("input-control text error-state");
		    }else if(!($("#theoreticalPaid").val()).match(regex)){
		    	if(correct==false){}
				else{
					document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Theoretical Paid must be a numeric.<p>";
					$("#theoreticalPaid").focus();
					correct=false;
				}
				$("#theoreticalPaidc").removeClass().addClass("input-control text error-state");
		    }else{
		    	 $("#theoreticalPaidc").attr("class","input-control text");
		    }
		}
	}
	
	if(null!=document.getElementById("pilist")){
		if($("input[name='piChk']").prop('checked') && $("#pilist :selected").text() == "Select"){
			if(correct==false){}
			else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please select the Penalty Interest Type.<p>";
				correct=false;
			}
			$("#pilist").css("border-color","red");
	    }else{
	    	$("#pilist").css("border-color","#919191");
	    }
		
		if($("input[name='piChk']").prop('checked') && $("#pilist :selected").text() != "Select" && (($("#pi").val())==null ||  ($("#pi").val())=="")){
			if(correct==false){}
			else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please calculate penalty interest rate.<p>";
				correct=false;
			}
			$("#pi").css("border-color","red");
	    }else{
	    	$("#pi").css("border-color","#919191");
	    }
		
	}
	
	
	//Claim Type Pop up message
	if(null!=document.getElementById("claimType")){
	    if(document.getElementById("claimType").value=='Select'){
	        if(correct==false){}
	        else{
	        	document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Claim Type.<p>";
	        	correct=false;
	        }
	        $("#claimType").css("border-color","red");
	    }else{
			$("#claimType").css("border-color","#919191");
		}
	}  
		
	/*Performance Group*/
	if($("select[name='priPGA'] :selected").text() == "Select Primary PGA"){
		if(correct==false){}
		else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please select Performance Group.<p>";
			correct=false
		}
		$("#priPGA").css("border-color","red");
		$("#secPGALists").css("border-color","red");
    }else{
    	$("#priPGA").css("border-color","#919191");
    	$("#secPGALists").css("border-color","#919191");
    }
	
	if($("select[name='secPGA'] :selected").text() == "Select Secondary PGA"){
		if(correct==false){}
		else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please select Secondary Performance Group.<p>";
			correct=false
		}
		$("#secPGALists").css("border-color","red");
    }else{
    	$("#secPGALists").css("border-color","#919191");
    }
	
	
	
	if($("select[name='processtype'] :selected").text() == "Select"){
		if(correct==false){}
		else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please select Process Type.<p>";
			correct=false
		}
		$("#processtype").css("border-color","red");
    }else{
    	$("#processtype").css("border-color","#919191");
    }
	
	/*if(($("#paids").val()>99999.99) && (($("#QAName").val()=="") && ($("#QANameId").val()==""))){	
		console.log("HD val "+($("#QAName").val()));
		console.log("HD val2 "+($("#QANameId").val()));
		if(correct==false){}
		else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please fill the high dollar form for this claim.";
			correct=false
		}
	}*/
	/*
	 * if(null!=document.getElementById("chkbox")){ if
	 * (document.getElementById("chkbox").checked) {
	 * if(null!=document.getElementById("input")){ var
	 * pi=document.getElementById("input").value; if (pi==null || pi==""){
	 * if(correct==false){} else{ document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please
	 * select the Penalty Interest Type.<p>"; correct=false; }
	 * $("#pilist").css("border-color","red"); }else{
	 * $("#pilist").css("border-color","#919191"); } } } }
	 */
	if(null!=document.getElementById("processtypeId")){
	    if(document.getElementById("processtypeId").value=='Select'){
	        if(correct==false){}
	        else{
	        	document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Process Type.<p>";
	        	correct=false;
	        }
	        $("#processtypeId").css("border-color","red");
	    }else{
			$("#processtypeId").css("border-color","#919191");
		}
	}    
	 if (correct) {
		    error.css("display", "none");
		    }
		else {
			console.log("error div");
		    error.css("display", "block");
		    $("#success").css("display", "none");
		}
		/* scroll to top */
		var formElement = document.getElementById("error");
		  if(formElement){
		      window.scrollTo(0, formElement.offsetTop);
		  }
	return correct;
}



// Function To Display Delete Popup



// dcnCheckPopUp
function divDcn_show() {
	document.getElementById('popupDcnCheckD').style.display = "block";
	document.getElementById('popupDcnD').style.display = "block";
}

function divDcn_hide(){
	document.getElementById('popupDcnCheckD').style.display = "none";
	document.getElementById('popupDcnD').style.display = "none";
}

// auditDelete
function divDelA_show() {
	document.getElementById('popupClaimRecordsD').style.display = "block";
	document.getElementById('popupClaimsD').style.display = "block";
}

function divDelA_hide(){
	document.getElementById('popupClaimRecordsD').style.display = "none";
	document.getElementById('popupClaimsD').style.display = "none";
}

// auditCount
function divCount_show() {
	document.getElementById('popupAuditCountD').style.display = "block";
	document.getElementById('popupAuditCount').style.display = "block";
}

function divCount_hide(){
	document.getElementById('popupAuditCountD').style.display = "none";
	document.getElementById('popupAuditCount').style.display = "none";
}


// associate
function divReport_show() {
	document.getElementById('popupAssociatesRecordsD').style.display = "block";
	document.getElementById('popupAssociatesD').style.display = "block";
}

function divReport_hide(){
	document.getElementById('popupAssociatesRecordsD').style.display = "none";
	document.getElementById('popupAssociatesD').style.display = "none";
}

//Performance Group
function divPerfGrpReport_show() {
	document.getElementById('popupPerfGrpRecordsD').style.display = "block";
	document.getElementById('popupPerfGrpD').style.display = "block";
}

function divPerfGrpReport_hide(){
	document.getElementById('popupPerfGrpRecordsD').style.display = "none";
	document.getElementById('popupPerfGrpD').style.display = "none";
}

// operational unit
function divdel_show() {
	document.getElementById('popupOperationalUnitD').style.display = "block";
	document.getElementById('popupD').style.display = "block";
}

function claimTypeChanged(){
	var dcn = $("eDCN").val();
	var dcnNew = dcn.substring(4,5);
	window.alert(dcnNew);
	if(dcnNew=='2'){
		var optionToSelect = '2';
		var optionToSelect = '3';
	    var claimtype = document.getElementById('claimtypeId');
	    		window.alert(claimtype.value());
	    		claimtype.selectedIndex = 2;
	    		claimtype.selectedIndex = 3;
	    		claimtype.options[1].disabled=true;
	    		claimtype.options[4].disabled=true;
	    		claimtype.options[5].disabled=true;
	          
	    }
	}
function processTypeChanged() {
	var processTypeVal = $("select[name='processtype'] :selected").text();			
	//if(processTypeVal == "Claim Adjustment" || processTypeVal == "Service Adjustment") {
	if(processTypeVal == "Adjustment" || processTypeVal == "Split Claim") {
		$("input[name='adjustment']").removeAttr("disabled");
	}
	else {
		$("input[name='adjustment']").removeAttr("disabled");
		
	}

	
	/*var dcn = $("#dcnNumber").val();
	var dcnNew = dcn.substr(dcn.length - 1);
	//window.alert(dcnNew);
	if(dcnNew == '0'){
	//window.alert(dcn);
	var optionToSelect = '1';
	    var processtypsel = document.getElementById('processtypeId');
	    		window.alert(processtypse1.value());
	        	processtypsel.selectedIndex = 1;
	        	processtypsel.options[0].disabled=true;
	        	processtypsel.options[2].disabled=true;
	        	processtypsel.options[3].disabled=true;
	          
	    }*/


	


	/*var dcn = $("#dcnNumber").val();
	var dcnNew = dcn.substr(dcn.length - 1);
	//window.alert(dcnNew);
	if(dcnNew == '0'){
	//window.alert(dcn);
	var optionToSelect = '1';
	    var processtypsel = document.getElementById('processtypeId');
	    		window.alert(processtypse1.value());
	        	processtypsel.selectedIndex = 1;
	        	processtypsel.options[0].disabled=true;
	        	processtypsel.options[2].disabled=true;
	        	processtypsel.options[3].disabled=true;
	          
	    }*/


}

//process List Change
/*function processListChange() {
	var val = 0;
	for(item of "${processTypes}"){
		
		if(item == "Regular Claim" && val == 0){
			${processTypes}.shift();		
		}
			
	}
}*/

function divdel_hide(){
	document.getElementById('popupOperationalUnitD').style.display = "none";
	document.getElementById('popupD').style.display = "none";
}

function Validates() {
	$("#error p").remove();
	var error = $("#error");
	var correct = true;
	
	if ($("#first").val() == "") {
		document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter first name for this associate.<p>"
	    $("#firstc").removeClass().addClass("input-control text error-state");
	    $("#first").focus();
	    correct = false;
	}else if(/^\d+$/.test($("#first").val())){
		document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>First name cannot have numbers only.<p>"
	    $("#firstc").removeClass().addClass("input-control text error-state");
	    $("#first").focus();
	    correct = false;
	}
	else{
		 $("#firstc").attr("class","input-control text");
	}
	
	
	if (/^\d+$/.test($("#middle").val())) {
		if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Middle name cannot have numbers only.</p>";
			$("#middle").focus();
		}
	    $("#middlec").removeClass().addClass("input-control text error-state");
	    correct = false;
	}
	else{
		 $("#middlec").attr("class","input-control text");
	}
	
	
	if ($("#last").val() == "") {
		if(correct==false){
		}else{
			document.getElementById("error").innerHTML=("<p style='color:white;padding-left:0px'>Please enter last name for this associate.<p>");
			$("#last").focus();
		}
		   $("#lastc").removeClass().addClass("input-control text error-state");
		   correct = false;
		}else if(/^\d+$/.test($("#last").val())){
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Last name cannot have numbers only.</p>";
				$("#last").focus();
			}
		    $("#lastc").removeClass().addClass("input-control text error-state");
		    correct = false;
		}
		else{
			 $("#lastc").attr("class","input-control text");
	}
	
	if ($("#jobId").val() == "0") {
		if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Job title for this associate.</p>";
		}
	    $("#jobId").css("border-color","red");
	    correct = false;
	}
	else{
		$("#jobId").css("border-color","#919191");
	}
	
	if ($("#datepick1").val() == "") {
		if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter date of hire for this associate</p>";
			$("#datepick1").focus();
		}
	    $("#doh1").removeClass().addClass("input-control text error-state");
	    correct = false;
	}
	else{
		var isValid=datevalidation("datepick1",$("#datepick1").val());
		if(isValid==false){
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a valid date</p>";
			    $("#datepick1").focus();
			}
			// $("#doh1").removeClass().addClass("input-control text error-state");
			$("#datepick1").css("border-color","red");
			correct = false;
		}else{
			$("#datepick1").css("border-color","#919191");
			// $("#doh1").attr("class","input-control text");
			$("#errmsgdatepick1").hide();
		}
	}
	
	if ($("#locId").val() == "0") {
	    if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter location for this associate.</p>";
		}
	    $("#locId").css("border-color","red");
	    correct = false;
	}
	else{
	 	$("#locId").css("border-color","#919191");
	} 
	
	if ($("#phone").val() == "") {
		if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter work phone for this associate.</p>";
			$("#phone").focus();
		}
	    $("#phonec").removeClass().addClass("input-control text error-state");
	    correct = false;
	}
 	else{
	 	$("#phonec").attr("class","input-control text");
	}
	
if($("#jobId").val() == "1"){	
	if ($("#wunitId").val() == "0") {
	    if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Work Unit for this associate.</p>";
		}
	    $("#wunitId").css("border-color","red");
	    correct = false;
	}
	else{
	 	$("#wunitId").css("border-color","#919191");
	} 
}

	if ($("#datepick2").val() == "") {
		if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Work Unit start date</p>";
			$("#datepick2").focus();
		}
		$("#datepick2").css("border-color","red");
		// $("#start2").removeClass().addClass("input-control text error-state");
	    correct = false;
	}
	else{
		var isValid=datevalidation("datepick2",$("#datepick2").val());
		if(isValid==false){
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a valid date</p>";
			    $("#datepick2").focus();
			}
			// $("#start2").removeClass().addClass("input-control text error-state");
			$("#datepick2").css("border-color","red");
			correct = false;
		}else{
			// $("#start2").attr("class","input-control text");
			$("#datepick2").css("border-color","#919191");
			$("#errmsgdatepick2").hide();
		}
	}
	if ($("#datepick3").val() == "") {
		if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Work Unit end date</p>";
			$("#datepick3").focus();
		}
		$("#datepick3").css("border-color","red");
	    // $("#end3").removeClass().addClass("input-control text error-state");
	    correct = false;
	}
 	else{
 		var isValid=datevalidation("datepick3",$("#datepick3").val());
		if(isValid==false){
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a valid date</p>";
			    $("#datepick3").focus();
			}
			// $("#end3").removeClass().addClass("input-control text error-state");
			$("#datepick3").css("border-color","red");
			correct = false;
		}else{
			$("#datepick3").css("border-color","#919191");
			// $("#end3").attr("class","input-control text");
			$("#errmsgdatepick3").hide();
		}
	}
	
	if(correct!=false){
		var hiredt = new Date($("#datepick1").val()); 
		var wuStartDt = new Date($("#datepick2").val());
		var wuEndDt = new Date($("#datepick3").val());
		if(wuStartDt>wuEndDt){
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Start date cannot be greater than end date</p>";
				$("#datepick2").focus();
				$("#datepick3").focus();
			}
			$("#datepick2").css("border-color","red");
			$("#datepick3").css("border-color","red");
			// $("#start2").removeClass().addClass("input-control text error-state");
			// $("#end3").removeClass().addClass("input-control text error-state");
		    correct = false;
		}
		else if(wuStartDt<hiredt){
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Work unit start date cannot be less than the date of hire for an associate</p>";
				$("#datepick2").focus();
				$("#datepick3").focus();
				 correct = false;
			}
			$("#datepick2").css("border-color","red");
			$("#datepick3").css("border-color","red");
		}
		else{
			$("#datepick2").css("border-color","#919191");
			$("datepick3").css("border-color","#919191");
			// $("#start2").attr("class","input-control text");
			// $("#end3").attr("class","input-control text");
		}
	}
	
	if(($("input[name=auditingStatus]:checked", "#associateForm").val()=="N") && ($("#jobId option:selected").text()=="Claims Processor")){
		if ($("#datepick4").val() == "") {
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Disabled date</p>";
				$("#datepick4").focus();
			}
			$("#datepick4").css("border-color","red");
		   // $("#datepick4c").removeClass().addClass("input-control text error-state");
		    correct = false;
		}
	 	else{
	 		var isValid=datevalidation("datepick4",$("#datepick4").val());
			if(isValid==false){
				if(correct==false){
				}else{
					document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a valid date</p>";
				    $("#datepick4").focus();
				}
				$("#datepick4").css("border-color","red");
				// $("#datepick4c").removeClass().addClass("input-control text error-state");
				 correct = false;
			}else{
				$("#datepick4").css("border-color","#919191");
				// $("#datepick4c").attr("class","input-control text");
				$("#errmsgdatepick4").hide();
			}
		}
		
	}
	
	if($("#jobId option:selected").text()=="Claims Processor"){
		if ($("#facetsId").val() == "") {
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter FACETS ID for this associate.</p>";
				$("#facetsId").focus();
			}
		    $("#facetsIdc").removeClass().addClass("input-control text error-state");
		    correct = false;
		}
	 	else{
		 	$("#facetsIdc").attr("class","input-control text");
		}
		
	}
	// otherwise, we show the error
	if (correct) {
	    error.css("display", "none");
	    }
	else {
	    error.css("display", "block");
	    $("#success").css("display", "none");
	}
	/* scroll to top */
	var formElement = document.getElementById("error");
	  if(formElement){
	      window.scrollTo(0, formElement.offsetTop);
	  }
	return correct;
	}


function ValidatesOU() {
	$("#error p").remove();
	var error = $("#error");
	var correct = true;
	
	if ($("#unitName").val() == "") {
		document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Operational Unit Name.</p>";
	    $("#unitNamec").removeClass().addClass("input-control text error-state");
	    $("#unitName").focus();
	    correct = false;
	}else if(/^\d+$/.test($("#unitName").val())){
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Operational Unit Name cannot have numbers only.<p>"
		    $("#unitNamec").removeClass().addClass("input-control text error-state");
		    $("#unitName").focus();
		    correct = false;
		}
	else{
		 $("#unitNamec").attr("class","input-control text");
	}
	
	if ($("#datepickop2").val() == "") {
		if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Effective Start Date</p>";
			$("#datepickop2").focus();
		}
		$("#datepickop2").css("border-color","red");
	    correct = false;
	}
	else{
		var isValid=datevalidation("datepickop2",$("#datepickop2").val());
		if(isValid==false){
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a valid date</p>";
			    $("#datepickop2").focus();
			}
			$("#datepickop2").css("border-color","red");
			correct = false;
		}else{
			$("#datepickop2").css("border-color","#919191");
			$("#errmsgdatepickop2").hide();
		}
	}
	
	if ($("#datepickop3").val() == "") {
		if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Effective End Date</p>";
			$("#datepickop3").focus();
		}
		$("#datepickop3").css("border-color","red");
	    correct = false;
	}
 	else{
 		var isValid=datevalidation("datepickop3",$("#datepickop3").val());
		if(isValid==false){
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a valid date</p>";
			    $("#datepickop3").focus();
			}
			$("#datepickop3").css("border-color","red");
			correct = false;
		}else{
			$("#datepickop3").css("border-color","#919191");
			$("#errmsgdatepickop3").hide();
		}
	}
	if(correct!=false){
		var ouStartDt = new Date($("#datepickop2").val());
		var ouEndDt = new Date($("#datepickop3").val());
		if(ouStartDt>ouEndDt){
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Start date cannot be greater than end date</p>";
				$("#datepickop2").focus();
				$("#datepicko3").focus();
			}
			$("#datepickop2").css("border-color","red");
			$("#datepickop3").css("border-color","red");
		    correct = false;
		}else{
			$("#datepickop2").css("border-color","#919191");
			$("#datepickop3").css("border-color","#919191");
		}
	}
	
	if (correct) {
	    error.css("display", "none");
	    }
	else {
	    error.css("display", "block");
	    $("#success").css("display", "none");
	}
	var formElement = document.getElementById("error");
	  if(formElement){
	      window.scrollTo(0, formElement.offsetTop);
	  }
	
	return correct;
}


function ValidatesErr() {
	$("#error p").remove();
	var error = $("#error");
	var correct = true;
	
	if ($("#errorName").val() == "") {
		document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Error Name.</p>";
	    $("#errorNamec").removeClass().addClass("input-control text error-state");
	    $("#errorName").focus();
	    correct = false;
	}else if(/^\d+$/.test($("#errorName").val())){
		document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Error Name cannot have numbers only.<p>"
		    $("#errorNamec").removeClass().addClass("input-control text error-state");
		    $("#errorName").focus();
		    correct = false;
	}else{
		 $("#errorNamec").attr("class","input-control text");
	}
	
	if ($("#errorDescription").val() == "") {
		if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Error Description.</p>";
			$("#errorDescription").focus();
		}
	    $("#errorDescriptionc").removeClass().addClass("input-control text error-state");
	    correct = false;
	}else{
		 $("#errorDescriptionc").attr("class","input-control text");
	}
	
	if (((!$("#grpSammd").is(':checked')) && (!$("#grpCd").is(':checked'))) && ($("#sAdmin").val() == "SADMIN")) {
		if(correct==false){ 
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please select at least one Area.</p>";
			$("#grpSammd").focus();
		}
	    $("#area").css("border","1px solid red");
	    correct = false;
	}else{
		 $("#area").css("border","");
	}
	
	if ($("#datepick1").val() == "") {
		if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Effective Start Date.</p>";
			$("#datepick1").focus();
		}
		$("#datepick1").css("border-color","red");
	    correct = false;
	}
 	else{
 		var isValid=datevalidation("datepick1",$("#datepick1").val());
		if(isValid==false){
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a valid date</p>";
			    $("#datepick1").focus();
			}
			$("#datepick1").css("border-color","red");
			correct = false;
		}else{
			$("#datepick1").css("border-color","#919191");
			$("#errmsgdatepick1").hide();
		}
	}

	if ($("#datepick2").val() == "") {
		if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Effective End Date.</p>";
			$("#datepick2").focus();
		}
		$("#datepick2").css("border-color","red");
	    correct = false;
	}
	else{
		var isValid=datevalidation("datepick2",$("#datepick2").val());
		if(isValid==false){
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a valid date</p>";
			    $("#datepick2").focus();
			}
			$("#datepick2").css("border-color","red");
			correct = false;
		}else{
			$("#datepick2").css("border-color","#919191");
			$("#errmsgdatepick2").hide();
		}
	}
	
	if(correct!=false){
		var errStartDt = new Date($("#datepick1").val());
		var errEndDt = new Date($("#datepick2").val());
		if(errStartDt>errEndDt){
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Start date cannot be greater than end date</p>";
				$("#datepick1").focus();
				$("#datepick2").focus();
			}
			$("#datepick1").css("border-color","red");
			$("#datepick2").css("border-color","red");
		    correct = false;
		}else{
			$("#datepick1").css("border-color","#919191");
			$("#datepick2").css("border-color","#919191");
		}
	}
	
	/*Disabled date validations*/
	var status=$("#status").is(":checked");
	if(status){
 		if($("#datepick3").val()=="" || $("#datepick4").val() == ""){
				if($("#datepick3").val()==""){
					if(correct==false){}
					else{
						document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Disable Start Date.</p>";
						$("#datepick3").focus();
					}
					$("#datepick3").css("border-color","red");
					correct = false;
				}
				if($("#datepick4").val() == ""){
					if(correct==false){}
					else{
						document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Disable End Date.</p>";
						$("#datepick4").focus();
					}
					$("#datepick4").css("border-color","red");
				    correct = false;
				}
 		}else{
 			var isValid=datevalidation("datepick3",$("#datepick3").val());
 			if(isValid==false){
 				if(correct==false){
 				}else{
 					document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a valid date</p>";
 					$("#datepick3").focus();
 				}
 				$("#datepick3").css("border-color","red");
				correct = false;
 			}else{
 				$("#datepick3").css("border-color","#919191");
 				$("#errmsgdatepick3").hide();
 			}
 			var isValid=datevalidation("datepick4",$("#datepick4").val());
 			if(isValid==false){
 				if(correct==false){
 				}else{
 					document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a valid date</p>";
 				    $("#datepick4").focus();
 				}
 				$("#datepick4").css("border-color","red");
 				correct = false;
 			}else{
 				$("#datepick4").css("border-color","#919191");
 				$("#errmsgdatepick4").hide();
 			}
 		}
 		
 		if(correct!=false){
 			var errStartDt = new Date($("#datepick3").val());
 	 		var errEndDt = new Date($("#datepick4").val());
 	 		if(errStartDt>errEndDt){
 	 			if(correct==false){
 	 			}else{
 	 				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Start date cannot be greater than end date</p>";
 	 				$("#datepick3").focus();
 	 				$("#datepick4").focus();
 	 			}
 	 			$("#datepick3").css("border-color","red");
 	 			$("#datepick4").css("border-color","red");
 	 		    correct = false;
 	 		}else{
 	 			$("#datepick3").css("border-color","#919191");
 	 			$("#datepick4").css("border-color","#919191");
 	 		}
 	 		
 		}
	}else{
		$("#datepick3").css("border-color","#919191");
		$("#datepick4").css("border-color","#919191");
	}
	
	// otherwise, we show the error
	if (correct) {
	    error.css("display", "none");
	    }
	else {
	    error.css("display", "block");
	    $("#success").css("display", "none");
	}
	/* scroll to top */
	var formElement = document.getElementById("error");
	  if(formElement){
	      window.scrollTo(0, formElement.offsetTop);
	  }
	return correct;
}

function ValidatesJobT() {
	$("#error p").remove();
	var error = $("#error");
	var correct = true;

	if ($("#titleName").val() == "") {
		document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a Job Title.</p>";
	    $("#titleNamec").removeClass().addClass("input-control text error-state");
	    $("#titleName").focus();
	    correct = false;
	}
	else{
		 $("#titleNamec").attr("class","input-control text");
	}
	

	// otherwise, we show the error
	if (correct) {
	    error.css("display", "none");
	    }
	else {
	    error.css("display", "block");
	    $("#success").css("display", "none");
	}
	/* scroll to top */
	
	var formElement = document.getElementById("error");
	  if(formElement){
	      window.scrollTo(0, formElement.offsetTop);
	  }
	
	return correct;
	}


function ValidatesSpecialty() {
	$("#error p").remove();
	var error = $("#error");
	var correct = true;

	if ($("#specialty").val() == "") {
		document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Specialty.</p>";
	    $("#specialtyc").removeClass().addClass("input-control text error-state");
	    $("#specialty").focus();
	    correct = false;
	}
	else{
		 $("#specialtyc").attr("class","input-control text");
	}
	
	if (((!$("#grpSammd").is(':checked')) && (!$("#grpCd").is(':checked'))) && ($("#sAdmin").val() == "SADMIN")) {
		if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please select at least one Area.</p>";
			$("#grpSammd").focus();
		}
	    $("#area").css("border","1px solid red");
	    correct = false;
	}else{
		 $("#area").css("border","");
	}
	
	if (correct) {
	    error.css("display", "none");
	    }
	else {
	    error.css("display", "block");
	    $("#success").css("display", "none");
	}
	/* scroll to top */
	
	var formElement = document.getElementById("error");
	  if(formElement){
	      window.scrollTo(0, formElement.offsetTop);
	  }
	
	return correct;
}



function ValidatesRootCs() {
	$("#error p").remove();
	var error = $("#error");
	var correct = true;

	if ($("#rootCauseName").val() == "") {
		if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Root Cause Name.</p>";
			$("#rootCauseName").focus();
		}
	    $("#rootCauseNamec").removeClass().addClass("input-control text error-state");
	    correct = false;
	}
	else{
		 $("#rootCauseNamec").attr("class","input-control text");
	}
	
	if ($("#rootCauseDescription").val() == "") {
		if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Root Cause Description.</p>";
			$("#rootCauseDescription").focus();
		}
	    $("#rootCauseDescriptionc").removeClass().addClass("input-control text error-state");
	    correct = false;
	}
	else{
		 $("#rootCauseDescriptionc").attr("class","input-control text");
	}
	
	/*SVET012196 N7*/
	if ($("#errorType").val() == "0") {
	    if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please select Error Type Mapping.</p>";
		}
	    $("#errorType").css("border-color","red");
	    correct = false;
	}
	else{
	 	$("#errorType").css("border-color","#919191");
	} 
	
	
	if (((!$("#grpSammd").is(':checked')) && (!$("#grpCd").is(':checked'))) && ($("#sAdmin").val() == "SADMIN")) {
		if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please select at least one Area.</p>";
			$("#grpSammd").focus();
		}
	    $("#area").css("border","1px solid red");
	    correct = false;
	}else{
		 $("#area").css("border","");
	}
	
	if ($("#datepick3").val() == "") {
		if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Effective Start Date.</p>";
			$("#datepick3").focus();
		}
		$("#datepick3").css("border-color","red");
	    correct = false;
	}
 	else{
 		var isValid=datevalidation("datepick3",$("#datepick3").val());
		if(isValid==false){
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a valid date</p>";
				$("#datepick3").focus();
			}
			console.log("yes");
			$("#datepick3c").removeClass("input-control text");
			$("#datepick3c").addClass("input-control text error-state");
		    correct = false;
		}else{
			$("#datepick3c").attr("class","input-control text");
			$("#errmsgdatepick3").hide();
		}
	}
	if ($("#datepick4").val() == "") {
		if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Effective End Date.</p>";
			$("#datepick4").focus();
		}
		// $("#datepick4").css("border-color","red");
		$("#datepick4c").removeClass().addClass("input-control text error-state");
	    correct = false;
	}
 	else{
 		var isValid=datevalidation("datepick4",$("#datepick4").val());
		if(isValid==false){
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a valid date</p>";
				$("#datepick4").focus();
			}
			$("#datepick4c").removeClass().addClass("input-control text error-state");
		    correct = false;
		}else{
			$("#datepick4c").attr("class","input-control text");
			$("#errmsgdatepick4").hide();
		}
	}
	
	if(correct!=false){
		var rcStartDt = new Date($("#datepick3").val());
		var rcEndDt = new Date($("#datepick4").val());
		if(rcStartDt>rcEndDt){
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Start date cannot be greater than end date</p>";
				$("#datepick3").focus();
				$("#datepick4").focus();
			}
			
			$("#datepick3c").removeClass().addClass("input-control text error-state");
			$("#datepick4c").removeClass().addClass("input-control text error-state");
		    correct = false;
		}else{
			$("#datepick3c").attr("class","input-control text");
			$("#datepick4c").attr("class","input-control text");
		}
	}
	
	var status=$("#disable").is(":checked");
	if(status){
 		if($("#datepick1").val()=="" || $("#datepick2").val() == ""){
 			if($("#datepick1").val()==""){
 				if(correct==false){
 				}else{
 					document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Disable Start Date.</p>";
 					$("#datepick1").focus();
 				}
 				// $("#datepick1").css("border-color","red");
 				$("#datepick1c").removeClass().addClass("input-control text error-state");
 				correct = false;
 			}else{
 				$("#datepick1c").attr("class","input-control text");
 			}
 			if($("#datepick2").val()==""){
 				if(correct==false){
 				}else{
 					document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Disable End Date.</p>";
 					$("#datepick2").focus();
 				}
 				$("#datepick2c").removeClass().addClass("input-control text error-state");
 				correct = false;
 			}else{
 				$("#datepick2c").attr("class","input-control text");
 			}
 			
 		}else{
 			var isValid=datevalidation("datepick1",$("#datepick1").val());
 			if(isValid==false){
 				if(correct==false){
 				}else{
 					document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a valid date</p>";
 					$("#datepick1").focus();
 				}
 				$("#datepick1c").removeClass().addClass("input-control text error-state");
				correct = false;
 			}else{
 				$("#datepick1c").attr("class","input-control text");
 				$("#errmsgdatepick1").hide();
 			}
 			var isValid=datevalidation("datepick2",$("#datepick2").val());
 			if(isValid==false){
 				if(correct==false){
 				}else{
 					document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a valid date</p>";
 					$("#datepick2").focus();
 				}
 				$("#datepick2c").removeClass().addClass("input-control text error-state");
				correct = false;
 			}else{
 				$("#datepick2c").attr("class","input-control text");
 				$("#errmsgdatepick2").hide();
 			}
 		}
 		
 		if(correct!=false){
 			var rcDisStartDt = new Date($("#datepick1").val());
 	 		var rcDisEndDt = new Date($("#datepick2").val());
 	 		if(rcDisStartDt>rcDisEndDt){
 	 			if(correct==false){
 	 			}else{
 	 				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Start date cannot be greater than end date</p>";
 	 				$("#datepick1").focus();
 	 				$("#datepick2").focus();
 	 			}
 	 			$("#datepick1c").removeClass().addClass("input-control text error-state");
 	 			$("#datepick2c").removeClass().addClass("input-control text error-state");
 	 		    correct = false;
 	 		}else{
 	 			$("#datepick1c").attr("class","input-control text");
 	 			$("#datepick2c").attr("class","input-control text");
 	 		}
 	 		
 		}
	}else{
		$("#datepick1c").attr("class","input-control text");
		$("#datepick2c").attr("class","input-control text");
	}
	
	if (correct) {
	    error.css("display", "none");
	    }
	else {
	    error.css("display", "block");
	    $("#success").css("display", "none");
	}
	/* scroll to top */
	
	var formElement = document.getElementById("error");
	  if(formElement){
	      window.scrollTo(0, formElement.offsetTop);
	  }
	
	return correct;
	}

function ValidatesPg() {
	$("#error p").remove();
	var error = $("#error");
	var correct = true;

	if ($("#groupId").val() == "") {
		document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Group ID for the Performance Group.<p>"
	    $("#groupIdc").removeClass().addClass("input-control text error-state");
	    $("#groupId").focus();
	    correct = false;
	}
	else{
		 $("#groupIdc").attr("class","input-control text");
	}
	
	if ($("#groupName").val() == "") {
		if(correct==false){}
		else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Group Name for the Performance Group.<p>"
			$("#groupName").focus();
		}
	    $("#groupNamec").removeClass().addClass("input-control text error-state");
	    correct = false;
	}
	else{
		 $("#groupNamec").attr("class","input-control text");
	}
	
	if(!($("input[name='type']").prop('checked'))){ //returns true for Primary radio selection and false for secondary selection
		if((($("select[name='secPGAMappings'] :selected").val() == undefined )||($("select[name='secPGAMappings'] :selected").val() == ""))){
			if(correct==false){}
			else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please select Primary PGA Mapping .<p>";
				correct=false
			}
			$("#secPGAMappingDD").css("border-color","red");
	    }else{
	    	$("#secPGAMappingDD").css("border-color","#919191");
	    }
		}
	
	/*
	 * if ((!$("#grpSammd").is(':checked')) && (!$("#grpCd").is(':checked'))) {
	 * document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please
	 * select at least one Area.<p>" $("#grpSammd").css("border","1px solid
	 * red"); $("#grpCd").css("border","1px solid red"); correct = false; }
	 * else{ $("#grpSammd").attr("style",""); $("#grpCd").attr("style",""); }
	 */
	
	if (((!$("#grpSammd").is(':checked')) && (!$("#grpCd").is(':checked'))) && ($("#sAdmin").val() == "SADMIN")) {
		if(correct==false){
		}else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please select at least one Area.</p>";
			$("#grpSammd").focus();
		}
	    $("#area").css("border","1px solid red");
	    correct = false;
	}else{
		 $("#area").css("border","");
	}
	
	
	if ($("#datepick1").val() == "") {
		if(correct==false){}
		else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Effective Start Date.<p>"
			$("#datepick1").focus();
		}
		$("#datepick1").css("border-color","red");
	    correct = false;
	}
 	else{
 		var isValid=datevalidation("datepick1",$("#datepick1").val());
		if(isValid==false){
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a valid date</p>";
			    $("#datepick1").focus();
			}
			$("#datepick1").css("border-color","red");
			correct = false;
		}else{
			$("#datepick1").css("border-color","#919191");
			$("#errmsgdatepick1").hide();
		}
	}
	if ($("#datepick2").val() == "") {
		if(correct==false){}
		else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Effective End Date.<p>"
			$("#datepick2").focus();
		}
		$("#datepick2").css("border-color","red");
	    correct = false;
	}
 	else{
 		var isValid=datevalidation("datepick2",$("#datepick2").val());
		if(isValid==false){
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a valid date</p>";
			    $("#datepick2").focus();
			}
			$("#datepick2").css("border-color","red");
			correct = false;
		}else{
			$("#datepick2").css("border-color","#919191");
			$("#errmsgdatepick2").hide();
		}
	}
	
	if(correct!=false){
		var pgStartDt = new Date($("#datepick1").val());
		var pgEndDt = new Date($("#datepick2").val());
		if(pgStartDt>pgEndDt){
			if(correct==false){
			}else{
				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Start date cannot be greater than end date</p>";
				$("#datepick1").focus();
				$("#datepick2").focus();
			}
			$("#datepick1").css("border-color","red");
			$("#datepick2").css("border-color","red");
		    correct = false;
		}else{
			$("#datepick1").css("border-color","#919191");
			$("#datepick2").css("border-color","#919191");
		}
	}
	
	if(($("#proceduralAccuracyExp").val()=="") || (/[0-9]{1}$/.test($("#proceduralAccuracyExp").val())) || (/^[0-9]{1}[.]{1}[0-9]{1}$/.test($("#proceduralAccuracyExp").val()))){
		$("#proceduralAccuracyExpc").attr("class","input-control text");
	}
	else{
		if(correct==false){}
		else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Expected accuracy should be numbers only.<p>"
			$("#proceduralAccuracyExp").focus();
		}
		 $("#proceduralAccuracyExpc").removeClass().addClass("input-control text error-state");
		 correct = false;
	}
	
	if(($("#proceduralMonthlyPenalty").val()=="")||(/[0-9]{1}$/.test($("#proceduralMonthlyPenalty").val())==true) || (/^[0-9]{1}[.]{1}[0-9]{1}$/.test($("#proceduralMonthlyPenalty").val())==true)){
		$("#proceduralMonthlyPenaltyc").attr("class","input-control text");
	}
	else{
		if(correct==false){}
		else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Monthly penalty should be numbers only.<p>"
			$("#proceduralMonthlyPenalty").focus();
		}
		 $("#proceduralMonthlyPenaltyc").removeClass().addClass("input-control text error-state");
		 correct = false;
	}
	
	if(($("#dollarFrequencyExp").val()=="")|| (/[0-9]{1}$/.test($("#dollarFrequencyExp").val())) || (/^[0-9]{1}[.]{1}[0-9]{1}$/.test($("#dollarFrequencyExp").val()))){
		$("#dollarFrequencyExpc").attr("class","input-control text");
	}
	else{
		if(correct==false){}
		else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Expected accuracy should be numbers only.<p>"
			$("#dollarFrequencyExp").focus();
		}
		 $("#dollarFrequencyExpc").removeClass().addClass("input-control text error-state");
		 correct = false;
	}
	
	if(($("#dollarFreqMonthlyPenalty").val()=="")||(/[0-9]{1}$/.test($("#dollarFreqMonthlyPenalty").val())) || (/^[0-9]{1}[.]{1}[0-9]{1}$/.test($("#dollarFreqMonthlyPenalty").val()))){
		$("#dollarFreqMonthlyPenaltyc").attr("class","input-control text");
	}
	else{
		if(correct==false){}
		else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Monthly penalty should be numbers only.<p>"
			$("#dollarFreqMonthlyPenalty").focus();
		}
		 $("#dollarFreqMonthlyPenaltyc").removeClass().addClass("input-control text error-state");
		 correct = false;
	}
	
	if(($("#dollarAccuracyExp").val()=="")||(/[0-9]{1}$/.test($("#dollarAccuracyExp").val())) || (/^[0-9]{1}[.]{1}[0-9]{1}$/.test($("#dollarAccuracyExp").val()))){
		$("#dollarAccuracyExpc").attr("class","input-control text");
	}
	else{
		if(correct==false){}
		else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Expected accuracy should be numbers only.<p>"
			$("#dollarAccuracyExp").focus();
		}
		 $("#dollarAccuracyExpc").removeClass().addClass("input-control text error-state");
		 correct = false;
	}
	
	if(($("#dollarAccuracyMonthlyPenalty").val()=="")||(/[0-9]{1}$/.test($("#dollarAccuracyMonthlyPenalty").val())) || (/^[0-9]{1}[.]{1}[0-9]{1}$/.test($("#dollarAccuracyMonthlyPenalty").val()))){
		$("#dollarAccuracyMonthlyPenaltyc").attr("class","input-control text");
	}
	else{
		if(correct==false){}
		else{
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Monthly penalty should be numbers only.<p>"
			$("#dollarAccuracyMonthlyPenalty").focus();
		}
		 $("#dollarAccuracyMonthlyPenaltyc").removeClass().addClass("input-control text error-state");
		 correct = false;
	}
	
	var status=$("#status").is(":checked");
	if(status){
 		if($("#datepick3").val()=="" || $("#datepick4").val() == ""){
				if($("#datepick3").val()==""){
					if(correct==false){}
					else{
						document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Disable Start Date.</p>";
						$("#datepick3").focus();
					}
					$("#datepick3").css("border-color","red");
					correct = false;
				}
				if($("#datepick4").val() == ""){
					if(correct==false){}
					else{
						document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter Disable End Date.</p>";
						$("#datepick4").focus();
					}
					$("#datepick4").css("border-color","red");
				    correct = false;
				}
 		}else{
 			var isValid=datevalidation("datepick3",$("#datepick3").val());
 			if(isValid==false){
 				if(correct==false){
 				}else{
 					document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a valid date</p>";
 					$("#datepick3").focus();
 				}
 				$("#datepick3").css("border-color","red");
				correct = false;
 			}else{
 				$("#datepick3").css("border-color","#919191");
 				$("#errmsgdatepick3").hide();
 			}
 			var isValid=datevalidation("datepick4",$("#datepick4").val());
 			if(isValid==false){
 				if(correct==false){
 				}else{
 					document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please enter a valid date</p>";
 				    $("#datepick4").focus();
 				}
 				$("#datepick4").css("border-color","red");
 				correct = false;
 			}else{
 				$("#datepick4").css("border-color","#919191");
 				$("#errmsgdatepick4").hide();
 			}
 		}
 		
 		if(correct!=false){
 			var pgStartDt = new Date($("#datepick3").val());
 	 		var pgEndDt = new Date($("#datepick4").val());
 	 		if(pgStartDt>pgEndDt){
 	 			if(correct==false){
 	 			}else{
 	 				document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Start date cannot be greater than end date</p>";
 	 				$("#datepick3").focus();
 	 				$("#datepick4").focus();
 	 			}
 	 			$("#datepick3").css("border-color","red");
 	 			$("#datepick4").css("border-color","red");
 	 		    correct = false;
 	 		}else{
 	 			$("#datepick3").css("border-color","#919191");
 	 			$("#datepick4").css("border-color","#919191");
 	 		}
 	 		
 		}
	}else{
		$("#datepick3").css("border-color","#919191");
		$("#datepick4").css("border-color","#919191");
	}
	
	// otherwise, we show the error
	/*
	 * if(null!=flag){ if(flag=="yes"){ error.append("<p style='color:white;padding-left:0px'>Please
	 * enter a valid date<p>"); } }
	 */
	if (correct) {
	    error.css("display", "none");
	    }
	else {
	    error.css("display", "block");
	    $("#success").css("display", "none");
	}
	/* scroll to top */
	
	var formElement = document.getElementById("error");
	  if(formElement){
	      window.scrollTo(0, formElement.offsetTop);
	  }
	
	return correct;
}

function ValidatesClaimsReport() {
	$("#error p").remove();
	var error = $("#error");
	var correct = true;
	
	
	if (($("#associateName").val() == "")&&($("#empNo").val() == "")&&($("#datepick1").val() == "")&&($("#datepick2").val() == "")&&($("#dcn").val() == "")&&($("#auditorName").val() == "")&&($("#datepick3").val() == "")&&($("#datepick4").val() == "")) {
		document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please provide an input.</p>";
	    correct = false;
	}else{
		if(($("#datepick1").val() == "")&&($("#datepick2").val() != "")){
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please provide Processed From date.</p>";
			$("#datepick1").focus();
			correct = false;
		}
		
		if(($("#datepick1").val() != "")&&($("#datepick2").val() == "")){
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please provide Processed To date.</p>";
			$("#datepick2").focus();
			correct = false;
		}
		
		if(($("#datepick3").val() == "")&&($("#datepick4").val() != "")){
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please provide Audit From date.</p>";
			$("#datepick3").focus();
			correct = false;
		}
		
		if(($("#datepick3").val() != "")&&($("#datepick4").val() == "")){
			document.getElementById("error").innerHTML="<p style='color:white;padding-left:0px'>Please provide Audit To date.</p>";
			$("#datepick4").focus();
			correct = false;
		}
	
	}
	
	if (correct) {
	    error.css("display", "none");
	    }
	else {
	    error.css("display", "block");
	}
	/* scroll to top */
	
	var formElement = document.getElementById("error");
	  if(formElement){
	      window.scrollTo(0, formElement.offsetTop);
	  }
	  return correct;
	}  


function hideErrorDiv(){
	$("#error").hide();
	$("#divDuplicate").hide();
}

function clearText(id){
	document.getElementById(id).value="";
}
function maxLimitForName(event, messageId,elementId){
	if ((event.which == 0) || event.which == 8) {}
	else if($("#"+elementId).val().length>49){
   		$("#"+messageId).html("Maximum limit of 50 char.").show().fadeOut("slow");
   		return false;
   	}
 	else{}
}

function datevalidation(id, val){
 	var splits = val.split("/");
    var dt = new Date(splits[0] + "/" + splits[1] + "/" + splits[2]);
    if(dt.getDate()==splits[1] && dt.getMonth()+1==splits[0] && dt.getFullYear()==splits[2]){
    	return true;
    }
    else{
    	$("#errmsg"+id).html("Invalid date").show();
    	return false;
    }
    return false;
}


function validateErrorFields(){
	$("#errorMessageDiv p").remove();
	var error = $("#errorMessageDiv");
	var correct = true;
	var count=0;
	var countSelect=0;
	var countErrcd=0;
	var countSp=0;
	var countRc=0;
	
	$('[name^="errorCodesId"]').each(function(){
		if(this.value=="" || this.value==null){
			countSelect++;
		}else{
			countErrcd++;
		}
		
	});
	
	if(countSelect>0 && countErrcd==0){
		if((document.getElementById("monetaryErrorV").value=="O" || document.getElementById("monetaryErrorV").value=="U")){
			if(correct==false){		
			}else{
				document.getElementById("errorMessageDiv").innerHTML="<p style='color:white;padding-left:0px'>Please indicate a monetary error as monetary details are provided in general details</p>";
				$("#errorCategory").css("border-color","red");
				correct = false;
			}
		}else{
			$("#errorCategory").css("border-color","#919191");
			
		}
	}
	else if(countSelect>0)
	{
		if(correct==false){
		}else{
			document.getElementById("errorMessageDiv").innerHTML="<p style='color:white;padding-left:0px'>Please remove empty row or select error code</p>";
			$("#monetarys").css("border-color","red");
			correct = false;
		}
		
	}
	else{
		$('[id^="errorCategory"]').each(function() {
			if(this.value=="" || this.value==null){
				count++;
			}else{
			}
		});
		if(count>0){
			if(correct==false){
			}else{
				document.getElementById("errorMessageDiv").innerHTML="<p style='color:white;padding-left:0px'>Please indicate whether the error selected is monetary or procedural</p>";
				$("#errorCategory").css("border-color","red");
				correct = false;
			}
		}else{
			
			var flag = false;
			$('[id^="errorCategory"]').each(function() {
				if((this.value=="M" || this.value=="Y") && (document.getElementById("monetaryErrorV").value=="N" || document.getElementById("monetaryErrorV").value=="")){
					if(correct==false){
					}else{
						document.getElementById("errorMessageDiv").innerHTML="<p style='color:white;padding-left:0px'>Please provide overpaid and underpaid in general details for monetary</p>";
						$("#errorCategory").css("border-color","red");
						correct = false;
					}
				}else{
					$("#errorCategory").css("border-color","#919191");
				}
			});
			$('[id^="errorCategory"]').each(function() {
				if((this.value=="M") || (this.value == "Y")){
					flag = true;
				}
				
				if((flag == false) && (document.getElementById("monetaryErrorV").value=="O" || document.getElementById("monetaryErrorV").value=="U")){
					if(correct==false){
					}else{
						document.getElementById("errorMessageDiv").innerHTML="<p style='color:white;padding-left:0px'>Please indicate a monetary error as monetary details are provided in general details</p>";
						$("#errorCategory").css("border-color","red");
						correct = false;
					}
				}else{
					$("#errorCategory").css("border-color","#919191");
					//correct = true;
				}
			});
		}
		
		var v = $('select[name="errorCodesId"] option[disabled]:selected').val();
		if(null != v){
			if(correct==false){
			}else{
				document.getElementById("errorMessageDiv").innerHTML="<p style='color:white;padding-left:0px'>The selected error code "+v+" is currently disabled.Please select another error code or contact admin to enable the error code.</p>";
				$("#monetarys").css("border-color","red");
				correct = false;
			}
		}
		
		
		$('[name^="specialitysId"]').each(function() {
			if(this.value=="" || this.value==null){
				countSp++;
			}else{
			}
		});
		if(countSp>0){
			if(correct==false){
 			}else{
 				document.getElementById("errorMessageDiv").innerHTML="<p style='color:white;padding-left:0px'>Please select a specialty</p>";
 				$('[name^="specialitysId"]').each(function() {
 					if(this.value=="" || this.value==null){
 						$(this).css("border-color","red");
 					}else{
 						$(this).css("#919191");
 					}
 				});
 				correct = false;
 			}
		}else{
			$('[name^="specialitysId"]').each(function() {
				$(this).css("border-color","#919191");
			});
		}
		
		$('[name^="rootCausesId"]').each(function() {
			if(this.value=="" || this.value==null){
				countRc++;
			}else{
			}
		});
		if(countRc>0){
			if(correct==false){
 			}else{
 				document.getElementById("errorMessageDiv").innerHTML="<p style='color:white;padding-left:0px'>Please select root cause</p>";
 				$('[name^="rootCausesId"]').each(function() {
 					if(this.value=="" || this.value==null){
 						$(this).css("border-color","red");
 					}else{
 						$(this).css("border-color","#919191");
 					}
 				});
 				correct = false;
 			}
		}else{
			$('[name^="rootCausesId"]').each(function() {
				$(this).css("border-color","#919191");
			});
		}
		
		if($("#reason").val()==''){
			if(correct==false){
 			}else{
 				document.getElementById("errorMessageDiv").innerHTML="<p style='color:white;padding-left:0px'>Please provide reason for error</p>";
 				$("#reasonc").removeClass().addClass("input-control text error-state");
 			    $("#reason").focus();
 			   correct = false;
 			}
		}else{
			$("#reasonc").attr("class","input-control text");
		}
		
		/*if($("#comments").val()==''){
			if(correct==false){
 			}else{
 				document.getElementById("errorMessageDiv").innerHTML="<p style='color:white;padding-left:0px'>Please enter Notes/Comments. Error assessed on claim</p>";
 				$("#commentsc").removeClass().addClass("input-control text error-state");
 			    $("#comments").focus();
 			   correct = false;
 			}
		}else{
			$("#commentsc").attr("class","input-control text");
		}*/
	}
	
	 if (correct) {
		    error.css("display", "none");
		    }
		else {
		    error.css("display", "block");
		    $("#success").css("display", "none");
		}
		/* scroll to top */

		var formElement = document.getElementById("errorMessageDiv");
		  if(formElement){
		      window.scrollTo(0, formElement.offsetTop);
		  }

		return correct;
}

// Masking js for Phone number

/*
 * jQuery Masked Input Plugin
 * 
 */
!function(factory) {
"function" == typeof define && define.amd ? define([ "jquery" ], factory) : factory("object" == typeof exports ? require("jquery") : jQuery);
}(function($) {
var caretTimeoutId, ua = navigator.userAgent, iPhone = /iphone/i.test(ua), chrome = /chrome/i.test(ua), android = /android/i.test(ua);
$.mask = {
    definitions: {
        "9": "[0-9]",
        a: "[A-Za-z]",
        "*": "[A-Za-z0-9]"
    },
    autoclear: !0,
    dataName: "rawMaskFn",
    placeholder: "_"
}, $.fn.extend({
    caret: function(begin, end) {
        var range;
        if (0 !== this.length && !this.is(":hidden")) return "number" == typeof begin ? (end = "number" == typeof end ? end : begin, 
        this.each(function() {
            this.setSelectionRange ? this.setSelectionRange(begin, end) : this.createTextRange && (range = this.createTextRange(), 
            range.collapse(!0), range.moveEnd("character", end), range.moveStart("character", begin), 
            range.select());
        })) : (this[0].setSelectionRange ? (begin = this[0].selectionStart, end = this[0].selectionEnd) : document.selection && document.selection.createRange && (range = document.selection.createRange(), 
        begin = 0 - range.duplicate().moveStart("character", -1e5), end = begin + range.text.length), 
        {
            begin: begin,
            end: end
        });
    },
    unmask: function() {
        return this.trigger("unmask");
    },
    mask: function(mask, settings) {
        var input, defs, tests, partialPosition, firstNonMaskPos, lastRequiredNonMaskPos, len, oldVal;
        if (!mask && this.length > 0) {
            input = $(this[0]);
            var fn = input.data($.mask.dataName);
            return fn ? fn() : void 0;
        }
        return settings = $.extend({
            autoclear: $.mask.autoclear,
            placeholder: $.mask.placeholder,
            completed: null
        }, settings), defs = $.mask.definitions, tests = [], partialPosition = len = mask.length, 
        firstNonMaskPos = null, $.each(mask.split(""), function(i, c) {
            "?" == c ? (len--, partialPosition = i) : defs[c] ? (tests.push(new RegExp(defs[c])), 
            null === firstNonMaskPos && (firstNonMaskPos = tests.length - 1), partialPosition > i && (lastRequiredNonMaskPos = tests.length - 1)) : tests.push(null);
        }), this.trigger("unmask").each(function() {
            function tryFireCompleted() {
                if (settings.completed) {
                    for (var i = firstNonMaskPos; lastRequiredNonMaskPos >= i; i++) if (tests[i] && buffer[i] === getPlaceholder(i)) return;
                    settings.completed.call(input);
                }
            }
            function getPlaceholder(i) {
                return settings.placeholder.charAt(i < settings.placeholder.length ? i : 0);
            }
            function seekNext(pos) {
                for (;++pos < len && !tests[pos]; ) ;
                return pos;
            }
            function seekPrev(pos) {
                for (;--pos >= 0 && !tests[pos]; ) ;
                return pos;
            }
            function shiftL(begin, end) {
                var i, j;
                if (!(0 > begin)) {
                    for (i = begin, j = seekNext(end); len > i; i++) if (tests[i]) {
                        if (!(len > j && tests[i].test(buffer[j]))) break;
                        buffer[i] = buffer[j], buffer[j] = getPlaceholder(j), j = seekNext(j);
                    }
                    writeBuffer(), input.caret(Math.max(firstNonMaskPos, begin));
                }
            }
            function shiftR(pos) {
                var i, c, j, t;
                for (i = pos, c = getPlaceholder(pos); len > i; i++) if (tests[i]) {
                    if (j = seekNext(i), t = buffer[i], buffer[i] = c, !(len > j && tests[j].test(t))) break;
                    c = t;
                }
            }
            function androidInputEvent() {
                var curVal = input.val(), pos = input.caret();
                if (oldVal && oldVal.length && oldVal.length > curVal.length) {
                    for (checkVal(!0); pos.begin > 0 && !tests[pos.begin - 1]; ) pos.begin--;
                    if (0 === pos.begin) for (;pos.begin < firstNonMaskPos && !tests[pos.begin]; ) pos.begin++;
                    input.caret(pos.begin, pos.begin);
                } else {
                    for (checkVal(!0); pos.begin < len && !tests[pos.begin]; ) pos.begin++;
                    input.caret(pos.begin, pos.begin);
                }
                tryFireCompleted();
            }
            function blurEvent() {
                checkVal(), input.val() != focusText && input.change();
            }
            function keydownEvent(e) {
                if (!input.prop("readonly")) {
                    var pos, begin, end, k = e.which || e.keyCode;
                    oldVal = input.val(), 8 === k || 46 === k || iPhone && 127 === k ? (pos = input.caret(), 
                    begin = pos.begin, end = pos.end, end - begin === 0 && (begin = 46 !== k ? seekPrev(begin) : end = seekNext(begin - 1), 
                    end = 46 === k ? seekNext(end) : end), clearBuffer(begin, end), shiftL(begin, end - 1), 
                    e.preventDefault()) : 13 === k ? blurEvent.call(this, e) : 27 === k && (input.val(focusText), 
                    input.caret(0, checkVal()), e.preventDefault());
                }
            }
            function keypressEvent(e) {
                if (!input.prop("readonly")) {
                    var p, c, next, k = e.which || e.keyCode, pos = input.caret();
                    if (!(e.ctrlKey || e.altKey || e.metaKey || 32 > k) && k && 13 !== k) {
                        if (pos.end - pos.begin !== 0 && (clearBuffer(pos.begin, pos.end), shiftL(pos.begin, pos.end - 1)), 
                        p = seekNext(pos.begin - 1), len > p && (c = String.fromCharCode(k), tests[p].test(c))) {
                            if (shiftR(p), buffer[p] = c, writeBuffer(), next = seekNext(p), android) {
                                var proxy = function() {
                                    $.proxy($.fn.caret, input, next)();
                                };
                                setTimeout(proxy, 0);
                            } else input.caret(next);
                            pos.begin <= lastRequiredNonMaskPos && tryFireCompleted();
                        }
                        e.preventDefault();
                    }
                }
            }
            function clearBuffer(start, end) {
                var i;
                for (i = start; end > i && len > i; i++) tests[i] && (buffer[i] = getPlaceholder(i));
            }
            function writeBuffer() {
                input.val(buffer.join(""));
            }
            function checkVal(allow) {
                var i, c, pos, test = input.val(), lastMatch = -1;
                for (i = 0, pos = 0; len > i; i++) if (tests[i]) {
                    for (buffer[i] = getPlaceholder(i); pos++ < test.length; ) if (c = test.charAt(pos - 1), 
                    tests[i].test(c)) {
                        buffer[i] = c, lastMatch = i;
                        break;
                    }
                    if (pos > test.length) {
                        clearBuffer(i + 1, len);
                        break;
                    }
                } else buffer[i] === test.charAt(pos) && pos++, partialPosition > i && (lastMatch = i);
                return allow ? writeBuffer() : partialPosition > lastMatch + 1 ? settings.autoclear || buffer.join("") === defaultBuffer ? (input.val() && input.val(""), 
                clearBuffer(0, len)) : writeBuffer() : (writeBuffer(), input.val(input.val().substring(0, lastMatch + 1))), 
                partialPosition ? i : firstNonMaskPos;
            }
            var input = $(this), buffer = $.map(mask.split(""), function(c, i) {
                return "?" != c ? defs[c] ? getPlaceholder(i) : c : void 0;
            }), defaultBuffer = buffer.join(""), focusText = input.val();
            input.data($.mask.dataName, function() {
                return $.map(buffer, function(c, i) {
                    return tests[i] && c != getPlaceholder(i) ? c : null;
                }).join("");
            }), input.one("unmask", function() {
                input.off(".mask").removeData($.mask.dataName);
            }).on("focus.mask", function() {
                if (!input.prop("readonly")) {
                    clearTimeout(caretTimeoutId);
                    var pos;
                    focusText = input.val(), pos = checkVal(), caretTimeoutId = setTimeout(function() {
                        input.get(0) === document.activeElement && (writeBuffer(), pos == mask.replace("?", "").length ? input.caret(0, pos) : input.caret(pos));
                    }, 10);
                }
            }).on("blur.mask", blurEvent).on("keydown.mask", keydownEvent).on("keypress.mask", keypressEvent).on("input.mask paste.mask", function() {
                input.prop("readonly") || setTimeout(function() {
                    var pos = checkVal(!0);
                    input.caret(pos), tryFireCompleted();
                }, 0);
            }), chrome && android && input.off("input.mask").on("input.mask", androidInputEvent), 
            checkVal();
        });
    }
});
});