package com.carefirst.qadb.controller;


import java.sql.SQLException;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Scanner;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import net.sf.jasperreports.engine.JRDataSource;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.SessionAttributes;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.carefirst.audit.model.Associate;
import com.carefirst.audit.model.AuditAssessment;
import com.carefirst.audit.model.AuditCounts;
import com.carefirst.audit.model.AuditDetails;
import com.carefirst.audit.model.AuditSave;
import com.carefirst.audit.model.AuditSearch;
import com.carefirst.audit.model.ClaimLevel;
import com.carefirst.audit.model.ErrorDetails;
import com.carefirst.audit.model.HighDollar;
import com.carefirst.audit.model.Message;
import com.carefirst.audit.model.ProcessType;
import com.carefirst.audit.model.ProductLevel;
import com.carefirst.audit.model.Products;
import com.carefirst.audit.model.QASamplingResponse;
import com.carefirst.audit.model.Statistics;
import com.carefirst.audit.model.StatusBlock;
import com.carefirst.audit.model.SubscriberLevel;
import com.carefirst.qadb.constant.QADBConstants;
import com.carefirst.qadb.dao.AuditDetailsDAO;
import com.carefirst.qadb.dao.AuditDetailsDAOImpl;
import com.carefirst.qadb.dao.AuditSearchDAO;
import com.carefirst.qadb.dao.ErrorDetailsDAO;
import com.carefirst.qadb.dao.ScoresReportsDAO;
import com.carefirst.qadb.service.AuthorizationService;
import com.carefirst.qadb.service.ClaimService;
import com.carefirst.qadb.utils.ServiceUtil;


@Controller
@SessionAttributes("AuditSearch")
public class AuditController {
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);
	
	@Autowired
    @Qualifier("claimService")
	private ClaimService claimService;
	
	@Autowired  
	AuditDetailsDAO auditDetailsDao;
	
	@Autowired
	ErrorDetailsDAO errorDetailsDao;
	
	@Autowired
	AuditSearchDAO auditSearchDao;
   
	@Autowired
	ScoresReportsDAO scoresReportsDAO;
	
	@Autowired  
	AuthorizationService authorizationService;
	
   /**
    * Method to populate the drop downs for Audit page 
    * @param model
 * @throws SQLException 
    */
   @ModelAttribute("auditForm")
   public void values(Map<String, Object> model,HttpServletRequest request) throws SQLException{
	   
	   //String userGrp = "('SAMMD','CD','ALL')";
	   
	   String userGrp = authorizationService.getUserGroup(request);

	   //String b[] = {"Contractor","PeopleSoft_ELM_User","onestop_users","qadb-superadmin_users"};
	 String b[] = {request.getHeader("iv-groups")};
	  /* if(!(((Arrays.toString(b)).replace("[", "")).replace("]", "")).equals("null")){
			int last = b.length - 1;
			if(((b[last].toString()).contains("qadb-superadmin_users"))){
				userGrp = "('SAMMD','CD','ALL')";
			}
			if(((b[last].toString()).contains("qadb-samd-auditor_users"))||((b[last].toString()).contains("qadb-samd-admin_users"))){
				userGrp = "('SAMMD','ALL')";
			}
			if(((b[last].toString()).contains("qadb-cd-auditor_users"))||((b[last].toString()).contains("qadb-cd-admin_users"))){
				userGrp = "('CD','ALL')";
			}
		}*/
	   
	   logger.debug("User Group : "+b+" "+userGrp);
	   HttpSession session = request.getSession(true);
	   
	   session.setAttribute("userGrp", userGrp);
	   
	   AuditDetails auditForm = new AuditDetails();
   	   model.put("auditForm", auditForm);
       
   	   List<AuditDetails> claimTypes = auditDetailsDao.getClaimType();
       model.put("claimTypes", claimTypes);
       
       List<AuditDetails> piTypeList = auditDetailsDao.getPenaltyInterestType();
       model.put("piTypes", piTypeList); 
   	   
       List<AuditDetails> auditTypeList = auditDetailsDao.getAuditType();
       model.put("auditTypes", auditTypeList);
       
       List<AuditDetails> priPGAList = auditDetailsDao.getPriPGA(userGrp);
       model.put("priPGAList", priPGAList);
     
       /*  List<AuditDetails> secPGAList = auditDetailsDao.getSecPGA(userGrp);
       model.put("secPGAList", secPGAList);*/
       
       List<AuditDetails> processTypeList = auditDetailsDao.getProcessType();
       model.put("processTypes", processTypeList); 
	   
   }
   
   /**
    * Authorization of the user based on the user role fetched from the request headers.
    * @param jspView
    * @param request
    * @return
    */
   public ModelAndView AuthorizeUser(String jspView,HttpServletRequest request){
		//HttpServletRequest request ;
		ModelAndView mv = null;
		String b[] = {request.getHeader("iv-groups")};
		if(!(((Arrays.toString(b)).replace("[", "")).replace("]", "")).equals("null")){
			int last = b.length - 1;
			if(((b[last].toString()).contains("qadb-samd-auditor_users"))||((b[last].toString()).contains("qadb-cd-auditor_users"))||((b[last].toString()).contains("qadb-samd-admin_users"))||((b[last].toString()).contains("qadb-cd-admin_users"))||((b[last].toString()).contains("qadb-superadmin_users"))){
				mv = new ModelAndView(jspView);
			}
			else{
				mv = new ModelAndView("unauthorized");
			}
		}
		else{
			mv = new ModelAndView("unauthorized");
		}
		return mv;
	}

   /***
	 * Method to show the page to add a new Audit.
	 * @return new model and view to enter Audit details.
 * @throws Exception 
	 */
   @RequestMapping(value = "/audit", method = RequestMethod.GET)
  	public ModelAndView audit(HttpServletRequest request) {
	   logger.debug("***audit method start***");
	  // ModelAndView modelAndView = AuthorizeUser("audit", request);
	   ModelAndView mv = null;
	   mv = authorizationService.AuthorizeUsersAudit("audit", request);
	   logger.debug("***audit method end***");
       return mv;
      }
   
   /***
    * This method is implemented to set the values of claim details fetched from the Facets service. Calculating penalty interest rate.
    * @param request
    * @param user
    * @return
    * @throws Exception
    */
    @RequestMapping(value="/validateDCN",method = RequestMethod.POST)
    public @ResponseBody ModelAndView retrieveClaimDetails(HttpServletRequest request,@ModelAttribute("auditForm") AuditDetails user) throws Exception,SQLException {
    	logger.debug("***retrieveClaimDetails method start***");
    	HttpSession session = request.getSession(true);
    	String dcn= request.getParameter("dcn");
    	String actionType= request.getParameter("actionType");
    	String newFlag= request.getParameter("newFlag");
    	String userGrp =  (String) session.getAttribute("userGrp");
    	session.setAttribute("jurisdiction", request.getParameter("jurisdiction"));
    	ModelAndView modelAndView=null;
    	modelAndView = new ModelAndView("audit_general");
    	Associate dcnDuplicateCheck = null;
    	
    	//Duplicate Claim check
    	if(null!=newFlag && newFlag.equalsIgnoreCase("N")){
    		Associate assoDetails = new Associate();
        	assoDetails.setDcnCheck(dcn);
        	assoDetails.setUserGroup(userGrp);
        	logger.debug("claim number = "+dcn);
        	try {
        		dcnDuplicateCheck = auditDetailsDao.getAssociateDetails(assoDetails);
    		} catch (SQLException e) {
    			logger.debug("Exception generated = "+e.getMessage()+" "+dcnDuplicateCheck);
    		}
    	}
    	if(null!=dcnDuplicateCheck && null!=dcnDuplicateCheck.getSucessCode() && (dcnDuplicateCheck.getSucessCode().equalsIgnoreCase("001"))){
    		logger.debug("Audit for this claim is already exist");
    		AuditSearch basicSearchTO = new AuditSearch();
    		basicSearchTO.setSearchType(QADBConstants.USER_ACTION_BASIC);
    		basicSearchTO.setDcn(dcn);
    		basicSearchTO.setUserGroup(userGrp);
    		List<AuditSearch> dupDcnResult = null;
        	try {
        		dupDcnResult = auditSearchDao.getSearchResults(basicSearchTO);
    		} catch (Exception e) {
    			logger.debug("Exception generated = "+e.getMessage());
    		}
        	for (AuditSearch auditSearch : dupDcnResult) {
        		logger.debug("Duplicates audit id = "+auditSearch.getAuditId());
			}
        	modelAndView.addObject("dupDcnResult",dupDcnResult);
        	modelAndView.addObject("dupFlag","Y");
    		
    	}else{
	    		String facetsId = request.getParameter("userId");
	    		int inSampleCount = 0;
	    		logger.debug("FACETS id : "+facetsId);
	        	if(actionType.equals("validateDCN")){
	    	    	QASamplingResponse domainSamplingResponse = new QASamplingResponse();
	    	    	try {
	    				domainSamplingResponse = claimService.getClaim(dcn);
	    			} catch (Exception e) {
	    				logger.debug("Exception generated = "+e.getMessage());
	    			}
	    	    	logger.debug("**StatusCode** "+domainSamplingResponse.getStatusBlock().getStatusCode());
	    	    	if(null!=domainSamplingResponse.getStatusBlock()){
	    				StatusBlock domainStatusBlock = new StatusBlock();
	    				domainStatusBlock.setStatusCode(domainSamplingResponse.getStatusBlock().getStatusCode());
	    				List<com.carefirst.audit.model.Message> messageList = new ArrayList<com.carefirst.audit.model.Message>();
	    				if(null!=domainSamplingResponse.getStatusBlock().getMessage()){
	    					com.carefirst.audit.model.Message messageObj = new com.carefirst.audit.model.Message();
	    					for(Message message : domainSamplingResponse.getStatusBlock().getMessage()){
	    						messageObj.setMessageCode(message.getMessageCode());
	    						messageObj.setMessageDescription(message.getMessageDescription());
	    						messageList.add(messageObj);
	    					}
	    				}
	    				domainStatusBlock.setMessage(messageList);
	    				domainSamplingResponse.setStatusBlock(domainStatusBlock);
	    			}
	    	    	
	    	    	if(null != domainSamplingResponse.getClstStatusLines()){
	    	    		//Restrict user to audit a claim related to the same group
	    	    		String b[] = {request.getHeader("iv-groups")};
	    	    		if((!(((Arrays.toString(b)).replace("[", "")).replace("]", "")).equals("null"))||(b.length != 0)){
	    	    			/*int last = b.length - 1;
	    	    			if(((b[last].toString()).contains("-samd-"))){
	    	    				if((domainSamplingResponse.getSubscriberLevel().getSBU().equalsIgnoreCase("cd"))){
	    	    	    			return new ModelAndView("audit_general","messageGroup","You are not authorized to audit  CD group claim");
	    	    	    		}
	    	    			}
	    	    			else if(((b[last].toString()).contains("-cd-"))){
	    	    				if((domainSamplingResponse.getSubscriberLevel().getSBU().equalsIgnoreCase("sammd"))){
	    	    	    			return new ModelAndView("audit_general","messageGroup","You are not authorized to audit SamMD group claim");
	    	    	    		}
	    	    			}else if(((b[last].toString()).contains("superadmin"))){
	    	    				
	    	    			}*/
	    	    			
	    	    			//Changes made in order to make auditors able to audit both types of claims - dual user access
	    	    			
	    	    			logger.info("Roles in the array is ---> "+ Arrays.toString(b));
	    	    			logger.info("Type of claim from service is cd ---> " + domainSamplingResponse.getSubscriberLevel().getSBU().equalsIgnoreCase("cd"));
	    	    			logger.info("Does array contains cd auditor role ---> " +(Arrays.toString(b)).contains("qadb-cd-auditor_users"));
	    	    			logger.info("Does array contains cd admin role ---> " +(Arrays.toString(b)).contains("qadb-cd-admin_users"));
	    	    			
	    	    			if(((Arrays.toString(b)).contains("qadb-samd-admin_users"))||((Arrays.toString(b)).contains("qadb-samd-auditor_users"))){
	    	    				if((domainSamplingResponse.getSubscriberLevel().getSBU().equalsIgnoreCase("cd")) &&
	    	    						!((Arrays.toString(b)).contains("qadb-cd-admin_users")||((Arrays.toString(b)).contains("qadb-cd-auditor_users")))){
	    	    	    			return new ModelAndView("audit_general","messageGroup","You are not authorized to audit CD group claim");
	    	    	    		}
	    	    			}
	    	    			else if(((Arrays.toString(b)).contains("qadb-cd-admin_users"))||((Arrays.toString(b)).contains("qadb-cd-auditor_users"))){
	    	    				if((domainSamplingResponse.getSubscriberLevel().getSBU().equalsIgnoreCase("sammd")) &&
	    	    						!((Arrays.toString(b)).contains("qadb-samd-admin_users")||((Arrays.toString(b)).contains("qadb-samd-auditor_users")))){
	    	    	    			return new ModelAndView("audit_general","messageGroup","You are not authorized to audit SamMD group claim");
	    	    	    		}
	    	    			}else if(((Arrays.toString(b)).contains("qadb-superadmin_users"))){
	    	    				
	    	    			}
	    	    		}else{
	    	    			return new ModelAndView("audit_general","messageGroup","You are not authorized to audit this claim");
	    	    		}
	    	    		
	    	    		List<com.carefirst.audit.model.ClstStatusLine> clstStatusLineList = new ArrayList<com.carefirst.audit.model.ClstStatusLine>();
	    	    		if(null!=domainSamplingResponse.getClstStatusLines().getClstStatusLine()){
	    	    			for(com.carefirst.audit.model.ClstStatusLine clstStatusLine : domainSamplingResponse.getClstStatusLines().getClstStatusLine()){
	    						com.carefirst.audit.model.ClstStatusLine clstStatusLineobj = new com.carefirst.audit.model.ClstStatusLine();
	    						clstStatusLineobj.setClstStatus(clstStatusLine.getProcessedDate());
	    						clstStatusLineobj.setProcessedDate(clstStatusLine.getProcessedDate());
	    						clstStatusLineobj.setUserID(clstStatusLine.getUserID());
	    						logger.debug("User id(Associate id) from Claim Status : "+clstStatusLine.getUserID()+ " Status :"+clstStatusLine.getClstStatus());
	    						clstStatusLineList.add(clstStatusLineobj);
	    					}
	    	    		}
	    	    		/*Enhancement*/
	    	    		if((null != domainSamplingResponse.getAdjustmentLevel()) && (!((domainSamplingResponse.getAdjustmentLevel().getLevels()).isEmpty())) ){
	    	    			logger.debug("adjustmentReasoncode "+domainSamplingResponse.getAdjustmentLevel().getLevels().get(0).getAdjustmentReasoncode());
		    	    		modelAndView.addObject("adjustmentReasoncode", domainSamplingResponse.getAdjustmentLevel().getLevels().get(0).getAdjustmentReasoncode());
	    	    		}
	    	    		/*Enhancement*/
	    	    		logger.debug("user list size--->"+clstStatusLineList.size()+" "+request.getParameter("clstStatus"));
	    	    		int numberOfUsers=0;
	    	    		Associate assoDetTO = new Associate();
	    		    	numberOfUsers=clstStatusLineList.size();
	    		    	modelAndView.addObject("numberOfUsers", numberOfUsers);
	    		    	if(numberOfUsers>1){
	    		    		if(null!=domainSamplingResponse.getClaimLevel()){
	    		    			if(null!=request.getParameter("processedDate")){
	    		    				domainSamplingResponse.getClaimLevel().setProcessDate(ServiceUtil.formatDate(request.getParameter("processedDate")));
	    		    			}
	    		    			if(null!=request.getParameter("clstStatus")){
	    		    				domainSamplingResponse.getClaimLevel().setClaimCurrentstatus(request.getParameter("clstStatus"));;
	    		    			}
	    	    	    	}
	        		    	assoDetTO.setFacetsId(facetsId);
	        		    	logger.debug("Associate id : "+facetsId);
	    		    	}else{
	    		    		String uID="";
	    		    		for(com.carefirst.audit.model.ClstStatusLine clstStatusLine : domainSamplingResponse.getClstStatusLines().getClstStatusLine()){
	    		    			uID=clstStatusLine.getUserID();
	    		    			logger.debug("Claim line user Id "+uID);
	    		    		}
	    		    		assoDetTO.setFacetsId(uID);
	    		    		logger.debug("Claim line user Id "+assoDetTO.getFacetsId());
	    		    	}
	    		    	Associate assocDetails = null;
	    		    	try {
	    		    		 assocDetails = auditDetailsDao.getAssociateDetails(assoDetTO);
	    				} catch (Exception e) {
	    					logger.debug("Exception generated = "+e.getMessage());
	    				}    		    	
	    		    	//Audit Count Start
	    		    	AuditCounts auditCountsTO= new AuditCounts();
	    		    	AuditCounts audCountRS = new AuditCounts();
	    		    	logger.debug("FACETS id "+assoDetTO.getFacetsId());
	    		    	auditCountsTO.setFacetsId(assoDetTO.getFacetsId());
	    	    		auditCountsTO.setInpProccessDate(domainSamplingResponse.getClaimLevel().getProcessDate());
	    	    		audCountRS =  auditSearchDao.getAuditCounts(auditCountsTO);
	    	    		//inSampleCount =auditDetailsDao.getInSampleCount(assocDetails.getAssociateDBId());
	    	    		if(audCountRS.getStatusCode().equalsIgnoreCase("000")){
	    	    			modelAndView.addObject("audCountRS",audCountRS);
	    	    			session.setAttribute("audCountRS", audCountRS);
	    	    		}else{
	    	    			modelAndView.addObject("audCountRS",null);
	    	    			session.removeAttribute("audCountRS");
	    	    		}
	    		    	//Audit Count End
	    		    	assocDetails.setAssociateId(assoDetTO.getFacetsId());
	    		    	logger.debug("InSampleCount "+audCountRS.getInsCount());
	    		    	//logger.debug("inSampleCount "+inSampleCount);
	    		    	logger.debug("FACETS id "+assoDetTO.getFacetsId());
	    		    	logger.debug("Associate id "+assocDetails.getAssociateId());
	    		    	logger.debug("Associate DB id "+assocDetails.getAssociateDBId());
	    		    	modelAndView.addObject("assoDetails", assocDetails);
	    		    	session.setAttribute("assoDetails",assocDetails);
	    		    	session.setAttribute("associateDBid",assocDetails.getAssociateDBId());
	    		    	logger.debug("Claim Processs date  "+domainSamplingResponse.getClaimLevel().getProcessDate());
	    		    	session.setAttribute("processDate",domainSamplingResponse.getClaimLevel().getProcessDate());
	    		    	session.setAttribute("facetsId",assoDetTO.getFacetsId());
	    	    	}
	    	    	session.setAttribute("domainSamplingResponse", domainSamplingResponse);
	    	    	session.setAttribute("dcn",dcn);
	    	    	//session.setAttribute("facetsId",facetsId);
	    	    	//modelAndView.addObject("inSampleCount",inSampleCount);
	    	    	modelAndView.addObject("dcn", dcn);
	    	    	modelAndView.addObject("claimDetails", domainSamplingResponse);
	    	    	
	    	    	return modelAndView;
	        	}
	    		
	    	}
    	logger.debug("***retrieveClaimDetails method end***");
    	return modelAndView;
    }
    
    /**
     * Method to show the calculated interest rate in the textfield
     * @param request
     * @return
     */
    @RequestMapping(value = "/piDiv", method = RequestMethod.POST)
  	public ModelAndView piDiv(HttpServletRequest request) {
    	logger.debug("***PIcalculatorDiv method start***");
    	String days= request.getParameter("days");
   		String amount= request.getParameter("amount");
   		String jurisdiction= request.getParameter("jurisdiction");
   		logger.debug("jurisdiction : "+jurisdiction + " -"+request.getParameter("juridiction"));
   		String processTypevalue= request.getParameter("processTypevalue");
   		ModelAndView modelAndView = new ModelAndView("piDiv");
   		double calculatedPi = 0.0;
		int idays=Integer.parseInt(days);
		double iamount=Double.parseDouble(amount);
		calculatedPi=calculatePenaltyInterest(jurisdiction,processTypevalue,idays,iamount);
		DecimalFormat df2 = new DecimalFormat(".##");
		logger.debug("Calculated PI = "+calculatedPi);
		modelAndView.addObject("calculatedPi",df2.format(calculatedPi));
	   logger.debug("***PIcalculatorDiv method end***");
       return modelAndView;
      }
    
    /**
     * 
     * @param request
     * @return
     */
    @RequestMapping(value = "/auditRedirect", method = RequestMethod.POST)
 	public ModelAndView auditRedirect(HttpServletRequest request, HttpSession session) {
	   logger.debug("***audit auditRdirect start***");
	   //ModelAndView modelAndView = AuthorizeUser("audit_general", request);
	   ModelAndView modelAndView = null;
	   modelAndView = authorizationService.AuthorizeUsersAudit("audit_general", request);
	   String lob = request.getParameter("lob");
	   String memberId = request.getParameter("memberId");
	   String processDate = request.getParameter("processDate");
	   String paidDate = request.getParameter("paidDate");
	   String totalCharge = request.getParameter("totalCharge");
	   String paid = request.getParameter("paid");
	   String claimType= request.getParameter("claimType");
	   String monetaryError = request.getParameter("monetaryError");
	   String amountp = request.getParameter("amountp");
	   String theoreticalPaid = request.getParameter("theoreticalPaid");
	   String isChk = request.getParameter("isChk");
	   String penalityInterest = request.getParameter("penalityInterest");
	   String piCalculated= request.getParameter("piCalculated");
	   String juridiction= request.getParameter("juridiction");
	   logger.debug("Juridiction "+juridiction + " Calculated Pi "+piCalculated);
	   String auditType= request.getParameter("auditType");
	   String priPGA= request.getParameter("priPGA");
	   String secPGA= request.getParameter("secPGA");
	   String mock= request.getParameter("mock");
	   String oos= request.getParameter("oos");
	   String e2e= request.getParameter("e2e");
	   String processtype= request.getParameter("processtype");
	   String riskAccount= request.getParameter("riskAccount");
	   String adjustment= request.getParameter("adjustment");
	   String platform= request.getParameter("platform");
	   logger.debug("***platform Value is getting printed***"+platform);
	   String fyicheck= request.getParameter("fyicheck");
	   String fyi= request.getParameter("fyi");
	   String subscriberCK= request.getParameter("subscriberCK");
	   String AccountID= request.getParameter("AccountID");
	   String AccountName= request.getParameter("AccountName");
	   String Group= request.getParameter("Group");
	   String claimCurrentStatus= request.getParameter("claimCurrentStatus");
	   String productID= request.getParameter("productID");
	   String adjustmentReasoncode = request.getParameter("adjustmentReasoncode");
	   int prodSize=Integer.parseInt(request.getParameter("prodSize")) ;
	   QASamplingResponse domainSamplingResponse = new QASamplingResponse();
	   ProductLevel productLevel = new ProductLevel();
	   List<Products> products = new ArrayList<Products>();
	   logger.debug("Products size "+prodSize);
	   for(int i=0; i<prodSize ; i++){
		   Products prod= new Products();
		   logger.debug("BSBSCode_ = "+request.getParameter("BSBSCode_"+i));
		   prod.setBSBSCode(request.getParameter("BSBSCode_"+i));
		   prod.setGroupID(request.getParameter("groupID_"+i));
		   prod.setProductLine(request.getParameter("productLine_"+i));
		   prod.setProductDescription(request.getParameter("productDescription_"+i));
		   products.add(prod);
	   }
	   products.get(0).setLineofBusiness(lob);
	   productLevel.setProducts(products);
	   StatusBlock statusBlock = new StatusBlock();
	   statusBlock.setStatusCode("00");
	   SubscriberLevel subscriberLevel = new SubscriberLevel();
	   subscriberLevel.setMemberID(memberId);
	   subscriberLevel.setCJAJurisdiction(juridiction);
	   subscriberLevel.setSubscriber_CK(subscriberCK);
	   subscriberLevel.setAccountID(AccountID);
	   subscriberLevel.setAccountName(AccountName);
	   subscriberLevel.setSBU(Group);
	   ClaimLevel claimLevel = new ClaimLevel();
	   claimLevel.setProcessDate(processDate);
	   claimLevel.setPaidDate(paidDate);
	   claimLevel.setPaidAmount(paid);
	   claimLevel.setTotalCharge(totalCharge);
	   claimLevel.setClaimCurrentstatus(claimCurrentStatus);
	   claimLevel.setProductID(productID);
	   /*High Dollar*/
	   if(null != (request.getParameter("examinerName")) ){
		   claimLevel.setCareFirstExaminerUserID(request.getParameter("examinerName"));
	   }
	   domainSamplingResponse.setClaimLevel(claimLevel);
	   domainSamplingResponse.setSubscriberLevel(subscriberLevel);
	   domainSamplingResponse.setStatusBlock(statusBlock);
	   domainSamplingResponse.setProductLevel(productLevel);
	   domainSamplingResponse.setPlatform(platform);
	   logger.debug("***platform Value is getting editpage***"+request.getParameter("editPage"));
	   if(null != (request.getParameter("editPage"))){
		   if((request.getParameter("editPage")).equalsIgnoreCase("Y")){
			   modelAndView.addObject("edit", "edit");
			   modelAndView.addObject("auditId", "auditId");
			   modelAndView.addObject("eDCN", request.getParameter("dcnNumber"));
			   Associate assocDetailsTo = new Associate();
			   String facetsId = request.getParameter("efacetsId");
			   assocDetailsTo.setFacetsId(facetsId);
			   Associate assocDetails = null;
		    	try {
		    		 assocDetails = auditDetailsDao.getAssociateDetails(assocDetailsTo);
				} catch (Exception e) {
	    			logger.debug("Exception generated = "+e.getMessage());
				}  
		    	assocDetails.setFacetsId(facetsId);
				modelAndView.addObject("assocEditDetails", assocDetails);
		   }
	   }
	   
	   modelAndView.addObject("claimType", claimType);
	   modelAndView.addObject("monetaryError", monetaryError);
	   modelAndView.addObject("amountp", amountp);
	   modelAndView.addObject("theoreticalPaid", theoreticalPaid);
	   modelAndView.addObject("isChk", isChk);
	   modelAndView.addObject("penalityInterest", penalityInterest);
	   modelAndView.addObject("piCalculated", piCalculated);
	   modelAndView.addObject("eJuridiction", juridiction);
	   modelAndView.addObject("jurisdiction", juridiction);
	   modelAndView.addObject("auditType", auditType);
	   modelAndView.addObject("priPGA", priPGA);
	   modelAndView.addObject("secPGA", secPGA);
	   modelAndView.addObject("mock", mock);
	   modelAndView.addObject("oos", oos);
	   modelAndView.addObject("e2e", e2e);
	   modelAndView.addObject("processtype", processtype);
	   modelAndView.addObject("riskAccount", riskAccount);
	   modelAndView.addObject("adjustment", adjustment);
	   modelAndView.addObject("ePlatform", platform);
	   modelAndView.addObject("fyicheck", fyicheck);
	   modelAndView.addObject("fyi", fyi);
	   modelAndView.addObject("adjustmentReasoncode", adjustmentReasoncode);
	   modelAndView.addObject("claimDetails", domainSamplingResponse);
	   
	   /*High Dollar Info*/
	   if(null != (request.getParameter("examinerName")) ){
		   	modelAndView.addObject("claimAdjFlag",request.getParameter("claimAdjFlag"));
	   		modelAndView.addObject("serviceAdjFlag",request.getParameter("serviceAdjFlag"));
	   		modelAndView.addObject("QAName",request.getParameter("QAName"));
	   		modelAndView.addObject("patientName",request.getParameter("patientName"));
	   		modelAndView.addObject("serviceDates",request.getParameter("serviceDates"));
	   		modelAndView.addObject("typeOfService",request.getParameter("typeOfService"));
	   		modelAndView.addObject("diagnosis",request.getParameter("diagnosis"));
	   		modelAndView.addObject("surgeryDOS",request.getParameter("surgeryDOS"));
	   		modelAndView.addObject("surgery",request.getParameter("surgery"));
	   		modelAndView.addObject("fileRef",request.getParameter("fileRef"));
	   		modelAndView.addObject("interestPaid",request.getParameter("interestPaid"));
	   		modelAndView.addObject("providerName",request.getParameter("providerName"));
	   		modelAndView.addObject("providerNumber",request.getParameter("providerNumber"));
	   		modelAndView.addObject("payee",request.getParameter("payee"));
	   		modelAndView.addObject("notes",request.getParameter("notes"));
	   		modelAndView.addObject("audSignDate",request.getParameter("audSignDate"));
	   		modelAndView.addObject("vpSignDate",request.getParameter("vpSignDate"));
	   		modelAndView.addObject("forwrdToDate",request.getParameter("forwardToDate"));
	   		modelAndView.addObject("rcvedFrmDate",request.getParameter("rcvedFrmDate"));
	   		modelAndView.addObject("releasedByDate",request.getParameter("releasedByDate"));
	   }
	   
	   logger.debug("***audit auditRdirect end*** "+domainSamplingResponse.getStatusBlock().getStatusCode());
	   return modelAndView;
     }
    
    /***
     * Method for penalty interest calculation
     * @param juridiction
     * @param product
     * @param daysOfInterest
     * @param amount
     * @return panaltyInterest
     */
    public double calculatePenaltyInterest(String juridiction, String product,int daysOfInterest, double amount){
    	logger.debug("***calculatePenaltyInterest method start***");
    	logger.debug("pi details "+juridiction+" "+product+" "+daysOfInterest+" "+amount);
    	double panaltyInterest=0;
    	int noOfDays = calculateDays();

    	if(juridiction.equals("VA")){
    		if(product.equals("Non-HMO")){
    			panaltyInterest=(double) ((daysOfInterest-17)*(0.06/noOfDays)*amount);  //Changes made as per request by Mike
    		}
    		else if(product.equals("HMO")){
    			panaltyInterest=(double) ((daysOfInterest-27)*(0.06/noOfDays));
    		}
    	}
    	else if(juridiction.equals("MD") || juridiction.equals("DC")){
    		if(daysOfInterest>120){
    			panaltyInterest=(((daysOfInterest-120)*(.025/noOfDays*12)*amount)+((12.0*30.0/noOfDays)*(0.015+2*.02))*amount);
    		}
    		else if(daysOfInterest>60){
    			panaltyInterest=(((daysOfInterest-60)*(0.02/noOfDays*12)*amount)+(0.015/noOfDays*12*30)*amount);
    		}
    		else if(daysOfInterest>30){
    			panaltyInterest=((daysOfInterest-30)*(0.015/noOfDays*12)*amount);
    		}
    		else{
    			return 0;
    		}
    	}
    	logger.debug("panaltyInterest = "+panaltyInterest);
    	logger.debug("***calculatePenaltyInterest method exit***");
    	return panaltyInterest;
    }
     
    /**
     * Method to show an error page
     * @param model
     * @param auditDetails
     * @param request
     * @return
     * @throws SQLException 
     */
    @RequestMapping(value = "/error", method = RequestMethod.POST)
   	public @ResponseBody ModelAndView errors(Map<String, Object> model,@ModelAttribute("errorForm") ErrorDetails errorDetails,@ModelAttribute("auditForm")AuditDetails auditDetails,HttpServletRequest request) throws Exception,SQLException {
    	logger.debug("*** errors method start ***");
    	HttpSession session = request.getSession(true);
    	//logger.debug("piCalculated type: "+request.getParameter("piCalculated"));
    	//logger.debug("Input 1 - Claim Details : "+auditDetails.getDcn()+" " +auditDetails.getClaimType()+ " "+auditDetails.getLob()+ " " +auditDetails.getMemberId()+ " "+auditDetails.getProcessDate()+" "+auditDetails.getPaidDate()+" "+auditDetails.getTotalCharge()+" "+auditDetails.getPaid());
    	//logger.debug("Input 2 - Monetary Info : "+auditDetails.getMonetaryError()+" " +auditDetails.getAmountp()+ " "+auditDetails.getTheoreticalPaid()+ " " +auditDetails.getPi());
    //	logger.debug("Input 3 - Audit Details : "+auditDetails.getAuditType()+" " +auditDetails.getPriPGA()+ " " +auditDetails.getSecPGA()+ " "+auditDetails.getMock()+"--oos-- "+auditDetails.getOos()+ " " +auditDetails.getE2e()+" "+auditDetails.getRiskAccount()+" "+auditDetails.getProcesstype()+ " " +auditDetails.getAdjustment()+ " " +auditDetails.getFyicheck()+ " "+auditDetails.getFyi());
    	if(null != (request.getParameter("piCalculated"))){
        	auditDetails.setPi(request.getParameter("piCalculated"));
    	}
    	logger.debug("Audit Pi "+auditDetails.getPi());
    	//auditDetails.setPi(request.getParameter("piCalculated"));
    	session.setAttribute("auditGeneralDetails",auditDetails); 
    	
    	/*ModelAndView modelAndView=null;
    	modelAndView = AuthorizeUser("audit_errors", request);*/
    	ModelAndView modelAndView = null;
 	    modelAndView = authorizationService.AuthorizeUsersAudit("audit_errors", request);
    	
    	String userGrp =  (String) session.getAttribute("userGrp");
    	
    	ErrorDetails errorForm = new ErrorDetails();
    	model.put("errorForm", errorForm);
    	
    	List<ErrorDetails> errorCodeList = errorDetailsDao.getErrorCodes("add",userGrp);
        model.put("errorCodes", errorCodeList);
    	
        List<ErrorDetails> specialityList = errorDetailsDao.getSpeciality(userGrp);
        model.put("speciality", specialityList);
    	
        List<ErrorDetails> rootCauseList = errorDetailsDao.getRootCause(userGrp);
        model.put("rootCause", rootCauseList);
        
	   /* List<ErrorDetails> errorTypeList = errorDetailsDao.getErrorTypes();
	    model.put("errorTypes", errorTypeList);*/
     	
    	try{
    		modelAndView.addObject("retainAudit",request.getParameter("userAction"));
    		modelAndView.addObject("numberOfUsers",request.getParameter("numberOfUsers"));
    		modelAndView.addObject("dcnNumber",request.getParameter("dcn"));
    		modelAndView.addObject("claimType",auditDetails.getClaimType());
    		modelAndView.addObject("lob",request.getParameter("lob"));
    		modelAndView.addObject("memberId",request.getParameter("memberId"));
    		modelAndView.addObject("processDate",request.getParameter("processDate"));
    		modelAndView.addObject("paidDate",request.getParameter("paidDate"));
    		modelAndView.addObject("totalCharge",request.getParameter("totalCharge"));
    		modelAndView.addObject("paid",request.getParameter("paid"));
    		modelAndView.addObject("monetaryError",auditDetails.getMonetaryError());
    		modelAndView.addObject("amountp", auditDetails.getAmountp());    			
    		modelAndView.addObject("theoreticalPaid",auditDetails.getTheoreticalPaid());
    		modelAndView.addObject("isChk",request.getParameter("piChk"));
    		modelAndView.addObject("penalityInterest",request.getParameter("penaltyInterestType"));
    		modelAndView.addObject("piCalculated",request.getParameter("piCalculated"));
    		modelAndView.addObject("auditType",request.getParameter("auditType"));
    		modelAndView.addObject("priPGA",request.getParameter("priPGA"));
    		modelAndView.addObject("secPGA",request.getParameter("secPGA"));
    		modelAndView.addObject("mock",auditDetails.getMock());
    		modelAndView.addObject("oos",auditDetails.getOos());
    		modelAndView.addObject("e2e",auditDetails.getE2e());
    		modelAndView.addObject("riskAccount",auditDetails.getRiskAccount());
    		modelAndView.addObject("processtype",request.getParameter("processtype"));
    		modelAndView.addObject("adjustment",auditDetails.getAdjustment());
    		modelAndView.addObject("juridiction",request.getParameter("juridiction"));
    		modelAndView.addObject("days",request.getParameter("days"));
    		modelAndView.addObject("fyicheck",auditDetails.getFyicheck());
    		modelAndView.addObject("fyi",auditDetails.getFyi());
    		modelAndView.addObject("platform",auditDetails.getPlatform());
    		//Brij
    		modelAndView.addObject("subscriberCK",request.getParameter("subscriberCK"));
    		modelAndView.addObject("AccountID",request.getParameter("AccountID"));
    		modelAndView.addObject("AccountName",request.getParameter("AccountName"));
    		modelAndView.addObject("Group",request.getParameter("Group"));
    		modelAndView.addObject("claimCurrentStatus",request.getParameter("claimCurrentStatus"));
    		modelAndView.addObject("productID",request.getParameter("productID"));
    		modelAndView.addObject("adjustmentReasoncode",request.getParameter("adjustmentReasoncode"));
    		modelAndView.addObject("productsList",auditDetails.getProducts());
    		/*High dollar details*/
    		logger.debug("High Dollar redirect Details "+auditDetails.getExaminerName() + " analyst "+auditDetails.getQAName()); 
    		modelAndView.addObject("claimAdjFlag",auditDetails.getClaimAdjFlag());
    		modelAndView.addObject("serviceAdjFlag",auditDetails.getServiceAdjFlag());
    		modelAndView.addObject("examinerName",auditDetails.getExaminerName());
    		modelAndView.addObject("QAName",auditDetails.getQAName());
    		modelAndView.addObject("patientName",auditDetails.getPatientName());
    		modelAndView.addObject("serviceDates",auditDetails.getServiceDates());
    		modelAndView.addObject("typeOfService",auditDetails.getTypeOfService());
    		modelAndView.addObject("diagnosis",auditDetails.getDiagnosis());
    		modelAndView.addObject("surgeryDOS",auditDetails.getSurgeryDOS());
    		modelAndView.addObject("surgery",auditDetails.getSurgery());
    		modelAndView.addObject("fileRef",auditDetails.getFileReferenced());
    		logger.debug("interestPaid "+ auditDetails.getInterestPaid());
    		modelAndView.addObject("interestPaid",auditDetails.getInterestPaid());
    		modelAndView.addObject("providerName",auditDetails.getProviderName());
    		modelAndView.addObject("providerNumber",auditDetails.getProviderNumber());
    		modelAndView.addObject("payee",auditDetails.getPayee());
    		modelAndView.addObject("notes",auditDetails.getNotes());
    		modelAndView.addObject("audSignDate",auditDetails.getAudSignDate());
    		modelAndView.addObject("vpSignDate",auditDetails.getVpSignDate());
    		modelAndView.addObject("forwardToDate",auditDetails.getForwrdToDate());
    		modelAndView.addObject("rcvedFrmDate",auditDetails.getRcvedFrmDate());
    		modelAndView.addObject("releasedByDate",auditDetails.getReleasedByDate());
    		
    	}catch(Exception e){
    		logger.error("Exception generated= "+e.getMessage());
    	}
    	if(null!=session.getAttribute("audCountRS")){
    		modelAndView.addObject("audCountRS",session.getAttribute("audCountRS"));
    	}
    	return modelAndView;
	}
    
    /**
     * Method to save an audit(Mapping name is High dollar because its the pdf name when we save high dollar claim)
     * @param model
     * @param errorDetails
     * @param request
     * @param redirectAttributes
     * @return
     */
    @RequestMapping(value = "/High_Dollar_Report", method = RequestMethod.GET)
   	public ModelAndView auditSave(Map<String, Object> model,@ModelAttribute("errorForm") ErrorDetails errorDetails,HttpServletRequest request,RedirectAttributes redirectAttributes) throws Exception {
    	logger.debug("*** AuditSave method start ***");
    	logger.debug("Error Category : "+errorDetails.getErrorCategory());
    	HttpSession session = request.getSession(true);
    	List<String> monetarys = new ArrayList<String>();
    	List<String> procedurals = new ArrayList<String>();
    	for(int i=1;i<errorDetails.getErrorCategory().size();i++){
    		if (errorDetails.getErrorCategory().get(i).equalsIgnoreCase("p")){
    			procedurals.add("Y");
    			monetarys.add("N");
    		}else{
    			procedurals.add("N");
    			monetarys.add("Y");
    		}
    	}
    	errorDetails.setMonetarys(monetarys);
    	errorDetails.setProcedurals(procedurals);
    	logger.debug("errorCodesList "+errorDetails.getErrorCodesId());
    	logger.debug("MonetaryList "+errorDetails.getMonetarys());
    	logger.debug("ProceduralList "+errorDetails.getProcedurals());
    	logger.debug("specialtyList "+errorDetails.getSpecialitysId());
    	logger.debug("editCodesList "+errorDetails.getEditCodesId());
    	
    	AuditDetails audGenDetails = (AuditDetails) session.getAttribute("auditGeneralDetails");
		AuditSave audSave = new AuditSave();
		//String userId=(String) session.getAttribute(QADBConstants.USERNAME);
		String userId = request.getHeader("givenname")+ " "+ request.getHeader("sn");
		String facetsId =(String) session.getAttribute("facetsId");
		QASamplingResponse domainSamplingResponse =(QASamplingResponse) session.getAttribute("domainSamplingResponse");
		logger.debug("User id "+userId+ " facets id "+facetsId);
		audSave.setUserid(((userId).toString()).replaceAll("\\%20", " "));
		audSave.setUserActyp("NEW");
		//audSave.setAuditId(null);   //Auto generated for Save
		audSave.setAssociID(facetsId);
		
		audSave.setDcn(audGenDetails.getDcn()) ;
		audSave.setClaimType(audGenDetails.getClaimType());
		audSave.setLob(audGenDetails.getLob());
		audSave.setMemberId(audGenDetails.getMemberId());
		audSave.setProcessDate(audGenDetails.getProcessDate());
		audSave.setPaidDate(audGenDetails.getPaidDate());
		audSave.setTotalCharge(audGenDetails.getTotalCharge());
		audSave.setPaid(audGenDetails.getPaid());
		audSave.setPlatform(domainSamplingResponse.getPlatform());
		audSave.setJurisdiction(domainSamplingResponse.getSubscriberLevel().getCJAJurisdiction());
		audSave.setMonetaryError(audGenDetails.getMonetaryError());
		audSave.setAmountp(audGenDetails.getAmountp());
		audSave.setTheoreticalPaid(audGenDetails.getTheoreticalPaid());
		audSave.setPiChk(audGenDetails.getPiChk());
		audSave.setPenaltyInterestType(audGenDetails.getPenaltyInterestType());
		logger.debug("Save pi "+audGenDetails.getPi());
		audSave.setPi(audGenDetails.getPi());
		
		audSave.setAuditType(audGenDetails.getAuditType());
		audSave.setPriPGA(audGenDetails.getPriPGA());
		logger.debug("Save pg1 "+audGenDetails.getPriPGA());
		audSave.setSecPGA(audGenDetails.getSecPGA());
		logger.debug("Save pg2 "+audGenDetails.getSecPGA());
		audSave.setMock(audGenDetails.getMock());
		audSave.setOos(audGenDetails.getOos());
		audSave.setE2e(audGenDetails.getE2e());
		audSave.setRiskAccount(audGenDetails.getRiskAccount());
		audSave.setProcesstype(audGenDetails.getProcesstype());
		audSave.setAdjustment(audGenDetails.getAdjustment());
		audSave.setFyiChk(audGenDetails.getFyicheck());
		audSave.setFyi(audGenDetails.getFyi());
		
		/*Additional Claims information*/
		audSave.setSubscriberCK(domainSamplingResponse.getSubscriberLevel().getSubscriber_CK());
		audSave.setAccountID(domainSamplingResponse.getSubscriberLevel().getAccountID());
		audSave.setAccountName(domainSamplingResponse.getSubscriberLevel().getAccountName()); 
		audSave.setGroup(domainSamplingResponse.getSubscriberLevel().getSBU()) ;
		audSave.setClaimCurrentStatus(domainSamplingResponse.getClaimLevel().getClaimCurrentstatus());
		audSave.setProductID(domainSamplingResponse.getClaimLevel().getProductID());
		logger.debug("Reason code"+audGenDetails.getAdjustmentReasoncode());
		audSave.setAdjustmentReasoncode(audGenDetails.getAdjustmentReasoncode());
		
		/*High dollar form*/
		audSave.setClaimAdjFlag(audGenDetails.getClaimAdjFlag());
		audSave.setServiceAdjFlag(audGenDetails.getServiceAdjFlag());
		audSave.setExaminerName(audGenDetails.getExaminerName());
		audSave.setQAName(audGenDetails.getQAName());
		audSave.setPatientName(audGenDetails.getPatientName());
		audSave.setServiceDates(audGenDetails.getServiceDates());
		audSave.setTypeOfService(audGenDetails.getTypeOfService());
		audSave.setDiagnosis(audGenDetails.getDiagnosis());
		audSave.setSurgeryDOS(audGenDetails.getSurgeryDOS());
		audSave.setSurgery(audGenDetails.getSurgery());
		audSave.setFileReferenced(audGenDetails.getFileReferenced());
		logger.debug("interestPaid "+ audGenDetails.getInterestPaid());
		audSave.setInterestPaid(audGenDetails.getInterestPaid());
		audSave.setProviderName(audGenDetails.getProviderName());
		audSave.setProviderNumber(audGenDetails.getProviderNumber());
		audSave.setPayee(audGenDetails.getPayee());
		audSave.setNotes(audGenDetails.getNotes());
		audSave.setAudSignDate(audGenDetails.getAudSignDate());
		audSave.setVpSignDate(audGenDetails.getVpSignDate());
		audSave.setForwrdToDate(audGenDetails.getForwrdToDate());
		audSave.setRcvedFrmDate(audGenDetails.getRcvedFrmDate());
		audSave.setReleasedByDate(audGenDetails.getReleasedByDate());
		
		List<String> BSBSCode  = new ArrayList<String>();
		List<String> groupID  = new ArrayList<String>();
		List<String> productLine  = new ArrayList<String>();
		List<String> productDescription  = new ArrayList<String>();
		
		for (Products rs : domainSamplingResponse.getProductLevel().getProducts()) {
			BSBSCode.add(rs.getBSBSCode());
			groupID.add(rs.getGroupID());
			productLine.add(rs.getProductLine());
			productDescription.add(rs.getProductDescription());
		}
		audSave.setBSBSCode(BSBSCode);
		audSave.setGroupID(groupID);
		audSave.setProductLine(productLine);
		audSave.setProductDescription(productDescription);
		logger.debug("BSBC code "+BSBSCode);
		
		audSave.setErrorCodesId(errorDetails.getErrorCodesId());
		audSave.setMonetarys(errorDetails.getMonetarys());
		audSave.setProcedurals(errorDetails.getProcedurals());
		audSave.setSpecialitysId(errorDetails.getSpecialitysId());
		audSave.setRootCausesId(errorDetails.getRootCausesId());
		audSave.setEditCodesId(errorDetails.getEditCodesId());

		audSave.setErrorType(errorDetails.getErrorType());
		audSave.setSop(errorDetails.getSop());
		audSave.setReason(errorDetails.getReason());
		audSave.setComments(errorDetails.getComments());
		audSave.setRequired(errorDetails.getRequired());
		audSave.setCompleted(errorDetails.getCompleted());
		audSave.setDateAdj(errorDetails.getDateAdj());
		audSave.setAppeal(errorDetails.getAppeal());
    	
		AuditSave audSaves = null;
		try {
			audSaves = auditDetailsDao.saveAudit(audSave);
		} catch (SecurityException e) {
			e.printStackTrace();
			logger.debug("SecurityException generated"+e.getMessage());
		} catch (NoSuchMethodException e) {
			e.printStackTrace();
			logger.debug("Exception generated"+e.getMessage());
		}
    	session.invalidate();
    	logger.debug("Success Code : "+audSaves.getSucessCode() + " "+audSave.getDcn());
		logger.debug("Success Message : "+audSaves.getSuccessMsg());
		redirectAttributes.addFlashAttribute("success", audSaves.getSuccessMsg());
		redirectAttributes.addFlashAttribute("saveDcn",audSave.getDcn() );
		logger.debug("*** auditSave method end ***"+audGenDetails.getPaid());
		ModelAndView mv = new ModelAndView("redirect:audit");
		
		if(((null != audGenDetails.getQAName()) && (!(audGenDetails.getQAName()).equalsIgnoreCase(""))) && ((null != audGenDetails.getPaid()) && ((Double.valueOf(audGenDetails.getPaid())) > 9999.99))){
			if(null !=(audSaves.getSucessCode())){
				mv = getHighDollarReport(audSaves.getSucessCode());
				logger.debug("*** High dollar Pdf generated ***");
			}
			else{
				mv = new ModelAndView("redirect:audit");
			}
			
		}
    	logger.debug("*** AuditSave method end ***");
		return mv;
   	}
    
    @RequestMapping(value = "/submitHighDollar", method = RequestMethod.POST)
   	public @ResponseBody ModelAndView submitHighDollar(HttpServletRequest request, @ModelAttribute("highDollarForm") HighDollar highDollarDetails) {
       	logger.debug("***High Dollar save start ***");
       	ModelAndView modelAndView = new ModelAndView("highDollarBean");
       	modelAndView.addObject("QAName",request.getParameter("QAName"));
       	logger.debug("***ClaimAdjFlag ***"+request.getParameter("claimAdjFlag"));
       	if(null!=request.getParameter("claimAdjFlag")){
       		modelAndView.addObject("claimAdjFlag",request.getParameter("claimAdjFlag"));
       	}else{
       		modelAndView.addObject("claimAdjFlag","N");
       	}
       	if(null!=request.getParameter("serviceAdjFlag")){
       		modelAndView.addObject("serviceAdjFlag",request.getParameter("serviceAdjFlag"));
       	}else{
       		modelAndView.addObject("serviceAdjFlag","N");
       	}
       	logger.debug("***ServiceAdjFlag ***"+request.getParameter("serviceAdjFlag"));
       	modelAndView.addObject("patientName",request.getParameter("patientName"));
       	modelAndView.addObject("serviceDates",request.getParameter("serviceDates"));
       	modelAndView.addObject("typeOfService",request.getParameter("typeOfService"));
       	modelAndView.addObject("diagnosis",request.getParameter("diagnosis"));
       	modelAndView.addObject("surgeryDOS",request.getParameter("surgeryDOS"));
       	modelAndView.addObject("surgery",request.getParameter("surgery"));
       	modelAndView.addObject("fileReferenced",request.getParameter("fileReferenced"));
    	modelAndView.addObject("interestPaid",request.getParameter("interestPaid"));
       	modelAndView.addObject("providerName",request.getParameter("providerName"));
       	modelAndView.addObject("providerNumber",request.getParameter("providerNumber"));
       	modelAndView.addObject("payee",request.getParameter("payee"));
       	modelAndView.addObject("notes",request.getParameter("notes"));
       	modelAndView.addObject("audSignDate",request.getParameter("audSignDate"));
       	modelAndView.addObject("vpSignDate",request.getParameter("vpSignDate"));
       	modelAndView.addObject("forwrdToDate",request.getParameter("forwrdToDate"));
       	modelAndView.addObject("rcvedFrmDate",request.getParameter("rcvedFrmDate"));
       	modelAndView.addObject("releasedByDate",request.getParameter("releasedByDate"));
       	logger.debug("***High Dollar save end ***");
       	return modelAndView;
   	}
    
	/**
     * Method to display basic search page
     * @return
	 * @throws SQLException 
     */
    @RequestMapping(value = "/searchBasic", method = RequestMethod.GET)
	public ModelAndView searchBasic(HttpServletRequest request) throws SQLException {
    	
    	ModelAndView modelAndView = null;
 	    modelAndView = authorizationService.AuthorizeUsersAudit("audit_search_basic", request);
    	//ModelAndView modelAndView = AuthorizeUser("audit_search_basic", request);
    	
    	List<Associate> claimProcessors = scoresReportsDAO.getClaimProcessors("ASSOCIATE","");
    	modelAndView.addObject("claimProcessors", claimProcessors);
    	
    	List<Associate> facetsIds = scoresReportsDAO.getFacetsIds();
    	modelAndView.addObject("facetsIds", facetsIds);
    	
    	return modelAndView;
	}
    
    /**
     * Method to get the search result for basic search
     * @param basicSearchTO
     * @param session
     * @return
     * @throws SQLException 
     */
    @RequestMapping(value = "/searchBasicRes", method = RequestMethod.GET)
   	public ModelAndView getSearchBasic(@ModelAttribute("searchBasicForm") AuditSearch basicSearchTO ,HttpSession session,HttpServletRequest request) throws Exception,SQLException {
    	logger.debug("*** Entry getSearchBasic method ***");
		ModelAndView mv = new ModelAndView("audit_search_basic");
		
		List<Associate> claimProcessors = scoresReportsDAO.getClaimProcessors("ASSOCIATE","");
    	mv.addObject("claimProcessors", claimProcessors);
		
    	List<Associate> facetsIds = scoresReportsDAO.getFacetsIds();
    	mv.addObject("facetsIds", facetsIds);
    	
		//String userGrp = "('SAMMD','CD','ALL')";
		//String b[] = {"Contractor","PeopleSoft_ELM_User","onestop_users","qadb-cd-admin_users"};
        String userGrp = authorizationService.getUserGroup(request);
		
		String b[] = {request.getHeader("iv-groups")};
		
		/*if(!(((Arrays.toString(b)).replace("[", "")).replace("]", "")).equals("null")){
				int last = b.length - 1;
				if(((b[last].toString()).contains("qadb-superadmin_users"))){
					userGrp = "('SAMMD','CD','ALL')";
				}
				if(((b[last].toString()).contains("qadb-samd-auditor_users"))||((b[last].toString()).contains("qadb-samd-admin_users"))){
					userGrp = "('SAMMD','ALL')";
				}
				if(((b[last].toString()).contains("qadb-cd-auditor_users"))||((b[last].toString()).contains("qadb-cd-admin_users"))){
					userGrp = "('CD','ALL')";
				}
		}		*/   
		   
		logger.debug("User Group : "+b+" "+userGrp);
		
    	logger.debug(" search basic associate id "+ basicSearchTO.getAssociateName());
    	mv.addObject("basicSearch", basicSearchTO);
    	basicSearchTO.setSearchType("BASIC");
    	basicSearchTO.setUserGroup(userGrp);
    	
    	List<AuditSearch> searchResult = null;
    	try {
    		searchResult = auditSearchDao.getSearchResults(basicSearchTO);
		} catch (Exception e) {
			logger.debug("Exception generated = "+e.getMessage());
		}
    	mv.addObject("searchResult",searchResult);
    	logger.debug("*** Exit getSearchBasic method ***");
    	
    	return mv;
   	}
    
    /**
     * Advance search page
     * @param model
     * @throws SQLException 
     */
    @ModelAttribute("searchAdvForm")
    public void searchValues(Map<String, Object> model,HttpServletRequest request) throws Exception,SQLException{
 	   
        String userGrp = authorizationService.getUserGroup(request);
 	   
 	    AuditSearch searchAdvForm = new AuditSearch();
    	model.put("searchAdvForm", searchAdvForm);
    	   
    	List<AuditDetails> processTypeList = auditDetailsDao.getProcessType();
        model.put("processTypes", processTypeList); 
        
        List<ErrorDetails> errorCodeList = errorDetailsDao.getErrorCodes("add",userGrp);
        model.put("errorCodes", errorCodeList);
         
        List<AuditDetails> piTypeList = auditDetailsDao.getPenaltyInterestType();
        model.put("piTypesL", piTypeList); 
        
        List<AuditDetails> priPGAList = auditDetailsDao.getPriPGA(userGrp);
        model.put("priPGALists", priPGAList); 
        
        List<Associate> claimProcessors = scoresReportsDAO.getClaimProcessors("ASSOCIATE","");
        model.put("claimProcessors", claimProcessors);
        
        List<Associate> facetsIds = scoresReportsDAO.getFacetsIds();
        model.put("facetsIds", facetsIds);
        
        List<AuditDetails> auditorsList = auditDetailsDao.getAuditors(userGrp);
        model.put("auditorsList", auditorsList);
        
        Map<String,String> samples = new LinkedHashMap<String,String>();
        samples.put("ALL","All");
        samples.put("Y","Out Of Sample");
        samples.put("N","In Sample");
        model.put("samples", samples);
		Map<String,String> platforms = new LinkedHashMap<String,String>();
		platforms.put("ALL","All");
		platforms.put("Facets G6","Facets G6");
		platforms.put("Facets Legacy","Facets Legacy");
		model.put("platforms", platforms);
       
        Map<String,String> e2e = new LinkedHashMap<String,String>();
        e2e.put("ALL","All");
        e2e.put("Y","E2E");
        e2e.put("N","Non-E2E");
        model.put("e2e", e2e);
        
    }
    
    /**
     * 
     * @param model
     * @return
     */
    @RequestMapping(value = "/searchAdv", method = RequestMethod.GET)
   	public ModelAndView searchAdvance(Map<String, Object> model,HttpServletRequest request) {
    	ModelAndView modelAndView = null;
 	    modelAndView = authorizationService.AuthorizeUsersAudit("audit_search_advance", request);
    	return modelAndView;
   	}
    
    /**
     * Method to fetch advanced search results
     * @param advSearchForm
     * @return
     */
    @RequestMapping(value = "/searchAdvRes", method = RequestMethod.GET)
   	public ModelAndView getSearchAdv(@ModelAttribute("searchAdvForm") AuditSearch advSearchForm,HttpServletRequest request) throws Exception {
    	logger.debug("*** Entry AdvanceSearchResults method ***");
		ModelAndView mv = new ModelAndView("audit_search_advance");
		
        String userGrp = authorizationService.getUserGroup(request);

    	logger.debug("Advance search Associate input : "+ advSearchForm.getAssociateName());
    	mv.addObject("advSearch", advSearchForm);
    	advSearchForm.setSearchType("ADV");
    	advSearchForm.setUserGroup(userGrp);
    	
    	List<AuditSearch> searchResult = null;
    	try {
    		searchResult = auditSearchDao.getSearchResults(advSearchForm);
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
    	mv.addObject("advSearchResult",searchResult);
    	logger.debug("*** Exit AdvanceSearchResults method ***");
    	return mv;
    } 
   
   /**
    * Edit General Method
    * @param id
    * @param dcn
    * @param request
    * @return
    */
    @RequestMapping(value = "/editAuditGeneral", method = RequestMethod.GET, params = {"id","dcnNo"})
    public ModelAndView editAuditGeneral(@RequestParam(value="id") String id,@RequestParam(value="dcnNo") String dcn,HttpServletRequest request) throws Exception {
    	logger.debug("*** Entry editAuditGeneral method ***");
    	ModelAndView mv = new ModelAndView("audit");
    	mv.addObject("edit","edit");
    	AuditSearch auditSearchTO = new AuditSearch();
    	auditSearchTO.setSearchType("EDIT_AUDIT");
    	auditSearchTO.setPageType("GNRL");
    	auditSearchTO.setAuditId(id);
    	auditSearchTO.setDcn(dcn);
    	AuditCounts auditCountsTO = new AuditCounts();
    	HttpSession session = request.getSession(true);
    	String userId=(String) session.getAttribute(QADBConstants.USERNAME);
    	logger.debug("User id "+userId);
    	
    	String actionType= request.getParameter("actionType");
    	logger.debug("Action type "+actionType);
    	
    	AuditSave audGenRS = null;
    	AuditCounts audCountRS = null;
    	Associate assocEditDetails = null;
    	//int inSampleCount=0;
    	try {
    		 audGenRS = auditSearchDao.getEditAudit(auditSearchTO);
    		 auditCountsTO.setFacetsId(audGenRS.getAssociateFacetsId());
    		 auditCountsTO.setInpProccessDate(audGenRS.getProcessDate());
    		 audCountRS =  auditSearchDao.getAuditCounts(auditCountsTO);
    		 logger.debug("get facets id "+audGenRS.getAssociateFacetsId());
    		 Associate assoDetTO = new Associate();
    	     assoDetTO.setFacetsId(audGenRS.getAssociateFacetsId());
    		 assocEditDetails = auditDetailsDao.getAssociateDetails(assoDetTO);
    		// inSampleCount =auditDetailsDao.getInSampleCount(assocEditDetails.getAssociateDBId());
    		 assocEditDetails.setFacetsId(audGenRS.getAssociateFacetsId());
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
    	logger.debug("inSampleCount "+audCountRS.getInsCount());
    	//mv.addObject("inSampleCount",inSampleCount);
    	mv.addObject("inSampleCount",audCountRS.getInsCount());
    	mv.addObject("assocEditDetails",assocEditDetails);
    	mv.addObject("audCountRS",audCountRS);
    	//mv.addObject("editRS",audGenRS );
    	mv.addObject("eDCN",audGenRS.getDcn() );
    	mv.addObject("eAudId",id);
    	mv.addObject("eClaimType",audGenRS.getClaimType() );
    	mv.addObject("ePlatform",audGenRS.getPlatform() );    	
    	mv.addObject("eLob",audGenRS.getLob() );
    	mv.addObject("eMemId",audGenRS.getMemberId() );
    	mv.addObject("eProDate",audGenRS.getProcessDate());
    	mv.addObject("ePaidDate",audGenRS.getPaidDate() );
    	mv.addObject("eTotalCharge",audGenRS.getTotalCharge() );
    	mv.addObject("ePaid",audGenRS.getPaid() );
    	logger.debug("MonetaryError "+audGenRS.getMonetaryError());
    	if((null!=audGenRS.getMonetaryError())){
    		if((audGenRS.getMonetaryError()).equals("U")){
        		mv.addObject("eMonErrorU",audGenRS.getMonetaryError());
        	}
        	else if((audGenRS.getMonetaryError()).equals("N")){
        		mv.addObject("eMonErrorN",audGenRS.getMonetaryError());
        	}
        	else{
        		mv.addObject("eMonErrorO",audGenRS.getMonetaryError());
        	}
    	}
    	mv.addObject("eAmount",audGenRS.getAmountp());
    	mv.addObject("eTheoPaid",audGenRS.getTheoreticalPaid());
    	logger.debug("PiChk "+audGenRS.getPiChk());
    	if((null!=audGenRS.getPiChk())){
	    	if((audGenRS.getPiChk()).equals("Y")){
	    		mv.addObject("ePiChk",audGenRS.getPiChk());
	    	}
    	}
    	logger.debug("eJuridiction "+audGenRS.getJurisdiction());
    	mv.addObject("eJuridiction",audGenRS.getJurisdiction() );
    	mv.addObject("ePItype",audGenRS.getPenaltyInterestType());
    	logger.debug("ePi"+audGenRS.getPi());
    	mv.addObject("ePi",audGenRS.getPi());
    	mv.addObject("eAudType",audGenRS.getAuditType());
    	mv.addObject("ePriPGA",audGenRS.getPriPGA());
    	logger.debug("ePG2 "+audGenRS.getSecPGA());
    	mv.addObject("eSecPGA",audGenRS.getSecPGA());
    	if((null!=audGenRS.getMock())){
	    	if((audGenRS.getMock()).equals("Y")){
	    		mv.addObject("eMock",audGenRS.getMock());
	    	}
    	}
    	if((null!=audGenRS.getOos())){
	    	if((audGenRS.getOos()).equals("Y")){
	    		mv.addObject("eOos",audGenRS.getOos());
	    	}
    	}
    	if((null!=audGenRS.getE2e())){
	    	if((audGenRS.getE2e()).equals("Y")){
	    		mv.addObject("eE2e",audGenRS.getE2e());
	    	}
    	}
    	if((null!=audGenRS.getRiskAccount())){
	    	if((audGenRS.getRiskAccount()).equals("N")){
	    		mv.addObject("eRiscAcc",audGenRS.getRiskAccount());
	    	}
    	}
    	mv.addObject("eProcType",audGenRS.getProcesstype());
    	
    	 /*List<AuditDetails> processTypeList = auditDetailsDao.getProcessType();
         mv.addObject("processTypes", processTypeList); 
         Iterator<AuditDetails> processTypeIterator = processTypeList.iterator();*/
 		
    	
    	logger.debug("eProcType process type");
    	Pattern regex=Pattern.compile("(.*)([0]+$)(.*)");
    	
    	//Pattern regex=Pattern.compile("/[0]+$/");
    	logger.debug("Pattern on 08/08/2018 by aac2838"+Pattern.compile("(.*)([0]+$)(.*)"));
    	
    	 Matcher m = regex.matcher(dcn);
    	 /*logger.debug("dcnnnnnn"+(dcn));
    	 logger.debug("MAtcher"+regex.matcher(dcn));*/
   //  boolean asd=m.find();
   //  logger.debug("Finder"+m.find());
    //	logger.debug("Found in"+m.group(0));
    	if(m.matches( ))
    	{
    		logger.debug("disableDropdown process type");
    		mv.addObject("disableDropdown","true");
    		
    		/*while (processTypeIterator.hasNext())
    		{
     			logger.debug("processTypeList "+processTypeIterator.next().getProcesstype());
     			

     		}*/
        }
    		
    	
    	if(null != (audGenRS.getAdjustment())){
    		if((audGenRS.getAdjustment()).equals("E")){
        		mv.addObject("eAdjustment",audGenRS.getAdjustment());
        	}
    	}
    	
    	mv.addObject("eFyi",audGenRS.getFyi());
    	mv.addObject("eSub",audGenRS.getSubscriberCK());
    	mv.addObject("eAccID",audGenRS.getAccountID());
    	mv.addObject("eAccName",audGenRS.getAccountName());
    	mv.addObject("eJuri",audGenRS.getJurisdiction());
    	mv.addObject("eGrp",audGenRS.getGroup());
    	mv.addObject("eStatus",audGenRS.getClaimCurrentStatus());
    	mv.addObject("ePrId",audGenRS.getProductID());
    	logger.debug("reason code "+audGenRS.getAdjustmentReasoncode());
    	mv.addObject("eAdjustmentReasoncode",audGenRS.getAdjustmentReasoncode());
    	//Restrict user to audit a claim related to the same group
    	String b[] = {request.getHeader("iv-groups")};
		if((!(((Arrays.toString(b)).replace("[", "")).replace("]", "")).equals("null"))||(b.length != 0)){
			if(((Arrays.toString(b)).contains("qadb-samd-admin_users"))||((Arrays.toString(b)).contains("qadb-samd-auditor_users"))){
				if((audGenRS.getGroup().equalsIgnoreCase("cd")) &&
						!((Arrays.toString(b)).contains("qadb-cd-admin_users")||((Arrays.toString(b)).contains("qadb-cd-auditor_users")))){
	    			return new ModelAndView("audit_general","messageGroup","You are not authorized to audit CD group claim");
	    		}
			}
			else if(((Arrays.toString(b)).contains("qadb-cd-admin_users"))||((Arrays.toString(b)).contains("qadb-cd-auditor_users"))){
				if((audGenRS.getGroup().equalsIgnoreCase("sammd")) &&
						!((Arrays.toString(b)).contains("qadb-samd-admin_users")||((Arrays.toString(b)).contains("qadb-samd-auditor_users")))){
	    			return new ModelAndView("audit_general","messageGroup","You are not authorized to audit SamMD group claim");
	    		}
			}else if(((Arrays.toString(b)).contains("qadb-superadmin_users"))){
				
			}
		}else{
			return new ModelAndView("audit","messageGroup","You are not authorized to audit this claim");
		}
    	
		logger.debug("Examiner Name "+audGenRS.getExaminerName());
		
		if((null != (audGenRS.getExaminerName())) && (!(audGenRS.getExaminerName()).equals(" "))){
			AuditDetails editAuditDetails = new AuditDetails();
			editAuditDetails.setClaimAdjFlag(audGenRS.getClaimAdjFlag());
			editAuditDetails.setServiceAdjFlag(audGenRS.getServiceAdjFlag());
			editAuditDetails.setExaminerName(audGenRS.getExaminerName());
			editAuditDetails.setQAName(audGenRS.getQAName());
			editAuditDetails.setPatientName(audGenRS.getPatientName());
			editAuditDetails.setServiceDates(audGenRS.getServiceDates());
			editAuditDetails.setTypeOfService(audGenRS.getTypeOfService());
			editAuditDetails.setDiagnosis(audGenRS.getDiagnosis());
			editAuditDetails.setSurgeryDOS(audGenRS.getSurgeryDOS());
			editAuditDetails.setSurgery(audGenRS.getSurgery());
			editAuditDetails.setFileReferenced(audGenRS.getFileReferenced());
			logger.debug("interest RC "+audGenRS.getInterestPaid());
			editAuditDetails.setInterestPaid(audGenRS.getInterestPaid());
			editAuditDetails.setProviderName(audGenRS.getProviderName());
			editAuditDetails.setProviderNumber(audGenRS.getProviderNumber());
			editAuditDetails.setPayee(audGenRS.getPayee());
			editAuditDetails.setNotes(audGenRS.getNotes());
			editAuditDetails.setAudSignDate(audGenRS.getAudSignDate());
			editAuditDetails.setVpSignDate(audGenRS.getVpSignDate());
			editAuditDetails.setForwrdToDate(audGenRS.getForwrdToDate());
			editAuditDetails.setRcvedFrmDate(audGenRS.getRcvedFrmDate());
			editAuditDetails.setReleasedByDate(audGenRS.getReleasedByDate());
			
			mv.addObject("editAuditDetails", editAuditDetails);
		}
		
		
    	List<Products> productsEO = audGenRS.getProductInfo();
    	
    	mv.addObject("eProducts",productsEO);
    	
    	mv.addObject("savedBy",audGenRS.getLastSavedBy());
    	mv.addObject("savedOn",audGenRS.getLastSavedOn());
    	
    	//auditSearchDao.getEditAudit(id);
    	HttpSession sessionE = request.getSession(true);
    	sessionE.setAttribute("assocEditDetails",assocEditDetails);
    	sessionE.setAttribute("ePi",audGenRS.getPi());
    	sessionE.setAttribute("paramAuditId", id);
    	sessionE.setAttribute("paramDcn", dcn);
    	sessionE.setAttribute("efacetsId", audGenRS.getAssociateFacetsId());
    	sessionE.setAttribute("audCountRS",audCountRS);
    	sessionE.setAttribute("savedByS",audGenRS.getLastSavedBy());
    	sessionE.setAttribute("savedOnS",audGenRS.getLastSavedOn());
    	sessionE.setAttribute("editExaminerName",audGenRS.getExaminerName());
    	logger.debug("paramAuditId "+sessionE.getAttribute("paramAuditId")+ " "+sessionE.getAttribute("paramDcn"));
    	
    	/*get statistics data*/
    	List<Statistics> stats = statistics(id, dcn);
    	if(stats.isEmpty()){
    		mv.addObject("statsLast", "--");
        	mv.addObject("statsCreated", "--");
        	mv.addObject("stat", "--");
    	}else{
    		Statistics statsCreated = stats.get(stats.size() - 1);
         	Statistics statsLast = stats.get(0);
         	mv.addObject("statsLast", statsLast);
        	mv.addObject("statsCreated", statsCreated);
        	mv.addObject("stat", stats);
    	}
    	
    	logger.debug("*** Exit editAuditGeneral method ***");
    	return mv;
    }
    
    /**
     * 
     * @param model
     * @param session
     * @param audGenDetails
     * @param redirectAttributes
     * @return
     * @throws SQLException 
     */
    @RequestMapping(value = "/editAuditError", method = RequestMethod.POST)
   	public @ResponseBody ModelAndView editError(Map<String, Object> model,HttpSession session,@ModelAttribute("auditForm") AuditDetails audGenDetails, RedirectAttributes redirectAttributes,HttpServletRequest request) throws Exception,SQLException {
    	logger.debug("*** Entry editError method ***");
        ModelAndView mv = new ModelAndView("audit_errors");
        mv.addObject("edit","edit");
    	
        ErrorDetails errorForm = new ErrorDetails();
        mv.addObject("errorForm", errorForm);
        
        String userGrp =  (String) session.getAttribute("userGrp");
        logger.debug("Edit error group "+userGrp);
        
        List<ErrorDetails> errorCodeList = errorDetailsDao.getErrorCodes("edit",userGrp);
        model.put("errorCodes", errorCodeList);
    	
        List<ErrorDetails> specialityList = errorDetailsDao.getSpeciality(userGrp);
        model.put("speciality", specialityList);
    	
        List<ErrorDetails> rootCauseList = errorDetailsDao.getRootCause(userGrp);
        model.put("rootCause", rootCauseList);

        /*List<ErrorDetails> errorTypeList = errorDetailsDao.getErrorTypes();
	    model.put("errorTypes", errorTypeList);*/
        
        AuditSearch auditSearchTO = new AuditSearch();
    	auditSearchTO.setSearchType("EDIT_AUDIT");
    	auditSearchTO.setPageType("ERR");
    	auditSearchTO.setAuditId((String) session.getAttribute("paramAuditId"));
    	auditSearchTO.setDcn((String) session.getAttribute("paramDcn"));
    	
    	logger.debug("Audit id "+auditSearchTO.getAuditId()+" dcn "+auditSearchTO.getDcn());
    	
    	AuditSave audGenErrRS = auditSearchDao.getEditAudit(auditSearchTO);
    	String auditId = (String) session.getAttribute("paramAuditId");
    	String dcn = (String) session.getAttribute("paramDcn");
    	logger.debug(" paramAuditId id" +auditId+" Dcn "+dcn);
    	
    	mv.addObject("assocEditDetails",(Associate) session.getAttribute("assocEditDetails"));
    	
    	List<ErrorDetails> errorsRowsRecv = errorDetailsDao.getErrorInfo(auditId);
    	
    	if(errorsRowsRecv.size() > 0){
    		mv.addObject("errorsRowsRecv", errorsRowsRecv);
    	}
    	
    	mv.addObject("eErrTypeId",audGenErrRS.getErrorType());
    	mv.addObject("eSop",audGenErrRS.getSop());
    	mv.addObject("eReason",audGenErrRS.getReason());
    	mv.addObject("eComments",audGenErrRS.getComments());
    	
    	if(null != (audGenErrRS.getRequired())){
    		if((audGenErrRS.getRequired()).equals("Y")){
        		mv.addObject("eRequired",audGenErrRS.getRequired());
        	}
    	}
    	if(null != (audGenErrRS.getCompleted())){
    		if((audGenErrRS.getCompleted()).equals("Y")){
    		mv.addObject("eComplted",audGenErrRS.getCompleted());
    		}
    	}
    	if(null != (audGenErrRS.getDateAdj())){
    		mv.addObject("eDateAdj",audGenErrRS.getDateAdj());
    	}
    	if(null != (audGenErrRS.getAppeal())){
    		if((audGenErrRS.getAppeal()).equals("Y")){
    		mv.addObject("eAppeal",audGenErrRS.getAppeal());	
    		}
    	}
        
    	logger.debug("*** mon *** "+audGenDetails.getMonetaryError());
    	mv.addObject("monetaryError",audGenDetails.getMonetaryError());
    	mv.addObject("eDCN",(String) session.getAttribute("paramDcn") );
    	AuditCounts audCountRS = (AuditCounts) session.getAttribute("audCountRS");
    	mv.addObject("audCountRS",audCountRS );
    	mv.addObject("eAudId",auditId);
    	mv.addObject("savedBy",(String) session.getAttribute("savedByS"));
    	mv.addObject("savedOn",(String) session.getAttribute("savedOnS"));
    	mv.addObject("editAuditGenInfo",audGenDetails);
    	HttpSession sessionE = request.getSession(true);
    	
    	logger.debug("Error Pi details "+request.getParameter("piCalculated") + " "+ audGenDetails.getPi()+" "+request.getParameter("ePi")+" "+session.getAttribute("ePi"));
    	if(null != request.getParameter("piCalculated")){
        	audGenDetails.setPi(request.getParameter("piCalculated"));
        	sessionE.setAttribute("ePi",request.getParameter("piCalculated"));
    	}else{
    		audGenDetails.setPi(((String)session.getAttribute("ePi")));
    	}
    	
    	session.setAttribute("editAuditGenInfo", audGenDetails);
    	/*get statistics data*/
    	List<Statistics> stats = statistics(auditId, dcn);
    	Statistics statsCreated = stats.get(stats.size() - 1);
     	Statistics statsLast = stats.get(0);
     	mv.addObject("statsLast", statsLast);
    	mv.addObject("statsCreated", statsCreated);
    	mv.addObject("stat", stats);
    	logger.debug("*** Exit editError method ***"); 
    	
    	//edit retain
    	try{
    		
    		logger.debug("Audit update retain details : "+request.getParameter("userAction")+" "+request.getParameter("numberOfUsers")+" "+request.getParameter("dcn")+ " "+audGenDetails.getClaimType() );
    		logger.debug("Edit AuditId "+auditSearchTO.getAuditId());
    		
    		mv.addObject("auditId",auditSearchTO.getAuditId());
    		mv.addObject("efacetsId",session.getAttribute("efacetsId"));
    		mv.addObject("retainAudit",request.getParameter("userAction"));
    		mv.addObject("numberOfUsers",request.getParameter("numberOfUsers"));
    		mv.addObject("dcnNumber",request.getParameter("dcn"));
    		mv.addObject("claimType",audGenDetails.getClaimType());
    		mv.addObject("lob",request.getParameter("lob"));
    		mv.addObject("memberId",request.getParameter("memberId"));
    		mv.addObject("processDate",request.getParameter("processDate"));
    		mv.addObject("paidDate",request.getParameter("paidDate"));
    		mv.addObject("totalCharge",request.getParameter("totalCharge"));
    		mv.addObject("paid",request.getParameter("paid"));
    		mv.addObject("monetaryError",audGenDetails.getMonetaryError());
    		mv.addObject("amountp", audGenDetails.getAmountp());    			
    		mv.addObject("theoreticalPaid",audGenDetails.getTheoreticalPaid());
    		mv.addObject("isChk",request.getParameter("piChk"));
    		mv.addObject("penalityInterest",request.getParameter("penaltyInterestType"));
    		mv.addObject("piCalculated",audGenDetails.getPi());
    		mv.addObject("auditType",request.getParameter("auditType"));
    		mv.addObject("priPGA",request.getParameter("priPGA"));
    		mv.addObject("secPGA",request.getParameter("secPGA"));
    		mv.addObject("mock",audGenDetails.getMock());
    		mv.addObject("oos",audGenDetails.getOos());
    		mv.addObject("e2e",audGenDetails.getE2e());
    		mv.addObject("riskAccount",audGenDetails.getRiskAccount());
    		mv.addObject("processtype",request.getParameter("processtype"));
    		mv.addObject("adjustment",audGenDetails.getAdjustment());
    		mv.addObject("juridiction",request.getParameter("juridiction"));
    		mv.addObject("days",request.getParameter("days"));
    		mv.addObject("fyicheck",audGenDetails.getFyicheck());
    		mv.addObject("fyi",audGenDetails.getFyi());
    		//Brij
    		mv.addObject("platform",audGenDetails.getPlatform());
    		mv.addObject("subscriberCK",request.getParameter("subscriberCK"));
    		mv.addObject("AccountID",request.getParameter("AccountID"));
    		mv.addObject("AccountName",request.getParameter("AccountName"));
    		mv.addObject("Group",request.getParameter("Group"));
    		mv.addObject("claimCurrentStatus",request.getParameter("claimCurrentStatus"));
    		mv.addObject("productID",request.getParameter("productID"));
    		mv.addObject("adjustmentReasoncode",request.getParameter("adjustmentReasoncode"));
    		mv.addObject("productsList",audGenDetails.getProducts());
    		
    		/*High dollar details*/
    		logger.debug("High Dollar details "+audGenDetails.getExaminerName() + " analyst "+audGenDetails.getQAName()+"s  "+session.getAttribute("editExaminerName")); 
    		mv.addObject("claimAdjFlag",audGenDetails.getClaimAdjFlag());
    		mv.addObject("serviceAdjFlag",audGenDetails.getServiceAdjFlag());
    		mv.addObject("examinerName",session.getAttribute("editExaminerName"));
    		mv.addObject("QAName",audGenDetails.getQAName());
    		mv.addObject("patientName",audGenDetails.getPatientName());
    		mv.addObject("serviceDates",audGenDetails.getServiceDates());
    		mv.addObject("typeOfService",audGenDetails.getTypeOfService());
    		mv.addObject("diagnosis",audGenDetails.getDiagnosis());
    		mv.addObject("surgeryDOS",audGenDetails.getSurgeryDOS());
    		mv.addObject("surgery",audGenDetails.getSurgery());
    		mv.addObject("fileRef",audGenDetails.getFileReferenced());
    		mv.addObject("interestPaid",audGenDetails.getInterestPaid());
    		mv.addObject("providerName",audGenDetails.getProviderName());
    		mv.addObject("providerNumber",audGenDetails.getProviderNumber());
    		mv.addObject("payee",audGenDetails.getPayee());
    		mv.addObject("notes",audGenDetails.getNotes());
    		mv.addObject("audSignDate",audGenDetails.getAudSignDate());
    		mv.addObject("vpSignDate",audGenDetails.getVpSignDate());
    		mv.addObject("forwardToDate",audGenDetails.getForwrdToDate());
    		mv.addObject("rcvedFrmDate",audGenDetails.getRcvedFrmDate());
    		mv.addObject("releasedByDate",audGenDetails.getReleasedByDate());
    	}catch(Exception e){
    		logger.error("Exception generated= "+e.getMessage());
    	}
    	logger.debug("*** Entry editError method ***");
   		return mv;
   	}
    
    /**
     * Method to update an audit
     * @param model
     * @param session
     * @param euser
     * @param redirectAttributes
     * @return
     */
    @RequestMapping(value = "/High_Dollar_Report_", method = RequestMethod.GET)
   	public ModelAndView auditUpdate(Map<String, Object> model,HttpSession session,@ModelAttribute("errorForm") ErrorDetails euser,RedirectAttributes redirectAttributes,HttpServletRequest request) throws Exception {
    	logger.debug("*** Entry auditUpdate method ***");
    	AuditDetails editAuditGenInfo = (AuditDetails) session.getAttribute("editAuditGenInfo");
    	AuditSave audUpdate = new AuditSave();
    	//String userId=(String) session.getAttribute(QADBConstants.USERNAME);
    	String userId = request.getHeader("givenname")+ " "+ request.getHeader("sn");
    	List<String> monetarys = new ArrayList<String>();
    	List<String> procedurals = new ArrayList<String>();
    	
    	for(int i=0;i<euser.getErrorCategory().size();i++){
    		if ((euser.getErrorCategory().get(i).equalsIgnoreCase("p")) || (euser.getErrorCategory().get(i).equalsIgnoreCase("n")) ){
    			procedurals.add("Y");
    			monetarys.add("N");
    		}else{
    			procedurals.add("N");
    			monetarys.add("Y");
    		}
    	}
    	euser.setMonetarys(monetarys);
    	euser.setProcedurals(procedurals);
		audUpdate.setUserid(((userId).toString()).replaceAll("\\%20", " "));
		audUpdate.setUserActyp("EDIT");
		logger.debug("update audit id "+editAuditGenInfo.getAuditId());
		audUpdate.setAuditId((String) session.getAttribute("paramAuditId")); 
		logger.debug((String) session.getAttribute("paramAuditId"));
		logger.debug("update facets id "+session.getAttribute("efacetsId"));
		String facetsId=(String) session.getAttribute("efacetsId");
		
		audUpdate.setAssociID(facetsId);
		audUpdate.setDcn(editAuditGenInfo.getDcn()) ;
		audUpdate.setClaimType(editAuditGenInfo.getClaimType());
		audUpdate.setLob(editAuditGenInfo.getLob());
		audUpdate.setMemberId(editAuditGenInfo.getMemberId());
		audUpdate.setProcessDate(editAuditGenInfo.getProcessDate());
		audUpdate.setPaidDate(editAuditGenInfo.getPaidDate());
		audUpdate.setTotalCharge(editAuditGenInfo.getTotalCharge());
		audUpdate.setPaid(editAuditGenInfo.getPaid());
    	
		logger.debug("Update Inputs 1 : "+editAuditGenInfo.getDcn()+" "+editAuditGenInfo.getClaimType()+ " "+editAuditGenInfo.getLob()+" "+editAuditGenInfo.getMemberId()+ " "+editAuditGenInfo.getProcessDate()+" "+editAuditGenInfo.getPaidDate()+" "+editAuditGenInfo.getTotalCharge()+ " "+editAuditGenInfo.getPaid());
		
		audUpdate.setJurisdiction( (String) session.getAttribute("jurisdiction"));
		audUpdate.setMonetaryError( editAuditGenInfo.getMonetaryError());
		audUpdate.setAmountp(editAuditGenInfo.getAmountp());
		audUpdate.setTheoreticalPaid(editAuditGenInfo.getTheoreticalPaid());
		audUpdate.setPiChk(editAuditGenInfo.getPiChk());
		audUpdate.setPenaltyInterestType(editAuditGenInfo.getPenaltyInterestType());
		audUpdate.setPi(editAuditGenInfo.getPi());
		
		logger.debug("Update Inputs 2 : "+audUpdate.getJurisdiction()+" "+editAuditGenInfo.getMonetaryError()+ " "+editAuditGenInfo.getAmountp()+" "+editAuditGenInfo.getTheoreticalPaid()+ " "+editAuditGenInfo.getPiChk()+" "+editAuditGenInfo.getPenaltyInterestType()+ " "+editAuditGenInfo.getPi());
		
		audUpdate.setAuditType(editAuditGenInfo.getAuditType());
		audUpdate.setPriPGA(editAuditGenInfo.getPriPGA());
		audUpdate.setSecPGA(editAuditGenInfo.getSecPGA());
		audUpdate.setMock(editAuditGenInfo.getMock());
		audUpdate.setOos(editAuditGenInfo.getOos());
		audUpdate.setE2e(editAuditGenInfo.getE2e());
		audUpdate.setRiskAccount(editAuditGenInfo.getRiskAccount());
		audUpdate.setProcesstype(editAuditGenInfo.getProcesstype());
		audUpdate.setAdjustment(editAuditGenInfo.getAdjustment());
		audUpdate.setFyiChk(editAuditGenInfo.getFyicheck());
		audUpdate.setFyi(editAuditGenInfo.getFyi());
		//Birj
		audUpdate.setPlatform(editAuditGenInfo.getPlatform());
		
		logger.debug("Update Inputs 3 : "+editAuditGenInfo.getAuditType()+" "+editAuditGenInfo.getPriPGA()+ " "+editAuditGenInfo.getSecPGA()+" "+editAuditGenInfo.getMock()+ " "+editAuditGenInfo.getOos()+" "+editAuditGenInfo.getE2e()+ " "+editAuditGenInfo.getRiskAccount()+" "+editAuditGenInfo.getProcesstype() +" "+editAuditGenInfo.getAdjustment()+" "+editAuditGenInfo.getFyi());
		logger.debug("Examiner name from session "+(String) session.getAttribute("editExaminerName") + "Object ex name "+editAuditGenInfo.getExaminerName());
		audUpdate.setClaimAdjFlag(editAuditGenInfo.getClaimAdjFlag());
		audUpdate.setServiceAdjFlag(editAuditGenInfo.getServiceAdjFlag());
		if(Double.valueOf((editAuditGenInfo.getPaid())) > 9999.99){
			if(null != ((String) session.getAttribute("editExaminerName"))){
				audUpdate.setExaminerName((String) session.getAttribute("editExaminerName"));
			}else{
				audUpdate.setExaminerName(facetsId);
			}
		}else{
			audUpdate.setExaminerName("");
		}
		
		audUpdate.setQAName(editAuditGenInfo.getQAName());
		audUpdate.setPatientName(editAuditGenInfo.getPatientName());
		audUpdate.setServiceDates(editAuditGenInfo.getServiceDates());
		audUpdate.setTypeOfService(editAuditGenInfo.getTypeOfService());
		audUpdate.setDiagnosis(editAuditGenInfo.getDiagnosis());
		audUpdate.setSurgeryDOS(editAuditGenInfo.getSurgeryDOS());
		audUpdate.setSurgery(editAuditGenInfo.getSurgery());
		audUpdate.setFileReferenced(editAuditGenInfo.getFileReferenced());
		audUpdate.setInterestPaid(editAuditGenInfo.getInterestPaid());
		audUpdate.setProviderName(editAuditGenInfo.getProviderName());
		audUpdate.setProviderNumber(editAuditGenInfo.getProviderNumber());
		audUpdate.setPayee(editAuditGenInfo.getPayee());
		audUpdate.setNotes(editAuditGenInfo.getNotes());
		audUpdate.setAudSignDate(editAuditGenInfo.getAudSignDate());
		audUpdate.setVpSignDate(editAuditGenInfo.getVpSignDate());
		audUpdate.setForwrdToDate(editAuditGenInfo.getForwrdToDate());
		audUpdate.setRcvedFrmDate(editAuditGenInfo.getRcvedFrmDate());
		audUpdate.setReleasedByDate(editAuditGenInfo.getReleasedByDate());
		
//		audUpdate.setErrorInfo(euser.getErrorInfo()); 
		audUpdate.setErrorCodesId(euser.getErrorCodesId());
		audUpdate.setMonetarys(euser.getMonetarys());
		audUpdate.setProcedurals(euser.getProcedurals());
		audUpdate.setSpecialitysId(euser.getSpecialitysId());
		audUpdate.setRootCausesId(euser.getRootCausesId());
		audUpdate.setEditCodesId(euser.getEditCodesId());
		
		logger.debug("Update Inputs Error 1 : "+euser.getErrorCodesId()+" "+euser.getMonetarys()+" "+euser.getMonetarys()+" "+euser.getProcedurals()+" "+euser.getSpecialitysId()+" "+euser.getRootCausesId()+" "+euser.getEditCodesId());
		
		audUpdate.setErrorType(euser.getErrorType());
		audUpdate.setSop(euser.getSop());
		audUpdate.setReason(euser.getReason());
		audUpdate.setComments(euser.getComments());
		audUpdate.setRequired(euser.getRequired());
		audUpdate.setCompleted(euser.getCompleted());
		audUpdate.setDateAdj(euser.getDateAdj());
		audUpdate.setAppeal(euser.getAppeal());
    	
		logger.debug("verbiage issue get reason for error=====Debug 0n 08/08/2018"+euser.getReason());
		
		AuditSave audSaves = null;
		try {
			audSaves = auditDetailsDao.saveAudit(audUpdate);
		} catch (SecurityException e) {
    		logger.error("Exception generated= "+e.getMessage());
			e.printStackTrace();
			
		} catch (NoSuchMethodException e) {
    		logger.error("Exception generated= "+e.getMessage());
			e.printStackTrace();
		}
		
		logger.debug("Success code = "+audSaves.getSucessCode());
		logger.debug("Success message "+audSaves.getSuccessMsg());
		redirectAttributes.addFlashAttribute("upSucess", audSaves.getSuccessMsg());
		redirectAttributes.addFlashAttribute("updateDcn", audUpdate.getDcn());

		logger.debug("Examiner name = "+editAuditGenInfo.getQAName()+" Audit id "+audUpdate.getAuditId()+" paid "+audUpdate.getPaid());
		ModelAndView mv = new ModelAndView("redirect:audit");

		if(((null != audUpdate.getQAName()) && (!(audUpdate.getQAName()).equalsIgnoreCase(""))) && ((null != audUpdate.getPaid()) && ((Double.valueOf(audUpdate.getPaid())) > 9999.99))){
			if(null !=(audUpdate.getAuditId())){
				mv = getHighDollarReport(audUpdate.getAuditId());
			}
			else{
				mv = new ModelAndView("redirect:audit");
			}
			
		}
		logger.debug("*** Exit auditUpdate method ***");
		return mv;
    }
    
    /**
   	 * Method to delete the  Audit
   	 * @param associateDetails
   	 * @param redirectAttributes
   	 * @param session
   	 * @return
   	 */
   	@RequestMapping(value = "/deleteAudit", method = RequestMethod.GET)
   	public ModelAndView deleteAudit(RedirectAttributes redirectAttributes,HttpSession session,HttpServletRequest request) throws Exception {
   		logger.debug("*** Entry deleteAudit method ***");
   		AuditSave auditIdDetails = new AuditSave();
   		String dcn = (String) session.getAttribute("paramDcn");
   		logger.debug("AuditId "+(String) session.getAttribute("paramAuditId")+" DCN "+dcn);
   		auditIdDetails.setAuditId((String) session.getAttribute("paramAuditId"));
   		ModelAndView mv = null;
 	    mv = authorizationService.AuthorizeUsersAudit("redirect:audit", request);
   		AuditSave audDelete;
   		try {
   			audDelete = auditDetailsDao.deleteAudit(auditIdDetails);
   			logger.debug("Sucess msg = " +audDelete.getSuccessMsg());
   			redirectAttributes.addFlashAttribute("delSucess", audDelete.getSuccessMsg());
   			redirectAttributes.addFlashAttribute("deleteDcn", dcn);
   		} catch (Exception e) {
   			logger.debug("Exception : "+e.getMessage());
   			e.printStackTrace();
   		}
   		logger.debug("*** Exit deleteAudit method ***");
   		return mv;
   	}
    
   	/**
   	 * 
   	 * @param model
   	 * @param session
   	 * @return
   	 */
    @RequestMapping(value = "/editAuditStats", method = RequestMethod.GET)
   	public ModelAndView statistics(Map<String, Object> model,HttpSession session) throws Exception {
    	logger.debug("*** Entry statistics method ***");
		ModelAndView mv = new ModelAndView("audit_statistics");
		AuditSearch auditSearchTO = new AuditSearch();
     	
     	auditSearchTO.setSearchType("EDIT_AUDIT");
     	auditSearchTO.setPageType("STAT");
     	auditSearchTO.setAuditId((String) session.getAttribute("paramAuditId"));
     	auditSearchTO.setDcn((String) session.getAttribute("paramDcn"));
    	
     	List<Statistics> stats = null;
     	Statistics statsCreated = null;
     	Statistics statsLast = null;
    	try {
    		stats =  auditSearchDao.getStatastics(auditSearchTO);
    		statsCreated = stats.get(stats.size() - 1);
    		statsLast =  stats.get(0);
		} catch (Exception e) {
			e.printStackTrace();
			logger.debug("Exception generated"+e.getMessage());
		}
    	mv.addObject("edit","edit");
    	mv.addObject("eDCN",auditSearchTO.getDcn());
    	mv.addObject("eAudId",auditSearchTO.getAuditId());
    	//mv.addObject("savedBy",statsLast.getSavedBy());
    	//mv.addObject("savedOn",statsLast.getSavedTime());
    	mv.addObject("statsLast", statsLast);
    	mv.addObject("statsCreated", statsCreated);
    	mv.addObject("stat", stats);
    	//logger.debug("*** last stat *** "+statsLast.getSavedTime()+" "+statsLast.getSavedDate());
    	//logger.debug("*** cre stat *** "+statsCreated.getSavedTime()+" "+statsCreated.getSavedDate());
    	logger.debug("*** Exit statistics method ***");
    	return mv;
   	}
    
	@RequestMapping(value = "/AuditAssesmentReport", method = RequestMethod.GET)
	public ModelAndView auditAssesmentReportPDF(ModelAndView modelAndView,
			HttpSession session) {
		logger.debug("*** Entry AuditAssesmentReport method ***" );
		AuditAssessment auditAssessmentReport = new AuditAssessment();
		String userId = (String) session.getAttribute(QADBConstants.USERNAME);
		String assoId= (String) session.getAttribute("associateDBid");   
		String processDate= (String) session.getAttribute("processDate"); 
		logger.debug(" User id " + userId);
		logger.debug(" Associate DB id " + assoId + " Process date "+processDate);
		auditAssessmentReport.setAssociateId(assoId);
		auditAssessmentReport.setProcessDateIn(processDate);
		JRDataSource datasource = null;
		JRDataSource outSampleDatasource = null;
		JRDataSource inSampleDatasource = null;
		String sampleType = "";
		try {
			outSampleDatasource = auditDetailsDao.getAssesmentReport(auditAssessmentReport);
			logger.debug("Sample "+sampleType);
			auditAssessmentReport.setType("in");
			inSampleDatasource = auditDetailsDao.getAssesmentReport(auditAssessmentReport);
			sampleType = AuditDetailsDAOImpl.type;
			if(sampleType.equalsIgnoreCase("is")){
				logger.debug("Insample Report");
				datasource = inSampleDatasource;
			}
			else{
				datasource = outSampleDatasource;
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
		Map<String, Object> parameterMap = new HashMap<String, Object>();
		parameterMap.put("datasource", datasource);
		parameterMap.put("JasperCustomSubReportDatasource1", inSampleDatasource );
		parameterMap.put("JasperCustomSubReportDatasource2", outSampleDatasource);
		if(sampleType.equalsIgnoreCase("oos")){
			logger.debug("OOS assesment report only");
			modelAndView = new ModelAndView("auditAssesmentsOos", parameterMap); //single pdf
			}
		else{
			modelAndView = new ModelAndView("auditAssesments", parameterMap);
		}
		logger.debug("*** Exit AuditAssesmentReport method ***");
		return modelAndView;
	}
	
	private ModelAndView getHighDollarReport(String auditId) throws Exception {
		
		//High dollar report Generation
		logger.debug("Audit for HD "+auditId);
		JRDataSource datasource = null;
		try {
			datasource = auditSearchDao.getHighDollarReport(auditId);
		} catch (SQLException e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		Map<String, Object> parameterMap = new HashMap<String, Object>();
		parameterMap.put("datasource", datasource);
		ModelAndView mv = new ModelAndView("highDollar", parameterMap);
		return mv;
	}
	
	/*New statistics method*/
	public List<Statistics> statistics (String auditId,String dcn) throws Exception{
		logger.debug("*** Entry statisticsNew method ***"+auditId+" "+dcn);
		AuditSearch auditSearchTO = new AuditSearch();
     	
     	auditSearchTO.setSearchType("EDIT_AUDIT");
     	auditSearchTO.setPageType("STAT");
     	auditSearchTO.setAuditId(auditId);
     	auditSearchTO.setDcn(dcn);
    	
     	List<Statistics> stats = null;
    	try {
    		stats =  auditSearchDao.getStatastics(auditSearchTO);
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
    	logger.debug("*** Exit statisticsNew method ***");
		return stats;
		
	}
	
	
	/**
     * Method to fill the errorType drop down
     * @param request
     * @return
	 * @throws SQLException 
     */
    @RequestMapping(value = "/errorTypeDropDown", method = RequestMethod.GET)
  	public ModelAndView errorTypeDropDown(HttpServletRequest request) throws Exception,SQLException {
    	logger.debug("***errorTypeDropDown method start***");
    	
    	String rootCauseId = request.getParameter("subject");
    	ModelAndView mv = new ModelAndView("audit_error_errorTypeDropDown_Div");
	 	
		if((rootCauseId.equalsIgnoreCase(""))||(rootCauseId == "")){
		}else{
			List<ErrorDetails> errorTypeList = errorDetailsDao.getErrorTypes(rootCauseId);
		    mv.addObject("errorTypes", errorTypeList);
		}
	    
		logger.debug("***errorTypeDropDown method end***");
		return mv;
    }	
	
    /**
     * Method to fill the Secondary PGA drop down
     * @param request
     * @return
     * @throws SQLException 
     */
    @RequestMapping(value = "/secPGADropDown", method = RequestMethod.GET)
  	public ModelAndView secPGADropDown(HttpServletRequest request) throws Exception,SQLException {
    	logger.debug("***secPGADropDown method start***");
    	
    	String priPGAId = request.getParameter("priId");
    	logger.info("priPGAId--> "+priPGAId);
    	ModelAndView mv = new ModelAndView("audit_general_secPGADropDown_Div");
	 	
    	HttpSession session = request.getSession(true);
    	String userGrp =  (String) session.getAttribute("userGrp");
    	logger.debug("userGrp "+userGrp);
		if((priPGAId.equalsIgnoreCase(""))||(priPGAId == "")||(priPGAId.equalsIgnoreCase("0"))){
		}else{
			List<AuditDetails> secPGAList = auditDetailsDao.getSecPGA(userGrp,priPGAId);
			 mv.addObject("secPGAList", secPGAList);
		} 
	     
		logger.debug("***secPGADropDown method end***");
		return mv;
    }	
    
    
    /**
     * Method to calculate whether the current is a
     * Leap year or not
     * @return
     */
    public int calculateDays(){
    	
    	int year = Calendar.getInstance().get(Calendar.YEAR);
		int noOfDays;
		
		if(year % 4 == 0){
			if(year % 100 == 0){
				if(year % 400 == 0){
					 noOfDays = 366; //Leap year
				}
				else{
					noOfDays = 365; //Non-Leap year
				}
			}
			else{
				noOfDays = 366; //Leap year
			}
		}
		else{
			noOfDays = 365; //Non-Leap year
		}
		return noOfDays;
    }
    
}
