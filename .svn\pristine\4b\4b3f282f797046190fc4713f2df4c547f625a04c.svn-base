package com.carefirst.audit.model;

public class AuditAssessment {

	private String associateId;
	private String processDateIn;
	private String month;
	private String associateName;
	private String empNO;
	private String supervisor;
	private String reportType;//oos and is
	private String type;
	
	//Out of Sample
	private String oosSno;
	private String oosDcn;
	private String oosMemId;
	private String oosPlatForm;
	private String oosPaid;
	private String oosUnderPaid;
	private String oosOverPaid;
	private String oosHighDoller;
	private String oosAuditType;
	private String oosMockAudit;
	private String oosAdjRequired;
	private String oosProcessOn;
	private String oosErrorCode;
	private String oosMonetary;
	private String oosProcedural;
	private String oosErrorType;
	private String oosExplanation;
	private String oosFyi;
	private String oosSop;
	private String oosPriPerfGroup;
	private String oosSecPerfGroup;
	private String oosAuditor;
	private String oosAuditDate;
	private String oosTotalClaims;
	private String oosProcClaims;
	private String oosMonClaims;
	private String oosTotalPaid;
	private String oosTotalOvPaid;
	private String oosTotalUnPaid;
	
	//In-sample
	private String isSno;
	private String isDcn;
	private String isMemId;
	private String isPlatForm;
	private String isPaid;
	private String isUnderPaid;
	private String isOverPaid;
	private String isHighDoller;
	private String isAuditType;
	private String isMockAudit;
	private String isAdjRequired;
	private String isAdjustmentsMade;
	private String isProcessOn;
	private String isErrorCode;
	private String isMonetary;
	private String isProcedural;
	private String isErrorType;
	private String isExplanation;
	private String isFyi;
	private String isSop;
	private String isPriPerfGroup;
	private String isSecPerfGroup;
	private String isAuditor;
	private String isAuditDate;
	private String isTotalClaims;
	private String isProcClaims;
	private String isMonClaims;
	private String isTotalPaid;
	private String isTotalOvPaid;
	private String isTotalUnPaid;
	
	//Monetary
	private String title;
	private String timeframe;
	private String e2e;
	private String auditType;
	private String subType;
	
	private String successMsg;
	private String sucessCode;
	
	public String getOosFyi() {
		return oosFyi;
	}
	public void setOosFyi(String oosFyi) {
		this.oosFyi = oosFyi;
	}
	public String getIsFyi() {
		return isFyi;
	}
	public void setIsFyi(String isFyi) {
		this.isFyi = isFyi;
	}
	public String getIsAdjustmentsMade() {
		return isAdjustmentsMade;
	}
	public void setIsAdjustmentsMade(String isAdjustmentsMade) {
		this.isAdjustmentsMade = isAdjustmentsMade;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getE2e() {
		return e2e;
	}
	public void setE2e(String e2e) {
		this.e2e = e2e;
	}
	public String getAuditType() {
		return auditType;
	}
	public void setAuditType(String auditType) {
		this.auditType = auditType;
	}
	public String getSubType() {
		return subType;
	}
	public void setSubType(String subType) {
		this.subType = subType;
	}
	public String getTimeframe() {
		return timeframe;
	}
	public void setTimeframe(String timeframe) {
		this.timeframe = timeframe;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getAssociateId() {
		return associateId;
	}
	public void setAssociateId(String associateId) {
		this.associateId = associateId;
	}
	public String getProcessDateIn() {
		return processDateIn;
	}
	public void setProcessDateIn(String processDateIn) {
		this.processDateIn = processDateIn;
	}
	public String getMonth() {
		return month;
	}
	public void setMonth(String month) {
		this.month = month;
	}
	public String getAssociateName() {
		return associateName;
	}
	public void setAssociateName(String associateName) {
		this.associateName = associateName;
	}
	public String getEmpNO() {
		return empNO;
	}
	public void setEmpNO(String empNO) {
		this.empNO = empNO;
	}
	public String getSupervisor() {
		return supervisor;
	}
	public void setSupervisor(String supervisor) {
		this.supervisor = supervisor;
	}
	public String getReportType() {
		return reportType;
	}
	public void setReportType(String reportType) {
		this.reportType = reportType;
	}
	public String getOosSno() {
		return oosSno;
	}
	public void setOosSno(String oosSno) {
		this.oosSno = oosSno;
	}
	public String getOosDcn() {
		return oosDcn;
	}
	public void setOosDcn(String oosDcn) {
		this.oosDcn = oosDcn;
	}
	public String getOosMemId() {
		return oosMemId;
	}
	public void setOosMemId(String oosMemId) {
		this.oosMemId = oosMemId;
	}
	public String getOosPlatForm() {
		return oosPlatForm;
	}
	public void setOosPlatForm(String oosPlatForm) {
		this.oosPlatForm = oosPlatForm;
	}
	public String getOosPaid() {
		return oosPaid;
	}
	public void setOosPaid(String oosPaid) {
		this.oosPaid = oosPaid;
	}
	public String getOosUnderPaid() {
		return oosUnderPaid;
	}
	public void setOosUnderPaid(String oosUnderPaid) {
		this.oosUnderPaid = oosUnderPaid;
	}
	public String getOosOverPaid() {
		return oosOverPaid;
	}
	public void setOosOverPaid(String oosOverPaid) {
		this.oosOverPaid = oosOverPaid;
	}
	public String getOosHighDoller() {
		return oosHighDoller;
	}
	public void setOosHighDoller(String oosHighDoller) {
		this.oosHighDoller = oosHighDoller;
	}
	public String getOosAuditType() {
		return oosAuditType;
	}
	public void setOosAuditType(String oosAuditType) {
		this.oosAuditType = oosAuditType;
	}
	public String getOosMockAudit() {
		return oosMockAudit;
	}
	public void setOosMockAudit(String oosMockAudit) {
		this.oosMockAudit = oosMockAudit;
	}
	public String getOosAdjRequired() {
		return oosAdjRequired;
	}
	public void setOosAdjRequired(String oosAdjRequired) {
		this.oosAdjRequired = oosAdjRequired;
	}
	public String getOosProcessOn() {
		return oosProcessOn;
	}
	public void setOosProcessOn(String oosProcessOn) {
		this.oosProcessOn = oosProcessOn;
	}
	public String getOosErrorCode() {
		return oosErrorCode;
	}
	public void setOosErrorCode(String oosErrorCode) {
		this.oosErrorCode = oosErrorCode;
	}
	public String getOosMonetary() {
		return oosMonetary;
	}
	public void setOosMonetary(String oosMonetary) {
		this.oosMonetary = oosMonetary;
	}
	public String getOosProcedural() {
		return oosProcedural;
	}
	public void setOosProcedural(String oosProcedural) {
		this.oosProcedural = oosProcedural;
	}
	public String getOosErrorType() {
		return oosErrorType;
	}
	public void setOosErrorType(String oosErrorType) {
		this.oosErrorType = oosErrorType;
	}
	public String getOosExplanation() {
		return oosExplanation;
	}
	public void setOosExplanation(String oosExplanation) {
		this.oosExplanation = oosExplanation;
	}
	public String getOosSop() {
		return oosSop;
	}
	public void setOosSop(String oosSop) {
		this.oosSop = oosSop;
	}
	public String getOosPriPerfGroup() {
		return oosPriPerfGroup;
	}
	public void setOosPriPerfGroup(String oosPriPerfGroup) {
		this.oosPriPerfGroup = oosPriPerfGroup;
	}
	public String getOosSecPerfGroup() {
		return oosSecPerfGroup;
	}
	public void setOosSecPerfGroup(String oosSecPerfGroup) {
		this.oosSecPerfGroup = oosSecPerfGroup;
	}
	public String getOosAuditor() {
		return oosAuditor;
	}
	public void setOosAuditor(String oosAuditor) {
		this.oosAuditor = oosAuditor;
	}
	public String getOosAuditDate() {
		return oosAuditDate;
	}
	public void setOosAuditDate(String oosAuditDate) {
		this.oosAuditDate = oosAuditDate;
	}
	public String getOosTotalClaims() {
		return oosTotalClaims;
	}
	public void setOosTotalClaims(String oosTotalClaims) {
		this.oosTotalClaims = oosTotalClaims;
	}
	public String getOosProcClaims() {
		return oosProcClaims;
	}
	public void setOosProcClaims(String oosProcClaims) {
		this.oosProcClaims = oosProcClaims;
	}
	public String getOosMonClaims() {
		return oosMonClaims;
	}
	public void setOosMonClaims(String oosMonClaims) {
		this.oosMonClaims = oosMonClaims;
	}
	public String getOosTotalPaid() {
		return oosTotalPaid;
	}
	public void setOosTotalPaid(String oosTotalPaid) {
		this.oosTotalPaid = oosTotalPaid;
	}
	public String getOosTotalOvPaid() {
		return oosTotalOvPaid;
	}
	public void setOosTotalOvPaid(String oosTotalOvPaid) {
		this.oosTotalOvPaid = oosTotalOvPaid;
	}
	public String getOosTotalUnPaid() {
		return oosTotalUnPaid;
	}
	public void setOosTotalUnPaid(String oosTotalUnPaid) {
		this.oosTotalUnPaid = oosTotalUnPaid;
	}
	public String getIsSno() {
		return isSno;
	}
	public void setIsSno(String isSno) {
		this.isSno = isSno;
	}
	public String getIsDcn() {
		return isDcn;
	}
	public void setIsDcn(String isDcn) {
		this.isDcn = isDcn;
	}
	public String getIsMemId() {
		return isMemId;
	}
	public void setIsMemId(String isMemId) {
		this.isMemId = isMemId;
	}
	public String getIsPlatForm() {
		return isPlatForm;
	}
	public void setIsPlatForm(String isPlatForm) {
		this.isPlatForm = isPlatForm;
	}
	public String getIsPaid() {
		return isPaid;
	}
	public void setIsPaid(String isPaid) {
		this.isPaid = isPaid;
	}
	public String getIsUnderPaid() {
		return isUnderPaid;
	}
	public void setIsUnderPaid(String isUnderPaid) {
		this.isUnderPaid = isUnderPaid;
	}
	public String getIsOverPaid() {
		return isOverPaid;
	}
	public void setIsOverPaid(String isOverPaid) {
		this.isOverPaid = isOverPaid;
	}
	public String getIsHighDoller() {
		return isHighDoller;
	}
	public void setIsHighDoller(String isHighDoller) {
		this.isHighDoller = isHighDoller;
	}
	public String getIsAuditType() {
		return isAuditType;
	}
	public void setIsAuditType(String isAuditType) {
		this.isAuditType = isAuditType;
	}
	public String getIsMockAudit() {
		return isMockAudit;
	}
	public void setIsMockAudit(String isMockAudit) {
		this.isMockAudit = isMockAudit;
	}
	public String getIsAdjRequired() {
		return isAdjRequired;
	}
	public void setIsAdjRequired(String isAdjRequired) {
		this.isAdjRequired = isAdjRequired;
	}
	public String getIsProcessOn() {
		return isProcessOn;
	}
	public void setIsProcessOn(String isProcessOn) {
		this.isProcessOn = isProcessOn;
	}
	public String getIsErrorCode() {
		return isErrorCode;
	}
	public void setIsErrorCode(String isErrorCode) {
		this.isErrorCode = isErrorCode;
	}
	public String getIsMonetary() {
		return isMonetary;
	}
	public void setIsMonetary(String isMonetary) {
		this.isMonetary = isMonetary;
	}
	public String getIsProcedural() {
		return isProcedural;
	}
	public void setIsProcedural(String isProcedural) {
		this.isProcedural = isProcedural;
	}
	public String getIsErrorType() {
		return isErrorType;
	}
	public void setIsErrorType(String isErrorType) {
		this.isErrorType = isErrorType;
	}
	public String getIsExplanation() {
		return isExplanation;
	}
	public void setIsExplanation(String isExplanation) {
		this.isExplanation = isExplanation;
	}
	public String getIsSop() {
		return isSop;
	}
	public void setIsSop(String isSop) {
		this.isSop = isSop;
	}
	public String getIsPriPerfGroup() {
		return isPriPerfGroup;
	}
	public void setIsPriPerfGroup(String isPriPerfGroup) {
		this.isPriPerfGroup = isPriPerfGroup;
	}
	public String getIsSecPerfGroup() {
		return isSecPerfGroup;
	}
	public void setIsSecPerfGroup(String isSecPerfGroup) {
		this.isSecPerfGroup = isSecPerfGroup;
	}
	public String getIsAuditor() {
		return isAuditor;
	}
	public void setIsAuditor(String isAuditor) {
		this.isAuditor = isAuditor;
	}
	public String getIsAuditDate() {
		return isAuditDate;
	}
	public void setIsAuditDate(String isAuditDate) {
		this.isAuditDate = isAuditDate;
	}
	public String getIsTotalClaims() {
		return isTotalClaims;
	}
	public void setIsTotalClaims(String isTotalClaims) {
		this.isTotalClaims = isTotalClaims;
	}
	public String getIsProcClaims() {
		return isProcClaims;
	}
	public void setIsProcClaims(String isProcClaims) {
		this.isProcClaims = isProcClaims;
	}
	public String getIsMonClaims() {
		return isMonClaims;
	}
	public void setIsMonClaims(String isMonClaims) {
		this.isMonClaims = isMonClaims;
	}
	public String getIsTotalPaid() {
		return isTotalPaid;
	}
	public void setIsTotalPaid(String isTotalPaid) {
		this.isTotalPaid = isTotalPaid;
	}
	public String getIsTotalOvPaid() {
		return isTotalOvPaid;
	}
	public void setIsTotalOvPaid(String isTotalOvPaid) {
		this.isTotalOvPaid = isTotalOvPaid;
	}
	public String getIsTotalUnPaid() {
		return isTotalUnPaid;
	}
	public void setIsTotalUnPaid(String isTotalUnPaid) {
		this.isTotalUnPaid = isTotalUnPaid;
	}
	public String getSuccessMsg() {
		return successMsg;
	}
	public void setSuccessMsg(String successMsg) {
		this.successMsg = successMsg;
	}
	public String getSucessCode() {
		return sucessCode;
	}
	public void setSucessCode(String sucessCode) {
		this.sucessCode = sucessCode;
	}
	@Override
	public String toString() {
		return "AuditAssessment [associateId=" + associateId + ", processDateIn=" + processDateIn + ", month=" + month
				+ ", associateName=" + associateName + ", empNO=" + empNO + ", supervisor=" + supervisor
				+ ", reportType=" + reportType + ", type=" + type + ", oosSno=" + oosSno + ", oosDcn=" + oosDcn
				+ ", oosMemId=" + oosMemId + ", oosPaid=" + oosPaid + ", oosUnderPaid=" + oosUnderPaid
				+ ", oosOverPaid=" + oosOverPaid + ", oosHighDoller=" + oosHighDoller + ", oosAuditType=" + oosAuditType
				+ ", oosMockAudit=" + oosMockAudit + ", oosAdjRequired=" + oosAdjRequired + ", oosProcessOn="
				+ oosProcessOn + ", oosErrorCode=" + oosErrorCode + ", oosMonetary=" + oosMonetary + ", oosProcedural="
				+ oosProcedural + ", oosErrorType=" + oosErrorType + ", oosExplanation=" + oosExplanation + ", oosFyi="
				+ oosFyi + ", oosSop=" + oosSop + ", oosPriPerfGroup=" + oosPriPerfGroup + ", oosSecPerfGroup="
				+ oosSecPerfGroup + ", oosAuditor=" + oosAuditor + ", oosAuditDate=" + oosAuditDate
				+ ", oosTotalClaims=" + oosTotalClaims + ", oosProcClaims=" + oosProcClaims + ", oosMonClaims="
				+ oosMonClaims + ", oosTotalPaid=" + oosTotalPaid + ", oosTotalOvPaid=" + oosTotalOvPaid
				+ ", oosTotalUnPaid=" + oosTotalUnPaid + ", isSno=" + isSno + ", isDcn=" + isDcn + ", isMemId="
				+ isMemId + ", isPaid=" + isPaid + ", isUnderPaid=" + isUnderPaid + ", isOverPaid=" + isOverPaid
				+ ", isHighDoller=" + isHighDoller + ", isAuditType=" + isAuditType + ", isMockAudit=" + isMockAudit
				+ ", isAdjRequired=" + isAdjRequired + ", isAdjustmentsMade=" + isAdjustmentsMade + ", isProcessOn="
				+ isProcessOn + ", isErrorCode=" + isErrorCode + ", isMonetary=" + isMonetary + ", isProcedural="
				+ isProcedural + ", isErrorType=" + isErrorType + ", isExplanation=" + isExplanation + ", isFyi="
				+ isFyi + ", isSop=" + isSop + ", isPriPerfGroup=" + isPriPerfGroup + ", isSecPerfGroup="
				+ isSecPerfGroup + ", isAuditor=" + isAuditor + ", isAuditDate=" + isAuditDate + ", isTotalClaims="
				+ isTotalClaims + ", isProcClaims=" + isProcClaims + ", isMonClaims=" + isMonClaims + ", isTotalPaid="
				+ isTotalPaid + ", isTotalOvPaid=" + isTotalOvPaid + ", isTotalUnPaid=" + isTotalUnPaid + ", title="
				+ title + ", timeframe=" + timeframe + ", e2e=" + e2e + ", auditType=" + auditType + ", subType="
				+ subType + ", successMsg=" + successMsg + ", sucessCode=" + sucessCode + "]";
	}
	
}
