package com.carefirst.qadb.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import javax.sql.DataSource;

import oracle.jdbc.OracleTypes;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

import com.carefirst.audit.model.AuditAssessment;
import com.carefirst.audit.model.PerformanceGroupErrorSheet;
import com.carefirst.qadb.constant.QADBConstants;

public class PerformanceGroupErrorSheetDAOImpl implements PerformanceGroupErrorSheetDAO {
	
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);

	@Autowired
	DataSource dataSource;
	
	@Override
	public JRDataSource getPGErrorSheet(PerformanceGroupErrorSheet pgErrorSheet)
			throws SQLException {
		
		logger.debug("***getPGErrorSheet entry***");
		String getPGErrorSheet = QADBConstants.REPORTS_PG_ERR_SHEET_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getPGErrorSheet);
		List<AuditAssessment> perfGroupErrorSheetList = new ArrayList<AuditAssessment>();
		
		try {
			
			callableStatment.setNull(1, java.sql.Types.VARCHAR);
			callableStatment.setString(2, pgErrorSheet.getMonth());
			callableStatment.setInt(3, Integer.parseInt(pgErrorSheet.getYear()));
			callableStatment.setString(4, pgErrorSheet.getGrouptype());
			if((pgErrorSheet.getGrouptype()).equalsIgnoreCase("P")){
				callableStatment.setInt(5, Integer.parseInt(pgErrorSheet.getPriGroupId()));
			}else{
				callableStatment.setInt(5, Integer.parseInt(pgErrorSheet.getSecGroupId()));
			}
			callableStatment.setNull(6, java.sql.Types.VARCHAR);
			
			callableStatment.registerOutParameter(7, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(8, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(9, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(10, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(11, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(12, OracleTypes.INTEGER);
			callableStatment.registerOutParameter(13, OracleTypes.INTEGER);

			callableStatment.registerOutParameter(14, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(15, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(16, OracleTypes.VARCHAR);
			
			callableStatment.executeUpdate();
			
			ResultSet  rs =   (ResultSet) callableStatment.getObject(14);
			
			logger.debug("rs== " +rs.getFetchSize());
			
			while(rs.next()){
				logger.debug("***Entry");
				AuditAssessment perfGroupSheet = new AuditAssessment();
				
				if((pgErrorSheet.getGrouptype()).equalsIgnoreCase("P")){
					perfGroupSheet.setTitle("Primary Performance");
				}
				else{
					perfGroupSheet.setTitle("Secondary Performance");
				}
				perfGroupSheet.setTimeframe(pgErrorSheet.getMonth()+" "+pgErrorSheet.getYear());
				
				perfGroupSheet.setAssociateName(callableStatment.getString(7));
				perfGroupSheet.setIsTotalClaims(callableStatment.getString(8));
				perfGroupSheet.setIsProcClaims(callableStatment.getString(9));
				perfGroupSheet.setIsMonClaims(callableStatment.getString(10));
				perfGroupSheet.setIsTotalPaid(callableStatment.getString(11));
				perfGroupSheet.setIsTotalOvPaid(callableStatment.getString(12));
				perfGroupSheet.setIsTotalUnPaid(callableStatment.getString(13));
				
				perfGroupSheet.setIsSno(rs.getString("SNO"));
				perfGroupSheet.setIsDcn(rs.getString("DCN_NBR"));
				perfGroupSheet.setIsMemId(rs.getString("MEMB_ID"));;
				perfGroupSheet.setIsPaid(rs.getString("TOTAL_PAID"));
				perfGroupSheet.setIsOverPaid(rs.getString("OVER_PAID"));
				perfGroupSheet.setIsUnderPaid(rs.getString("UNDER_PAID"));
				perfGroupSheet.setIsHighDoller(rs.getString("HIGH_DOLLAR_FLG"));
				perfGroupSheet.setIsAuditType(rs.getString("AUDIT_TYPE"));
				perfGroupSheet.setIsMockAudit((rs.getString("MOCK_FLG")).toString());
				perfGroupSheet.setReportType((rs.getString("OOS_FLG")).toString());
				
				if(null != (rs.getString("REQUIRED_FLG"))){
					perfGroupSheet.setIsAdjRequired((rs.getString("REQUIRED_FLG")).toString());
				}
				else{
					perfGroupSheet.setIsAdjRequired("");
				}
				perfGroupSheet.setIsProcessOn((rs.getString("PROCESS_DATE")).toString());
				
				if(null != (rs.getString("ERROR_ID"))){
					perfGroupSheet.setIsErrorCode((rs.getString("ERROR_ID")).toString());
				}
				else{ 
					perfGroupSheet.setIsErrorCode("");
				}
				if(null != (rs.getString("MONETARY_FLG"))){
					perfGroupSheet.setIsMonetary((rs.getString("MONETARY_FLG")).toString());
				}
				else{
					perfGroupSheet.setIsMonetary("");
				}
				if(null != (rs.getString("PROCEDURAL_FLG"))){
					perfGroupSheet.setIsProcedural((rs.getString("PROCEDURAL_FLG")).toString());
				}
				else{
					perfGroupSheet.setIsProcedural("");
				}
				if(null != (rs.getString("ERROR_TYPE"))){
					perfGroupSheet.setIsErrorType((rs.getString("ERROR_TYPE")).toString());
				}
				else{
					perfGroupSheet.setIsErrorType("");
				}
				if(null != (rs.getString("ERROR_REASON_DESC"))){
					perfGroupSheet.setIsExplanation((rs.getString("ERROR_REASON_DESC")).toString());
				}
				else{
					perfGroupSheet.setIsExplanation("");
				}
				if(null != (rs.getString("SOP_NEWS_FLASH_REFNC"))){
					perfGroupSheet.setIsSop((rs.getString("SOP_NEWS_FLASH_REFNC")).toString());
				}
				else{
					perfGroupSheet.setIsSop("");
				}
				
				perfGroupSheet.setIsAuditDate((rs.getString("ASSOCIATE")).toString());
				perfGroupSheet.setIsAuditor((rs.getString("MANAGER")).toString());
				
				perfGroupErrorSheetList.add(perfGroupSheet);
				logger.debug("***Exit");
			}
			
			
			rs.close();
			
			callableStatment.close();
			 
			conn.close();
			
		} catch (Exception e) {
			logger.debug("Exception generated"+e.getMessage());
		}
		
		JRDataSource ds = new JRBeanCollectionDataSource(perfGroupErrorSheetList);	
		
		logger.debug("***getPGErrorSheet exit***");
		// Return the wrapped collection
		return ds;
	
	}
	
	
}