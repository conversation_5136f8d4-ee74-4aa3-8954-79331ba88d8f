package com.carefirst.qadb.dao;

import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.sql.DataSource;

import oracle.jdbc.OracleTypes;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import com.carefirst.audit.model.PerformanceGroup;
import com.carefirst.qadb.constant.QADBConstants;
import com.carefirst.qadb.converter.CheckConverter;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

public class PerformanceGroupDAOImpl implements PerformanceGroupDAO{
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);

	@Autowired
	DataSource dataSource;
	/**
	 * Method to call a procedure to save new performance group.
	 */
	@Override
	public PerformanceGroup savePerformanceGroup(PerformanceGroup performanceGroup){

		logger.debug("*** Entry savePerformanceGroup method ***"+new CheckConverter().convert(performanceGroup.getStatus())+performanceGroup.getStatus());
		String errorSp = QADBConstants.ADMIN_PERF_GROUP_SAVE_SP;
		Connection conn = null;
		CallableStatement callableStatment = null;
		PerformanceGroup performanceGroupRO = new PerformanceGroup();
		try {
			//SimpleDateFormat sdf = new SimpleDateFormat("MM/dd/yy");
			logger.debug("Grp Checkbox "+performanceGroup.getGrpCd()+" sd "+performanceGroup.getGrpSammd());
			conn = dataSource.getConnection();
			callableStatment = conn.prepareCall(errorSp);
			//Set the input parameters for the procedure
			callableStatment.setString(1, performanceGroup.getUserid());
			callableStatment.setString(2, performanceGroup.getUserActyp());
			if(null!=performanceGroup.getGrpid()){
				callableStatment.setInt(3,Integer.parseInt(performanceGroup.getGrpid()));
			}else{
				callableStatment.setNull(3, java.sql.Types.INTEGER);
			}
			callableStatment.setString(4, performanceGroup.getType());
			callableStatment.setString(5, performanceGroup.getGroupId());
			callableStatment.setString(6, performanceGroup.getGroupName());
			if((null != performanceGroup.getSecPGAMappings())&&((performanceGroup.getType()).equals("S"))){
				String secPGAMappingsArrString = (performanceGroup.getSecPGAMappings()).toString().replaceAll("[\\s\\[\\]]", "");
				secPGAMappingsArrString=secPGAMappingsArrString.replace(",", "||");
				logger.debug("--->"+secPGAMappingsArrString);
				callableStatment.setString(7, secPGAMappingsArrString);
			}else{
				callableStatment.setNull(7, java.sql.Types.VARCHAR);
			}
			
			logger.debug("grp--- "+ new CheckConverter().getUserGroup(performanceGroup.getGrpSammd(), performanceGroup.getGrpCd()));
			callableStatment.setString(8,new CheckConverter().getUserGroup(performanceGroup.getGrpSammd(), performanceGroup.getGrpCd()));
			callableStatment.setString(9,performanceGroup.getEffectiveFrom());
			callableStatment.setString(10,performanceGroup.getEffectiveTo());
			if("N".equalsIgnoreCase(new CheckConverter().convert(performanceGroup.getStatus()))){
				callableStatment.setNull(11, java.sql.Types.DATE);
				callableStatment.setNull(12, java.sql.Types.DATE);
			}else{
				if(null!=performanceGroup.getDisableFrom() && null!=performanceGroup.getDisableTo()){
					callableStatment.setString(11, performanceGroup.getDisableFrom());
					callableStatment.setString(12, performanceGroup.getDisableTo());
				}else{
					callableStatment.setNull(11, java.sql.Types.DATE);
					callableStatment.setNull(12, java.sql.Types.DATE);
				}
				
			}
			if(null!=performanceGroup.getProceduralAccuracyExp()){
				if(performanceGroup.getProceduralAccuracyExp().equalsIgnoreCase("")){
					callableStatment.setString(13, "0");
				}else{
					callableStatment.setString(13, performanceGroup.getProceduralAccuracyExp());
				}
			}else{
				callableStatment.setNull(13, java.sql.Types.VARCHAR);
			}
			if(null!=performanceGroup.getProceduralMonthlyPenalty()){
				if(performanceGroup.getProceduralMonthlyPenalty().equalsIgnoreCase("")){
					callableStatment.setString(14, "0");
				}else{
					callableStatment.setString(14, performanceGroup.getProceduralMonthlyPenalty());
				}
			}else{
				callableStatment.setString(14, "0");
			}
			
			if(null!=performanceGroup.getDollarFrequencyExp()){
				if(performanceGroup.getDollarFrequencyExp().equalsIgnoreCase("")){
					callableStatment.setString(15, "0");
				}else{
					callableStatment.setString(15, performanceGroup.getDollarFrequencyExp());
				}
			}else{
				callableStatment.setString(15, "0");
			}
			
			if(null!=performanceGroup.getDollarFreqMonthlyPenalty()){
				if(performanceGroup.getDollarFreqMonthlyPenalty().equalsIgnoreCase("")){
					callableStatment.setString(16, "0");
				}else{
					callableStatment.setString(16, performanceGroup.getDollarFreqMonthlyPenalty());
				}
			}else{
				callableStatment.setString(16, "0");
			}
			
			if(null!=performanceGroup.getDollarAccuracyExp()){
				if(performanceGroup.getDollarAccuracyExp().equalsIgnoreCase("")){
					callableStatment.setString(17, "0");
				}else{
					callableStatment.setString(17, performanceGroup.getDollarAccuracyExp());
				}
			}else{
				callableStatment.setString(17, "0");
			}
			
			if(null!=performanceGroup.getDollarAccuracyMonthlyPenalty()){
				if(performanceGroup.getDollarAccuracyMonthlyPenalty().equalsIgnoreCase("")){
					callableStatment.setString(18, "0");
				}else{
					callableStatment.setString(18, performanceGroup.getDollarAccuracyMonthlyPenalty());
				}
			}else{
				callableStatment.setString(18, "0");
			}
			
			if("N".equalsIgnoreCase(new CheckConverter().convert(performanceGroup.getStatus()))){
				callableStatment.setString(19, "Y");
			}else{
				callableStatment.setString(19, "N");
			}
			
			callableStatment.registerOutParameter(20, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(21, OracleTypes.VARCHAR);
			callableStatment.execute();
			logger.debug("set inputs");
			logger.debug("status code = "+callableStatment.getString(20));
			logger.debug("status message = "+callableStatment.getString(21));
			
			performanceGroupRO.setSuccessCode(callableStatment.getString(20));
			performanceGroupRO.setSuccessMsg(callableStatment.getString(21));
			
		} catch (Exception e) {
			logger.debug(e.getMessage());
			try {
				conn.rollback();
			} catch (SQLException e1) {
				logger.debug(e1.getMessage());
				e1.printStackTrace();
			}
			e.printStackTrace();
			
		}finally {
			if (callableStatment != null) {
				try {
					callableStatment.close();
				} catch (SQLException e) {
					logger.debug(e.getMessage());
					e.printStackTrace();
				}
			}

			if (conn != null) {
				try {
					conn.close();
				} catch (SQLException e) {
					logger.debug(e.getMessage());
					e.printStackTrace();
				}
			}
		}
		logger.debug("*** Exit savePerformanceGroup method ***");
		return performanceGroupRO;
	}
	
	/**
	 * Method to search performance group by entered group id or group name
	 */
	@Override
	public List<PerformanceGroup> searchperformanceGroup(PerformanceGroup performanceGroup) throws SQLException{

		logger.debug("*** Entry searchperformanceGroup method ***"+performanceGroup.getGroupId()+performanceGroup.getGroupName()+performanceGroup.getType());
		String getPerformanceGroupDetails = QADBConstants.ADMIN_PERF_GROUP_SEARCH_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getPerformanceGroupDetails);
		List<PerformanceGroup> performanceGroupList= new ArrayList<PerformanceGroup>();
		try {
			
			callableStatment.setString(1,performanceGroup.getUserid());
			callableStatment.setString(2,performanceGroup.getUserActyp());
			logger.debug("usrGrp "+performanceGroup.getUserGrp());
			callableStatment.setString(3,performanceGroup.getUserGrp());
			callableStatment.setInt(4,Types.INTEGER);
			if(null!=performanceGroup.getGroupId()){
				if(performanceGroup.getGroupId().equalsIgnoreCase("")){
					callableStatment.setString(5,null);
				}else{
					callableStatment.setString(5,performanceGroup.getGroupId());
				}
			}else{
				callableStatment.setNull(5, java.sql.Types.VARCHAR);
			}
			if(null!=performanceGroup.getGroupName()){
				if(performanceGroup.getGroupName().equalsIgnoreCase("")){
					callableStatment.setNull(6, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(6,performanceGroup.getGroupName());
				}
			}else{
				callableStatment.setNull(6, java.sql.Types.VARCHAR);
			}
			callableStatment.setString(7,performanceGroup.getType());
			callableStatment.setNull(8, java.sql.Types.VARCHAR);
			callableStatment.setNull(9, java.sql.Types.VARCHAR);
			callableStatment.registerOutParameter(10, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(11, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(12, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(12));
			callableStatment.executeUpdate();
			
			ResultSet  rs =   (ResultSet) callableStatment.getObject(10);			
			logger.debug("Row count" +rs.getRow());
			
			while (rs.next()) {
				PerformanceGroup performanceGroupEO = new PerformanceGroup();
				if(null!=rs.getString("PERF_GROUP_ID")){
					performanceGroupEO.setGrpid((rs.getString("PERF_GROUP_ID")).toString());
				}
				if(null!=rs.getString("PERF_GROUP_GRP_ID")){
					performanceGroupEO.setGroupId((rs.getString("PERF_GROUP_GRP_ID")).toString());
				}
				if(null!=rs.getString("PERF_GROUP_NAME")){
					performanceGroupEO.setGroupName((rs.getString("PERF_GROUP_NAME")).toString());
				}
				if(null!=rs.getString("STATUS")){
					performanceGroupEO.setStatus((rs.getString("STATUS")).toString());
				}
				performanceGroupList.add(performanceGroupEO);
			}	
			rs.close();
			
		} catch (Exception e) {
			logger.debug(e.getMessage());
		}finally{
			callableStatment.close();
			conn.close();
		}
		logger.debug("*** Exit searchperformanceGroup method ***");
		return performanceGroupList;
	}
	
	//Change for DMND0003323
	@Override
	public JRDataSource searchperformanceGroupList(PerformanceGroup performanceGroup) throws SQLException{

		logger.debug("*** Entry searchperformanceGroup method ***"+performanceGroup.getGroupId()+performanceGroup.getGroupName()+performanceGroup.getType());
		String getPerformanceGroupDetails = QADBConstants.ADMIN_PERF_GROUP_SEARCH_SP;
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getPerformanceGroupDetails);
		List<PerformanceGroup> performanceGroupList= new ArrayList<PerformanceGroup>();
		try {
			
			callableStatment.setString(1,performanceGroup.getUserid());
			callableStatment.setString(2,performanceGroup.getUserActyp());
			logger.debug("usrGrp "+performanceGroup.getUserGrp());
			callableStatment.setString(3,performanceGroup.getUserGrp());
			callableStatment.setInt(4,Types.INTEGER);
			if(null!=performanceGroup.getGroupId()){
				if(performanceGroup.getGroupId().equalsIgnoreCase("")){
					callableStatment.setString(5,null);
				}else{
					callableStatment.setString(5,performanceGroup.getGroupId());
				}
			}else{
				callableStatment.setNull(5, java.sql.Types.VARCHAR);
			}
			if(null!=performanceGroup.getGroupName()){
				if(performanceGroup.getGroupName().equalsIgnoreCase("")){
					callableStatment.setNull(6, java.sql.Types.VARCHAR);
				}else{
					callableStatment.setString(6,performanceGroup.getGroupName());
				}
			}else{
				callableStatment.setNull(6, java.sql.Types.VARCHAR);
			}
			callableStatment.setString(7,performanceGroup.getType());
			callableStatment.setNull(8, java.sql.Types.VARCHAR);
			callableStatment.setNull(9, java.sql.Types.VARCHAR);
			callableStatment.registerOutParameter(10, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(11, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(12, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(12));
			callableStatment.executeUpdate();
			
			ResultSet  rs =   (ResultSet) callableStatment.getObject(10);			
			logger.debug("Row count" +rs.getRow());
			
			while (rs.next()) {
				PerformanceGroup performanceGroupEO = new PerformanceGroup();
				if(null!=rs.getString("PERF_GROUP_ID")){
					performanceGroupEO.setGrpid((rs.getString("PERF_GROUP_ID")).toString());
				}
				if(null!=rs.getString("PERF_GROUP_GRP_ID")){
					performanceGroupEO.setGroupId((rs.getString("PERF_GROUP_GRP_ID")).toString());
				}
				if(null!=rs.getString("PERF_GROUP_NAME")){
					performanceGroupEO.setGroupName((rs.getString("PERF_GROUP_NAME")).toString());
				}
				if(null!=rs.getString("STATUS")){
					performanceGroupEO.setStatus((rs.getString("STATUS")).toString());
				}
				performanceGroupList.add(performanceGroupEO);
			}	
			rs.close();
			
		} catch (Exception e) {
			logger.debug(e.getMessage());
		}finally{
			callableStatment.close();
			conn.close();
		}
		JRDataSource ds= new JRBeanCollectionDataSource(performanceGroupList);
		logger.debug("*** Exit searchperformanceGroup method ***");
		return ds;
	}
	
	/**
	 * Method to populate the data for the performance group selected for editing
	 */
	@Override
	public PerformanceGroup getPerformanceGroupData(PerformanceGroup performanceGroup) throws SQLException{

		logger.debug("*** Entry getPerformanceGroupData method ***"+performanceGroup.getGrpid());
		String getPerformanceGroupDetails =QADBConstants.ADMIN_PERF_GROUP_SEARCH_SP;
		PerformanceGroup performanceGroupEO = new PerformanceGroup();
		Connection conn = dataSource.getConnection();
		CallableStatement callableStatment = conn.prepareCall(getPerformanceGroupDetails);
		String secMappingWithComma = "";
		try {
			callableStatment.setString(1,performanceGroup.getUserid());
			callableStatment.setString(2,performanceGroup.getUserActyp());
			callableStatment.setString(3,performanceGroup.getUserGrp());
			callableStatment.setInt(4,Integer.parseInt(performanceGroup.getGrpid()));
			callableStatment.setNull(5, java.sql.Types.VARCHAR);
			callableStatment.setNull(6, java.sql.Types.VARCHAR);
			callableStatment.setNull(7, java.sql.Types.VARCHAR);
			callableStatment.setNull(8, java.sql.Types.VARCHAR);
			callableStatment.setNull(9, java.sql.Types.VARCHAR);
			callableStatment.registerOutParameter(10, OracleTypes.CURSOR);
			callableStatment.registerOutParameter(11, OracleTypes.VARCHAR);
			callableStatment.registerOutParameter(12, OracleTypes.VARCHAR);
			logger.debug("execute proc"+callableStatment.execute());
			logger.debug("status message = "+callableStatment.getString(12));
			
			callableStatment.executeUpdate();
			ResultSet  rs =   (ResultSet) callableStatment.getObject(10);			
			logger.debug("Row count" +rs.getRow());

			while (rs.next()) {
				if(null!=rs.getString("PERF_GROUP_ID")){
					if((performanceGroup.getGrpid()).equals(rs.getString("PERF_GROUP_ID"))){
						performanceGroupEO.setGrpid((rs.getString("PERF_GROUP_ID")).toString());
					}
				}
				if(null!=rs.getString("PERF_GROUP_TYP")){
					performanceGroupEO.setType((rs.getString("PERF_GROUP_TYP")).toString());
				}
				if(null!=rs.getString("PERF_GROUP_GRP_ID")){
					performanceGroupEO.setGroupId((rs.getString("PERF_GROUP_GRP_ID")).toString());
				}
				if(null!=rs.getString("PERF_GROUP_NAME")){
					performanceGroupEO.setGroupName((rs.getString("PERF_GROUP_NAME")).toString());
				}
				if(null!=rs.getString("PERF_GROUP_EFFECTIVE_START_DT")){
					performanceGroupEO.setEffectiveFrom((rs.getString("PERF_GROUP_EFFECTIVE_START_DT")).toString());
				}
				if(null!=rs.getString("PERF_GROUP_EFFECTIVE_END_DT")){
					performanceGroupEO.setEffectiveTo((rs.getString("PERF_GROUP_EFFECTIVE_END_DT")).toString());
				}
				if(null!=rs.getString("PERF_GROUP_DISABLE_START_DT")){
					performanceGroupEO.setDisableFrom((rs.getString("PERF_GROUP_DISABLE_START_DT")).toString());
				}
				if(null!=rs.getString("PERF_GROUP_DISABLE_END_DT")){
					performanceGroupEO.setDisableTo((rs.getString("PERF_GROUP_DISABLE_END_DT")).toString());
				}
				if(null!=rs.getString("PROCEDURAL_EXPECTED_ACCURACY")){
					performanceGroupEO.setProceduralAccuracyExp((rs.getString("PROCEDURAL_EXPECTED_ACCURACY")).toString());
				}
				if(null!=rs.getString("PROCEDURAL_MONTHLY_PENALTY")){
					performanceGroupEO.setProceduralMonthlyPenalty((rs.getString("PROCEDURAL_MONTHLY_PENALTY")).toString());
				}
				if(null!=rs.getString("DOLLAR_FREQ_EXPECTED_ACCURACY")){
					performanceGroupEO.setDollarFrequencyExp((rs.getString("DOLLAR_FREQ_EXPECTED_ACCURACY")).toString());
				}
				if(null!=rs.getString("DOLLAR_FREQ_MONTHLY_PENALTY")){
					performanceGroupEO.setDollarFreqMonthlyPenalty((rs.getString("DOLLAR_FREQ_MONTHLY_PENALTY")).toString());
				}
				if(null!=rs.getString("DOLLAR_EXPECTED_ACCURACY")){
					performanceGroupEO.setDollarAccuracyExp((rs.getString("DOLLAR_EXPECTED_ACCURACY")).toString());
				}
				if(null!=rs.getString("DOLLAR_MONTHLY_PENALTY")){
					performanceGroupEO.setDollarAccuracyMonthlyPenalty((rs.getString("DOLLAR_MONTHLY_PENALTY")).toString());
				}
				if(null!=rs.getString("STATUS")){
					performanceGroupEO.setStatus((rs.getString("STATUS")).toString());
				}
				if (null != (rs.getString("PERF_GROUP_USER_GROUP"))) {
					performanceGroupEO.setUserGrp((rs.getString("PERF_GROUP_USER_GROUP")).toString());
				} else {
					performanceGroupEO.setUserGrp("");
				}
				if(null!=rs.getString("PERF_GROUP_MAPPING")){
					secMappingWithComma = ((rs.getString("PERF_GROUP_MAPPING")).toString());
				}
				logger.debug("Ro grp "+rs.getString("PERF_GROUP_USER_GROUP") +"mapping  "+rs.getString("PERF_GROUP_MAPPING")+" "+secMappingWithComma);
				break;
			}	
			rs.close();
			
			String a[] = secMappingWithComma.split("(?<!\\\\),");
			performanceGroupEO.setSecPGAMappingsRO(Arrays.asList(a));
			
		} catch (Exception e) {
			logger.debug(e.getMessage());
		}finally{
			callableStatment.close();
			conn.close();
		}
		logger.debug("*** Exit getPerformanceGroupData method ***");

		return performanceGroupEO;
	}
}
