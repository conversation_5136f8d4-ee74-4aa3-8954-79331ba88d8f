package com.carefirst.qadb.dao;

import java.sql.SQLException;
import java.util.List;

import com.carefirst.audit.model.Mapping;
import com.carefirst.audit.model.RootCause;

public interface MappingDAO {

	public Mapping saveMapping(Mapping mappingTO) throws SQLException;
	
	public Mapping getMapping(Mapping mappingSearch) throws SQLException;

	public List<Mapping> searchMapping(Mapping mappingSearch) throws SQLException;

	public List<String> getYears();

	//public RootCause getRootCause(RootCause rootCauseTO) throws SQLException;

}
