package com.carefirst.audit.model;

import java.util.List;

public class AuditSave {

	// temp
	private String userid;
	private String userActyp;
	private String auditId;
	private String associID;
	private String associateFacetsId;
	private String successMsg;
	private String sucessCode;
	private String lastSavedBy;
	private String lastSavedOn;

	private String savedBy;
	private String savedTime;
	private String savedDate;

	private String dcn;
	private String lob;
	private String memberId;
	private String processDate;
	private String paidDate;

	private String totalCharge;
	private String paid;
	private String jurisdiction;

	private String monetaryError;
	private String amountp;
	private String theoreticalPaid;
	private String piChk;
	private String pi;

	private String mock;
	private String oos;
	private String e2e;
	private String riskAccount;
	private String adjustment;
	private String fyiChk;
	private String fyi;
	private String claimType;
	private String penaltyInterestType;
	private String auditType;
	private String priPGA;
	private String secPGA;
	private String processtype;
	private String platform;
	
	private String subscriberCK;
	private String AccountID;
	private String AccountName; 
	private String Group;
	private String claimCurrentStatus;
	private String productID;
	private String adjustmentReasoncode;
	
	private List<Products> productInfo;
	
	private List<String> BSBSCode;
	private List<String> productLine;
	private List<String> productDescription;
    private List<String> groupID;
	
	
	
	/* Error Details */

	// private int errorCodesId[];
	private List<String> errorCodesId;
	private List<String> errorCodes;

	private List<String> monetarys;
	private List<String> procedurals;

	private List<String> specialitysId;
	private String specialitys;

	private List<String> rootCausesId;
	private String rootCauses;

	private List<String> editCodesId;
	private String editCodes;

	/*
	 * private boolean monetary; private boolean procedural; private
	 * ClaimDetails claimDetails; private Adjustments adjustments;
	 */
	private String errorType;
	private String sop;
	private String reason;
	private String comments;
	private String required;
	private String completed;
	private String dateAdj;
	private String appeal;
	
	//High Dollar
		private String claimAdjFlag;
		private String serviceAdjFlag;
		private String examinerName;
		private String QAName;
		private String patientName;
		private String serviceDates;
		private String typeOfService;
		private String diagnosis;
		private String surgeryDOS;
		private String surgery;
		private String fileReferenced;
		private String interestPaid;/*Enhancement*/
		private String providerName;
		private String providerNumber;
		private String payee;
		private String notes;
		private String audSignDate;
		private String vpSignDate;
		private String forwrdToDate;
		private String rcvedFrmDate;
		private String releasedByDate;
		
		public String getInterestPaid() {
			return interestPaid;
		}

		public void setInterestPaid(String interestPaid) {
			this.interestPaid = interestPaid;
		}

		public String getAdjustmentReasoncode() {
			return adjustmentReasoncode;
		}

		public void setAdjustmentReasoncode(String adjustmentReasoncode) {
			this.adjustmentReasoncode = adjustmentReasoncode;
		}

		public String getClaimAdjFlag() {
			return claimAdjFlag;
		}

		public void setClaimAdjFlag(String claimAdjFlag) {
			this.claimAdjFlag = claimAdjFlag;
		}

		public String getServiceAdjFlag() {
			return serviceAdjFlag;
		}

		public void setServiceAdjFlag(String serviceAdjFlag) {
			this.serviceAdjFlag = serviceAdjFlag;
		}

		public String getExaminerName() {
			return examinerName;
		}

		public void setExaminerName(String examinerName) {
			this.examinerName = examinerName;
		}

		public String getQAName() {
			return QAName;
		}

		public void setQAName(String qAName) {
			QAName = qAName;
		}

		public String getPatientName() {
			return patientName;
		}

		public void setPatientName(String patientName) {
			this.patientName = patientName;
		}
		public String getPlatform() {
			return platform;
		}

		public void setPlatform(String platform) {
			this.platform = platform;
		}

		public String getServiceDates() {
			return serviceDates;
		}

		public void setServiceDates(String serviceDates) {
			this.serviceDates = serviceDates;
		}

		public String getTypeOfService() {
			return typeOfService;
		}

		public void setTypeOfService(String typeOfService) {
			this.typeOfService = typeOfService;
		}

		public String getDiagnosis() {
			return diagnosis;
		}

		public void setDiagnosis(String diagnosis) {
			this.diagnosis = diagnosis;
		}

		public String getSurgeryDOS() {
			return surgeryDOS;
		}

		public void setSurgeryDOS(String surgeryDOS) {
			this.surgeryDOS = surgeryDOS;
		}

		public String getSurgery() {
			return surgery;
		}

		public void setSurgery(String surgery) {
			this.surgery = surgery;
		}

		public String getFileReferenced() {
			return fileReferenced;
		}

		public void setFileReferenced(String fileReferenced) {
			this.fileReferenced = fileReferenced;
		}

		public String getProviderName() {
			return providerName;
		}

		public void setProviderName(String providerName) {
			this.providerName = providerName;
		}

		public String getProviderNumber() {
			return providerNumber;
		}

		public void setProviderNumber(String providerNumber) {
			this.providerNumber = providerNumber;
		}

		public String getPayee() {
			return payee;
		}

		public void setPayee(String payee) {
			this.payee = payee;
		}

		public String getNotes() {
			return notes;
		}

		public void setNotes(String notes) {
			this.notes = notes;
		}

		public String getAudSignDate() {
			return audSignDate;
		}

		public void setAudSignDate(String audSignDate) {
			this.audSignDate = audSignDate;
		}

		public String getVpSignDate() {
			return vpSignDate;
		}

		public void setVpSignDate(String vpSignDate) {
			this.vpSignDate = vpSignDate;
		}

		public String getForwrdToDate() {
			return forwrdToDate;
		}

		public void setForwrdToDate(String forwrdToDate) {
			this.forwrdToDate = forwrdToDate;
		}

		public String getRcvedFrmDate() {
			return rcvedFrmDate;
		}

		public void setRcvedFrmDate(String rcvedFrmDate) {
			this.rcvedFrmDate = rcvedFrmDate;
		}

		public String getReleasedByDate() {
			return releasedByDate;
		}

		public void setReleasedByDate(String releasedByDate) {
			this.releasedByDate = releasedByDate;
		}

	public List<Products> getProductInfo() {
		return productInfo;
	}

	public void setProductInfo(List<Products> productInfo) {
		this.productInfo = productInfo;
	}

	public String getSubscriberCK() {
		return subscriberCK;
	}

	public void setSubscriberCK(String subscriberCK) {
		this.subscriberCK = subscriberCK;
	}

	public String getAccountID() {
		return AccountID;
	}

	public void setAccountID(String accountID) {
		AccountID = accountID;
	}

	public String getAccountName() {
		return AccountName;
	}

	public void setAccountName(String accountName) {
		AccountName = accountName;
	}

	public String getGroup() {
		return Group;
	}

	public void setGroup(String group) {
		Group = group;
	}

	public String getClaimCurrentStatus() {
		return claimCurrentStatus;
	}

	public void setClaimCurrentStatus(String claimCurrentStatus) {
		this.claimCurrentStatus = claimCurrentStatus;
	}

	public String getProductID() {
		return productID;
	}

	public void setProductID(String productID) {
		this.productID = productID;
	}

	public List<String> getBSBSCode() {
		return BSBSCode;
	}

	public void setBSBSCode(List<String> bSBSCode) {
		BSBSCode = bSBSCode;
	}

	public List<String> getProductLine() {
		return productLine;
	}

	public void setProductLine(List<String> productLine) {
		this.productLine = productLine;
	}

	public List<String> getProductDescription() {
		return productDescription;
	}

	public void setProductDescription(List<String> productDescription) {
		this.productDescription = productDescription;
	}

	public List<String> getGroupID() {
		return groupID;
	}

	public void setGroupID(List<String> groupID) {
		this.groupID = groupID;
	}

	public String getFyiChk() {
		return fyiChk;
	}

	public void setFyiChk(String fyiChk) {
		this.fyiChk = fyiChk;
	}

	public String getFyi() {
		return fyi;
	}

	public void setFyi(String fyi) {
		this.fyi = fyi;
	}

	public String getAssociateFacetsId() {
		return associateFacetsId;
	}

	public void setAssociateFacetsId(String associateFacetsId) {
		this.associateFacetsId = associateFacetsId;
	}

	public String getSavedTime() {
		return savedTime;
	}

	public void setSavedTime(String savedTime) {
		this.savedTime = savedTime;
	}

	public String getSavedDate() {
		return savedDate;
	}

	public void setSavedDate(String savedDate) {
		this.savedDate = savedDate;
	}

	public String getSavedBy() {
		return savedBy;
	}

	public void setSavedBy(String savedBy) {
		this.savedBy = savedBy;
	}

	public String getLastSavedBy() {
		return lastSavedBy;
	}

	public void setLastSavedBy(String lastSavedBy) {
		this.lastSavedBy = lastSavedBy;
	}

	public String getLastSavedOn() {
		return lastSavedOn;
	}

	public void setLastSavedOn(String lastSavedOn) {
		this.lastSavedOn = lastSavedOn;
	}

	public String getSucessCode() {
		return sucessCode;
	}

	public void setSucessCode(String sucessCode) {
		this.sucessCode = sucessCode;
	}

	public String getSuccessMsg() {
		return successMsg;
	}

	public void setSuccessMsg(String successMsg) {
		this.successMsg = successMsg;
	}

	public String getUserid() {
		return userid;
	}

	public void setUserid(String userid) {
		this.userid = userid;
	}

	public String getUserActyp() {
		return userActyp;
	}

	public void setUserActyp(String userActyp) {
		this.userActyp = userActyp;
	}

	public String getAuditId() {
		return auditId;
	}

	public void setAuditId(String auditId) {
		this.auditId = auditId;
	}

	public String getAssociID() {
		return associID;
	}

	public void setAssociID(String associID) {
		this.associID = associID;
	}

	public String getProcessDate() {
		return processDate;
	}

	public void setProcessDate(String processDate) {
		this.processDate = processDate;
	}

	public String getPaidDate() {
		return paidDate;
	}

	public void setPaidDate(String paidDate) {
		this.paidDate = paidDate;
	}

	public String getJurisdiction() {
		return jurisdiction;
	}

	public void setJurisdiction(String jurisdiction) {
		this.jurisdiction = jurisdiction;
	}

	public String getPi() {
		return pi;
	}

	public void setPi(String pi) {
		this.pi = pi;
	}

	public List<String> getEditCodesId() {
		return editCodesId;
	}

	public void setEditCodesId(List<String> editCodesId) {
		this.editCodesId = editCodesId;
	}

	public String getEditCodes() {
		return editCodes;
	}

	public void setEditCodes(String editCodes) {
		this.editCodes = editCodes;
	}

	public List<String> getErrorCodesId() {
		return errorCodesId;
	}

	public void setErrorCodesId(List<String> errorCodesId) {
		this.errorCodesId = errorCodesId;
	}

	public List<String> getErrorCodes() {
		return errorCodes;
	}

	public void setErrorCodes(List<String> errorCodes) {
		this.errorCodes = errorCodes;
	}

	public List<String> getMonetarys() {
		return monetarys;
	}

	public void setMonetarys(List<String> monetarys) {
		this.monetarys = monetarys;
	}

	public List<String> getProcedurals() {
		return procedurals;
	}

	public void setProcedurals(List<String> procedurals) {
		this.procedurals = procedurals;
	}

	public List<String> getSpecialitysId() {
		return specialitysId;
	}

	public void setSpecialitysId(List<String> specialitysId) {
		this.specialitysId = specialitysId;
	}

	public String getSpecialitys() {
		return specialitys;
	}

	public void setSpecialitys(String specialitys) {
		this.specialitys = specialitys;
	}

	public List<String> getRootCausesId() {
		return rootCausesId;
	}

	public void setRootCausesId(List<String> rootCausesId) {
		this.rootCausesId = rootCausesId;
	}

	public String getRootCauses() {
		return rootCauses;
	}

	public void setRootCauses(String rootCauses) {
		this.rootCauses = rootCauses;
	}

	public String getClaimType() {
		return claimType;
	}

	public void setClaimType(String claimType) {
		this.claimType = claimType;
	}

	public String getAuditType() {
		return auditType;
	}

	public void setAuditType(String auditType) {
		this.auditType = auditType;
	}

	public String getPriPGA() {
		return priPGA;
	}

	public void setPriPGA(String priPGA) {
		this.priPGA = priPGA;
	}

	public String getSecPGA() {
		return secPGA;
	}

	public void setSecPGA(String secPGA) {
		this.secPGA = secPGA;
	}

	public String getProcesstype() {
		return processtype;
	}

	public void setProcesstype(String processtype) {
		this.processtype = processtype;
	}

	public String getPenaltyInterestType() {
		return penaltyInterestType;
	}

	public void setPenaltyInterestType(String penaltyInterestType) {
		this.penaltyInterestType = penaltyInterestType;
	}

	public String getDcn() {
		return dcn;
	}

	public void setDcn(String dcn) {
		this.dcn = dcn;
	}

	public String getLob() {
		return lob;
	}

	public void setLob(String lob) {
		this.lob = lob;
	}

	public String getMemberId() {
		return memberId;
	}

	public void setMemberId(String memberId) {
		this.memberId = memberId;
	}


	public String getTotalCharge() {
		return totalCharge;
	}

	public void setTotalCharge(String totalCharge) {
		this.totalCharge = totalCharge;
	}

	public String getPaid() {
		return paid;
	}

	public void setPaid(String paid) {
		this.paid = paid;
	}

	public String getMonetaryError() {
		return monetaryError;
	}

	public void setMonetaryError(String monetaryError) {
		this.monetaryError = monetaryError;
	}

	public String getTheoreticalPaid() {
		return theoreticalPaid;
	}

	public void setTheoreticalPaid(String theoreticalPaid) {
		this.theoreticalPaid = theoreticalPaid;
	}

	public String getMock() {
		return mock;
	}

	public void setMock(String mock) {
		this.mock = mock;
	}

	public String getOos() {
		return oos;
	}

	public void setOos(String oos) {
		this.oos = oos;
	}

	public String getE2e() {
		return e2e;
	}

	public void setE2e(String e2e) {
		this.e2e = e2e;
	}

	public String getRiskAccount() {
		return riskAccount;
	}

	public void setRiskAccount(String riskAccount) {
		this.riskAccount = riskAccount;
	}

	public String getAdjustment() {
		return adjustment;
	}

	public void setAdjustment(String adjustment) {
		this.adjustment = adjustment;
	}


	public String getAmountp() {
		return amountp;
	}

	public void setAmountp(String amountp) {
		this.amountp = amountp;
	}

	public String getPiChk() {
		return piChk;
	}

	public void setPiChk(String piChk) {
		this.piChk = piChk;
	}

	public String getErrorType() {
		return errorType;
	}

	public void setErrorType(String errorType) {
		this.errorType = errorType;
	}

	public String getSop() {
		return sop;
	}

	public void setSop(String sop) {
		this.sop = sop;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	public String getRequired() {
		return required;
	}

	public void setRequired(String required) {
		this.required = required;
	}

	public String getCompleted() {
		return completed;
	}

	public void setCompleted(String completed) {
		this.completed = completed;
	}

	public String getDateAdj() {
		return dateAdj;
	}

	public void setDateAdj(String dateAdj) {
		this.dateAdj = dateAdj;
	}

	public String getAppeal() {
		return appeal;
	}

	public void setAppeal(String appeal) {
		this.appeal = appeal;
	}

}
