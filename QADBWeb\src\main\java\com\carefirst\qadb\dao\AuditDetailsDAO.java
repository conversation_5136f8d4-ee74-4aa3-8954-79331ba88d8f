package com.carefirst.qadb.dao;


import java.sql.SQLException;
import java.util.List;

import net.sf.jasperreports.engine.JRDataSource;

import com.carefirst.audit.model.Associate;
import com.carefirst.audit.model.AuditAssessment;
import com.carefirst.audit.model.AuditDetails;
import com.carefirst.audit.model.AuditSave;
import com.carefirst.audit.model.RecentAudits;



public interface AuditDetailsDAO {
	
	public List<AuditDetails> getClaimType() throws SQLException;
	
	public List<AuditDetails> getPenaltyInterestType() throws SQLException;
	
	public List<AuditDetails> getAuditType() throws SQLException;
	
	public List<AuditDetails> getPriPGA(String userGrp) throws SQLException;
	
	public List<AuditDetails> getSecPGA(String userGrp, String priPGAId) throws SQLException;
	
	public List<AuditDetails> getSecPGAList(String userGrp) throws SQLException;
	
	public List<AuditDetails> getProcessType() throws SQLException;
	
	public List<AuditDetails> getAuditors(String userGrp) throws SQLException;
	
	public AuditSave saveAudit(AuditSave aud) throws SecurityException, NoSuchMethodException ;

	public Associate getAssociateDetails(Associate assoDetails) throws SQLException;

	public List<RecentAudits> getRecentAudits(RecentAudits rcTO) throws SQLException;

	public AuditSave deleteAudit(AuditSave auditIdDetails);

	public JRDataSource getAssesmentReport(AuditAssessment auditAssessmentReport)throws SQLException;
	
	public int getInSampleCount(String AssociateId);



	}
