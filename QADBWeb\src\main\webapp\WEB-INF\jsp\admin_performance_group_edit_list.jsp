<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib  prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<head>
<link href="webResources/css/qadb.css" rel="stylesheet">

<script type="text/javascript" src="webResources/js/metro.min.js"></script>
<!-- Local JavaScript -->

<link href="webResources/css/iconFont.css" rel="stylesheet">
<link href="webResources/css/docs.css" rel="stylesheet">
<link href="webResources/js/prettify/prettify.css" rel="stylesheet">

<!-- Load JavaScript Libraries -->
<script src="webResources/js/jquery/jquery.min.js"></script>
<script src="webResources/js/jquery/jquery.widget.min.js"></script>
<script src="webResources/js/jquery/jquery.dataTables.js"></script>
<style>
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
	margin-right: auto;
	height: 150%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}
.hidden
        {
            display: none;
        }
</style>



<title><spring:message code="admin.perfGroup.title" /></title>
</head>
<body>
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container2">

		<!-- Sidebar Start-->
		<div id="left-sidebar">
			<h2 style="color: white;padding-top:32px"><spring:message code="admin.perfGroup.edit.leftNav.heading1" /> </h2>
			<h3 style="color: white;padding-top:10px"><spring:message code="admin.perfGroup.edit.leftNav.heading2" /> </h3>

		
		</div>
		<!-- Sidebar End-->

		<div id="content-container" style="padding-left: 40px">

			<form:form id="perfGrpSearchForm" name="perfGrpSearchForm" method="GET"
				commandName="perfGrpSearchForm" action="editPerformanceGroupRes" style="width:700px">


				<div style="padding: 10px 0px 0px 0px;">
					<span class="bread1"><spring:message
							code="admin.perfGroup.bread1" /> </span><span class="bread2"><spring:message
							code="admin.perfGroup.edit.leftNav.heading1" />
					</span>
				</div>
				<br>
				<span style="color: #0070c0; font-size: 16px"><spring:message code="admin.perfGroup.edit.group" /></span>
				<div class="line-separator"></div>
				<br>

				<table class="tableForm">
					
					<tr>
						<td><spring:message code="admin.perfGroup.groupDetails.groupId" /></td>
						<td><div class="input-control text">
								<input type="text" id="groupId" name="groupId"  placeholder="Group Id" value="${groupId }" />
							</div>
						</td>
					</tr>
					<tr>
						<td><spring:message code="admin.perfGroup.groupDetails.groupName" /></td>
						<td><div class="input-control text">
								<input type="text" id="groupName" name="groupName"  placeholder="Group Name" value="${groupName }" />
							</div>
						</td>
					</tr>
					<tr>
						<td style="padding-top : 15px">
							<spring:message code="admin.perfGroup.groupDetails.type" />
						</td>
						
						<td>
						
						<c:choose>
							<c:when test="${null!=type }">
								<c:choose>
									<c:when test="${type =='S'}">
										<div class="input-control radio default-style margin1"
										data-role="input-control">
										<label class="input-control radio small-check">
											<input type="radio" name="type" value="P">
												<span class="check"></span>
													<span class="caption"><spring:message
										code="admin.perfGroup.groupDetails.primary" /></span>
										</label>
									</div>
									
									<div class="input-control radio default-style margin10"
										data-role="input-control" >
										<label class="input-control radio small-check">
											<input type="radio" name="type"  checked="true" value="S">
												<span class="check"></span>
													<span class="caption"><spring:message
										code="admin.perfGroup.groupDetails.secondary" /></span>
										</label>
									</div>
									</c:when>
									<c:otherwise>
										<div class="input-control radio default-style margin1"
										data-role="input-control">
										<label class="input-control radio small-check">
											<input type="radio" name="type" checked="true" value="P">
												<span class="check"></span>
													<span class="caption"><spring:message
										code="admin.perfGroup.groupDetails.primary" /></span>
										</label>
									</div>
									
									<div class="input-control radio default-style margin10"
										data-role="input-control" >
										<label class="input-control radio small-check">
											<input type="radio" name="type" value="S">
												<span class="check"></span>
													<span class="caption"><spring:message
										code="admin.perfGroup.groupDetails.secondary" /></span>
										</label>
									</div>
									</c:otherwise>
								</c:choose>
							</c:when>
							<c:otherwise>
								<div class="input-control radio default-style margin1"
										data-role="input-control">
										<label class="input-control radio small-check">
											<input type="radio" name="type" checked="true" value="P">
												<span class="check"></span>
													<span class="caption"><spring:message
										code="admin.perfGroup.groupDetails.primary" /></span>
										</label>
									</div>
									
									<div class="input-control radio default-style margin10"
										data-role="input-control" >
										<label class="input-control radio small-check">
											<input type="radio" name="type" value="S">
												<span class="check"></span>
													<span class="caption"><spring:message
										code="admin.perfGroup.groupDetails.secondary" /></span>
										</label>
									</div>
							</c:otherwise>
						</c:choose>
						<c:set var="userRole" > <%=request.getHeader("iv-groups")%> </c:set> 
						<c:if test="${(fn:contains(userRole, 'qadb-superadmin_users')) && (!(fn:contains(userRole, 'null')))}">
									<input type="hidden" name="userGrp" value="ALL">
						</c:if>
						<c:if test="${(fn:contains(userRole, 'qadb-samd-admin_users')) && (!(fn:contains(userRole, 'null')))}">
									<input type="hidden" name="userGrp" value="SAMMD">
						</c:if>
						<c:if test="${(fn:contains(userRole, 'qadb-cd-admin_users')) && (!(fn:contains(userRole, 'null')))}">
									<input type="hidden" name="userGrp" value="CD">
						</c:if>
								
						</td>
					</tr>

				</table>
				

				<div style="width: 750px">
					<div style="float: right">
						<button type="button" onclick="clearFields();" class="button inverse"><spring:message code="qadb.reset" /></button>
						<button style="background-color: #298fd8"
							class="button default"> <spring:message code="qadb.search" /></button>
					</div>
				</div>

			</form:form>

			
			<c:if test="${null!=performanceGroupRO }">
			<br> <br> <span style="color: #0070c0; font-size: 16px"> <spring:message code="qadb.searchResults" />
				</span> <br>
			<br>
			<div class="line-separator"></div>
			<table class="table striped hovered dataTable" id="searchDatatable"
				width="700px">
				<thead>
					<tr style="height: 30px">

						<td class="text-left" font-size="25px"><spring:message code="admin.perfGroup.edit.id" /></td>
						<td class="text-left" font-size="25px"><spring:message code="admin.perfGroup.groupDetails.groupId" /></td>
						<td class="text-left" font-size="25px"><spring:message code="admin.perfGroup.groupDetails.groupName" /></td>
						<td class="text-left" font-size="25px"><spring:message code="admin.perfGroup.edit.status" /></td>

					</tr>
				</thead>

				<tbody>
					<c:forEach items="${performanceGroupRO}" var="rs">
						<tr style="height: 30px">
							<td>${rs.grpid}</td>
							<td>${rs.groupId}</td>
							<td>${rs.groupName}</td>
							<td>${rs.status}</td>
						</tr>
					</c:forEach> 

				</tbody>
				

			</table>
			</c:if>
			 <script>
				$(function() {
					$('#searchDatatable').dataTable({
										
					"columns" : [
					{
           				 sClass: "hidden"
      				},
					{
					"render": function(data, type, full, meta) {
					/* return '<a href="'+full[0]+'"><u>'+data+'<u></a>'; */
					return '<a href="getPerformanceGroupData?id='+full[0]+'"><u>'+data+'<u></a>';
                   
            }
            }, {}, {} ]
									});
				});
			</script> 

		</div>
	</div>
<script type="text/javascript">
				function clearFields(){
					document.getElementById("groupId").value="";
					document.getElementById("groupName").value="";
					$("input:radio[name=type][value='P']").prop("checked", "checked");
				}
</script>
	<jsp:include page="footer.jsp"></jsp:include>

</body>

</html>
