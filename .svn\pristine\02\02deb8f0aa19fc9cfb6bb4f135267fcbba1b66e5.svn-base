package com.carefirst.audit.model;

import java.util.Date;
import java.util.List;

public class Audit {
    
    List<Error> errors ;
    private User auditor ;
    private Date auditDate ;
    private Claim claim ;
    /*Audit can be mock or actual. Mock audits are not considered against A
    ssociate's performance evaluation.*/
    
    private boolean isMock ;
    private int auditType ;
    private int primaryPGA;
    private int secondayPGA;
    
    
    /**
     * Add audit to the database.
     */
    public void add() {
        
    }
    
    /**
     * User would be able to edit Audit, errors or adjustments
     */
    public void edit() {
        
    }
    
    /**
     * Soft delete of the audit?
     */
    public void delete() {
        
    }
    
    /**
     * save edited audit
     */
    public void saveAudit() {
        
    }
    
    /**
     * preview edited audit
     */
    public void previewAudit() {
        
    }
    
    /**
     * print edited audit
     */
    public void printAudit() {
        
    }
    
    /**
     * search audit
     */
    public void searchAudit() {
        
    }
    
    /**
     * counts audit
     */
    public void auditCount() {
        
    }
}
