package com.carefirst.qadb.dao;

import java.sql.SQLException;
import java.util.List;

import com.carefirst.audit.model.Associate;
import com.carefirst.audit.model.OperationalUnit;

import net.sf.jasperreports.engine.JRDataSource;



public interface OperationalUnitDAO {
	
	public List<OperationalUnit> getOperations() throws SQLException;

	public List<OperationalUnit> getDivisions() throws SQLException;

	public List<OperationalUnit> getDirectors() throws SQLException;

	public List<OperationalUnit> getManagers() throws SQLException;

	public List<OperationalUnit> getSupervisors() throws SQLException;

	public OperationalUnit saveUpdateOpUnit( OperationalUnit operationalUnitDetails) throws Exception;
	
	public List<OperationalUnit> getOpUnitSearchResults(OperationalUnit opUnitSearchTO) throws SQLException;

	public OperationalUnit getOpUnit(OperationalUnit opUnitSearchTO) throws SQLException;

	public List<OperationalUnit> getAssociatesRS(OperationalUnit opUnitSearchTO) throws SQLException;

	public OperationalUnit deleteOpUnit(OperationalUnit opUnitDetails) throws SQLException;
	
	public JRDataSource getAssociatesRSList(OperationalUnit opUnitSearchTO) throws SQLException;
	
	public JRDataSource getOpUnitSearchResultsList(OperationalUnit opUnitSearchTO) throws SQLException;

	}
