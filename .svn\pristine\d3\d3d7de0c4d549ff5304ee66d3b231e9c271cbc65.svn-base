<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="tree-template" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="NoDataSection"  columnWidth="802" >
	<property name="com.jasperassistant.designer.Grid" value="false"/>
	<property name="com.jasperassistant.designer.SnapToGrid" value="false"/>
	<property name="com.jasperassistant.designer.GridWidth" value="12"/>
	<property name="com.jasperassistant.designer.GridHeight" value="12"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	
	<parameter name="JasperCustomSubReportLocation1" class="net.sf.jasperreports.engine.JasperReport"/>
	<parameter name="JasperCustomSubReportDatasource1" class="net.sf.jasperreports.engine.data.JRBeanCollectionDataSource"/>
	<parameter name="JasperCustomSubReportLocation2" class="net.sf.jasperreports.engine.JasperReport"/>
	<parameter name="JasperCustomSubReportDatasource2" class="net.sf.jasperreports.engine.data.JRBeanCollectionDataSource"/>
	<background>
		<band height="530" splitType="Stretch">
			
			<textField>
				<reportElement x="644" y="480" width="100" height="30"  />
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="744" y="480" width="100" height="30"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="EEEE, MMMMM dd, yyyy">
				<reportElement x="4" y="480" width="146" height="17"  />
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="180" y="478" width="420" height="16"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[Quality Assurance Productivity Management]]></text>
			</staticText>
			
		</band>
	</background>
	<title>
		<band height="40" splitType="Stretch">
			<printWhenExpression><![CDATA[!($P{JasperCustomSubReportDatasource1}.getData()).isEmpty()]]></printWhenExpression>
			<subreport>
				<reportElement x="-20" y="-35" width="176" height="10">
                </reportElement>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource ($P{JasperCustomSubReportDatasource1}.getData())]]></dataSourceExpression>
				<subreportExpression class="net.sf.jasperreports.engine.JasperReport"><![CDATA[$P{JasperCustomSubReportLocation1}]]></subreportExpression>
			</subreport>
			<break>
				<reportElement x="0" y="0" width="804" isRemoveLineWhenBlank="true" height="1"/>
			</break>
		</band>
	</title>
	<summary>
		<band height="1" splitType="Stretch">
			<printWhenExpression><![CDATA[!($P{JasperCustomSubReportDatasource2}.getData()).isEmpty()]]></printWhenExpression>
			<subreport>
				<reportElement x="-20" y="-35" width="200" height="1" />
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource ($P{JasperCustomSubReportDatasource2}.getData())]]></dataSourceExpression>
				<subreportExpression class="net.sf.jasperreports.engine.JasperReport"><![CDATA[$P{JasperCustomSubReportLocation2}]]></subreportExpression>
			</subreport>
		</band>
	</summary>
	<noData>
		<band height="55" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="300" y="0" width="316" height="47" forecolor="#000050">
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="22" isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[No records found!!]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>
