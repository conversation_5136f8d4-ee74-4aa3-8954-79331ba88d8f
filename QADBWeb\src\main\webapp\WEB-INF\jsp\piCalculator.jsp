<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<script type="text/javascript" src="webResources/js/qadb.js"></script>
<style>
<!--
.ui-widget {
	font-family: Verdana,Arial,sans-serif;
	font-size: 1.1em;
}
.ui-widget .ui-widget {
	font-size: 1em;
}
.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
	font-family: Verdana,Arial,sans-serif;
	font-size: 1em;
}
.ui-widget-content {
	border: 1px solid #aaaaaa;
	background: #ffffff url() 50% 50% repeat-x;
	color: #222222;
}
.ui-widget-content a {
	color: #222222;
}
/* .ui-widget-header {
	border: 1px solid #aaaaaa;
	background:#2573ab  url(images/ui-bg_highlight-soft_75_cccccc_1x100.png) 50% 50% repeat-x;
	color: #e6f5fc;
	font-weight: bold;
} */
.ui-widget-header a {
	color: #222222;
}
.ui-datepicker-trigger{
background-image: url("webResources/images/Forms/Icn_Date_Picker.png");
height: 36px;
width: 36px;
background-color:white;
}
.ui-icon-circle-triangle-e{
background-image: url("webResources/images/skip_forward.png");
}
.ui-icon-circle-triangle-w{
background-image: url("webResources/images/skip_backward.png");
}
-->

#dollar {
  position: absolute;
  display: block;
  transform: translate(0, -50%);
  top: 44%;
  pointer-events: none;
  width: 25px;
  text-align: center;
  font-style: normal;
  padding-bottom: 2px;
}

#dollar2 {
  position: absolute;
  display: block;
  transform: translate(0, -50%);
  top: 44%;
  pointer-events: none;
  width: 25px;
  text-align: center;
  font-style: normal;
  padding-bottom: 0px;
}

</style>

<div id="popupPIouter">
		<!-- Popup Div Starts Here -->

	</div>
	<div id="popupPI" style="border-color: gray;border-radius: 5px;
    box-shadow: 5px 5px 5px #888888;">
		<img id="close" src="webResources/images/Misc/Icn_Close2.png"
			onclick="div_hide()">
		<div align="center" style="padding: 15px 15px 15px 15px">
			<span style="font-size: 12px"><b>Calculate Penalty
					Interest Rate</b> </span>
			<hr>
			<table style="font-size: 12px; width: 420px">
			<tr><td colspan="2"><span id="errmsg" style="color:red"></td></tr>
				<tr>
					<td>Jurisdiction:</td>
				<%-- 	<td style="height: 34px"><c:out
							value="${claimDetails.subscriberLevel.CJAJurisdiction }"></c:out>
					</td> --%>
					<c:choose>
						<c:when test="${edit != null}">
							<td class="input-control text"><input type="text" readonly="true" style="border: 0px" 
								name="juridiction" value="${eJuridiction}"/></td>
						</c:when>
						
						<c:otherwise>
						<td class="input-control text"><input type="text" readonly="true" style="border: 0px" 
								name="juridiction" value="${claimDetails.subscriberLevel.CJAJurisdiction }"/></td>
						</c:otherwise>
					</c:choose>
					<%-- <td class="input-control text"><input type="text" readonly="true" style="border: 0px" 
						name="juridiction" value="${claimDetails.subscriberLevel.CJAJurisdiction }${eJuridiction}"/></td> --%>
				</tr>
				<tr>
					<td>Product:</td>
					<td>
						<div class="input-control select">
							<select id="processTypedd"
								style="width: 200px; font-size: 12px; border-color: #919191">
								<option value="1">HMO</option>
								<option value="2">Non-HMO</option>
							</select>
						</div>
					</td>
				</tr>
				<tr>
					<td>Interest From Date:</td>
					<td>
						
						<div class="input-control text">
							<input id="datepick2" size="50" style="width: 120px" class="form-control"/>
						</div>
					</td>
				</tr>
				<tr>
					<td>Interest To Date:</td>
					<td class="input-control text">
						<div class="input-control text">
							<input size="50" id="datepick3" style="width: 120px" class="form-control"/>
						</div>
					</td>
				</tr>
				<tr>
					<td>Days of Interest:</td>
					<td class="input-control text"><input type="text" id="days"
						name="days" /></td>
				</tr>
				<tr>
					<td>Amount to which Interest is Applicable</td>
					<td class="input-control text">
					<i id="dollar2">$</i>
					<input type="text" id="amount" style="padding-left: 17px;" name="amount" /></td>
				</tr>
				<tr>
					<td height="5px"></td>
					<td height="5px"></td>
				</tr>
			</table>
			<table style="width: 420px">
				<tr>
					<td><a style="float: right;" href="#" id="cancel" onclick="div_hide()">Cancel</a></td>
					<td>
					<c:choose>
						<c:when test="${edit != null}">
						<a href="javascript:check_empty('${eJuridiction}')"
						id="submit">Ok</a>
						</c:when>
						<c:otherwise>
						<a href="javascript:check_empty('${claimDetails.subscriberLevel.CJAJurisdiction }')"
						id="submit">Ok</a>
						</c:otherwise>
					</c:choose>
					</td>
				</tr>
			</table>
		</div>
	</div>
	
<script type="text/javascript">
	/* $("#datepick2").keypress(function(e) {
	    e.preventDefault();
	});
	$("#datepick3").keypress(function(e) {
	    e.preventDefault();
	}); */

	$("#datepick2").datepicker({
	showOn:"button",
    onSelect: function(selected) {
      $("#datepick3").datepicker("option","minDate", selected)
    },
	});
	$("#datepick3").datepicker({
	showOn:"button",
		onSelect : function(selected) {
			console.log("yes triggered");
			$("#datepick2").datepicker("option","maxDate", selected)
			var start = $('#datepick2').datepicker('getDate');
			var end = $('#datepick3').datepicker('getDate');
			var days = Math.round((end - start) / 1000 / 60 / 60 / 24);
			var numbOfDays=(days+1);
			console.log(days);
			console.log(document.getElementById("days"));
			document.getElementById("days").value = numbOfDays;
		}
	}); 
	jQuery(function($){
		$("#datepick2").mask("99/99/9999",{placeholder:"MM/DD/YYYY"});
		$("#datepick3").mask("99/99/9999",{placeholder:"MM/DD/YYYY"});
	});	
	$("#datepick3").change(function(){
			var start = $('#datepick2').datepicker('getDate');
			var end = $('#datepick3').datepicker('getDate');
			var days = Math.round((end - start) / 1000 / 60 / 60 / 24);
			var numbOfDays=(days+1);
			console.log(days);
			console.log(document.getElementById("days"));
			document.getElementById("days").value = numbOfDays;
	});
	$("#datepick2").change(function(){
			var start = $('#datepick2').datepicker('getDate');
			var end = $('#datepick3').datepicker('getDate');
			var days = Math.round((end - start) / 1000 / 60 / 60 / 24);
			var numbOfDays=(days+1);
			console.log(days);
			console.log(document.getElementById("days"));
			document.getElementById("days").value = numbOfDays;
	});
	</script>
	
	