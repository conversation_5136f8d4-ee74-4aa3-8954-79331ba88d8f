<?xml version="1.0" encoding="UTF-8"?>
<web-app id="WebApp_ID" version="3.0"
	xmlns="http://java.sun.com/xml/ns/javaee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd">
	<display-name>QADBWeb</display-name>

	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
	</listener>
	<servlet>
		<servlet-name>qadb</servlet-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>

		<load-on-startup>1</load-on-startup>
	</servlet>
	<servlet-mapping>
		<servlet-name>qadb</servlet-name>
		<url-pattern>/</url-pattern>
	</servlet-mapping>

	<!-- <context-param> <param-name>log4jConfigLocation</param-name> <param-value>classpath:/main/resources/log4j.properties</param-value> 
		</context-param> -->

	<!-- <resource-ref> <res-ref-name>jdbc/QADBApp</res-ref-name> <res-type>javax.sql.DataSource</res-type> 
		<res-auth>Container</res-auth> <res-sharing-scope>Shareable</res-sharing-scope> 
		</resource-ref> -->
	<resource-ref>
		<description>DataSource</description>
		<res-ref-name>jdbc/QADBApp</res-ref-name>
		<res-type>javax.sql.DataSource</res-type>
		<res-auth>Container</res-auth>
		<res-sharing-scope>Shareable</res-sharing-scope>
		<mapped-name>jdbc/QADBApp</mapped-name>
	</resource-ref>
	<session-config>
		<session-timeout>90</session-timeout>
	</session-config>
	
</web-app>