<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="scriptlet" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="30" bottomMargin="30" whenNoDataType="NoDataSection" whenResourceMissingType="Empty" >
	<property name="com.jasperassistant.designer.Grid" value="false"/>
	<property name="com.jasperassistant.designer.SnapToGrid" value="false"/>
	<property name="com.jasperassistant.designer.GridWidth" value="12"/>
	<property name="com.jasperassistant.designer.GridHeight" value="12"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="net.sf.jasperreports.awt.ignore.missing.font" value="true"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="status" class="java.lang.String">
		<fieldDescription><![CDATA[status]]></fieldDescription>
	</field>
	<field name="errorCode" class="java.lang.String">
		<fieldDescription><![CDATA[errorCode]]></fieldDescription>
	</field>
	<field name="errorName" class="java.lang.String">
		<fieldDescription><![CDATA[errorName]]></fieldDescription>
	</field>
	<field name="errorDescription" class="java.lang.String">
		<fieldDescription><![CDATA[errorDescription]]></fieldDescription>
	</field>
	<field name="errorStatus" class="java.lang.String">
		<fieldDescription><![CDATA[errorStatus]]></fieldDescription>
	</field>
	<field name="count" class="java.lang.String">
		<fieldDescription><![CDATA[count]]></fieldDescription>
	</field>
	<group name="dummy"> 		
	<groupExpression><![CDATA["dummy"]]></groupExpression> 
		<groupHeader>
			
		</groupHeader>
	</group>
	
	<title>
		<band height="40" splitType="Stretch">
			<textField>
				<reportElement x="0" y="0" width="500" height="30" forecolor="#000080"  />
				<textElement>
					<font fontName="Times New Roman" size="20" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["List of "+$F{status}+" Errors"]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
	<band height="25" splitType="Stretch">
			<rectangle>
				<reportElement x="0" y="20" width="555" height="3" forecolor="#FFFFFF" backcolor="#000080"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="0" y="3" width="72" height="18" forecolor="#000080"  />
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[ Error ID]]></text>
			</staticText>
			<staticText>
				<reportElement x="87" y="3" width="72" height="18" forecolor="#000080"  />
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Name]]></text>
			</staticText>
			<staticText>
				<reportElement x="316" y="4" width="72" height="18" forecolor="#000080"  />
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Description]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band/>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="19" y="1" width="60" height="10"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{errorCode}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="87" y="1" width="223" height="10"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{errorName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="325" y="1" width="223" height="10"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{errorDescription}]]></textFieldExpression>
			</textField>
		</band>
		
	</detail>
	<pageFooter>
		<band height="56" splitType="Stretch">
			<textField>
				<reportElement x="381" y="20" width="100" height="30" forecolor="#000080"  />
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="481" y="20" width="100" height="30" forecolor="#000080"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="EEEE, MMMMM dd, yyyy">
				<reportElement x="4" y="20" width="133" height="17" forecolor="#000080"  />
				<textElement>
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="140" y="20" width="260" height="16" forecolor="#000080"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Quality Assurance Productivity Management]]></text>
			</staticText>
			<rectangle>
				<reportElement x="0" y="16" width="555" height="3" forecolor="#FFFFFF" backcolor="#C0C0C0"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
		</band>
	</pageFooter>
	<lastPageFooter>
		<band height="56" splitType="Stretch">
			<textField>
				<reportElement x="381" y="20" width="100" height="30" forecolor="#000080"  />
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="481" y="20" width="100" height="30" forecolor="#000080"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="EEEE, MMMMM dd, yyyy">
				<reportElement x="4" y="20" width="133" height="17" forecolor="#000080"  />
				<textElement>
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="140" y="20" width="260" height="16" forecolor="#000080"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Quality Assurance Productivity Management]]></text>
			</staticText>
			<rectangle>
				<reportElement x="0" y="16" width="555" height="3" forecolor="#FFFFFF" backcolor="#C0C0C0"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
		</band>
	</lastPageFooter>
	<summary>
		<band height="49" splitType="Stretch">
			<staticText>
				<reportElement x="12" y="10" width="94" height="30" forecolor="#000080"  />
				<textElement>
					<font fontName="Times New Roman" size="14" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Total Errors]]></text>
			</staticText>
			<textField>
				<reportElement x="106" y="11" width="100" height="30"  />
				<textElement>
					<font fontName="Arial" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{count}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
	<noData>
		<band height="55" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="180" y="0" width="316" height="47" forecolor="#000050">
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="22" isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[No records found!!]]></text>
			</staticText>
		</band>
	</noData>
</jasperReport>
