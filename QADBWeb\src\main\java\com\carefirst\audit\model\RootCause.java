package com.carefirst.audit.model;

public class RootCause {

	private String rootCauseId;
	private String rootCauseName;
	private String rootCauseDescription;
	private String errorTypeMapping;
	private String grpSammd;
	private String grpCd;
	private String userGrp;
	private String disable ;
	private String disableFrom;
	private String disableTo;
	private String applicableAssociate;
	private String applicableSupervisor ;
	private String applicableManager ;
	private String applicableDirector ;
	private String applicableDivision ;
	private String applicablePriPG ;
	private String applicableSecPG ;
	private String effectiveStartDate;
	private String effectiveEndDate;
	private String rootCauseStatus;
	private String userid;
	private String userActyp;
	private String successCode;
	private String successMsg;
	
	public String getErrorTypeMapping() {
		return errorTypeMapping;
	}
	public void setErrorTypeMapping(String errorTypeMapping) {
		this.errorTypeMapping = errorTypeMapping;
	}
	public String getGrpSammd() {
		return grpSammd;
	}
	public void setGrpSammd(String grpSammd) {
		this.grpSammd = grpSammd;
	}
	public String getGrpCd() {
		return grpCd;
	}
	public void setGrpCd(String grpCd) {
		this.grpCd = grpCd;
	}
	public String getUserGrp() {
		return userGrp;
	}
	public void setUserGrp(String userGrp) {
		this.userGrp = userGrp;
	}
	public String getUserid() {
		return userid;
	}
	public void setUserid(String userid) {
		this.userid = userid;
	}
	public String getUserActyp() {
		return userActyp;
	}
	public void setUserActyp(String userActyp) {
		this.userActyp = userActyp;
	}
	public String getSuccessCode() {
		return successCode;
	}
	public void setSuccessCode(String successCode) {
		this.successCode = successCode;
	}
	public String getSuccessMsg() {
		return successMsg;
	}
	public void setSuccessMsg(String successMsg) {
		this.successMsg = successMsg;
	}
	public String getRootCauseId() {
		return rootCauseId;
	}
	public void setRootCauseId(String rootCauseId) {
		this.rootCauseId = rootCauseId;
	}
	public String getRootCauseName() {
		return rootCauseName;
	}
	public void setRootCauseName(String rootCauseName) {
		this.rootCauseName = rootCauseName;
	}
	public String getRootCauseDescription() {
		return rootCauseDescription;
	}
	public void setRootCauseDescription(String rootCauseDescription) {
		this.rootCauseDescription = rootCauseDescription;
	}
	public String getDisable() {
		return disable;
	}
	public void setDisable(String disable) {
		this.disable = disable;
	}
	public String getDisableFrom() {
		return disableFrom;
	}
	public void setDisableFrom(String disableFrom) {
		this.disableFrom = disableFrom;
	}
	public String getDisableTo() {
		return disableTo;
	}
	public void setDisableTo(String disableTo) {
		this.disableTo = disableTo;
	}
	public String getApplicableAssociate() {
		return applicableAssociate;
	}
	public void setApplicableAssociate(String applicableAssociate) {
		this.applicableAssociate = applicableAssociate;
	}
	public String getApplicableSupervisor() {
		return applicableSupervisor;
	}
	public void setApplicableSupervisor(String applicableSupervisor) {
		this.applicableSupervisor = applicableSupervisor;
	}
	public String getApplicableManager() {
		return applicableManager;
	}
	public void setApplicableManager(String applicableManager) {
		this.applicableManager = applicableManager;
	}
	public String getApplicableDirector() {
		return applicableDirector;
	}
	public void setApplicableDirector(String applicableDirector) {
		this.applicableDirector = applicableDirector;
	}
	public String getApplicableDivision() {
		return applicableDivision;
	}
	public void setApplicableDivision(String applicableDivision) {
		this.applicableDivision = applicableDivision;
	}
	public String getApplicablePriPG() {
		return applicablePriPG;
	}
	public void setApplicablePriPG(String applicablePriPG) {
		this.applicablePriPG = applicablePriPG;
	}
	public String getApplicableSecPG() {
		return applicableSecPG;
	}
	public void setApplicableSecPG(String applicableSecPG) {
		this.applicableSecPG = applicableSecPG;
	}
	public String getEffectiveStartDate() {
		return effectiveStartDate;
	}
	public void setEffectiveStartDate(String effectiveStartDate) {
		this.effectiveStartDate = effectiveStartDate;
	}
	public String getEffectiveEndDate() {
		return effectiveEndDate;
	}
	public void setEffectiveEndDate(String effectiveEndDate) {
		this.effectiveEndDate = effectiveEndDate;
	}
	public String getRootCauseStatus() {
		return rootCauseStatus;
	}
	public void setRootCauseStatus(String rootCauseStatus) {
		this.rootCauseStatus = rootCauseStatus;
	}

	
	
}
