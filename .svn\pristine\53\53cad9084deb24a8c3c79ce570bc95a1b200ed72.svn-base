
<!DOCTYPE html>
<%@page language="java"
	contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>

<html style="background-color: #dddfe1;">

<div id="popupClaimRecordsD">
		<!-- popup claim records Starts Here -->
	</div>
	<div id="popupClaimsD"
		style="text-align: center; padding: 25px 25px 25px 25px;border-radius: 5px;box-shadow: 5px 5px 5px #888888;">
		<img id="close" src="webResources/images/Misc/Icn_Close2.png"
			onclick="divDelA_hide()"> <span style="font-size: 15px;"><b>
				<spring:message code="audit.new.delete.message" /></b> </span>
		<br>
		<br>
		<div align="center" style="width: 450">
		<table>	
			<tr>
				<a  onclick="divDelA_hide();">	<button type="reset" class="button inverse">
					<spring:message code="audit.new.risk.account.no" /></button> </a>
				<a  href="deleteAudit">	<button style="background-color: #298fd8"
							class="button default">
							<spring:message code="audit.new.risk.account.yes" /></button> </a>
				</tr>
		</table>	
		</div>
	</div>
	
	</html>