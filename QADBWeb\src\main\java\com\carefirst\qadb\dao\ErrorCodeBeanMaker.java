package com.carefirst.qadb.dao;

 import java.util.ArrayList; 
import java.util.List;

import com.carefirst.audit.model.ErrorCodeBean;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;


public class ErrorCodeBeanMaker {
	
	public JRDataSource getErrorCodeList() {
		
		List<ErrorCodeBean> errorCodeList= new ArrayList<ErrorCodeBean>();
		errorCodeList.add(produce("001", "PROVIDER # ERROR","false", ""));
		errorCodeList.add(produce("002", "CLAIM TYPE FORM", "false", ""));
		errorCodeList.add(produce("003", "CPT/REV/ADA CODE INCORRECT", "false", ""));
		errorCodeList.add(produce("004", "DATE OF SERVICE", "false", ""));
		errorCodeList.add(produce("006", "TYPE OF SERVICE", "false", ""));
		errorCodeList.add(produce("007", "MODIFIER", "false", ""));
		errorCodeList.add(produce("009", "DIAGNOSIS CODE", "false", ""));
		errorCodeList.add(produce("010", "TYPE OF BILL", "false", ""));
		errorCodeList.add(produce("011", "OMITTED/CODED BILLED AMOUNT INCORRECT", "false", ""));
		errorCodeList.add(produce("012", "PATIENT ACCT# OR MRN#", "false", ""));
		errorCodeList.add(produce("013", "SHOULD HAVE PAID SUBSCRIBER", "false", ""));
		errorCodeList.add(produce("014", "SHOULD HAVE PAID PROVIDER", "false", ""));
		errorCodeList.add(produce("015", "PATIENT NOT ON MEMBERSHIP", "false", ""));
		
JRDataSource ds = new JRBeanCollectionDataSource(errorCodeList);	
		
		// Return the wrapped collection
		return ds;
		
	}
	public ErrorCodeBean produce( String errorId,String name,String deleted, String description){
		ErrorCodeBean errorCodeBean= new ErrorCodeBean();
		errorCodeBean.setErrorId(errorId);
		errorCodeBean.setName(name);
		errorCodeBean.setDeleted(deleted);
		errorCodeBean.setDescription(description);
		return errorCodeBean;
	}

}
