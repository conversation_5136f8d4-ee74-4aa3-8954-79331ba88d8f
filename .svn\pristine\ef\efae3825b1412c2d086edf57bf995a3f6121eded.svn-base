<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<head>
<link href="webResources/css/qadb.css" rel="stylesheet">
<script type="text/javascript" src="webResources/js/qadb.js"></script>
<script type="text/javascript" src="webResources/js/metro.min.js"></script>
<!-- Local JavaScript -->
<link href="webResources/css/iconFont.css" rel="stylesheet">
<link href="webResources/css/docs.css" rel="stylesheet">
<link href="webResources/js/prettify/prettify.css" rel="stylesheet">
<link href="webResources/css/jquery-ui.css" rel="stylesheet">

<!-- Load JavaScript Libraries -->
<script type="text/javascript" src="webResources/js/jquery.loadmask.js"></script>
<script type="text/javascript" src="webResources/js/datep.js"></script>
<script type="text/javascript" src="webResources/js/jquery-ui.js"></script> 
<script src="webResources/js/jquery/jquery.min.js"></script>
<script src="webResources/js/jquery/jquery.widget.min.js"></script>
<script src="webResources/js/jquery/jquery.dataTables.js"></script>
<script type="text/javascript" src="webResources/js/jquery-ui.js"></script> 
<style>
/* Col */
/* Collapse -Expand */
.headerA {
	background: url('webResources/images/Forms/Icn_Accordion_Collapse.png')
		no-repeat;
	background-position: right 0px;
	cursor: pointer;
}

.collapsed .headerA {
	background-image:
		url('webResources/images/Forms/Icn_Accordion_Expand.png');
}

.contentA {
	height: auto;
	min-height: 100px;
	overflow: hidden;
	transition: all 0.3s linear;
	-webkit-transition: all 0.3s linear;
	-moz-transition: all 0.3s linear;
	-ms-transition: all 0.3s linear;
	-o-transition: all 0.3s linear;
	background-color:#fafafa;
}

.collapsed .contentA {
	min-height: 0px;
	height: 0px;
}

.correct {
	display: none;
}
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
	margin-right: auto;
	height: 240%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}

div.success {
	padding-left: 5px;
	padding-top: 5px;
	height: 30px; 
	background: #73AD21; 
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	color : white;
}

div.errorInfo {
    padding: 5px 5px 5px 5px ;
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	width:350px;
	font-size:11pt;
	background-color:#CE352C;
	color:white;
}

#error {
    padding: 5px 5px 5px 5px ;
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	width:350px;
	font-size:11pt;
	background-color:#CE352C;
	color:white;
}
p {
    display: block;
    text-align: left;
    color : white;
    padding-left:40px;
}

.hidden {
	display: none;
}


.ui-widget {
	font-family: Verdana,Arial,sans-serif;
	font-size: 1.1em;
}
.ui-widget .ui-widget {
	font-size: 1em;
}
.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
	font-family: Verdana,Arial,sans-serif;
	font-size: 1em;
}
.ui-widget-content {
	border: 1px solid #aaaaaa;
	background: #ffffff url() 50% 50% repeat-x;
	color: #222222;
}
.ui-widget-content a {
	color: #222222;
}
/* .ui-widget-header {
	border: 1px solid #aaaaaa;
	background:#2573ab  url(images/ui-bg_highlight-soft_75_cccccc_1x100.png) 50% 50% repeat-x;
	color: #e6f5fc;
	font-weight: bold;
} */
.ui-widget-header a {
	color: #222222;
}
.ui-datepicker-trigger{
background-image: url("webResources/images/Forms/Icn_Date_Picker.png");
height: 36px;
width: 36px;
background-color:white;
}
.ui-icon-circle-triangle-e{
background-image: url("webResources//Navbar/Icn_Right_Arrow.png");
}
.ui-icon-circle-triangle-w{
background-image: url("webResources/images/skip_backward.png");
}

</style>
<script type="text/javascript">
	var a = 1;
	$(document).ready(function() {
	if (a == 1) {
		$('#success').delay(800).fadeIn(400);
			
	}
	})

</script>


<title><spring:message code="admin.associate.title" /></title>
</head>
<body>
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container2">
		<!-- Sidebar Start-->
		<c:choose>
   		<c:when test="${edit != null}">
   			<div id="left-sidebar">
				<h2 style="color: white ; padding:32px 0px 0px 20px">
					${associateRO.firstName} ${associateRO.middleName} ${associateRO.lastName}
				</h2>
				<h3 style="color: white; padding:10px 0px 0px 20px">
					<table>
					<tr>
                      	<td style="padding-right:5px;"><img alt="errors" src="webResources/images/Sidebar/Icn_Associate_ID.png"></td>
						<td> Associate ID </td>
                	</tr>
                	<tr>
                		<td></td>
                		<td> ${associateRO.associateId}</td>
                	</tr>
					</table>
				</h3>
			</div>
    	</c:when>
    	<c:otherwise>
    		<div id="left-sidebar">
				<h2 style="color: white ; padding-top:32px"><spring:message code="admin.associate.leftNav.heading1" /></h2>
				<h3 style="color: white; padding-top:10px">
					<spring:message code="admin.associate.leftNav.heading2" />
				</h3>
			</div>
    	</c:otherwise>
    	</c:choose>

	<div id="content-container" style="padding-left: 40px">

	<!--change form action depending upon add /update action  -->

	<%String formAction=""; %>

 	<c:if test="${edit != null}"> 
  		<% formAction="updateAssociate" ;%>
    </c:if>
	<c:if test="${edit == null}"> 
  		<% formAction="saveAssociate" ;%>
    </c:if>


	<form:form id="associateForm" name="associateForm" method="GET" onsubmit="return Validates()" style="width:700px" commandName="associateForm" action="<%=formAction%>">
	<input type="hidden" id="audStatus" value="${associateRO.auditingStatus }"/>
	<input type="hidden" id="endDt" value="${associateRO.workUnitEndDate}"/>
	<input type="hidden" id="hidDt1" value="${associateRO.dateOfHire }"/>
	<input type="hidden" id="hidDt2" value="${associateRO.workUnitStartDate }"/>
	<input type="hidden" id="hidDt3" value="${associateRO.disableDate }"/>
	
	
				<div style="padding: 10px 0px 0px 0px;">
					<span class="bread1"><spring:message code="admin.associate.bread1" /> </span>
					<c:if test="${edit == null}">
					<span class="bread2"><spring:message code="admin.associate.leftNav.heading1" /></span>
					</c:if>
					<c:if test="${edit != null}">
					<span class="bread2"><spring:message code="admin.associate.update.bread" /></span>
					</c:if>
				</div>
				<table width="98%" style="padding: 10px 10px 10px 50px">
					<tr style="height: 20px; border-bottom-color: gray;">
						<c:choose>
						<c:when test="${edit != null}">
							<td width="40%"><span style="font-size: 20px">${associateRO.firstName} ${associateRO.middleName} ${associateRO.lastName}</span></td>
							<td width="60%" style="text-align: right;">
								<!-- hidden field to store associate id -->
								<input type="hidden" name="associateId" value="${associateRO.associateId}"> 
								<!--end  -->
								<button title="Cancel" type="reset" onclick="hideErrorDiv();" style="background-color: transparent; border-color: transparent; size: 10px;">
									<img src="webResources/images/Actions/Icn_Cancel.png" alt="Cancel">
								</button> 
								<!-- <a href="#" onclick="divdel_show();">
									<button title="Delete" type="button" style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Delete.png" >
									</button>
								</a>  -->
								<button title="Save" type="submit" style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png" alt="Save">
								</button>
							</td>
							</c:when>
							<c:otherwise>
								<td width="30%"><span style="font-size: 20px"><spring:message code="admin.associate.leftNav.heading1"></spring:message> </span></td>
								<td width="70%" style="text-align: right;">
									<button title="Reset" onclick="hideErrorDiv();" type="reset" style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Cancel.png">
									</button>
									<button title="Save" type="submit" style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png">
									</button>
								</td>
							</c:otherwise>
						</c:choose>
					</tr>
				</table>
				
			<!--Success Msgs  -->
			<div id="error" ></div>
			<div>
			<c:if test="${(successCode != '000') && (successCode != '201') && (successCode != null)}"> 
				<div style="padding: 5px; border-radius: 3px; width: 350px; color: white; font-size: 11pt; box-shadow: 5px 5px 5px #888888; background-color: rgb(206, 53, 44);">${successMessage}</div>
			</c:if>
			<c:if test="${success == 'SUCCESS'}"> 
				<div id="success" class="success" style="width: 240px;">Associate added successfully!</div>
			</c:if>
			<c:if test="${upSucess=='SUCCESS'}"> 
				<div id="success" class="success" style="width: 280px;">Associate details updated successfully!</div>
			</c:if>	
			<c:if test="${delSucess=='SUCCESS'}"> 
				<div id="success" style="padding-left: 5px; height: 20px; width: 220px; background: #99FF99; display: none;">Associate deleted successfully!</div>
			</c:if>	
			<%-- <c:if test="${(fn:contains(success, 'System')) || (fn:contains(upSucess, 'System')) || (fn:contains(delSucess, 'System'))}">
				<div id="success" class="errorInfo" style="width: 300px;font-size:10pt">System Error, please try again after some time !</div>
			</c:if> --%>
			<c:if test="${successCode == '201'}">
				<div id="success" class="errorInfo" style="width: 300px;">System Error, please try again after some time !</div>
			</c:if>	
			</div>
			<br>
			<div class="line-separator"></div>
			<br>
			<div style="color:red">* indicates required</div><br>
			<div class="panelContainer">
				<div class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
						<span style="color: #0070c0; font-size: 16px"><spring:message code="admin.associate.name" /></span>
						<!-- <div class="line-separator"></div> -->
					</div>
					<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
						<table class="tableForm" style="padding-left: 50px;">
							<tr>
								<td><spring:message code="admin.associate.name.first" />&nbsp;<span style="color:red">*</span></td>
								<td><div id="firstc" class="input-control text">
										<input type="text" id="first" name="firstName" autofocus="autofocus" placeholder="First Name" value="${associateRO.firstName}" />
										&nbsp;<span id="errmsgfname" style="color:red">
									</div>
								</td>
							</tr>
							<tr>
								<td><spring:message code="admin.associate.name.middle" /></td>
								<td><div id="middlec" class="input-control text">
										<input id="middle" type="text" name="middleName" placeholder="Middle Name" value="${associateRO.middleName}" />
										&nbsp;<span id="errmsgmname" style="color:red">
									</div>
								</td>
							</tr>
							<tr>
								<td><spring:message code="admin.associate.name.last" />&nbsp;<span style="color:red">*</span></td>
								<td><div id="lastc" class="input-control text">
										<input type="text" id="last" name="lastName" placeholder="Last Name" value="${associateRO.lastName}" />
										&nbsp;<span id="errmsglname" style="color:red">
									</div>
								</td>
							</tr>
						</table>
					</div>
				</div>
			</div>
			
			<div class="panelContainer">
				<div class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
						<span style="color: #0070c0; font-size: 16px"><spring:message code="admin.associate.job" /></span>
						<!-- <div class="line-separator"></div> -->
					</div>
						<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
							<table class="tableForm">
								<tr>
									<td><spring:message code="admin.associate.job.title" />&nbsp;<span style="color:red">*</span></td>
									<td><div id="jobIdc" class="input-control select">
											<select id="jobId" name="jobTitleId"  onchange="jobIdFunction();"
												style="width: 200px; font-size: 14px; border-color: #919191">
												 <option  selected value="0">Select</option>
												<c:forEach items="${jobTitles}" var="jobTitles">
													<option value="${jobTitles.jobTitleId}"
														 ${jobTitles.jobTitleId== associateRO.jobTitleId ? 'selected="selected"' : ''}>${jobTitles.jobTitles}</option>
												</c:forEach>
											</select>
										</div></td>
								</tr>
								<tr>
									<td><spring:message code="admin.associate.job.dateOfHire" />&nbsp;<span style="color:red">*</span></td>
									<td><div id="doh1" class="input-control text">
												<input id="datepick1" size="50" name="dateOfHire" value="${associateRO.dateOfHire}"/>
												&nbsp;<span id="errmsgdatepick1" style="color:red">
											</div>
									</td>
								</tr>
			
			
							</table>
						</div>
				</div>
			</div> 
			<div class="panelContainer">	
				<div class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
						<span style="color: #0070c0; font-size: 16px"><spring:message code="admin.associate.office" />&nbsp;<spring:message code="admin.associate.office.location" /></span>
						<!-- <div class="line-separator"></div> -->
					</div>
					<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
						<table class="tableForm">
							<tr>
								<td><spring:message code="admin.associate.office.location" />&nbsp;<span style="color:red">*</span></td>
								<td><div id="locIdc" class="input-control select">
										<select id="locId" name="locationId"
											style="width: 200px; font-size: 14px; border-color: #919191">
											<option  selected value="0">Select</option>
											<c:forEach items="${locations}" var="locations">
												<option value="${locations.locationId}"
													${locations.locationId== associateRO.locationId ? 'selected="selected"' : ''}>${locations.locations}</option>
											</c:forEach>
										</select>
									</div>
								</td>
							</tr>
							<tr>
								<td><spring:message code="admin.associate.office.workPhone" />&nbsp;<span style="color:red">*</span></td>
								<td><div id="phonec" class="input-control text">
										<input id="phone" type="text"  name="workPhone" value="${associateRO.workPhone}" /> &nbsp;<span id="errmsg" style="color:red"></span>
									</div>
								</td>
							</tr>
						</table>
					</div>
				</div>
			</div>
			<div class="panelContainer">	
				<div class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">	
						<span style="color: #0070c0; font-size: 16px"><spring:message code="admin.associate.workUnit" /></span>
						<!-- <div class="line-separator"></div> -->
					</div>
					<div class="contentA" style="padding-top: 10px;padding-left: 7px;">	
						<table class="tableForm">
							<tr id="wunitIdRow">
								<td style="width:33%"><spring:message code="admin.associate.workUnit.id" />&nbsp;<span style="color:red">*</span></td>
								<td><div id="wunitIdc" class="input-control select">
										<select id="wunitId" name="workUnitId"
											style="width: 300px; font-size: 14px; border-color: #919191">
											<option selected="selected" value="0">Select</option>
											<optgroup label="Id      --- Name  --------- Supervisor">
											<c:forEach items="${workunits}" var="workunits">
												<option value="${workunits.unitId}"
													${workunits.unitId== associateRO.workUnitId ? 'selected="selected"' : ''}>${workunits.unitId} . ${workunits.unitName} -- ${workunits.supervisor}</option>
											</c:forEach>
											</optgroup>
										</select>
									</div>
								</td>
							</tr>
							<tr>
								<td><spring:message code="admin.associate.workUnit.startDate" />&nbsp;<span style="color:red">*</span></td>
								<td>
								<div id="start2" class="input-control text">
									<input id="datepick2" size="50" style="width: 200px" class="form-control" name="workUnitStartDate" value="${associateRO.workUnitStartDate}"/>
									&nbsp;<span id="errmsgdatepick2" style="color:red">
								</div>
								
								
								</td>
							</tr>
							<tr>
								<td><spring:message code="admin.associate.workUnit.endDate" />&nbsp;<span style="color:red">*</span></td>
								<td>
								<div id="end3" class="input-control text">
									<input id="datepick3" size="50" style="width: 200px" class="form-control" name="workUnitEndDate" value="${associateRO.workUnitEndDate}"/>
									&nbsp;<span id="errmsgdatepick3" style="color:red">
								</div>
								
								</td>
							</tr>
						</table>
					</div>
				</div>
			</div>
				
			<div class="panelContainer">	
				<div class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
						<span style="color: #0070c0; font-size: 16px"><spring:message code="admin.associate.database" /></span>
						<!-- <div class="line-separator"></div> -->
					</div>
					<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
						<table class="tableForm">
							<tr>
								<td><spring:message code="admin.associate.database.facetsId" /></td>
								<td><div id="facetsIdc" class="input-control text">
										<input id="facetsId" type="text" name="facetsId" value="${associateRO.facetsId}" />
									</div>
								</td>
							</tr>
						</table>
					</div>
				</div>
			</div>
				
	<!--Auditing status for updating  -->
	
	<c:if test="${edit != null}">		
				<div class="panelContainer" id = "auditingStatusDiv">	
					<div class="containerA" style="padding-left:7px;padding-top: 7px;">
						<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 16px"><spring:message code="admin.associate.auditingstatus" /></span>
							<!-- <div class="line-separator"></div> -->
						</div>
						<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
							<table class="tableForm" >
							<c:choose>
					  			<c:when test="${associateRO.auditingStatus == 'N'}">
								<tr>
									<td style="width:33.2%"><spring:message code="admin.associate.edit.status" /></td>
									<td>
					  								<div class="input-control radio default-style">
														<label> <input type="radio" name="auditingStatus" onclick="disable1()"  value="Y"> <span
																class="check"></span> 
														</label>
													</div><spring:message code="admin.associate.auditingstatus.enabled"></spring:message>
					  			
					  								<div class="input-control radio default-style">
														<label> <input type="radio" name="auditingStatus" onclick="disable2()" checked="checked" value="N"> <span
																class="check"></span> </label>
													</div><spring:message code="admin.associate.auditingstatus.disabled"></spring:message>
					  					
					  					
									</td>	
								</tr>
								<tr>
									<td style="width:33.2%"><spring:message code="admin.associate.auditingstatus.disableDate" /></td>
									<td><div id="datepick4c" class="input-control text">
												<input id="datepick4" size="50" name="disableDate" value="${associateRO.disableDate}"  />
												&nbsp;<span id="errmsgdatepick4" style="color:red">
									</div>
									</td>
									
								</tr>
							</c:when>
							
							<c:otherwise>
					  			<tr>
									<td style="width:33.2%"><spring:message code="admin.associate.edit.status" /></td>
									<td>		
					  								<div class="input-control radio default-style">
														<label> <input type="radio" name="auditingStatus" onclick="disable1()" checked="checked" value="Y"> <span
																class="check"></span> 
														</label>
													</div><spring:message code="admin.associate.auditingstatus.enabled"></spring:message>
					  			
					  								<div class="input-control radio default-style">
														<label> <input type="radio" name="auditingStatus" onclick="disable2()" value="N"> <span
																class="check"></span> </label>
													</div><spring:message code="admin.associate.auditingstatus.disabled"></spring:message>
					  				</td>	
								</tr>
								<tr>
									<td style="width:33.2%"><spring:message code="admin.associate.auditingstatus.disableDate" /></td>
									<td><div id="datepick4c" class="input-control text">
												<input id="datepick4" size="50" name="disableDate" disabled="disabled" value="${associateRO.disableDate}"/>
												&nbsp;<span id="errmsgdatepick4" style="color:red">
											</div>
									</td>
									
								</tr>		
					  						
					  		</c:otherwise> 
							</c:choose>
							
							</table>
						</div>
					</div>
				</div>
	</c:if>	
				<div class="panelContainer">	
					<div class="containerA" style="padding-left:7px;padding-top: 7px;">
						<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 16px"><spring:message code="admin.associate.other" /></span>
							<!-- <div class="line-separator"></div> -->
						</div>
						<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
							<table class="tableForm">
								<tr>
									<td style="width:33%"><spring:message code="admin.associate.other.comments" /></td>
									<td><div id="commentsc" class="input-control textarea">
												<textarea id="comments" name="comments" style="width: 242px; height: 106px;font-family: Segoe UI_, Open Sans, Verdana, Arial, Helvetica, sans-serif" >${associateRO.comments}</textarea>
												&nbsp;<span id="errmsgcname" style="color:red">
											</div>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</div>
				<div class="line-separator"></div>
				<table width="98%" style="padding: 10px 10px 10px 50px">
					<tr style="height: 20px; border-bottom-color: gray;">

								<td width="30%">
									<div style="float: left">
						<a href="#top" style="float: left;"><spring:message code="qadb.backToTop" /></a>
					</div>
								</td>
								<td width="70%" style="text-align: right;">
									<button title="Reset" type="reset" onclick="hideErrorDiv();"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Cancel.png">
									</button>
									<button title="Save" type="submit"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png">
									</button></td>
					</tr>
				</table>
				
				
				
				
				

			</form:form>

		</div>
<script type="text/javascript">
	
	$("#datepick1").datepicker({
		showOn:"button"
	});
	$("#datepick4").datepicker({
		showOn:"button"
	});	
	
	
	/*To-From Date validations START*/
	$("#datepick2").datepicker({
		showOn:"button",
    	onSelect: function(selected) {
     	$("#datepick3").datepicker("option","minDate", selected)
   		},
	});
	$("#datepick3").datepicker({
		showOn:"button",
		onSelect : function(selected) {
		$("#datepick2").datepicker("option","maxDate", selected)
		}
	}); 
	/*To-From Date validations END*/
	
	/*disable on radio select  */
	
	function disable1() {
		document.getElementById('datepick4').value=""
		document.getElementById("datepick4").disabled = true;
		$("#datepick4").datepicker( "option", "showOn", "focus" );
	}
	
	function disable2() {
		//document.getElementById("datepick4").disabled = false;
		document.getElementById("datepick4").disabled = false;
		$("#datepick4").datepicker( "option", "showOn", "button" );
		
	}

	 /*form validate end*/
   
	
	/* Phone number masking start*/
	 jQuery(function($){
  
  	 $("#phone").mask("(*************");
  	 $("#datepick1").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("hidDt1").value);
  	 $("#datepick2").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("hidDt2").value);
  	 $("#datepick3").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("endDt").value);
  	 $("#datepick4").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("hidDt3").value);
   
});
	/* Phone number masking end*/
	
	$(document).ready(function() {
		if(document.getElementById("audStatus").value!="N"){
			$("#datepick4").datepicker( "option", "showOn", "focus" );
		}
		if(document.getElementById("endDt").value==""){
			$("#datepick3").datepicker( "setDate" , "12/31/9999" );
		}
		
	})	
	/*disable on radio select end */
		
  	//First name validation start
	$("#firstc").keypress(function (e) {
		if ((e.which == 0) || e.which == 8) {}
		else{
			var check = maxLimitForName(e,"errmsgfname","first");
			if(check==false){
			return false;
		}
		}
	});
	$("#firstc").keyup(function (e) {
		if(e.ctrlKey && e.keyCode==86){
			if ((e.which == 0) || e.which == 8) {
			}
			else{
				var check = maxLimitForName(e,"errmsgfname","first");
				if(check==false){
				return false;
				}
			}
		 }
	});
	$("#firstc").keyup(function (e) {
		if($("#first").val().length>50){
		var name = $("#first").val().substring(0,50);
		document.getElementById("first").value=name;
		}
	}); 
	//First name validation end
   
   //Middle name validation start
	$("#middlec").keypress(function (e) {
		if ((e.which == 0) || e.which == 8) {}
		else{
			var check = maxLimitForName(e,"errmsgmname","middle");
			if(check==false){
			return false;
		}
		}
	});
  	$("#middlec").keydown(function (e) {
		if(e.ctrlKey && e.keyCode==86){
		   if ((e.which == 0) || e.which == 8) {}
			else{
				var check = maxLimitForName(e,"errmsgmname","middle");
				if(check==false){
				return false;
			}
		}
		}
	});
	$("#middlec").keyup(function (e) {
		if($("#middle").val().length>50){
		var name = $("#middle").val().substring(0,50);
		document.getElementById("middle").value=name;
		}
	}); 
   //Middle name validation end
   
   //Last name validation start
	$("#lastc").keypress(function (e) {
		if ((e.which == 0) || e.which == 8) {}
		else{
			var check = maxLimitForName(e,"errmsglname","last");
			if(check==false){
			return false;
		}
		}
	});
  	$("#lastc").keydown(function (e) {
		if(e.ctrlKey && e.keyCode==86){
		   if ((e.which == 0) || e.which == 8) {}
			else{
				var check = maxLimitForName(e,"errmsglname","last");
				if(check==false){
				return false;
			}
			}
		}
	});
	$("#lastc").keyup(function (e) {
		if($("#last").val().length>50){
		var name = $("#last").val().substring(0,50);
		document.getElementById("last").value=name;
		}
	}); 
   //Last name validation end
    //Last name validation start
	$("#commentsc").keypress(function (e) {
		if ((e.which == 0) || e.which == 8) {}
		else if($("#comments").val().length>999){
   		$("#errmsgcname").html("Maximum limit of 1000 char.").show().fadeOut("slow");
   		return false;
   		}
 		else{}
	});
  	$("#commentsc").keydown(function (e) {
		if(e.ctrlKey && e.keyCode==86){
		   if ((e.which == 0) || e.which == 8) {}
			else if($("#comments").val().length>999){
	   		$("#errmsgcname").html("Maximum limit of 1000 char.").show().fadeOut("slow");
	   		return false;
	   		}
	 		else{}
		}
	});
	$("#commentsc").keyup(function (e) {
		if($("#comments").val().length>999){
		var name = $("#comments").val().substring(0,1000);
		document.getElementById("comments").value=name;
		$("#errmsgcname").html("Maximum limit of 1000 char.").show().fadeOut("slow");
		}
	}); 
   //Last name validation end
   
	$("#phonec").keypress(function (e) {
     //if the letter is not digit then display error and don't type anything
     if ((e.which != 8) && (e.which != 0) && ((e.which < 48) || (e.which > 57)) ) {
        //display error message
        $("#errmsg").html("Numbers only").show().fadeOut("slow");
               return false;
    }
   });
	
	$(function() {
		$('.headerA').click(function() {
			$(this).closest('.containerA').toggleClass('collapsed');
		});

	});
	
	// Enable facetsId input only for claims processor jobIdFunction
	
	function jobIdFunction() {
		 var jobId = $("#jobId").val();
    	 document.getElementById("facetsId").disabled = jobId != "1";
    	 console.log("val --->" + jobId);
   		 if(jobId != "1"){
   		 	$("#auditingStatusDiv").hide();
   		 	$("#wunitIdRow").hide();
   		 	console.log("val1 --->" + jobId);
   		 }else{
   		 	$("#auditingStatusDiv").show();
   		 	$("#wunitIdRow").show();
   		 	console.log("val 2--->" + jobId);
   		 }
	}
	
	$(function() {
					jobIdFunction();
				});
	
	
	
	
	
</script>
</div>
</div>
<jsp:include page="footer.jsp"></jsp:include>
</body>
</html>