<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib  prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<head>
<link href="webResources/css/qadb.css" rel="stylesheet">

<script type="text/javascript" src="webResources/js/metro.min.js"></script>
<!-- Local JavaScript -->

<link href="webResources/css/iconFont.css" rel="stylesheet">
<link href="webResources/css/docs.css" rel="stylesheet">
<link href="webResources/js/prettify/prettify.css" rel="stylesheet">

<!-- Load JavaScript Libraries -->
<script type="text/javascript" src="webResources/js/datep.js"></script>
<script src="webResources/js/jquery/jquery.min.js"></script>
<script src="webResources/js/jquery/jquery.widget.min.js"></script>


<script type="text/javascript" src="webResources/js/jquery.min.js"></script>
<script type="text/javascript" src="webResources/js/jquery-ui.js"></script> 
<script type="text/javascript" src="webResources/js/qadb.js"></script>
<link href="webResources/css/qadb.css" rel="stylesheet">
<link href="webResources/css/jquery-ui.css" rel="stylesheet">
<style>

#dollar {
  position: absolute;
  display: block;
  transform: translate(0, -50%);
  top: 44%;
  pointer-events: none;
  width: 25px;
  text-align: center;
  font-style: normal;
  padding-bottom: 2px;
}

/* Collapse -Expand */
.headerA {
	background: url('webResources/images/Forms/Icn_Accordion_Collapse.png')
		no-repeat;
	background-position: right 0px;
	cursor: pointer;
}

.collapsed .headerA {
	background-image:
		url('webResources/images/Forms/Icn_Accordion_Expand.png');
}

.contentA {
	height: auto;
	min-height: 100px;
	overflow: hidden;
	transition: all 0.3s linear;
	-webkit-transition: all 0.3s linear;
	-moz-transition: all 0.3s linear;
	-ms-transition: all 0.3s linear;
	-o-transition: all 0.3s linear;
	background-color:#fafafa;
}

.collapsed .contentA {
	min-height: 0px;
	height: 0px;
}

.correct {
	display: none;
}
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
	margin-right: auto;
	height: 240%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}
div.success {
	padding-left: 5px;
	padding-top: 5px;
	height: 30px; 
	background: #73AD21; 
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	color : white;
}

div.errorInfo {
    padding: 5px 5px 5px 5px ;
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	width:350px;
	font-size:10pt;
	background-color:#CE352C;
	color:white;
}

#error {
    padding: 5px 5px 5px 5px ;
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	width:350px;
	font-size:11pt;
	background-color:#CE352C;
	color:white;
}
.hidden {
	display: none;
}
.ui-widget {
	font-family: Verdana,Arial,sans-serif;
	font-size: 1.1em;
}
.ui-widget .ui-widget {
	font-size: 1em;
}
.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
	font-family: Verdana,Arial,sans-serif;
	font-size: 1em;
}
.ui-widget-content {
	border: 1px solid #aaaaaa;
	background: #ffffff url() 50% 50% repeat-x;
	color: #222222;
}
.ui-widget-content a {
	color: #222222;
}
/* .ui-widget-header {
	border: 1px solid #aaaaaa;
	background:#2573ab  url(images/ui-bg_highlight-soft_75_cccccc_1x100.png) 50% 50% repeat-x;
	color: #e6f5fc;
	font-weight: bold;
} */
.ui-widget-header a {
	color: #222222;
}
.ui-datepicker-trigger{
background-image: url("webResources/images/Forms/Icn_Date_Picker.png");
height: 36px;
width: 36px;
background-color:white;
}
.ui-icon-circle-triangle-e{
background-image: url("webResources//Navbar/Icn_Right_Arrow.png");
}
.ui-icon-circle-triangle-w{
background-image: url("webResources/images/skip_backward.png");
}
</style>

<script type="text/javascript">
	var a = 1;
	$(document).ready(function() {
	if (a == 1) {
		$('#success').delay(800).fadeIn(400);
			
	}
	})
</script>

<title><spring:message code="admin.perfGroup.title" /></title>
</head>
<body>
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container2">

		<!-- Sidebar Start-->
		<c:choose>
   			 <c:when test="${edit != null}">
   			 	
   			 	<div id="left-sidebar">
					<h2  style="color: white ; padding:32px 0px 0px 30px ">
					${performanceGroupRO.groupName}
					</h2>
					<h3 style="color: white; padding:10px 0px 0px 30px">
						<table>
						<tr>
                        <td style="padding-right:5px;"><img alt="Group" src="webResources/images/Sidebar/Icn_Peformannce_Group_ID.png"></td>
						<td>Group ID #</td>
                		</tr>
                		<tr>
                		<td></td>
                		<td>${performanceGroupRO.groupId}</td>
                		</tr>
		
					
				</table> </h3>
				</div>
   			 
    		 </c:when>
    		 <c:otherwise>
    		 		<div id="left-sidebar">
					<h2 style="color: white ; padding-top:32px">
						<spring:message code="admin.perfGroup.leftNav.heading1" />
					</h2>
					<h3 style="color: white; padding-top:10px">
						<spring:message code="admin.perfGroup.leftNav.heading2" />
						</h3>
				</div>
    		 
    		 </c:otherwise>
    		 
    		 
    	</c:choose>
		
	
		<!-- Sidebar End-->

		<div id="content-container" style="padding-left: 40px">
<%String formAction=""; %>

 	<c:if test="${edit != null}"> 
  		<% formAction="updatePerformanceGroup" ;%>
    </c:if>
	<c:if test="${edit == null}"> 
  		<% formAction="savePerformanceGroup" ;%>
    </c:if> 
			<form:form id="perfGrpForm" name="perfGrpForm" method="GET" onsubmit="return ValidatesPg()" commandName="perfGrpForm" action="<%=formAction%>" style="width:700px">
			<input type="hidden" id="pgEndDt" value="${performanceGroupRO.effectiveTo}"/>
			<input type="hidden" id="pgEndDtdis" value="${performanceGroupRO.disableTo}"/>
			<input type="hidden" id="pgEffFrm" value="${performanceGroupRO.effectiveFrom}"/>
			<input type="hidden" id="pgDisFrm" value="${performanceGroupRO.disableFrom}"/>
			
			
			
				<div style="padding: 10px 0px 0px 0px;">
					<span class="bread1"><spring:message code="admin.perfGroup.bread1" /> </span>
					<c:if test="${edit == null}">
					<span class="bread2"><spring:message code="admin.perfGroup.leftNav.heading1" /></span>
					</c:if>
					<c:if test="${edit != null}">
					<span class="bread2"><spring:message code="admin.perfGroup.update.bread2" /></span>
					</c:if>
				</div>
				<table width="98%" style="padding: 10px 10px 10px 50px">
					<tr style="height: 20px; border-bottom-color: gray;">


						<c:choose>
							<c:when test="${edit != null}">
								<td width="40%"><span style="font-size: 20px">${performanceGroupRO.groupName}
										 </span></td>
								<td width="60%" style="text-align: right;">

									<button title="Cancel" type="reset" onclick="hideErrorDiv();"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Cancel.png" alt="Cancel">
									</button> 
									<button title="Save" type="submit"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png" alt="Save">
									</button></td>
									<tr>
									</tr>
									
							</c:when>

							<c:otherwise>
								<td width="40%"><span style="font-size: 20px"><spring:message
											code="admin.perfGroup.leftNav.heading1"></spring:message> </span></td>
								<td width="60%" style="text-align: right;">
									<button type="reset" title="Cancel" onclick="hideErrorDiv();"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Cancel.png">
									</button>
									<button type="submit" title="Save"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png">
									</button></td>
							</c:otherwise>
						</c:choose>
					</tr>
				</table>
				
<div ><br>
				<div id="error" style="width: 400px"></div><div>
					<c:if test="${(successCode != '000') && (successCode != '201') && (successCode != null)}"> 
						<div id="divDuplicate" style="width: 280px;color: red;font-weight: bold;">${successMessage}</div>
					</c:if>
					<c:if test="${success == 'SUCCESS'}"> 
						<div id="success" class="success" style="width: 300px;">Performance Group added successfully!</div>
					</c:if>
					<c:if test="${upSucess=='SUCCESS'}"> 
						<div id="success" class="success" style="width: 350px;">Performance Group details updated successfully!</div>
					</c:if>	
					<c:if test="${delSucess=='SUCCESS'}"> 
						<div id="success" style="padding-left: 5px; height: 20px; width: 220px; background: #99FF99; display: none;">Performance Group deleted successfully!</div>
					</c:if>
					<%-- <c:if test="${(fn:contains(success, 'System')) || (fn:contains(upSucess, 'System')) || (fn:contains(delSucess, 'System'))}">
						<div id="success" class="errorInfo" style="width: 300px;">System Error, please try again after some time !</div>
					</c:if> --%>
					<c:if test="${successCode == '201'}">
						<div id="success" class="errorInfo" style="width: 300px;">System Error, please try again after some time !</div>
					</c:if>		
				</div>
				<br>
				<div class="line-separator"></div><br>
				<div style="color:red">* indicates required</div><br>
				<div class="panelContainer">
					<div class="containerA" style="padding-left:7px;padding-top: 7px;">
						<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 16px"><spring:message
									code="admin.perfGroup.groupDetails" />
							</span>
						</div>
						<div class="contentA" style="padding-top: 10px">
							<table class="tableForm" style="padding-left: 50px;">
								<tr>
									<td id="types" style="padding-top : 15px;padding-right: 120px;">
										<spring:message code="admin.perfGroup.groupDetails.type" />
									</td>
									
									<td>
										<c:choose>
											<c:when test="${performanceGroupRO.type=='S' }">
												<div class="input-control radio default-style margin1"
													data-role="input-control">
													<label class="input-control radio small-check">
														<input type="radio" name="type" value="P">
															<span class="check"></span>
																<span class="caption"><spring:message
													code="admin.perfGroup.groupDetails.primary" /></span>
													</label>
												</div>
												
												<div class="input-control radio default-style margin10"
													data-role="input-control" >
													<label class="input-control radio small-check">
														<input type="radio" name="type" checked="true" value="S">
															<span class="check"></span>
																<span class="caption"><spring:message
													code="admin.perfGroup.groupDetails.secondary" /></span>
													</label>
												</div>
											</c:when>
											<c:otherwise>
												<div class="input-control radio default-style margin1"
													data-role="input-control">
													<label class="input-control radio small-check">
														<input type="radio" name="type" checked="true" value="P">
															<span class="check"></span>
																<span class="caption"><spring:message
													code="admin.perfGroup.groupDetails.primary" /></span>
													</label>
												</div>
												
												<div class="input-control radio default-style margin10"
													data-role="input-control" >
													<label class="input-control radio small-check">
														<input type="radio" name="type"  value="S">
															<span class="check"></span>
																<span class="caption"><spring:message
													code="admin.perfGroup.groupDetails.secondary" /></span>
													</label>
												</div>
											</c:otherwise>
										</c:choose>
								</td>
								</tr>
								<tr>
									<td><spring:message code="admin.perfGroup.groupDetails.groupId" />&nbsp;<span style="color:red">*</span></td>
									<td><div id="groupIdc" class="input-control text">
											<input type="text" id="groupId" name="groupId"  placeholder="Group ID" value="${performanceGroupRO.groupId }" />
											&nbsp;<span id="errmsgGroupId" style="color:red">
										</div>
									</td>
								</tr>
								<tr>
									<td><spring:message code="admin.perfGroup.groupDetails.groupName" />&nbsp;<span style="color:red">*</span></td>
									<td><div id="groupNamec" class="input-control text">
											<input type="text" id="groupName" name="groupName"  placeholder="Group Name" value="${performanceGroupRO.groupName }" />
											&nbsp;<span id="errmsgGroupName" style="color:red">
										</div>
									</td>
								</tr>
								
								<!-- SVET012196 N7 Primary Perf Grp Mapping-->
								<tr id="secMappingRow">
									<td><spring:message
									code="admin.perfGroup.groupDetails.priPGAMapping" />&nbsp;<span style="color:red">*</span></td>
									<td><div class="input-control select" id="priPGAMapping">
											<select style="width: 300px; font-size: 14px; border-color: #919191" name="secPGAMappings" id="secPGAMappingDD">
  												<option value="" ><spring:message
																code="audit.new.error.selectOne" /></option>
  												<c:if test="${edit != null}">
													<c:forEach items="${priPGAList}" var="priPGAList">
														<c:set var="match" value=""></c:set>
  															 <c:forEach items="${performanceGroupRO.secPGAMappingsRO}" var="id">
																<c:if test="${priPGAList.pPgaId == id}">
																	<c:set var="match" value="true"></c:set>
																		 <option value="${priPGAList.pPgaId}"
																		${priPGAList.pPgaId == id ? 'selected="selected"' : ''}>${priPGAList.priPGA}</option>
																</c:if>
  															</c:forEach> 
  														<c:if test="${match != 'true' }">
  														 	<option value="${priPGAList.pPgaId}">${priPGAList.priPGA}</option>
  														</c:if>
  													</c:forEach> 
  												</c:if>
  												<c:if test="${edit == null}">
  													<c:forEach items="${priPGAList}" var="priPGAList">
														<option value="${priPGAList.pPgaId}">${priPGAList.priPGA}</option>
													</c:forEach>
  												</c:if>
											</select>
										</div></td>
								</tr>
								
								
								
								<c:set var="userRole" > <%=request.getHeader("iv-groups")%> </c:set> 
								
								<c:choose>
   								<c:when test="${(edit != null) && (fn:contains(userRole, 'qadb-superadmin_users')) && (!(fn:contains(userRole, 'null'))) }">
        							
        								<c:if test="${performanceGroupRO.userGrp == '' }">
        									<tr>
											<td style="padding-top : 5px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div class="input-control checkbox">
												<label> <input id="grpSammd" type="checkbox" name="grpSammd"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input id="grpCd" type="checkbox" name="grpCd"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>											
											</td>
											</tr>
		   								</c:if>	
		   								<c:if test="${performanceGroupRO.userGrp == 'ALL' }">
        									<tr>
											<td style="padding-top : 5px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div class="input-control checkbox">
												<label> <input id="grpSammd" type="checkbox" name="grpSammd" checked="checked"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input id="grpCd" type="checkbox" name="grpCd" checked="checked"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>											
											</td>
											</tr>
		   								</c:if>	
		   								<c:if test="${performanceGroupRO.userGrp == 'SAMMD' }">
        									<tr>
											<td style="padding-top : 5px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div class="input-control checkbox">
												<label> <input id="grpSammd" type="checkbox" name="grpSammd" checked="checked"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input id="grpCd" type="checkbox" name="grpCd" > <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>											
											</td>
											</tr>
		   								</c:if>
		   								<c:if test="${performanceGroupRO.userGrp == 'CD' }">
        									<tr>
											<td style="padding-top : 5px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div class="input-control checkbox">
												<label> <input id="grpSammd" type="checkbox" name="grpSammd" > <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input id="grpCd" type="checkbox" name="grpCd" checked="checked" > <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>											
											</td>
											</tr>
		   								</c:if>			
								    </c:when>
    								<c:otherwise>
        								<c:if test="${(fn:contains(userRole, 'qadb-superadmin_users')) && (!(fn:contains(userRole, 'null')))}">
										<tr>
											<td style="padding-top : 5px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div id="area" style="width:200px;">
											<div class="input-control checkbox">
												<label> <input id="grpSammd" type="checkbox" name="grpSammd"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input id="grpCd" type="checkbox" name="grpCd"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>	
											</div>										
											</td>
										</tr>
										</c:if>  
    								</c:otherwise>
								</c:choose>
								
								<c:if test="${(fn:contains(userRole, 'qadb-superadmin_users')) && (!(fn:contains(userRole, 'null')))}">
									<input type="hidden" id="sAdmin" name="sAdmin" value="SADMIN">
								</c:if>
								<c:if test="${(fn:contains(userRole, 'qadb-samd-admin_users')) && (!(fn:contains(userRole, 'null')))}">
 								<input type="hidden" name="grpSammd" value="SAMMD">
								</c:if>
								<c:if test="${(fn:contains(userRole, 'qadb-cd-admin_users')) && (!(fn:contains(userRole, 'null')))}">
									<input type="hidden" name="grpCd" value="CD">
								</c:if>
								
								<c:if test="${edit != null}">
								<tr>
									<td style="width:33.2%"><spring:message code="admin.perfGroup.update.disable" /></td>
									
									<c:choose>
										<c:when test="${null!=performanceGroupRO.disableFrom}">
											<td><div class="input-control checkbox">
											<label> <input type="checkbox" id="status" checked="checked" onclick="enableDisDate()" name="status" value="${performanceGroupRO.status}"/> <span class="check"></span>
												 </label>
											</div></td>
										</c:when>
										<c:otherwise>
											<td><div class="input-control checkbox">
											<label> <input type="checkbox" id="status" onclick="enableDisDate()" name="status" value="${performanceGroupRO.status}"/> <span class="check"></span>
												 </label>
											</div></td>
										</c:otherwise>
									</c:choose>
								</tr>
								<tr>
									<td><spring:message code="admin.perfGroup.effectiveTimeFrom" /></td>
									
									<td><div id="datepick3c" class="input-control text">
												<input id="datepick3" size="50" name="disableFrom" value="${performanceGroupRO.disableFrom}"/>
												&nbsp;<span id="errmsgdatepick3" style="color:red">
											</div>
									</td>
								</tr>
								
								<tr>
									<td><spring:message code="admin.perfGroup.effectiveTimeTo" /></td>
									
									<td><div id="datepick4c" class="input-control text">
												<input id="datepick4" size="50" name="disableTo" value="${performanceGroupRO.disableTo}"/>
												&nbsp;<span id="errmsgdatepick4" style="color:red">
											</div>
									</td>
								</tr>
								</c:if>
							</table>
						</div>
					</div>
				</div>
				
				<div class="panelContainer">
					<div class="containerA" style="padding-left:7px;padding-top: 7px;">
						<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 16px"><spring:message
									code="admin.perfGroup.effectiveTimeFrame" />
							</span>
						</div>
						<div class="contentA" style="padding-top: 10px">
							<table class="tableForm" style="padding-left: 50px;">
								<tr>
									<td><spring:message code="admin.perfGroup.effectiveTimeFrom" />&nbsp;<span style="color:red">*</span></td>
									<td><div id="datepick1c" class="input-control text">
												<input id="datepick1" size="50" name="effectiveFrom" value="${performanceGroupRO.effectiveFrom}"/>
												&nbsp;<span id="errmsgdatepick1" style="color:red">
											</div>
									</td>
								</tr>
								<tr>
									<td><spring:message code="admin.perfGroup.effectiveTimeTo" />&nbsp;<span style="color:red">*</span></td>
									<td><div id="datepick2c" class="input-control text">
												<input id="datepick2" size="50" name="effectiveTo" value="${performanceGroupRO.effectiveTo}"/>
												&nbsp;<span id="errmsgdatepick2" style="color:red">
											</div>
									</td>
								</tr>
								
			
							</table>
						</div>
					</div>
				</div>
				<div class="panelContainer">
					<div class="containerA" style="padding-left:7px;padding-top: 7px;">
						<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 16px"><spring:message
									code="admin.perfGroup.procedural" />
							</span>
						</div>
						<div class="contentA" style="padding-top: 10px">
							<table class="tableForm" style="padding-left: 50px;">
								<tr>
									<td><spring:message code="admin.perfGroup.expected" /></td>
									<td><div id="proceduralAccuracyExpc" class="input-control text">
											<input type="text" id="proceduralAccuracyExp" name="proceduralAccuracyExp" placeholder="0.00"  value="${performanceGroupRO.proceduralAccuracyExp}" />
											&nbsp;<span id="errmsgPA" style="color:red">
										</div>
									</td>
								</tr>
								<tr>
									<td><spring:message code="admin.perfGroup.penalty" /></td>
									<td><div id="proceduralMonthlyPenaltyc" class="input-control text">
											<i id="dollar">$</i>
											<input type="text" id="proceduralMonthlyPenalty" name="proceduralMonthlyPenalty" style="padding-left:17px;" value="${performanceGroupRO.proceduralMonthlyPenalty}" />
											&nbsp;<span id="errmsgPP" style="color:red">
										</div>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</div>
				
				<div class="panelContainer">
					<div class="containerA" style="padding-left:7px;padding-top: 7px;">
						<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 16px"><spring:message
									code="admin.perfGroup.dollarFrequency" />
							</span>
						</div>
						<div class="contentA" style="padding-top: 10px">
							<table class="tableForm" style="padding-left: 50px;">
								<tr>
									<td><spring:message code="admin.perfGroup.expected" /></td>
									<td><div id="dollarFrequencyExpc" class="input-control text">
											<input type="text" id="dollarFrequencyExp" name="dollarFrequencyExp" placeholder="0.00"  value="${performanceGroupRO.dollarFrequencyExp}" />
											&nbsp;<span id="errmsgDE" style="color:red">
										</div>
									</td>
								</tr>
								<tr>
									<td><spring:message code="admin.perfGroup.penalty" /></td>
									<td><div id="dollarFreqMonthlyPenaltyc" class="input-control text">
											<i id="dollar">$</i>
											<input type="text" id="dollarFreqMonthlyPenalty" name="dollarFreqMonthlyPenalty"  style="padding-left:17px;" value="${performanceGroupRO.dollarFreqMonthlyPenalty}" />
											&nbsp;<span id="errmsgDP" style="color:red">
										</div>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</div>
				<div class="panelContainer">
					<div class="containerA" style="padding-left:7px;padding-top: 7px;">
						<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 16px"><spring:message
									code="admin.perfGroup.dollarAccuracy" />
							</span>
						</div>
						<div class="contentA" style="padding-top: 10px">
							<table class="tableForm" style="padding-left: 50px;">
								<tr>
									<td><spring:message code="admin.perfGroup.expected" /></td>
									<td><div id="dollarAccuracyExpc" class="input-control text">
											<input type="text" id="dollarAccuracyExp" name="dollarAccuracyExp" placeholder="0.00"  value="${performanceGroupRO.dollarAccuracyExp}" />
											&nbsp;<span id="errmsgDA" style="color:red">
										</div>
									</td>
								</tr>
								<tr>
									<td><spring:message code="admin.perfGroup.penalty" /></td>
									<td><div id="dollarAccuracyMonthlyPenaltyc" class="input-control text">
										<i id="dollar">$</i>
											<input type="text" id="dollarAccuracyMonthlyPenalty" name="dollarAccuracyMonthlyPenalty"  style="padding-left:17px;" value="${performanceGroupRO.dollarAccuracyMonthlyPenalty}" />
											&nbsp;<span id="errmsgDM" style="color:red">
										</div>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</div>
				
						<div class="line-separator"></div>
				<table width="98%" style="padding: 10px 10px 10px 50px">
					<tr style="height: 20px; border-bottom-color: gray;">

								<td width="30%">
									<div style="float: left">
						<a href="#top" style="float: left;"><spring:message code="qadb.backToTop" /></a>
					</div>
								</td>
								<td width="70%" style="text-align: right;">
									<button  title="Reset" type="reset" onclick="hideErrorDiv();"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Cancel.png">
									</button>
									<button title="Save" type="submit"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png">
									</button></td>
					</tr>
				</table>
				
				
				
	</div>
			</form:form>
		</div>
		
		<script type="text/javascript">
		
		$(function() {
		$('.headerA').click(function() {
			$(this).closest('.containerA').toggleClass('collapsed');
		});

	});
		
		$(document).ready(function() {
		
			if(($("input[name='type']").prop('checked'))){ //returns true for Primary radio selection and false for secondary selection
			document.getElementById('types').style.padding = '15px 0px 0px 0px';
			
			$('#secMappingRow').hide();
			}
		}); 
		
		$("input[name='type']").click(function() {
    		var group = $(this).val();
			if(group == 'P'){
			document.getElementById('types').style.padding = '15px 0px 0px 0px';
			$('#secMappingRow').hide();
			}else{
			document.getElementById('types').style.padding = '15px 120px 0px 0px';
			$('#secMappingRow').show();
		   }
 		}); 
 		
		</script>
		
		
	<script type="text/javascript">
		
	$("#datepick1").datepicker({
	showOn:"button",
    onSelect: function(selected) {
      $("#datepick2").datepicker("option","minDate", selected)
    },
	});
	$("#datepick2").datepicker({
	showOn:"button",
    onSelect: function(selected) {
      $("#datepick1").datepicker("option","maxDate", selected)
    },
	});
	
	$("#datepick3").datepicker({
	showOn:"button",
    onSelect: function(selected) {
      $("#datepick4").datepicker("option","minDate", selected)
    },
	});
	$("#datepick4").datepicker({
	showOn:"button",
    onSelect: function(selected) {
      $("#datepick3").datepicker("option","maxDate", selected)
    },
	});
	/*Code to restrict user to enter date manually START */
	
	
	jQuery(function($){
  
  	 $("#datepick1").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("pgEffFrm").value);
  	 $("#datepick2").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("pgEndDt").value);
  	 $("#datepick3").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("pgDisFrm").value);
  	 $("#datepick4").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("pgEndDtdis").value);
   
});
	
	$(document).ready(function() {
	
		if(document.getElementById("pgEndDt").value==""){
			$("#datepick2").datepicker( "setDate" , "12/31/9999" );
		}
		if(document.getElementById("pgEndDtdis").value==""){
			document.getElementById("status").checked = false;
			document.getElementById("datepick3").disabled = true;
			$("#datepick3").datepicker( "option", "showOn", "focus" );
			document.getElementById("datepick4").disabled = true;
			$("#datepick4").datepicker( "option", "showOn", "focus" );
			$("#datepick4").datepicker( "setDate" , "12/31/9999" );
		}
	})
	
	function enableDisDate(){
	if($("#status").is(":checked")){
		document.getElementById("datepick3").disabled = false;
		$("#datepick3").datepicker( "option", "showOn", "button" );
		document.getElementById("datepick4").disabled = false;
		$("#datepick4").datepicker( "option", "showOn", "button" );
	}else{
		document.getElementById("datepick3").disabled = true;
		$("#datepick3").datepicker( "option", "showOn", "focus" );
		document.getElementById("datepick4").disabled = true;
		$("#datepick4").datepicker( "option", "showOn", "focus" );
	}
	}
	
	$("#groupIdc").keypress(function (e) {
		if ((e.which == 0) || e.which == 8) {}
		else if($("#groupId").val().length>14){
   			$("#errmsgGroupId").html("Maximum limit of 15 char.").show().fadeOut("slow");
   			return false;
   		}
 		else{}
	});
  	$("#groupIdc").keydown(function (e) {
		if(e.ctrlKey && e.keyCode==86){
		   if ((e.which == 0) || e.which == 8) {}
				else if($("#groupId").val().length>14){
		   		$("#errmsgGroupId").html("Maximum limit of 15 char.").show().fadeOut("slow");
		   		return false;
	   		}
	 		else{}
		}
	});
	 $("#groupIdc").keyup(function (e) {
		if($("#groupId").val().length>14){
		var name = $("#groupId").val().substring(0,15);
		document.getElementById("groupId").value=name;
		$("#errmsgGroupId").html("Maximum limit of 15 char.").show().fadeOut("slow");
		}
	});
	
	
	$("#groupNamec").keypress(function (e) {
 		if ((e.which == 0) || e.which == 8) {}
		else{
			var check = maxLimitForName(e,"errmsgGroupName","groupName");
			if(check==false){
			return false;
			}
		}
	});
  	$("#groupNamec").keydown(function (e) {
		if(e.ctrlKey && e.keyCode==86){
		   	if ((e.which == 0) || e.which == 8) {}
			else{
				var check = maxLimitForName(e,"errmsgGroupName","groupName");
				if(check==false){
				return false;
				}
			}
		}
	});
	$("#groupNamec").keyup(function (e) {
		if($("#groupName").val().length>49){
		var name = $("#groupName").val().substring(0,50);
		document.getElementById("groupName").value=name;
		$("#errmsgGroupName").html("Maximum limit of 50 char.").show().fadeOut("slow");
		}
	});	
	
	$("#proceduralAccuracyExpc").keypress(function (e) {
	
	if ((e.which == 0) || e.which == 8){
	}
	else{
		if (e.which == 46) {
			if(($("#proceduralAccuracyExp").val().length ==0) && e.which == 46){
	        	return false;
			}else if(($("#proceduralAccuracyExp").val()).indexOf(".") !== -1){
				return false;
			}else{
			}
		}
		else{
			if ((e.which != 8) && (e.which != 0) && ((e.which < 48) || (e.which > 57)) ) {
	        $("#errmsgPA").html("Numbers only").show().fadeOut("slow");
	        return false;
		    }
		    else if($("#proceduralAccuracyExp").val().length>4){
		    	$("#errmsgPA").html("Maximum limit of 5 char.").show().fadeOut("slow");
		        return false;
		    }
		}
	}
   });
   
   $("#proceduralAccuracyExpc").keydown(function (e) {
		if(e.ctrlKey && e.keyCode==86){
			if($("#proceduralAccuracyExp").val().length>4){
			    $("#errmsgPA").html("Maximum limit of 5 char.").show().fadeOut("slow");
			    return false;
	    	}		
   		}
	});
	$("#proceduralAccuracyExpc").keyup(function (e) {
		if($("#proceduralAccuracyExp").val().length>4){
		var name = $("#proceduralAccuracyExp").val().substring(0,5);
		document.getElementById("proceduralAccuracyExp").value=name;
		$("#errmsgPA").html("Maximum limit of 5 char.").show().fadeOut("slow");
		}
	});		
	
   $("#proceduralMonthlyPenaltyc").keypress(function (e) {
   
  	if ((e.which == 0) || e.which == 8){
	}
	else{
		if (e.which == 46) {
			if(($("#proceduralMonthlyPenalty").val().length ==0) && e.which == 46){
	        	return false;
			}else if(($("#proceduralMonthlyPenalty").val()).indexOf(".") !== -1){
				return false;
			}else{
			}
		}
		else{
			if ((e.which != 8) && (e.which != 0) && ((e.which < 48) || (e.which > 57)) ) {
	        $("#errmsgPP").html("Numbers only").show().fadeOut("slow");
	        return false;
		    }
		    else if($("#proceduralMonthlyPenalty").val().length>4){
		    	$("#errmsgPP").html("Maximum limit of 5 char.").show().fadeOut("slow");
		        return false;
		    }
		}
	}
   });
   
   $("#proceduralMonthlyPenaltyc").keydown(function (e) {
		if(e.ctrlKey && e.keyCode==86){
			if($("#proceduralMonthlyPenalty").val().length>4){
			    $("#errmsgPP").html("Maximum limit of 5 char.").show().fadeOut("slow");
			    return false;
	    	}		
   		}
	});
   
   $("#proceduralMonthlyPenaltyc").keyup(function (e) {
		if($("#proceduralMonthlyPenalty").val().length>4){
		var name = $("#proceduralMonthlyPenalty").val().substring(0,5);
		document.getElementById("proceduralMonthlyPenalty").value=name;
		$("#errmsgPP").html("Maximum limit of 5 char.").show().fadeOut("slow");
		}
	});
	
   $("#dollarFrequencyExpc").keypress(function (e) {
   	if ((e.which == 0) || e.which == 8){
	}
	else{
		if (e.which == 46) {
			if(($("#dollarFrequencyExp").val().length ==0) && e.which == 46){
	        	return false;
			}else if(($("#dollarFrequencyExp").val()).indexOf(".") !== -1){
				return false;
			}else{
			}
		}
		else{
			if ((e.which != 8) && (e.which != 0) && ((e.which < 48) || (e.which > 57)) ) {
	        $("#errmsgDE").html("Numbers only").show().fadeOut("slow");
	        return false;
		    }
		    else if($("#dollarFrequencyExp").val().length>4){
		    	$("#errmsgDE").html("Maximum limit of 5 char.").show().fadeOut("slow");
		        return false;
		    }
		}
	}
   });
   
   $("#dollarFrequencyExpc").keydown(function (e) {
		if(e.ctrlKey && e.keyCode==86){
			if($("#dollarFrequencyExp").val().length>4){
			    $("#errmsgDE").html("Maximum limit of 5 char.").show().fadeOut("slow");
			    return false;
	    	}		
   		}
	});
   $("#dollarFrequencyExpc").keyup(function (e) {
		if($("#dollarFrequencyExp").val().length>4){
		var name = $("#dollarFrequencyExp").val().substring(0,5);
		document.getElementById("dollarFrequencyExp").value=name;
		$("#errmsgDE").html("Maximum limit of 5 char.").show().fadeOut("slow");
		}
	});	
   
   $("#dollarFreqMonthlyPenaltyc").keypress(function (e) {
   	if ((e.which == 0) || e.which == 8){
	}
	else{
		if ((e.which == 0) || e.which == 8 || e.which == 46) {
			if(($("#dollarFreqMonthlyPenalty").val().length ==0) && e.which == 46){
	        	return false;
			}else if(($("#dollarFreqMonthlyPenalty").val()).indexOf(".") !== -1){
				return false;
			}else{
			}
		}
		else{
			if ((e.which != 8) && (e.which != 0) && ((e.which < 48) || (e.which > 57)) ) {
	        $("#errmsgDP").html("Numbers only").show().fadeOut("slow");
	        return false;
		    }
		    else if($("#dollarFreqMonthlyPenalty").val().length>4){
		    	$("#errmsgDP").html("Maximum limit of 5 char.").show().fadeOut("slow");
		        return false;
		    }
		}
	}
   });
   
   $("#dollarFreqMonthlyPenaltyc").keydown(function (e) {
		if(e.ctrlKey && e.keyCode==86){
			if($("#dollarFreqMonthlyPenalty").val().length>4){
			    $("#errmsgDP").html("Maximum limit of 5 char.").show().fadeOut("slow");
			    return false;
	    	}		
   		}
	});
   $("#dollarFreqMonthlyPenaltyc").keyup(function (e) {
		if($("#dollarFreqMonthlyPenalty").val().length>4){
		var name = $("#dollarFreqMonthlyPenalty").val().substring(0,5);
		document.getElementById("dollarFreqMonthlyPenalty").value=name;
		$("#errmsgDP").html("Maximum limit of 5 char.").show().fadeOut("slow");
		}
	});	
   
   
   $("#dollarAccuracyExpc").keypress(function (e) {
   	if ((e.which == 0) || e.which == 8){
	}
	else{
	   if (e.which == 46) {
			if(($("#dollarAccuracyExp").val().length ==0) && e.which == 46){
	        	return false;
			}else if(($("#dollarAccuracyExp").val()).indexOf(".") !== -1){
				return false;
			}else{
			}
		}else{
		     if ((e.which != 8) && (e.which != 0) && ((e.which < 48) || (e.which > 57)) ) {
		        $("#errmsgDA").html("Numbers only").show().fadeOut("slow");
		               return false;
		    }
		    else if($("#dollarAccuracyExp").val().length>4){
		    	$("#errmsgDA").html("Maximum limit of 5 char.").show().fadeOut("slow");
		        return false;
		    }
	    }
	 }
   })
   
   $("#dollarAccuracyExpc").keydown(function (e) {
		if(e.ctrlKey && e.keyCode==86){
			if($("#dollarAccuracyExp").val().length>4){
			    $("#errmsgDA").html("Maximum limit of 5 char.").show().fadeOut("slow");
			    return false;
	    	}		
   		}
	});
   $("#dollarAccuracyExpc").keyup(function (e) {
		if($("#dollarAccuracyExp").val().length>4){
		var name = $("#dollarAccuracyExp").val().substring(0,5);
		document.getElementById("dollarAccuracyExp").value=name;
		$("#errmsgDA").html("Maximum limit of 5 char.").show().fadeOut("slow");
		}
	});
	
   $("#dollarAccuracyMonthlyPenaltyc").keypress(function (e) {
   	if ((e.which == 0) || e.which == 8){
	}
	else{
	   if (e.which == 46) {
			if(($("#dollarAccuracyMonthlyPenalty").val().length ==0) && e.which == 46){
	        	return false;
			}else if(($("#dollarAccuracyMonthlyPenalty").val()).indexOf(".") !== -1){
				return false;
			}else{
			}
		}else{
		     if ((e.which != 8) && (e.which != 0) && ((e.which < 48) || (e.which > 57)) ) {
		        $("#errmsgDM").html("Numbers only").show().fadeOut("slow");
		               return false;
		    }
		    else if($("#dollarAccuracyMonthlyPenalty").val().length>4){
		    	$("#errmsgDM").html("Maximum limit of 5 char.").show().fadeOut("slow");
		        return false;
		    }
		}
	}
   })
   
   $("#dollarAccuracyMonthlyPenaltyc").keydown(function (e) {
		if(e.ctrlKey && e.keyCode==86){
			if($("#dollarAccuracyMonthlyPenalty").val().length>4){
			    $("#errmsgDM").html("Maximum limit of 5 char.").show().fadeOut("slow");
			    return false;
	    	}		
   		}
	});
   $("#dollarAccuracyMonthlyPenaltyc").keyup(function (e) {
		if($("#dollarAccuracyMonthlyPenalty").val().length>4){
		var name = $("#dollarAccuracyMonthlyPenalty").val().substring(0,5);
		document.getElementById("dollarAccuracyMonthlyPenalty").value=name;
		$("#errmsgDM").html("Maximum limit of 5 char.").show().fadeOut("slow");
		}
	});
	
	
	</script>
		
	</div>

	<jsp:include page="footer.jsp"></jsp:include>

</body>

</html>
