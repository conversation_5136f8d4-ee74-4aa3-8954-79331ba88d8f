package com.carefirst.audit.model;

import java.util.List;

public class Mapping {
	
	private String userId;
	private String userActTyp;
	private String auditorName;
	private String associateIdRS;
	private String associateName;
	private List<String> associatesIds;
	private String month;
	private String year;
	private String oldMonth;
	private String oldYear;
	private String successCode;
	private String successMsg;
	
	public String getOldMonth() {
		return oldMonth;
	}
	public void setOldMonth(String oldMonth) {
		this.oldMonth = oldMonth;
	}
	public String getOldYear() {
		return oldYear;
	}
	public void setOldYear(String oldYear) {
		this.oldYear = oldYear;
	}
	public String getAssociateIdRS() {
		return associateIdRS;
	}
	public void setAssociateIdRS(String associateIdRS) {
		this.associateIdRS = associateIdRS;
	}
	public String getAssociateName() {
		return associateName;
	}
	public void setAssociateName(String associateName) {
		this.associateName = associateName;
	}
	public String getSuccessCode() {
		return successCode;
	}
	public void setSuccessCode(String successCode) {
		this.successCode = successCode;
	}
	public String getSuccessMsg() {
		return successMsg;
	}
	public void setSuccessMsg(String successMsg) {
		this.successMsg = successMsg;
	}
	public String getUserActTyp() {
		return userActTyp;
	}
	public void setUserActTyp(String userActTyp) {
		this.userActTyp = userActTyp;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getAuditorName() {
		return auditorName;
	}
	public void setAuditorName(String auditorName) {
		this.auditorName = auditorName;
	}
	public List<String> getAssociatesIds() {
		return associatesIds;
	}
	public void setAssociatesIds(List<String> associatesIds) {
		this.associatesIds = associatesIds;
	}
	public String getMonth() {
		return month;
	}
	public void setMonth(String month) {
		this.month = month;
	}
	public String getYear() {
		return year;
	}
	public void setYear(String year) {
		this.year = year;
	}
	
	
	
}
