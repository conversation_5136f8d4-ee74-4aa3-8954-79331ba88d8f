<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>QADBWebEAR</groupId>
  <artifactId>QADBWebEAR</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <packaging>ear</packaging>
  <name>QADBWebEAR</name>
  <description/>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
  <dependencies>
    <dependency>
      <groupId>carefirst.QADBWeb</groupId>
      <artifactId>QADBWeb</artifactId>
      <version>0.0.1-SNAPSHOT</version>
      <type>war</type>
      <scope>compile</scope>
    </dependency>
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-ear-plugin</artifactId>
        <version>2.10.1</version>
        <configuration>
          <version>6</version>
          <earSourceDirectory>${basedir}/EarContent</earSourceDirectory>
          <modules>
            <webModule>
              <groupId>carefirst.QADBWeb</groupId>
              <artifactId>QADBWeb</artifactId>
              <bundleFileName>QADBWeb.war</bundleFileName>
              <bundleDir>/</bundleDir>
              <contextRoot>/QADBWeb</contextRoot>
            </webModule>
          </modules>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>