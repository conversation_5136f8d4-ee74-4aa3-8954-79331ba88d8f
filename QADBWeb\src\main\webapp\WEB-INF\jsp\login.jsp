<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="jspform" uri="http://www.springframework.org/tags/form"%>
<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<head>
<link href="webResources/css/qadb.css" rel="stylesheet">




<title>QADB</title>
<style>
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	width: 1040px;
	background-color: white;
	height: 500px;
	padding: 0.4px 15px 0px 15px;
	
}
</style>
</head>
<body class="metro">
	<div class="top">
		<div class="container">
			<header class="margin20 nrm nlm">
				<div class="clearfix">

					<div class="place-left">
						<form>
							<img src="webResources/images/Misc/logo.jpg" alt="CareFirst">
						</form>
					</div>
				</div>

			</header>
		</div>
	</div>


	<%=request.getHeader("sn")%>--
		 	<%=request.getHeader("givenname")%>
				<%=request.getHeader("iv-user")%>--
		 	<%=request.getHeader("iv-groups")%>


	<center>
		<div class="container2">

<center>
<jspform:form action="authorize" method="post" commandName="usermodel" style="width:50%">

Enter username : <jspform:input path="userId"/> <BR><BR>

<input type="submit" value="login">

</jspform:form>
		</center>

		</div>
	</center>	

	<div class="bottom">
		<div class="container">
			<footer style="font-size: 16px">
				<center>&copy;&nbsp; CareFirst of Mary Land Inc. 2015 . All
					Rights Reserverd</center>
			</footer>
		</div>
	</div>
</body>

</html>