/**
 * File: com.carefirst.provider.redesign.utils.TextEditorUtil.java
 * Created on: Oct 10, 2011
 * 
 */
package com.carefirst.qadb.utils;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import javax.portlet.PortletRequest;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.log4j.Logger;

import com.carefirst.qadb.constant.QADBConstants;

/**
 * Description : This is the utility class for formatting of rich text data
 * 
 * <AUTHOR> Technologies Ltd
 * 
 * **********      Modification History      **********
 * Date: 
 * Modified By:
 * Change Description:
 */
public class TextEditorUtil {
	
	static Logger log = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);
	
	/**
	 * Method to place appropriate html tags in the patient history data to render the information on jsp page
	 * @param richTextData
	 * @return String
	 */
	
	
	public static HashMap<String,String> arrMap= new HashMap<String, String>();
	public static HashMap<String,String> arrValMap= new HashMap<String, String>();
	public static HashMap<String,String> latinCharMap= new HashMap<String, String>();
	
	@SuppressWarnings("deprecation")
	public static String formatRichTextData(String richTextData){
		//log.info("Enter TextEditorUtil . formatRichTextData()");
		if(null != richTextData){
			richTextData=StringEscapeUtils.unescapeHtml(richTextData);
			Set<String> keyset = arrMap.keySet();
			if(keyset.size()<=0){
				//hardcode the data in both the maps
			}
			
			for (String key:keyset){
				richTextData = richTextData.replaceAll("\\"+key, arrMap.get(key));
			}
			
			try{
				richTextData=java.net.URLDecoder.decode(richTextData);	
			}catch(Exception e){
				log.error("Error caused dute to encoding and decoding of :"+richTextData);
			}
			keyset.clear();
			
			keyset = arrValMap.keySet();
			for (String key:keyset){
				richTextData = richTextData.replaceAll(key,"\\"+arrValMap.get(key));
			}
			
			//log.info("rictText Data Before removing special Chars :\t"+richTextData);
			Set<String> latinKeys = latinCharMap.keySet();
			for(String key:latinKeys){
				if(richTextData.contains(key)){
					richTextData =richTextData.replaceAll(key,latinCharMap.get(key));
				}
			}
			
			for(String val:QADBConstants.SPCLCHARLATINARR){
				if(richTextData.contains(val)){
					richTextData =richTextData.replaceAll(val,"");
				}
			}
			//log.info("rictText Data After removing special Chars :\t"+richTextData);
			
			richTextData = richTextData.replaceAll("&lt;", "<");
			richTextData = richTextData.replaceAll("&gt;", ">");
			richTextData = richTextData.replaceAll("<NOBR>", "");
			richTextData = richTextData.replaceAll("</NOBR>", "");

		}
	//	log.info("Exit TextEditorUtil . formatRichTextData()"+richTextData);
		return richTextData;
	}

	
public static void main(String[] args) {
	
}
	
	
	
	
}
