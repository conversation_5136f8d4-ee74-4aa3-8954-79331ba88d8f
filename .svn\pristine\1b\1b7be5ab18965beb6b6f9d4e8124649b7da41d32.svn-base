<!DOCTYPE HTML><%@page language="java"
	contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>

<html>
<head>
<!-- Load JavaScript Libraries -->


<!-- Local JavaScript -->

<link href="webResources/css/iconFont.css" rel="stylesheet">
<link href="webResources/css/docs.css" rel="stylesheet">
<link href="webResources/js/prettify/prettify.css" rel="stylesheet">

<!-- Load JavaScript Libraries -->
<script src="webResources/js/jquery/jquery.dataTables.js"></script>

</head>



<body>
	<div style="padding: 10px 0px 0px 50px;">
		<span class="bread1">Auditing > </span><span class="bread1">
				Audits > </span><span class="bread2">${eDCN} </span>
	</div>
	<form:form id="statForm" name="statForm" method="GET"
		commandName="statForm" style="width:700px;">

		<table width="98%" style="padding: 10px 10px 10px 50px">
			<tr style="height: 20px; border-bottom-color: gray;">
				<td width="40%" style="padding-left: 50px;"><span
					style="font-size: 20px">DCN # ${eDCN} </span>
				</td>
				<td width="60%" style="text-align: right;">
					
					
					<!-- <button
						style="background-color: transparent; border-color: transparent; size: 10px;">
						<img src="webResources/images/Actions/Icn_Save.png">
					</button>
					<button
						style="background-color: transparent; border-color: transparent; size: 10px;">
						<img src="webResources/images/Actions/Icn_Preview.png">
					</button>
					<button
						style="background-color: transparent; border-color: transparent; size: 10px;">
						<img src="webResources/images/Actions/Icn_Print.png">
					</button>
					<button
						style="background-color: transparent; border-color: transparent; size: 10px;">
						<img src="webResources/images/Actions/Icn_Search.png">
					</button>
					<button
						style="background-color: transparent; border-color: transparent; size: 10px;">
						<img src="webResources/images/Actions/Icn_Snapshot.png">
					</button> -->
				</td>
			</tr>
			<div style="color: red;">
				<c:if test="${claimDetails.statusBlock.statusCode=='02' }">
					<c:forEach items="${claimDetails.statusBlock.message }"
						var="messagedesc">
						<c:out value="${messagedesc.value }"></c:out>
					</c:forEach>
				</c:if>
			</div>
		</table>

		<div class="panel panel-default"
			style="padding: 10px 10px 10px 50px; width: 98%">
			<h3><spring:message code="audit.edit.created" /></h3>
			<p>
			<h3>${statsCreated.savedBy}</h3>
			</p>
			<p>${statsCreated.savedTime} ${statsCreated.savedDate}</p>
		</div>
		<div style="padding: 10px 10px 10px 50px">
			<table width="100%"
				style="font-size: 18px; padding: 10px 10px 10px 50px">
				<tr height="30px">
					<td width="50%" style="color: #0070c0;"><spring:message
							code="audit.edit.history" /></td>
				</tr>
			</table>
		</div>
		<div class="panel panel-default"
			style="padding: 10px 10px 10px 50px; width: 98%">


			<table class="table striped hovered dataTable" id="example"
				>
				<thead>
					<tr style="height:30px" >
						<td class="text-left" font-size="25px"><spring:message code="audit.edit.history.column1" /></td>
						<td class="text-left" font-size="25px"><spring:message code="audit.new.dcn.records.column2" /></td>
						<td class="text-left" font-size="25px"><spring:message code="audit.new.dcn.records.column3" /></td>
					</tr>
				</thead>

				<tbody>

					<%-- <c:if test="${claimDetails.statusBlock.statusCode=='02' }"> --%>
						<c:forEach items="${stat}"
							var="stats">
						<tr style="height:30px" >
							<a href=""><td>${stats.savedBy} </td></a>
							<td>${stats.savedTime}</td>
							<td>${stats.savedDate}</td>
						</tr>
						</c:forEach>
					<%-- </c:if> --%>

				</tbody>


			</table>

			<script>
				$(document).ready(function() {
					var table = $('#example').DataTable();

					$('#example tbody').on('click', 'tr', function() {
						var data = table.row(this).data();
						/* alert('You clicked on ' + data[0] + '\'s row'); */
					});
				});
			</script>

		</div>

	</form:form>

</body>
</html>