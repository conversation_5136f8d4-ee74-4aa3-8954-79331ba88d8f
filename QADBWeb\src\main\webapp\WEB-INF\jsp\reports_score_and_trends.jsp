<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>

<head>
<link href="webResources/css/qadb.css" rel="stylesheet">

<script type="text/javascript" src="webResources/js/metro.min.js"></script>
<!-- Local JavaScript -->

<link href="webResources/css/iconFont.css" rel="stylesheet">
<link href="webResources/css/docs.css" rel="stylesheet">
<link href="webResources/js/prettify/prettify.css" rel="stylesheet">
<link href="webResources/css/jquery-ui.css" rel="stylesheet">

<!-- Load JavaScript Libraries -->

<script src="webResources/js/jquery/jquery.min.js"></script>
<script src="webResources/js/jquery/jquery.widget.min.js"></script>
<script type="text/javascript" src="webResources/js/jquery-ui.js"></script>
<script type="text/javascript" src="webResources/js/qadb.js"></script>
<script type="text/javascript" src="webResources/js/datep.js"></script>
<style>
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
	margin-right: auto;
	height: 210%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}

.ui-widget {
	font-family: Verdana, Arial, sans-serif;
	font-size: 1.1em;
}

.ui-widget .ui-widget {
	font-size: 1em;
}

.ui-widget input, .ui-widget select, .ui-widget textarea, .ui-widget button
	{
	font-family: Verdana, Arial, sans-serif;
	font-size: 1em;
}

.ui-widget-content {
	border: 1px solid #aaaaaa;
	background: #ffffff url() 50% 50% repeat-x;
	color: #222222;
}

.ui-widget-content a {
	color: #222222;
}

/* .ui-widget-header {
	border: 1px solid #aaaaaa;
	background: #2573ab url(images/ui-bg_highlight-soft_75_cccccc_1x100.png)
		50% 50% repeat-x;
	color: #e6f5fc;
	font-weight: bold;
} */

.ui-widget-header a {
	color: #222222;
}

.ui-datepicker-trigger {
	background-image: url("webResources/images/Forms/Icn_Date_Picker.png");
	height: 36px;
	width: 36px;
	background-color: white;
}

.ui-icon-circle-triangle-e {
	background-image: url("webResources//Navbar/Icn_Right_Arrow.png");
}

.ui-icon-circle-triangle-w {
	background-image: url("webResources/images/skip_backward.png");
}
</style>
<!-- /* Collapse -Expand */ -->
<style>
.headerA {
	background: url('webResources/images/Forms/Icn_Accordion_Collapse.png')
		no-repeat;
	background-position: right 0px;
	cursor: pointer;
}

.collapsed .headerA {
	background-image:
		url('webResources/images/Forms/Icn_Accordion_Expand.png');
}

.contentA {
	height: auto;
	min-height: 100px;
	overflow: hidden;
	transition: all 0.3s linear;
	-webkit-transition: all 0.3s linear;
	-moz-transition: all 0.3s linear;
	-ms-transition: all 0.3s linear;
	-o-transition: all 0.3s linear;
	background-color: #fafafa;
}

.collapsed .contentA {
	min-height: 0px;
	height: 0px;
}

.correct {
	display: none;
}
</style>

<script type="text/javascript">
	function collapseExpand(divId) {
		if (document.getElementById(divId).style.display == 'none') {
			document.getElementById(divId).style.display = 'inline';
		} else {
			document.getElementById(divId).style.display = 'none';
		}
	}
</script>


<title>Reports-Scores & Trends</title>
</head>
<body>
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container2">

		<!-- Sidebar Start-->
		<div id="left-sidebar">
			<h2 style="color: white;padding-top: 32px;">
				<spring:message code="reports.scores.leftNav.heading1"></spring:message>
			</h2>
			<h3 style="color: white;">
				<spring:message code="reports.scores.leftNav.heading2"></spring:message>
			</h3>


		</div>
		<!-- Sidebar End-->

		<div id="content-container" style="padding: 10px 0px 0px 40px;">
			<span class="bread1"><spring:message code="reports.bread1"></spring:message> </span><span class="bread2"><spring:message code="reports.scores.leftNav.heading1"></spring:message></span>

			<form:form id="scoresForm" name="scoresForm" method="GET"
				commandName="scoresForm" action="" style="width:700px">

				<br>
				<div class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA"
						style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
						<span style="color: #0070c0;font-size:16px"><spring:message
								code="reports.scores.subjectOfReport"></spring:message></span>
					</div>

					<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
						<table style="width:98%" class="tableForm1">
							<tr>
								<td width="30%" style="padding:5px 0px 0px 5px"><spring:message
										code="reports.scores.subjectOfReport.subject"></spring:message></td>
								<td width="70%">
									<div class="input-control select">
										<select id="subject" name="subject"
											onchange="getNameDropdown();">
											<option value="ASSOCIATE">Associate</option>
											<option value="SUPERVISOR">Supervisor</option>
											<option value="MANAGER">Manager</option>
											<option value="DIRECTOR">Director</option>
											<option value="DIVISION">Division</option>
											<option value="LOB">LOB</option>
											<option value="PPG">Primary Performance Group</option>
											<option value="SPG">Secondary Performance Group</option>
											<!-- <option value="HB3">HB3</option> -->
										</select>
									</div>
								</td>
							</tr>
							<tr>
								<td width="30%" style="padding:5px 0px 0px 5px"><spring:message
										code="reports.scores.subjectOfReport.Name"></spring:message></td>
								<td width="70%">

									<div class="input-control select" id="name_div">
										<jsp:include page="../jsp/reports_nameDropDown_Div.jsp"></jsp:include>

										<%-- <select id="name" name="name">
									<c:forEach items="${claimProcessors}" var="claimProcessors">
													<option value="${claimProcessors.associateId}"
														 ${claimProcessors.associateId== associateRO.associateId ? 'selected="selected"' : ''}>${claimProcessors.associateName}</option>
												</c:forEach>
								</select> --%>
									</div>
								</td>
							</tr>

						</table>
					</div>
				</div>
				<br>

				<div class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA"
						style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
						<span style="color: #0070c0;font-size:16px"><spring:message
								code="reports.scores.reportType"></spring:message></span>
					</div>

					<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
						<table style="width:98%" class="tableForm1">
							<tr>
								<td width="30%" style="padding:5px 0px 0px 5px"><spring:message
										code="reports.scores.reportType.type"></spring:message></td>
								<td width="70%">
									<div class="input-control select">
										<select id="type" name="type">
											<option value="SCORES">Audit Scores</option>
											<option value="TRENDS">Error Trends</option>
										</select>
									</div>
								</td>
							</tr>
							<tr>
								<td width="30%" style="padding:5px 0px 0px 5px"><spring:message
										code="reports.scores.reportType.subType"></spring:message></td>
								<td width="70%">
									<div class="input-control select">
										<select name="subType">
											<option value="N">Non-Mock</option>
											<option value="Y">Mock</option>
											<option value="ALL">All</option>
										</select>
									</div>
								</td>
							</tr>
							<tr>
								<td width="30%" style="padding:5px 0px 0px 5px"><spring:message
										code="audit.new.type"></spring:message></td>
								<td width="70%">
									<div class="input-control select">
										<select name="auditType">
											<option value="0">All</option>
											<option value="2">Pre Pay</option>
											<option value="1">Post Pay</option>
										</select>
									</div>
								</td>
							</tr>
							<!-- SVET012164 N1  Pri PGA and Sec PGA-->
							<tr id="pPGArow">
									<td width="30%" style="padding:0px 0px 0px 5px"><spring:message code="audit.new.primary.pga.special.audits" />
									</td>
									<td>
										<div class="input-control select">
											<select id="priPGA" name="priPGA" onchange="getSecPGADropdown();"
															style="font-size: 14px; border-color: #919191">
															<option  selected value="0">All</option>
															<c:forEach items="${priPGALists}" var="priPGAList">
																<option value="${priPGAList.pPgaId}"
																	 ${priPGAList.pPgaId== advSearch.priPGA ? 'selected="selected"' : ''}>${priPGAList.priPGA}</option>
															</c:forEach>
											</select>			
										</div>
									</td>
							</tr>
							<tr id="sPGArow">
									<td width="30%" style="padding:0px 0px 0px 5px"><spring:message code="audit.new.secondary.pga.special.audits" />
									</td>
									<td>
										<div class="input-control select" id="secPGA" >
											<jsp:include page="../jsp/audit_general_secPGADropDown_Div.jsp"></jsp:include>
										</div>
									</td>
							</tr>
							<tr>
								<td width="30%" style="padding:5px 0px 0px 5px"><spring:message
										code="reports.scores.reportType.e2e"></spring:message></td>
								<td width="70%">
									<div class="input-control select">
										<select name="e2e">
											<option value="ALL">All</option>
											<option value="Y">E2E</option>
											<option value="N">Non-E2E</option>
										</select>
									</div>
									<div class="input-control checkbox ">
										<label> 
											<input name="e2eMethod" type="checkbox" value="Y"> 
												<span class="check"></span>
												E2E Scoring Methodology 
										</label>
									</div>
								</td>
							</tr>

						</table>
					</div>
				</div>

				<br>
				<div class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA"
						style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
						<span style="color: #0070c0;font-size:16px"><spring:message
								code="reports.scores.timePeriod"></spring:message></span>
					</div>



					<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
						<table style="width:98%" class="tableForm1">
							<tr>
								<td width="30%" style="padding:5px 0px 0px 5px"><spring:message
										code="reports.scores.timePeriod.use"></spring:message></td>
								<td>
									<div class="input-control radio default-style">
										<label> <input type="radio" name="use" id="TF"
											onclick="disable1()" checked="checked" value="TF"> <span
											class="check"></span>
										</label>

									</div> <spring:message code="reports.scores.timePeriod.use.timeFrame"></spring:message>
									<div class="input-control radio default-style">
										<label> <input type="radio" name="use" id="DR"
											onclick="disable2()" value="DR"> <span class="check"></span>
										</label>

									</div> <spring:message code="reports.scores.timePeriod.use.date"></spring:message>
								</td>
							</tr>
						</table>


						<%-- 	
				<div  class="containerA" style="padding-left:7px;padding-top: 7px;">
		<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
						<span style="color: #0070c0;font-size:16px;padding:5px 0px 0px 5px;"><spring:message
						code="reports.scores.timePeriod.use.timeFrame"></spring:message></span>
					</div>
				<div class="contentA" style="padding-top: 10px;padding-left: 7px;"> --%>

						<span
							style="color: #0070c0;font-size:16px;padding:5px 0px 0px 5px;"><spring:message
								code="reports.scores.timePeriod.use.timeFrame"></spring:message></span>
						<table style="width:98%" class="tableForm1">

							<tr>
								<td width="30%" style="padding:5px 0px 0px 15px"><spring:message
										code="reports.scores.timePeriod.use.timeFrame"></spring:message></td>
								<td>
									<div class="input-control select">
										<select id="T2" name="timeframe">
											<option value="N">None</option> 
											<option value="M" selected="selected">Monthly</option>
											<option value="Q">Quarterly</option>
											<option value="S">SemiAnnual</option>
										</select>
									</div>
								</td>
							</tr>
							<tr>
								<td width="30%" style="padding:5px 0px 0px 15px"><spring:message
										code="reports.scores.timePeriod.period"></spring:message></td>
								<td>
									<div class="input-control select">
										<select id="T3" name="period">
											<option value="N">None</option>
										</select>
									</div>
								</td>
							</tr>
							<tr>
								<td width="30%" style="padding:5px 0px 0px 15px">
									<%-- <spring:message
								code="reports.scores.timePeriod.year"></spring:message> --%>
									Year
								</td>
								<td>
									<div class="input-control select">
										<select id="T1" name="year">
										</select>
									</div>
								</td>
							</tr>
						</table>



						<%-- <div  class="containerA" style="padding-left:7px;padding-top: 7px;">
		<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
						<span style="color: #0070c0;font-size:16px;padding:5px 0px 0px 5px"><spring:message
						code="reports.scores.timePeriod.use.date"></spring:message></span>
					</div>
				<div class="contentA" style="padding-top: 10px;padding-left: 7px;"> --%>
						<span
							style="color: #0070c0;font-size:16px;padding:5px 0px 0px 5px"><spring:message
								code="reports.scores.timePeriod.use.date"></spring:message></span>

						<table style="width:98%" class="tableForm1">
							<tr>
								<td width="30%" style="padding:5px 0px 0px 15px"><spring:message
										code="reports.scores.timePeriod.from"></spring:message></td>
								<td>
									<div class="input-control text">
										<input id="datepick1" name="fromDate" size="50" disabled
											required />

									</div>
								</td>
							</tr>
							<tr>
								<td width="30%" style="padding:5px 0px 0px 15px"><spring:message
										code="reports.scores.timePeriod.to"></spring:message></td>
								<td>
									<div class="input-control text">
										<input id="datepick2" name="toDate" size="50" disabled
											required />

									</div>
								</td>
							</tr>
						</table>
					</div>
				</div>
				<br>
				<!--Commented Status field as not required in reports  -->
				<%--  <span style="color: #0070c0;font-size:16px"><spring:message code="reports.scores.statusHeader"></spring:message></span>
		<div class="line-separator"></div>
		  <br>
		 
		  <table style="width:98%" class="tableForm1" >
		 <tr>
		  		<td width="30%"><spring:message code="reports.scores.status"></spring:message></td>
		  		<td width="70%">
		  			<div class="input-control select" >
							<select>
								<option class="s" selected>Enabled</option>
								<option>Disabled</option>
								<option>All</option>
							</select>
						</div>
		  		</td>
		  </tr>
		  
		  
		  </table> --%>
				<div style="width:750px">
					<div id="nonXls" style="float:right">
						<button type="reset" id="reset" class="button inverse">Reset</button>
						<button type="submit" id="generate"
							style="background-color:#298fd8" class="button default">Generate</button>
					</div>
					<div id="xls" style="float:right">
						<button type="reset" id="reset" class="button inverse">Reset</button>
						<button type="submit" id="generatePdf"
							style="background-color:#298fd8" class="button default">Generate
							PDF</button>
						<button type="submit" id="generateXls"
							style="background-color:#298fd8" class="button default">Generate
							XLS</button>
					</div>
				</div>

			</form:form>




			<%-- </form:form> --%>

			<script type="text/javascript">
				$("#datepick1").datepicker(
						{
							showOn : "button",
							onSelect : function(selected) {
								$("#datepick2").datepicker("option", "minDate",
										selected)
							},
						});
				$("#datepick2").datepicker(
						{
							showOn : "button",
							onSelect : function(selected) {
								$("#datepick1").datepicker("option", "maxDate",
										selected)
							},
						});

				jQuery(function($) {
					$("#datepick1").mask("99/99/9999", {
						placeholder : "MM/DD/YYYY"
					});
					$("#datepick2").mask("99/99/9999", {
						placeholder : "MM/DD/YYYY"
					});
				});
			</script>

			<!-- Radio disable script -->
			<script>
				$(document)
						.ready(
								function() {
									/*year values with current year from 2000  */
									var max = new Date().getFullYear(), min = 2000,

									select = document.getElementById('T1');

									for (var i = max; i >= min; i--) {
										var opt = document
												.createElement('option');
										opt.value = i;
										opt.innerHTML = i;
										select.appendChild(opt);
									}

									/*Enable disable TF  */
									var use = document
											.querySelector('input[name="use"]:checked').value;

									if (use == "TF") {
										disable1();
									}
									if (use == "DR") {
										disable2();
									}
								})
				function disable1() {
					document.getElementById("datepick1").disabled = true;
					document.getElementById("datepick2").disabled = true;
					$("#datepick1").datepicker("option", "showOn", "focus");
					$("#datepick2").datepicker("option", "showOn", "focus");
					document.getElementById("T1").disabled = false;
					document.getElementById("T2").disabled = false;
					document.getElementById("T3").disabled = false;
				}
				function disable2() {
					document.getElementById("datepick1").disabled = false;
					document.getElementById("datepick2").disabled = false;
					$("#datepick1").datepicker("option", "showOn", "button");
					$("#datepick2").datepicker("option", "showOn", "button");
					document.getElementById("T1").disabled = true;
					document.getElementById("T2").disabled = true;
					document.getElementById("T3").disabled = true;
				}
			</script>

			<script type="text/javascript">
				/* Script for collapse */
				$(function() {
					$('.headerA').click(
							function() {
								$(this).closest('.containerA').toggleClass(
										'collapsed');
							});

				});
			</script>


			<!-- Script for dependent Dropdown for timeframe -->
			<script type="text/javascript">
				$("#T2").on(
						"change",
						function() {
							$("#T3 option").hide().removeAttr("selected")
									.parent().find(
											"option[value^=" + $(this).val()
													+ "]").show().first().prop(
											"selected", true);

						});
				$("#T2").change();
			</script>
			
			<script type="text/javascript">
				function getNameDropdown(val) {
					if (val == null) {
						var subject = document.getElementById("subject").value;
					} else {
						var subject = 'ASSOCIATE';
					}
					var url = "nameDropDown";
					console.log("inpt--->" + subject);
					$.ajax({
						type : "GET",
						url : url,
						dataType : "html",
						data : {
							'subject' : subject
						},
						success : function(response) {
							$("#name_div").html(response);
						},
						error : function() {

						}

					});
				}
			</script>
			<script type="text/javascript">
				$("#generate")
						.click(
								function() {

									var type = $('#type').val();

									$("#scoresForm").removeAttr("action");

									if (type == "SCORES") {
										$("#scoresForm").attr("action",
												"ScoresReport");
									} else if (type == "TRENDS") {
										$("#scoresForm").attr("action",
												"TrendsReport");
									} else if (type == "MONETARY") {
										$("#scoresForm").attr("action",
												"Monetary_Error_Sheet");
									}

								});

				$("#generatePdf").click(
						function() {

							var type = $('#type').val();

							$("#scoresForm").removeAttr("action");

							if (type == "MONETARY") {
								$("#scoresForm").attr("action",
										"Monetary_Error_Sheet");
							}

						});

				$("#generateXls").click(function() {

					var type = $('#type').val();

					$("#scoresForm").removeAttr("action");

					if (type == "MONETARY") {
						$("#scoresForm").attr("action", "MonetaryErrorSheet");
					}

				});
			</script>
			<!--Reset form  -->
			<script type="text/javascript">
				$(document)
						.ready(
								function() {
									//hide show xls pdf button for manager1 - setting onload values
									$("#xls").hide();
									$("#nonXls").show();

									getPeriodValues();
									getNameDropdown();
									
									//SVET012164 onload hide for pri/sec PGA
									$("#pPGArow").hide();
									$("#sPGArow").hide();

									$("#reset")
											.click(
													function() {
														document
																.getElementById("datepick1").disabled = true;
														document
																.getElementById("datepick2").disabled = true;
														$("#datepick1")
																.datepicker(
																		"option",
																		"showOn",
																		"focus");
														$("#datepick2")
																.datepicker(
																		"option",
																		"showOn",
																		"focus");
														document
																.getElementById("T1").disabled = false;
														document
																.getElementById("T2").disabled = false;
														document
																.getElementById("T3").disabled = false;
														getNameDropdown("Asso");

													});
								});

				$("#subject")
						.change(
								function() {
									$("#type").empty();
									var options = $("#type");
									if (($("#subject :selected").text()) == "Manager"
											|| ($("#subject :selected").text()) == "Supervisor"
											|| ($("#subject :selected").text()) == "Division") {
										options.append(new Option(
												"Audit Scores", "SCORES"));
										options.append(new Option(
												"Error Trends", "TRENDS"));
										options.append(new Option(
												"Monetary Error Sheet",
												"MONETARY"));
									} else {
										options.append(new Option(
												"Audit Scores", "SCORES"));
										options.append(new Option(
												"Error Trends", "TRENDS"));
									}

									if ((($("#subject :selected").text()) == "Manager")
											&& (($("#type").val()) == "MONETARY")) {
										$("#nonXls").hide();
										$("#xls").show();
									} else {
										$("#xls").hide();
										$("#nonXls").show();
									}
									
									/* SVET012164 N1 hide pri/sec PGA */
									if ((($("#subject :selected").text()) == "Supervisor") || 
									(($("#subject :selected").text()) == "Manager") || 
									(($("#subject :selected").text()) == "Director")) {
										$("#pPGArow").show();
										$("#sPGArow").show();
									} else {
										$("#pPGArow").hide();
										$("#sPGArow").hide();
									}
									//$("#type").empty().append(options);
								});

				$("#type").change(
						function() {
							//hide show xls pdf button for manager2
							if ((($("#subject :selected").text()) == "Manager")
									&& (($("#type").val()) == "MONETARY")) {
								$("#nonXls").hide();
								$("#xls").show();
							} else {
								$("#xls").hide();
								$("#nonXls").show();
							}
						});

				$("#T2").change(function() {

					getPeriodValues();
					//$("#type").empty().append(options);
				});

				function getPeriodValues() {
					$("#T3").empty();
					var options = $("#T3");
					if (($("#T2 :selected").text()) == "None") {
						options.append(new Option("None", "N"));
					} else if (($("#T2 :selected").text()) == "Monthly") {
						options.append(new Option("January", "MJAN"));
						options.append(new Option("February", "MFEB"));
						options.append(new Option("March", "MMAR"));
						options.append(new Option("April", "MAPR"));
						options.append(new Option("May", "MMAY"));
						options.append(new Option("June", "MJUN"));
						options.append(new Option("July", "MJUL"));
						options.append(new Option("August", "MAUG"));
						options.append(new Option("September", "MSEP"));
						options.append(new Option("October", "MOCT"));
						options.append(new Option("November", "MNOV"));
						options.append(new Option("December", "MDEC"));
					} else if (($("#T2 :selected").text()) == "Quarterly") {
						options.append(new Option("1st Quarter", "Q1"));
						options.append(new Option("2nd Quarter", "Q2"));
						options.append(new Option("3rd Quarter", "Q3"));
						options.append(new Option("4th Quarter", "Q4"));
					} else if (($("#T2 :selected").text()) == "SemiAnnual") {
						options.append(new Option("First Half", "S1"));
						options.append(new Option("Second Half", "S2"));
					}
				}
			</script>
			<!-- SVET012164 N1  -->
			<script type="text/javascript">
				$(document).ready(function() {
									$('#secPGALists').empty().append('<option value="">All</option>');
								});
			
				function getSecPGADropdown(val) {
					if (val == null) {
						var priId = document.getElementById("priPGA").value;
					} 
					
					if(priId == "0"){
							$('#secPGALists').empty().append('<option value="">All</option>');
					}else{
					var url = "secPGADropDown";
					console.log("inptA--->" + priId);
					$.ajax({
						type : "GET",
						url : url,
						dataType : "html",
						data : {
							'priId' : priId
						},
						success : function(response) {
							$("#secPGA").html(response);
						},
						error : function() {

						}

					});
					}
				}
			</script>
		</div>
	</div>

	<jsp:include page="footer.jsp"></jsp:include>

</body>

</html>
