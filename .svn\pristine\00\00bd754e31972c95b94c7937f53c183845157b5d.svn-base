<!DOCTYPE HTML><%@page language="java"
	contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<script type="text/javascript" src="webResources/js/qadb.js"></script>

<style>

#dollar2 {
  position: absolute;
  display: block;
  transform: translate(0, -50%);
  top: 44%;
  pointer-events: none;
  width: 25px;
  text-align: center;
  font-style: normal;
  padding-bottom: 1px;
}
</style>
<!-- High Dollar popup -->
	<div id="highDollarOverlay" ></div>
	
	<div id="highDollarPopup" style="text-align: left;padding: 25px 25px 25px 25px;border-radius: 5px; box-shadow: 5px 5px 5px #888888; width:750px">
		<img id="close" src="webResources/images/Misc/Icn_Close2.png" onclick="hideHighDollar();">
			 <span style="font-size: 15px;text-align: center;font-weight: bold"></span>	
			 <p style="font-size: 15px;text-align: left"></p>
			  <p style="font-size: 15px;text-align: center"></p>
			<form:form  id="highDollarForm" commandName="auditForm" action="submitHighDollar" method="POST">
			  <input type="hidden" id="surgeryDOShid" name="surgeryDOShid" value="${editAuditDetails.surgeryDOS}${surgeryDOS}"/>
			  <input type="hidden" id="audSignDatehid" name="audSignDatehid" value="${editAuditDetails.audSignDate}${audSignDate}"/>
			  <input type="hidden" id="vpSignDatehid" name="vpSignDatehid" value="${editAuditDetails.vpSignDate} ${vpSignDate}"/>
			  <input type="hidden" id="forwrdToDatehid" name="forwrdToDatehid"  value="${editAuditDetails.forwrdToDate}${forwrdToDate}" />
			  <input type="hidden" id="rcvedFrmDatehid" name="rcvedFrmDatehid"  value="${editAuditDetails.rcvedFrmDate}${rcvedFrmDate}" />
			  <input type="hidden" id="releasedByDatehid" name="releasedByDatehid" value="${editAuditDetails.releasedByDate}${releasedByDate}" />
			  
			<div style="padding:10px 0px 15px 0px" style="width: 700px;">
			
			<div style="text-align: center;width: 700px;"><b><h3>HIGH-DOLLAR CLAIM AUTHORIZATION</h3></b></div>
			<br>
			<div style="text-align: left;width: 700px;">
			High-dollar claims with paid amounts over $40,000.00 must have the appropriate sign-offs. Attach this sheet to the claim or screen prints for the appropriate signatures.
			</div><br>
			<div style="text-align: left;width: 700px;color: red" id="errmsgHigh"></div>
			<br>
			<div>
			<div style="width: 700px; text-align: center;">Claims Processing System</div>
			<table class="table striped hovered dataTable" id="highDollarTableHead" style="width: 700px;">
			<tr style="height: 35px">
			<td style="font-size: 15px;text-align: center;padding-top: 5px">
			<c:choose>
								<c:when test="${edit != null && editAuditDetails.claimAdjFlag=='Y'}">
									<form:checkbox id="claimAdjFlag" name="claimAdjFlag" checked="checked" path="claimAdjFlag" value="Y"/>
									<span class="check"></span>	CLAIMS/CLAIM ADJUSTMENTS
								</c:when>
								<c:when test="${claimAdjFlag=='Y'}">
									<form:checkbox id="claimAdjFlag" name="claimAdjFlag" checked="checked" path="claimAdjFlag" value="Y"/>
									<span class="check"></span>	CLAIMS/CLAIM ADJUSTMENTS
								</c:when>
								<c:otherwise>
									<form:checkbox id="claimAdjFlag" name="claimAdjFlag" path="claimAdjFlag" value="Y"/>
									<span class="check"></span>	CLAIMS/CLAIM ADJUSTMENTS
								</c:otherwise>
						</c:choose>
			</td>
			<td style="font-size: 15px;text-align: center;padding-top: 5px">
			<c:choose>
								<c:when test="${edit != null && editAuditDetails.serviceAdjFlag=='Y'}">
									<form:checkbox id="serviceAdjFlag" name="serviceAdjFlag" checked="checked" path="serviceAdjFlag" value="Y"/>
									<span class="check"></span>	SERVICE ADJUSTMENTS		
								</c:when>
								<c:when test="${serviceAdjFlag=='Y'}">
									<form:checkbox id="serviceAdjFlag" name="serviceAdjFlag" checked="checked" path="serviceAdjFlag" value="Y"/>
									<span class="check"></span>	SERVICE ADJUSTMENTS		
								</c:when>
								<c:otherwise>
									<form:checkbox id="serviceAdjFlag" name="serviceAdjFlag" path="serviceAdjFlag" value="Y"/>
									<span class="check"></span>	SERVICE ADJUSTMENTS		
								</c:otherwise>
						</c:choose>
			</td>
			</tr>
			</table>
			<div style="width: 700px; text-align: center;">**For CARE BlueCard Home - Examiners to complete form**<br>
**QA Analyst to complete form for other accounts** 
			</div>
			</div>
			<br>
			<table class="table striped hovered dataTable" id="highDollarTable" style="width: 700px;">
				<tr style="height: 35px">
						<td class="text-left" style="font-size: 15px;">Claim #</td>
						<c:choose>
								<c:when test="${edit != null}">
									<td class="text-left" style="font-size: 15px;">${eDCN}</td>						
								</c:when>
								<c:otherwise>
									<td class="text-left" style="font-size: 15px;">${dcn}</td>						
								</c:otherwise>
						</c:choose>
				</tr>
				<tr style="height: 35px">
						<td class="text-left" style="font-size: 15px;">Examiner Name</td>
						<c:choose>
								<c:when test="${(edit != null) && ((editAuditDetails.examinerName) != null)}">
									<td class="text-left" style="font-size: 15px;">${editAuditDetails.examinerName}</td>						
								</c:when>
								<c:when test="${(edit != null) && ((editAuditDetails.examinerName) == null)}">
									<td class="text-left" style="font-size: 15px;">${assocEditDetails.facetsId}</td>						
								</c:when>
								<c:otherwise>
									<td class="text-left" style="font-size: 15px;">${claimDetails.claimLevel.careFirstExaminerUserID}</td>						
								</c:otherwise>
						</c:choose>
<%-- 						<td class="text-left" style="font-size: 15px;">${claimDetails.claimLevel.careFirstExaminerUserID}${assocEditDetails.facetsId}${editAuditDetails.examinerName}</td>						
 --%>				</tr><%--  ${editAuditDetails.examinerName} --%>
				<tr style="height: 35px">
						<td class="text-left" style="font-size: 15px;">QA Analyst Name</td>
						<td style="font-size: 15px;">
						
						<c:choose>
								<c:when test="${edit != null}">
									<div id="QANamec" class="input-control text">	
									<input type="text" id="QANameId" name="QAName" value="${editAuditDetails.QAName}${QAName}"/></div>
								</c:when>
								<c:otherwise>
									<div id="QANamec" class="input-control text">						
									<input id="QANameId" name="QAName"  type="text" value="${QAName}"/>
									</div>
								</c:otherwise>
						</c:choose>
					</td>						
				</tr>
				<tr style="height: 35px">
						<td class="text-left" style="font-size: 15px;">Paid Date</td>
						<td class="text-left" style="font-size: 15px;">${claimDetails.claimLevel.paidDate}${ePaidDate}</td>						
				</tr>
				<tr style="height: 35px">
						<td class="text-left" style="font-size: 15px;">Membership #</td>
						<td class="text-left" style="font-size: 15px;">${claimDetails.subscriberLevel.memberID}${eMemId}</td>						
				</tr>
				<tr style="height: 35px">
						<td class="text-left" style="font-size: 15px;">Patient Name</td>
						<td style="font-size: 15px;">
							<c:choose>
								<c:when test="${edit != null}">
									<div id="patientNamec" class="input-control text">
									<input type="text" id="patientNameId"  name="patientName" value="${editAuditDetails.patientName}${patientName}"/></div>
								</c:when>
								<c:otherwise>
									<div id="patientNamec" class="input-control text">
									<input type="text" id="patientNameId" name="patientName" value="${patientName}" /></div>
								</c:otherwise>
							</c:choose>
						</td>						
				</tr>
				<tr style="height: 35px">
						<td class="text-left" style="font-size: 15px;">Service Dates</td>
						<td class="input-control text-left" style="font-size: 15px;">
							<c:choose>
								<c:when test="${edit != null}">
									<div id="serviceDatesc" class="input-control text">
									<input type="text" id="serviceDatesId" name="serviceDates" value="${editAuditDetails.serviceDates}${serviceDates}"/></div>
								</c:when>
								<c:otherwise>
									<div id="serviceDatesc" class="input-control text">
									<input type="text" id="serviceDatesId" name="serviceDates" value="${serviceDates}"/></div>
								</c:otherwise>
							</c:choose>
						</td>						
				</tr>
				<tr style="height: 35px">
						<td class="text-left" style="font-size: 15px;">Type of Service</td>
						<td class="input-control text-left" style="font-size: 15px;">
						<c:choose>
								<c:when test="${edit != null}">
									<div id="typeOfServicec" class="input-control text">
									<input type="text" id="typeOfServiceId" name="typeOfService" value="${editAuditDetails.typeOfService}${typeOfService}"/></div>
								</c:when>
								<c:otherwise>
									<div id="typeOfServicec" class="input-control text">
									<input type="text" id="typeOfServiceId" name="typeOfService" value="${typeOfService}" /></div>
								</c:otherwise>
						</c:choose>
						</td>						
				</tr>
				<tr style="height: 35px">
						<td class="text-left" style="font-size: 15px;">Diagnosis (Code & Verbiage)</td>
						<td class="input-control text-left" style="font-size: 15px;">
						<c:choose>
								<c:when test="${edit != null}">
									<div id="diagnosisc" class="input-control text">
									<input type="text" id="diagnosisId" name="diagnosis"  value="${editAuditDetails.diagnosis}${diagnosis}"/></div>
								</c:when>
								<c:otherwise>
									<div id="diagnosisc" class="input-control text">
									<input type="text" id="diagnosisId" name="diagnosis" value="${diagnosis}" />
									</div>
								</c:otherwise>
						</c:choose>
						</td>						
				</tr>
				<tr style="height: 35px">
						<td class="text-left" style="font-size: 15px;">Surgery Date of Service</td>
						<td class="input-control text-left" style="font-size: 15px;">
						<c:choose>
								<c:when test="${edit != null}">
									<div id="surgeryDOSc" class="input-control text">
									<input type="text" name="surgeryDOS" id="datePicksurgeryDOS" /></div>
								</c:when>
								<c:otherwise>
									<div id="surgeryDOSc" class="input-control text">
									<input name="surgeryDOS" id="datePicksurgeryDOS" type="text" />
									</div>
								</c:otherwise>
						</c:choose>
						<span id="errmsgsdatePicksurgeryDOS" style="color:red"></span>
						</td>	
				</tr>
				<tr style="height: 35px">
						<td class="text-left" style="font-size: 15px;">Surgery (CPT Code & Verbiage)</td>
						<td class="input-control text-left" style="font-size: 15px;">
						<c:choose>
								<c:when test="${edit != null}">
									<div id="surgeryc" class="input-control text">
									<input type="text" id="surgeryId" name="surgery"  value="${editAuditDetails.surgery}${surgery}"/></div>
								</c:when>
								<c:otherwise>
									<div id="surgeryc" class="input-control text">
									<input type="text" id="surgeryId" name="surgery" value="${surgery}"/></div>
								</c:otherwise>
						</c:choose>
						</td>						
				</tr>
				<tr style="height: 35px">
						<td class="text-left" style="font-size: 15px;">Auth on File Referenced (if applicable)</td>
						<td class="input-control text-left" style="font-size: 15px;">
						<c:choose>
								<c:when test="${edit != null}">
									<div id="fileReferencedc" class="input-control text">
									<input type="text" id="fileReferencedId" name="fileReferenced"  value="${editAuditDetails.fileReferenced}${fileRef}"/></div>
								</c:when>
								<c:otherwise>
									<div id="fileReferencedc" class="input-control text">
									<input type="text" id="fileReferencedId" name="fileReferenced" value="${fileRef}" /></div>
								</c:otherwise>
						</c:choose>
						</td>						
				</tr>
				<tr style="height: 35px">
						<td class="text-left" style="font-size: 15px;">Total Charge</td>
						<td class="text-left" style="font-size: 15px;">
						<c:choose>
								<c:when test="${edit != null}">$
								${eTotalCharge}${claimDetails.claimLevel.totalCharge}
								</c:when>
								<c:otherwise>$
								${claimDetails.claimLevel.totalCharge}
								</c:otherwise>
						</c:choose>
						</td>						
				</tr>
				<tr style="height: 35px">
						<td class="text-left" style="font-size: 15px;">Total Paid</td>
						<td class="input-control text-left" style="font-size: 15px;">
						<c:choose>
								<c:when test="${edit != null}">$
								${ePaid}${claimDetails.claimLevel.paidAmount}
								</c:when>
								<c:otherwise>$
									${claimDetails.claimLevel.paidAmount}
								</c:otherwise>
						</c:choose>
					</td>						
				</tr>
				<tr style="height: 35px">
						<td class="text-left" style="font-size: 15px;">Interest Paid (estimate at time of signoff)</td>
						<td class="input-control text-left" style="font-size: 15px;">
						<div id="interestPaidc" class="input-control text">
						<i id="dollar2">$</i>
						<c:choose>
								<c:when test="${edit != null}">
									<input type="text" id="interestPaidId" name="interestPaid" style="padding-left:17px;" value="${editAuditDetails.interestPaid}${interestPaid}"/></div>
								</c:when>
								<c:otherwise>
									<input type="text" id="interestPaidId" name="interestPaid" style="padding-left:17px;" value="${interestPaid}"/>
								</c:otherwise>
						</c:choose>
						</div>
						</td>						
				</tr>
				<tr style="height: 35px">
						<td class="text-left" style="font-size: 15px;">Provider Name</td>
						<td class="input-control text-left" style="font-size: 15px;">
						<c:choose>
								<c:when test="${edit != null}">
									<div id="providerNamec" class="input-control text">
									<input type="text" id="providerNameId" name="providerName"  value="${editAuditDetails.providerName}${providerName}"/></div>
								</c:when>
								<c:otherwise>
									<div id="providerNamec" class="input-control text">
									<input type="text" id="providerNameId" name="providerName" value="${providerName}"/></div>
								</c:otherwise>
						</c:choose>
						</td>						
				</tr>
				<tr style="height: 35px">
						<td class="text-left" style="font-size: 15px;">Provider #</td>
						<td class="input-control text-left" style="font-size: 15px;">
						<c:choose>
								<c:when test="${edit != null}">
									<div id="providerNumberc" class="input-control text">
									<input type="text" id="providerNumberId" name="providerNumber"  value="${editAuditDetails.providerNumber}${providerNumber}"/></div>
								</c:when>
								<c:otherwise>
									<div id="providerNumberc" class="input-control text">
								<input type="text" id="providerNumberId" name="providerNumber" value="${providerNumber}"/></div>
								</c:otherwise>
						</c:choose>
						</td>						
				</tr>
				<tr style="height: 35px">
						<td class="text-left" style="font-size: 15px;">Payee (subscriber or provider)</td>
						<td class="input-control text-left" style="font-size: 15px;">
						<c:choose>
								<c:when test="${edit != null}">
									<div id="payeec" class="input-control text">
									<input type="text" id="payeeId" name="payee"  value="${editAuditDetails.payee}${payee}"/></div>
								</c:when>
								<c:otherwise>
									<div id="payeec" class="input-control text">
									<input type="text" id="payeeId" name="payee" value="${payee}"/></div>
								</c:otherwise>
						</c:choose>
						</td>						
				</tr>
				<tr style="height: 35px">
						<td class="text-left" style="font-size: 15px;">Notes (if applicable)</td>
						<td class="input-control text-left" style="font-size: 15px;">
						<c:choose>
								<c:when test="${edit != null}">
									<div id="notesc" class="input-control text">
									<input type="text" id="notesId" name="notes"  value="${editAuditDetails.notes}${notes}"/></div>
								</c:when>
								<c:otherwise>
									<div id="notesc" class="input-control text">
									<input type="text" id="notesId" name="notes" value="${notes}"/></div>
								</c:otherwise>
						</c:choose>
						</td>						
				</tr>

			</table>
			
			</div>
			
			<div  style="padding:10px 0px 15px 0px; width: 700px;">
			<span style="font-size: 15px;"><b></b></span>	
			<table class="table striped hovered dataTable highDollarTables" id="" style="width: 700px; " >
			<tr style="height: 35px;">
				<td>Required Authorization </td>
				<td>If Paid Amount is...</td>
				<td style="padding-left: 50px;">Signature </td>
				<td>Date </td>
			</tr>
			<tr  style="height: 35px;">
				<td> QA Auditor</td>
				<td>Over $40,000.00 </td>
				<td class="input-control">
					<!-- <div id="authSign1c" class="input-control text">
						<input id="authSign1" type="text" name="authSign1" />
					</div> -->
					<div id="authSign1c" style="width: 200px;">
					</div>
				</td>
				<td class="input-control">
				<c:choose>
					<c:when test="${edit != null}">
						<div id="audSignDatec" class="input-control text">
						<input type="text" name="audSignDate" id="datePickaudSignDate"/></div>
					</c:when>
					<c:otherwise>
						<div id="audSignDatec" class="input-control text">
						<input id="datePickaudSignDate" name="audSignDate" type="text" /></div>
					</c:otherwise>
				</c:choose>
				<span id="errmsgdatePickaudSignDate" style="color:red"></span>
				</td>
			</tr>
			</table>
			
			<table class="table striped hovered dataTable highDollarTables" id="" style="width: 700px; vertical-align: middle;">
			<tr  style="height: 35px;">
				<td>Required Authorization </td>
				<td>If Paid Amount is... </td>
				<td style="padding-left: 50px;">Signature</td>
				<td >Date </td>
			</tr>
			<tr  style="height: 35px;">
				<td> Vice President</td>
				<td>Over $100,000.00</td>
				<td class="input-control">
					<!-- <div id="authSign2c" class="input-control text">
						<input id="authSign2" type="text" name="authSign2" />
					</div> -->
					<div id="authSign2c" style="width: 200px;">
					</div>
				</td>
				<td class="input-control">
				<c:choose>
					<c:when test="${edit != null}">
						<div id="vpSignDatec" class="input-control text">
						<input type="text" id="datePickvpSignDate" name="vpSignDate"/></div>
					</c:when>
					<c:otherwise>
						<div id="vpSignDatec" class="input-control text">
						<input id="datePickvpSignDate" name="vpSignDate" type="text" /></div>
					</c:otherwise>
				</c:choose>
				<!-- <span id="errmsgdatePickvpSignDate" style="color:red"></span> -->
				</td>
			</tr>
			</table>
			Inpatient Institutional/Outpatient Institutional and all Professional Claims<br>
			<table class="table striped hovered dataTable highDollarTables" id="" style="width: 700px; vertical-align: middle;">
			<tr  style="height: 35px;">
				<td ></td>
				<td></td>
				<td>Date</td>
			</tr>
			<tr  style="height: 35px;">
				<td>Forwarded to</td>
				<td>Vice President</td>
				<td>
				<c:choose>
					<c:when test="${edit != null}">
						<div id="forwrdToDatec" class="input-control text">
						<input type="text" id="datePickforwrdToDate"  name="forwrdToDate"/></div>
					</c:when>
					<c:otherwise>
						<div id="forwrdToDatec" class="input-control text">
						<input id="datePickforwrdToDate" name="forwrdToDate" type="text" /></div>
					</c:otherwise>
				</c:choose>
				<span id="errmsgdatePickforwrdToDate" style="color:red"></span>
				</td>
			</tr>
			<tr  style="height: 35px;">
				<td>Received from</td>
				<td>Vice President</td>
				<td>
				<c:choose>
					<c:when test="${edit != null}">
						<div id="rcvedFrmDatec" class="input-control text">
						<input type="text" id="datePickrcvedFrmDate" name="rcvedFrmDate" /></div>
					</c:when>
					<c:otherwise>
						<div id="rcvedFrmDatec" class="input-control text">
						<input id="datePickrcvedFrmDate" name="rcvedFrmDate" type="text" /></div>
					</c:otherwise>
				</c:choose>
				<!-- <span id="errmsgdatePickrcvedFrmDate" style="color:red"></span> -->
				</td>
			</tr>
			<tr  style="height: 35px;">
				<td>Released by</td>
				<td>QA</td>
				<td>
				<c:choose>
					<c:when test="${edit != null}">
						<div id="releasedByDate" class="input-control text">
						<input type="text" id="datePickreleasedByDate" name="releasedByDate" /></div>
					</c:when>
					<c:otherwise>
						<div id="releasedByDatec" class="input-control text">
						<input id="datePickreleasedByDate" name="releasedByDate" type="text" /></div>
					</c:otherwise>
				</c:choose>
				<!-- <span id="errmsgdatePickreleasedByDate" style="color:red"></span> -->
				</td>
			</tr>
			</table>
			</div>
			<c:set var="userRole" > <%=request.getHeader("iv-groups")%> </c:set> 
			<c:choose>
			<c:when test="${!((fn:contains(userRole, 'qadb-samd-readonly_user'))||(fn:contains(userRole, 'qadb-cd-readonly_user')) ||(fn:contains(userRole, 'null')))}">
				<button type="button" style="background-color: #298fd8" class="button default" onclick="submitHighDollar();" >Save</button>
			</c:when>
			<c:otherwise>
			<button type="button" style="background-color: #298fd8" class="button default" disabled="disabled" >Save</button>
			</c:otherwise>
			</c:choose>
			<!-- <div style="background-color: #298fd8" class="button default"><a href="javascript:void(0);" onclick="submitHighDollar();" >Submit</a></div> -->
		</form:form>
	</div>
	
<script type="text/javascript">
	
	/* jQuery(function($){
		$("#surgeryDOS").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("datepickSurgeryDOShid").value);
		$("#audSignDate").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("datepickAudSignDatehid").value);
		$("#vpSignDate").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("datepickVpSignDatehid").value);
		$("#forwrdToDate").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("datepickForwrdToDatehid").value);
		$("#rcvedFrmDate").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("datepickRcvedFrmDatehid").value);
		$("#releasedByDate").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("datepickReleasedByDatehid").value);
	}); */
	
	jQuery(function($){
		$("#datePicksurgeryDOS").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("surgeryDOShid").value);
		$("#datePickaudSignDate").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("audSignDatehid").value);
		$("#datePickvpSignDate").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("vpSignDatehid").value);
		$("#datePickforwrdToDate").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("forwrdToDatehid").value);
		$("#datePickrcvedFrmDate").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("rcvedFrmDatehid").value);
		$("#datePickreleasedByDate").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("releasedByDatehid").value);
	});
	
	
	$("#authSign1").keypress(function(e) {
			e.preventDefault(); 
	});
	$("#authSign2").keypress(function(e) {
			e.preventDefault(); 
	});
	
</script>
	
	
	