<!DOCTYPE html>
<html style="background-color:#dddfe1;">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<head>

<style>
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
    margin-right: auto;
	height: 350%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}
</style>

<link href="webResources/css/qadb.css" rel="stylesheet">
<!-- Local JavaScript -->

<title><spring:message code="audit.search.errors.title" /></title> 
</head> 



<body>
<jsp:include page="../jsp/header.jsp"></jsp:include>
<div class="container2" id="auditErrorDiv">
		<div id="left-sidebar">
       	 <jsp:include page="audit_leftNav.jsp"></jsp:include>
   		</div>
    <div id="content-container">
    	<div id = "err" >
			<jsp:include page="../jsp/audit_error_tab.jsp"></jsp:include>
		</div>
		<c:if test="${edit != null}">
			<div id="stat" style="width:645px">
				<jsp:include page="../jsp/audit_statistics_tab.jsp"></jsp:include>       
   	 		</div>
    	</c:if>         
    </div>
</div>


 <jsp:include page="footer.jsp" ></jsp:include>		

<script type="text/javascript">
$('#statsLi').removeClass('active');
$('#auditLeftLi').removeClass('active');
$('#errorleftLi').addClass('active');

$('#stat').hide();
$('#stats').click(function() {
	console.log("in stats show");
    $('#err').hide();
    $('#aud').hide();
    $('#stat').show();
    $('#auditLeftLi').removeClass('active');
    $('#errorleftLi').removeClass('active');
	$('#statsLi').addClass('active');
});
</script>
</body> 

</html> 
