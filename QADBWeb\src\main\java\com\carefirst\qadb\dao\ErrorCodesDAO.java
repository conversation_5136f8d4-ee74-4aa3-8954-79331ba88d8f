package com.carefirst.qadb.dao;

import java.sql.SQLException;
import java.util.List;

import javax.sql.DataSource;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import com.carefirst.audit.model.Associate;
import com.carefirst.audit.model.ErrorCodes;

import net.sf.jasperreports.engine.JRDataSource;

public interface ErrorCodesDAO {
	
	public ErrorCodes saveErrorCodes(ErrorCodes errorcodes) throws Exception;	
	public List<ErrorCodes> searchErrorCodes(ErrorCodes errorcodes)throws SQLException;
	public ErrorCodes getErrorData(ErrorCodes errorCodes)throws SQLException;
	public ErrorCodes deleteError(ErrorCodes errorCodes);
	public JRDataSource searchErrorCodesList(ErrorCodes errorCodes) throws SQLException;
}
