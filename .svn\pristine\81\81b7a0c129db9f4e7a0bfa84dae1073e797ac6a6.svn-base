/**
 * ClaimLevel.java
 *
 * This file was auto-generated from WSDL
 * by the IBM Web services WSDL2Java emitter.
 * cf011339.01 v10113164338
 */

package com.carefirst.audit.model;

public class ClaimLevel  {
    private java.lang.String careFirstExaminerUserID;
    private java.lang.String careFirstExaminerName;
    private java.lang.String productID;
    private java.lang.String productName;
    private java.lang.String processDate;
    private java.lang.String paidDate;
    private java.lang.String claimCurrentstatus;
    private java.lang.String paidAmount;
    private java.lang.String totalCharge;
    
    public ClaimLevel() {
    }

    public java.lang.String getCareFirstExaminerUserID() {
        return careFirstExaminerUserID;
    }

    public void setCareFirstExaminerUserID(java.lang.String careFirstExaminerUserID) {
        this.careFirstExaminerUserID = careFirstExaminerUserID;
    }

    public java.lang.String getCareFirstExaminerName() {
        return careFirstExaminerName;
    }

    public void setCareFirstExaminerName(java.lang.String careFirstExaminerName) {
        this.careFirstExaminerName = careFirstExaminerName;
    }

    public java.lang.String getProductID() {
        return productID;
    }

    public void setProductID(java.lang.String productID) {
        this.productID = productID;
    }

    public java.lang.String getProductName() {
        return productName;
    }

    public void setProductName(java.lang.String productName) {
        this.productName = productName;
    }

    public java.lang.String getProcessDate() {
        return processDate;
    }

    public void setProcessDate(java.lang.String processDate) {
        this.processDate = processDate;
    }

    public java.lang.String getPaidDate() {
        return paidDate;
    }

    public void setPaidDate(java.lang.String paidDate) {
        this.paidDate = paidDate;
    }

    public java.lang.String getClaimCurrentstatus() {
        return claimCurrentstatus;
    }

    public void setClaimCurrentstatus(java.lang.String claimCurrentstatus) {
        this.claimCurrentstatus = claimCurrentstatus;
    }

    public java.lang.String getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(java.lang.String paidAmount) {
        this.paidAmount = paidAmount;
    }

    public java.lang.String getTotalCharge() {
        return totalCharge;
    }

    public void setTotalCharge(java.lang.String totalCharge) {
        this.totalCharge = totalCharge;
    }

}
