<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib  prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<head>
<link href="webResources/css/qadb.css" rel="stylesheet">

<script type="text/javascript" src="webResources/js/metro.min.js"></script>
<!-- Local JavaScript -->

<link href="webResources/css/iconFont.css" rel="stylesheet">
<link href="webResources/css/docs.css" rel="stylesheet">
<link href="webResources/js/prettify/prettify.css" rel="stylesheet">

<!-- Load JavaScript Libraries -->
<script type="text/javascript" src="webResources/js/datep.js"></script>
<script src="webResources/js/jquery/jquery.min.js"></script>
<script src="webResources/js/jquery/jquery.widget.min.js"></script>

<script type="text/javascript" src="webResources/js/jquery.min.js"></script>
<script type="text/javascript" src="webResources/js/jquery-ui.js"></script> 
<script type="text/javascript" src="webResources/js/qadb.js"></script>
<link href="webResources/css/qadb.css" rel="stylesheet">
<link href="webResources/css/jquery-ui.css" rel="stylesheet">
<style>
/* Collapse -Expand */
.headerA {
	background: url('webResources/images/Forms/Icn_Accordion_Collapse.png')
		no-repeat;
	background-position: right 0px;
	cursor: pointer;
}

.collapsed .headerA {
	background-image:
		url('webResources/images/Forms/Icn_Accordion_Expand.png');
}

.contentA {
	height: auto;
	min-height: 100px;
	overflow: hidden;
	transition: all 0.3s linear;
	-webkit-transition: all 0.3s linear;
	-moz-transition: all 0.3s linear;
	-ms-transition: all 0.3s linear;
	-o-transition: all 0.3s linear;
	background-color:#fafafa;
}

.collapsed .contentA {
	min-height: 0px;
	height: 0px;
}

.correct {
	display: none;
}
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
	margin-right: auto;
	height: 210%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}
div.success {
	padding-left: 5px;
	padding-top: 5px;
	height: 30px; 
	background: #73AD21; 
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	color : white;
}

div.errorInfo {
    padding: 5px 5px 5px 5px ;
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	width:350px;
	font-size:10pt;
	background-color:#CE352C;
	color:white;
}

#error {
    padding: 5px 5px 5px 5px ;
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	width:350px;
	font-size:11pt;
	background-color:#CE352C;
	color:white;
}
.hidden {
	display: none;
}
.ui-widget {
	font-family: Verdana,Arial,sans-serif;
	font-size: 1.1em;
}
.ui-widget .ui-widget {
	font-size: 1em;
}
.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
	font-family: Verdana,Arial,sans-serif;
	font-size: 1em;
}
.ui-widget-content {
	border: 1px solid #aaaaaa;
	background: #ffffff url() 50% 50% repeat-x;
	color: #222222;
}
.ui-widget-content a {
	color: #222222;
}
/* .ui-widget-header {
	border: 1px solid #aaaaaa;
	background:#2573ab  url(images/ui-bg_highlight-soft_75_cccccc_1x100.png) 50% 50% repeat-x;
	color: #e6f5fc;
	font-weight: bold;
} */
.ui-widget-header a {
	color: #222222;
}
.ui-datepicker-trigger{
background-image: url("webResources/images/Forms/Icn_Date_Picker.png");
height: 36px;
width: 36px;
background-color:white;
}
.ui-icon-circle-triangle-e{
background-image: url("webResources//Navbar/Icn_Right_Arrow.png");
}
.ui-icon-circle-triangle-w{
background-image: url("webResources/images/skip_backward.png");
}
</style>

<script type="text/javascript">
	var a = 1;
	$(document).ready(function() {
	if (a == 1) {
		$('#success').delay(800).fadeIn(400);
			
	}
	})
</script>

<title><spring:message code="admin.rootCause.title" /></title>
</head>
<body>
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container2">

		<!-- Sidebar Start-->
		<c:choose>
   			 <c:when test="${edit != null}">
   			 	
   			 	<div id="left-sidebar">
					<h2 align="left" style="color: white ; padding:32px 0px 0px 20px ">
					${rootCauseRO.rootCauseName} 
					</h2>
					<h3 style="color: white; padding:10px 0px 0px 20px">
						<spring:message code="admin.rootCause.update.leftNav.heading2" />
					</h3>
				</div>
   			 
    		 </c:when>
    		 <c:otherwise>
    		 		<div id="left-sidebar">
					<h2 style="color: white ; padding-top:32px">
						<spring:message code="admin.rootCause.leftNav.heading1" />
					</h2>
					<h3 style="color: white; padding-top:10px">
						<spring:message code="admin.rootCause.leftNav.heading2" />
						</h3>
				</div>
    		 
    		 </c:otherwise>
    		 
    		 
    	</c:choose>
		
	
		<!-- Sidebar End-->

		<div id="content-container" style="padding-left: 40px">
<%String formAction=""; %>

 	<c:if test="${edit != null}"> 
  		<% formAction="updateRootCause" ;%>
    </c:if>
	<c:if test="${edit == null}"> 
  		<% formAction="saveRootCause" ;%>
    </c:if> 
			<form:form id="rootCauseForm" name="rootCauseForm" method="GET" onsubmit="return ValidatesRootCs()" commandName="rootCauseForm" action="<%=formAction%>" style="width:700px">

			<input type="hidden" id="rootCsEndDt" value="${rootCauseRO.effectiveEndDate}"/>
			<input type="hidden" id="rootCsStartDt" value="${rootCauseRO.effectiveStartDate}"/>
			<input type="hidden" id="rootCsFrmDtDis" value="${rootCauseRO.disableFrom}"/>
			<input type="hidden" id="rootCsToDt" value="${rootCauseRO.disableTo}"/>
			 
				<div style="padding: 10px 0px 0px 0px;">
					<span class="bread1"><spring:message code="admin.rootCause.bread1" /> </span>
					<c:if test="${edit == null}">
					<span class="bread2"><spring:message code="admin.rootCause.leftNav.heading1" /></span>
					</c:if>
					<c:if test="${edit != null}">
					<span class="bread2"><spring:message code="admin.rootCause.update.bread2" /></span>
					</c:if>
				</div>
				<table width="98%" style="padding: 10px 10px 10px 50px">
					<tr style="height: 20px; border-bottom-color: gray;">


						<c:choose>
							<c:when test="${edit != null}">
								<td width="40%"><span style="font-size: 20px">${rootCauseRO.rootCauseName}
										</span></td>
								<td width="60%" style="text-align: right;">

									<button title="Cancel" type="reset" onclick="hideErrorDiv();"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Cancel.png" alt="Cancel">
									</button> 
									<button title="Save" type="submit"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png" alt="Save">
									</button></td>
									<tr>
									
									</tr>
									
							</c:when>

							<c:otherwise>
								<td width="40%"><span style="font-size: 20px"><spring:message
											code="admin.rootCause.leftNav.heading1"></spring:message> </span></td>
								<td width="60%" style="text-align: right;">
									<button type="reset" title="Cancel" onclick="hideErrorDiv();"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Cancel.png">
									</button>
									<button type="submit" title="Save"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png">
									</button></td>
							</c:otherwise>
						</c:choose>
					</tr>
				</table>
				
<div >
				<div id="error" ></div><div>
					<c:if test="${(successCode != '000') && (successCode != '201') && (successCode != null)}"> 
						<div id="divDuplicate" style="width: 240px;color: red;font-weight: bold;">${successMessage}</div>
					</c:if>
					<c:if test="${success == 'SUCCESS'}"> 
						<div id="success" class="success" style="width: 240px;">Root Cause added successfully!</div>
					</c:if>
					<c:if test="${upSucess=='SUCCESS'}"> 
						<div id="success" class="success" style="width: 280px;">Root Cause details updated successfully!</div>
					</c:if>	
					<c:if test="${delSucess=='SUCCESS'}"> 
						<div id="success" style="padding-left: 5px; height: 20px; width: 220px; background: #99FF99; display: none;">Root Cause deleted successfully!</div>
					</c:if>
					<%-- <c:if test="${(fn:contains(success, 'System')) || (fn:contains(upSucess, 'System')) || (fn:contains(delSucess, 'System'))}">
						<div id="success" class="errorInfo" style="width: 300px;">System Error, please try again after some time !</div>
					</c:if> --%>
					<c:if test="${successCode == '201'}">
						<div id="success" class="errorInfo" style="width: 300px;">System Error, please try again after some time !</div>
					</c:if>	
				</div>
				<br>
				<div class="line-separator"></div>
				<br><div style="color:red">* indicates required</div><br>
				<div class="panelContainer">
					<div class="containerA" style="padding-left:7px;padding-top: 7px;">
						<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 16px"><spring:message code="admin.rootCause.rootCauseDetails" />
							</span>
						</div>
						<div class="contentA" style="padding-top: 10px">
						<table class="tableForm" style="padding-left: 50px;">
							<tr>
								<td><spring:message code="admin.rootCause.rootCauseDetails.name" />&nbsp;<span style="color:red">*</span></td>
								<td><div id="rootCauseNamec" class="input-control text">
										<input type="text" id="rootCauseName" name="rootCauseName" autofocus="autofocus" placeholder="Root Cause Name" value="${rootCauseRO.rootCauseName}" />
										&nbsp;<span id="errmsgRootCauseName" style="color:red">
									</div>
								</td>
							</tr>
							<tr>
								<td style="width:33%"><spring:message code="admin.rootCause.rootCauseDetails.rootCauseDescription" />&nbsp;<span style="color:red">*</span></td>
								<td><div id="rootCauseDescriptionc" class="input-control textarea">
											<textarea id="rootCauseDescription" name="rootCauseDescription" style="width: 200px; height: 106px;font-family: Segoe UI_, Open Sans, Verdana, Arial, Helvetica, sans-serif"" >${rootCauseRO.rootCauseDescription}</textarea>
											&nbsp;<span id="errmsgRootCauseDesc" style="color:red">
										</div>
								</td>
							</tr>
							
							
							<tr>
								<td style="width:33%"><spring:message code="admin.rootCause.rootCauseDetails.errorTypeMapping" />&nbsp;<span style="color:red">*</span></td>
								<td><div class="input-control select" id="errorTypeD">
										<select id="errorType" name="errorTypeMapping"
											style="width: 200px; font-size: 14px; border-color: #919191">
										<option value="0"><spring:message code="audit.new.error.select" /></option>
											<c:forEach items="${errorTypes}" var="errorTypes">
												<option value="${errorTypes.errorTypes}"
													${errorTypes.errorTypes == rootCauseRO.errorTypeMapping ? 'selected="selected"' : ''}>${errorTypes.errorTypes}</option>
											</c:forEach>
										</select>
									</div>
								</td>
							</tr>
							
								<c:set var="userRole" > <%=request.getHeader("iv-groups")%> </c:set> 
								
								<c:choose>
  								<c:when test="${(edit != null) && (fn:contains(userRole, 'qadb-superadmin_users')) && (!(fn:contains(userRole, 'null'))) }">
 	                                
        								<c:if test="${rootCauseRO.userGrp == '' }">
        									<tr>
											<td style="padding-top : 5px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div class="input-control checkbox">
												<label> <input id="grpSammd" type="checkbox" name="grpSammd"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input id="grpCd" type="checkbox" name="grpCd"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>											
											</td>
											</tr>
		   								</c:if>	
		   								<c:if test="${rootCauseRO.userGrp == 'ALL' }">
        									<tr>
											<td style="padding-top : 5px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div class="input-control checkbox">
												<label> <input id="grpSammd" type="checkbox" name="grpSammd" checked="checked"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input id="grpCd" type="checkbox" name="grpCd" checked="checked"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>											
											</td>
											</tr>
		   								</c:if>	
		   								<c:if test="${rootCauseRO.userGrp == 'SAMMD' }">
        									<tr>
											<td style="padding-top : 5px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div class="input-control checkbox">
												<label> <input id="grpSammd" type="checkbox" name="grpSammd" checked="checked"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input id="grpCd" type="checkbox" name="grpCd" > <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>											
											</td>
											</tr>
		   								</c:if>
		   								<c:if test="${rootCauseRO.userGrp == 'CD' }">
        									<tr>
											<td style="padding-top : 5px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div class="input-control checkbox">
												<label> <input id="grpSammd" type="checkbox" name="grpSammd" > <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input id="grpCd" type="checkbox" name="grpCd" checked="checked" > <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>											
											</td>
											</tr>
		   								</c:if>			
								    </c:when>
    								<c:otherwise>
        								<c:if test="${(fn:contains(userRole, 'qadb-superadmin_users')) && (!(fn:contains(userRole, 'null')))}">
										<tr>
											<td style="padding-top : 5px">
											<spring:message code="qadb.admin.area" />&nbsp;<span style="color:red">*</span>
											</td>
											<td>
											<div id="area" style="width:200px;">
											<div class="input-control checkbox">
												<label> <input id="grpSammd" type="checkbox" name="grpSammd"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.sammd" /></span>     
												</label>
											</div>
											<div class="input-control checkbox" style="padding-left: 10px">
												<label> <input id="grpCd" type="checkbox" name="grpCd"> <span class="check"></span><span class="caption"><spring:message code="qadb.admin.area.cd" /></span>
											 	</label>
											</div>	
											</div>											
											</td>
										</tr>
										</c:if>  
    								</c:otherwise>
								</c:choose>
								
								<c:if test="${(fn:contains(userRole, 'qadb-superadmin_users')) && (!(fn:contains(userRole, 'null')))}">
									<input type="hidden" id="sAdmin" name="sAdmin" value="SADMIN">
								</c:if>
								<c:if test="${(fn:contains(userRole, 'qadb-samd-admin_users')) && (!(fn:contains(userRole, 'null')))}">
 								<input type="hidden" name="grpSammd" value="SAMMD">
								</c:if>
								<c:if test="${(fn:contains(userRole, 'qadb-cd-admin_users')) && (!(fn:contains(userRole, 'null')))}">
									<input type="hidden" name="grpCd" value="CD">
								</c:if>
							
							
							
							<c:if test="${edit != null}">
							<tr>
								<td style="width:33.2%"><spring:message code="admin.rootCause.update.disable" />${rootCauseRO.rootCauseStatus}</td>
								<td>
								<%-- <c:choose>
								<c:when test="${rootCauseRO.disable == 'Disabled'}">
									<div class="input-control checkbox">
										<label> <input type="checkbox" name="disable" checked="checked"/> <span class="check"></span>
											 </label>
									</div>
								</c:when>
								<c:otherwise>
									<div class="input-control checkbox">
										<label> <input type="checkbox" name="disable"/> <span class="check"></span>
											 </label>
									</div>
								</c:otherwise>
								</c:choose> --%>
								
								<c:choose>
										<c:when test="${null!=rootCauseRO.disableFrom}">
											<div class="input-control checkbox">
											<label> <input type="checkbox" onclick="enableDisDatert()" id="disable" name="disable" checked="checked"/> <span class="check"></span>
											 </label>
										</div>
										</c:when>
										<c:otherwise>
											<div class="input-control checkbox">
											<label> <input type="checkbox" onclick="enableDisDatert()" id="disable" name="disable"/> <span class="check"></span>
											 </label>
											</div>
										</c:otherwise>
									</c:choose>
								
								</td>
								
							</tr>
							<tr>
								<td><spring:message code="admin.rootCause.update.disable.from" /></td>
								
								<td><div id="datepick1c" class="input-control text">
											<input id="datepick1" size="50" name="disableFrom" value="${rootCauseRO.disableFrom}"/>
											&nbsp;<span id="errmsgdatepick1" style="color:red">
										</div>
								</td>
							</tr>
							
							<tr>
								<td><spring:message code="admin.rootCause.update.disable.to" /></td>
								
								<td><div id="datepick2c" class="input-control text">
											<input id="datepick2" size="50" name="disableTo" value="${rootCauseRO.disableTo}"/>
											&nbsp;<span id="errmsgdatepick2" style="color:red">
										</div>
								</td>
							</tr>
							</c:if>
						</table>
					</div>
				</div>
			</div>
				
				<div class="panelContainer">
					<div class="containerA" style="padding-left:7px;padding-top: 7px;">
						<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 16px"><spring:message code="admin.rootCause.applicableTo" /></span>
						</div>
						
						<div class="contentA" style="padding-top: 10px">
						<table class="table striped hovered dataTable" 
						style="width:700px">
						<thead>
							<tr style="height: 30px">
		
								<td class="text-left" font-size="25px" style="width:32% ;padding-left:10px;"><spring:message code="admin.rootCause.applicableTo.type" /></td>
								<td class="text-left" font-size="25px"><spring:message code="admin.rootCause.applicableTo.applicable" /></td>
							
							</tr>
						</thead>
		
						<tbody>
		
							<tr>
								<td style="width:33.2% ;padding:8px 0px 0px 10px" ><spring:message code="admin.rootCause.applicableTo.associate" /> </td>
								<td>
								
								<c:choose>
								<c:when test="${rootCauseRO.applicableAssociate=='Y'}">
									<div class="input-control checkbox">
										<label> <input type="checkbox" name="applicableAssociate" checked="checked"> <span class="check"></span>
											 </label>
									</div>
								</c:when>
								<c:otherwise>
									<div class="input-control checkbox">
										<label> <input type="checkbox" name="applicableAssociate"> <span class="check"></span>
											 </label>
									</div>
								</c:otherwise>
								</c:choose>
								</td>
							</tr>
							<tr>
								<td style="width:33.2% ;padding:8px 0px 0px 10px" ><spring:message code="admin.rootCause.applicableTo.supervisor" /> </td>
								<td>
								<c:choose>
								<c:when test="${rootCauseRO.applicableSupervisor=='Y'}">
									<div class="input-control checkbox">
										<label> <input type="checkbox" name="applicableSupervisor" checked="checked"> <span class="check"></span>
											 </label>
									</div>
								</c:when>
								<c:otherwise>
									<div class="input-control checkbox">
										<label> <input type="checkbox" name="applicableSupervisor"> <span class="check"></span>
											 </label>
									</div>
								</c:otherwise>
								</c:choose>
								</td>
							</tr>
							<tr>
								<td style="width:33.2% ;padding:8px 0px 0px 10px" ><spring:message code="admin.rootCause.applicableTo.manager" /> </td>
								<td>
								<c:choose>
								<c:when test="${rootCauseRO.applicableManager=='Y'}">
									<div class="input-control checkbox">
										<label> <input type="checkbox" name="applicableManager" checked="checked"> <span class="check"></span>
											 </label>
									</div>
								</c:when>
								<c:otherwise>
									<div class="input-control checkbox">
										<label> <input type="checkbox" name="applicableManager"> <span class="check"></span>
											 </label>
									</div>
								</c:otherwise>
								</c:choose>
								
								</td>
							</tr>
							<tr>
								<td style="width:33.2% ;padding:8px 0px 0px 10px" ><spring:message code="admin.rootCause.applicableTo.director" /> </td>
								<td>
								<c:choose>
								<c:when test="${rootCauseRO.applicableDirector=='Y'}">
									<div class="input-control checkbox">
										<label> <input type="checkbox" name="applicableDirector" checked="checked"> <span class="check"></span>
											 </label>
									</div>
								</c:when>
								<c:otherwise>
									<div class="input-control checkbox">
										<label> <input type="checkbox" name="applicableDirector"> <span class="check"></span>
											 </label>
									</div>
								</c:otherwise>
								</c:choose>
								</td>
							</tr>
							<tr>
								<td style="width:33.2% ;padding:8px 0px 0px 10px" ><spring:message code="admin.rootCause.applicableTo.division" /> </td>
								<td>
								<c:choose>
								<c:when test="${rootCauseRO.applicableDivision=='Y'}">
									<div class="input-control checkbox">
										<label> <input type="checkbox" name="applicableDivision" checked="checked"> <span class="check"></span>
											 </label>
									</div>
								</c:when>
								<c:otherwise>
									<div class="input-control checkbox">
										<label> <input type="checkbox" name="applicableDivision"> <span class="check"></span>
											 </label>
									</div>
								</c:otherwise>
								</c:choose>
								</td>
							</tr>
							<tr>
								<td style="width:33.2% ;padding:8px 0px 0px 10px" ><spring:message code="admin.rootCause.applicableTo.priPG" /> </td>
								<td>
								<c:choose>
								<c:when test="${rootCauseRO.applicablePriPG=='Y'}">
									<div class="input-control checkbox">
										<label> <input type="checkbox" name="applicablePriPG" checked="checked"> <span class="check"></span>
											 </label>
									</div>
								</c:when>
								<c:otherwise>
									<div class="input-control checkbox">
										<label> <input type="checkbox" name="applicablePriPG"> <span class="check"></span>
											 </label>
									</div>
								</c:otherwise>
								</c:choose>
								</td>
							</tr>
							<tr>
								<td style="width:33.2% ;padding:8px 0px 0px 10px" ><spring:message code="admin.rootCause.applicableTo.secPG" /> </td>
								<td>
								<c:choose>
								<c:when test="${rootCauseRO.applicableSecPG=='Y'}">
									<div class="input-control checkbox">
										<label> <input type="checkbox" name="applicableSecPG" checked="checked"> <span class="check"></span>
											 </label>
									</div>
								</c:when>
								<c:otherwise>
									<div class="input-control checkbox">
										<label> <input type="checkbox" name="applicableSecPG"> <span class="check"></span>
											 </label>
									</div>
								</c:otherwise>
								</c:choose>
								</td>
							</tr>
							</tbody>
							</table>
						</div>
					</div>
				</div>
				
				<div class="panelContainer">
					<div class="containerA" style="padding-left:7px;padding-top: 7px;">
						<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 16px"><spring:message
									code="admin.rootCause.effective" />
							</span>
						</div>
						<div class="contentA" style="padding-top: 10px">
								<table class="tableForm" style="padding-left: 50px;">
									
									<tr>
										<td><spring:message code="admin.rootCause.effective.start" />&nbsp;<span style="color:red">*</span></td>
										
										<td><div id="datepick3c" class="input-control text">
													<input id="datepick3" size="50" name="effectiveStartDate" value="${rootCauseRO.effectiveStartDate}"/>
													&nbsp;<span id="errmsgdatepick3" style="color:red">
												</div>
										</td>
									</tr>
									
									<tr>
										<td><spring:message code="admin.rootCause.effective.end" />&nbsp;<span style="color:red">*</span></td>
										
										<td><div id="datepick4c" class="input-control text">
													<input id="datepick4" size="50" name="effectiveEndDate" value="${rootCauseRO.effectiveEndDate}"/>
													&nbsp;<span id="errmsgdatepick4" style="color:red">
												</div>
										</td>
									</tr>
				
								</table>
							</div>
						</div>
					</div>
				
				<div class="line-separator"></div>
				<table width="98%" style="padding: 10px 10px 10px 50px">
					<tr style="height: 20px; border-bottom-color: gray;">

								<td width="30%">
									<div style="float: left">
						<a href="#top" style="float: left;"><spring:message code="qadb.backToTop" /></a>
					</div>
								</td>
								<td width="70%" style="text-align: right;">
									<button title="Reset" type="reset" onclick="hideErrorDiv();"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Cancel.png">
									</button>
									<button title="Save" type="submit"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png">
									</button></td>
					</tr>
				</table>
				
	</div>
		
			</form:form>

</div>
		<script type="text/javascript">
		
		$(function() {
		$('.headerA').click(function() {
			$(this).closest('.containerA').toggleClass('collapsed');
		});

	});
		
		</script>
	
	<script type="text/javascript">
	$("#datepick1").datepicker({
		showOn:"button",
	    onSelect: function(selected) {
	      $("#datepick2").datepicker("option","minDate", selected)
	    },
	});
	$("#datepick2").datepicker({
		showOn:"button",
	    onSelect: function(selected) {
	      $("#datepick1").datepicker("option","maxDate", selected)
	    },
	});
	
	$("#datepick3").datepicker({
		showOn:"button",
	    onSelect: function(selected) {
	      $("#datepick4").datepicker("option","minDate", selected)
	    },
	});
	$("#datepick4").datepicker({
		showOn:"button",
	    onSelect: function(selected) {
	     $("#datepick3").datepicker("option","maxDate", selected)
	    },
	});

	jQuery(function($){
  
  	 $("#phone").mask("(*************");
  	 $("#datepick1").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("rootCsFrmDtDis").value);
  	 $("#datepick2").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("rootCsToDt").value);
  	 $("#datepick3").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("rootCsStartDt").value);
  	 $("#datepick4").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("rootCsEndDt").value);
   
});
	
	$(document).ready(function() {
		if(document.getElementById("rootCsEndDt").value==""){
			$("#datepick4").datepicker( "setDate" , "12/31/9999" );
		}
		if(document.getElementById("rootCsToDt").value==""){
			document.getElementById("disable").checked = false;
			document.getElementById("datepick1").disabled = true;
			$("#datepick1").datepicker( "option", "showOn", "focus" );
			document.getElementById("datepick2").disabled = true;
			$("#datepick2").datepicker( "option", "showOn", "focus" );
			$("#datepick2").datepicker( "setDate" , "12/31/9999" );
		}
	})
	
	/* $(function() {
		$('.headerA').click(function() {
			$(this).closest('.containerA').toggleClass('collapsed');
		});

	}); */
	
	$("#rootCauseNamec").keypress(function (e) {
		if ((e.which == 0) || e.which == 8) {}
		else{
			var check = maxLimitForName(e,"errmsgRootCauseName","rootCauseName");
			if(check==false){
				return false;
			}
		}
	});
  	$("#rootCauseNamec").keydown(function (e) {
		if(e.ctrlKey && e.keyCode==86){
		   if ((e.which == 0) || e.which == 8) {}
		else{
			var check = maxLimitForName(e,"errmsgRootCauseName","rootCauseName");
			if(check==false){
				return false;
			}
		}
		}
	});
	$("#rootCauseNamec").keyup(function (e) {
		if($("#rootCauseName").val().length>49){
		var name = $("#rootCauseName").val().substring(0,50);
		document.getElementById("rootCauseName").value=name;
		$("#errmsgRootCauseName").html("Maximum limit of 50 char.").show().fadeOut("slow");
		}
	});
	
	$("#rootCauseDescriptionc").keypress(function (e) {
		if ((e.which == 0) || e.which == 8) {}
		else if($("#rootCauseDescription").val().length>199){
   			$("#errmsgRootCauseDesc").html("Maximum limit of 200 char.").show().fadeOut("slow");
   			return false;
   		}
 		else{}
	});
  	$("#rootCauseDescriptionc").keydown(function (e) {
		if(e.ctrlKey && e.keyCode==86){
		   if ((e.which == 0) || e.which == 8) {}
			else if($("#rootCauseDescription").val().length>199){
	   			$("#errmsgRootCauseDesc").html("Maximum limit of 200 char.").show().fadeOut("slow");
	   			return false;
	   		}
	 		else{}
		}
	});
	$("#rootCauseDescriptionc").keyup(function (e) {
		if($("#rootCauseDescription").val().length>199){
		var name = $("#rootCauseDescription").val().substring(0,200);
		document.getElementById("rootCauseDescription").value=name;
		$("#errmsgRootCauseDesc").html("Maximum limit of 200 char.").show().fadeOut("slow");
		}
	});
	
	function enableDisDatert(){
		if($("#disable").is(":checked")){
			document.getElementById("datepick1").disabled = false;
			$("#datepick1").datepicker( "option", "showOn", "button" );
			document.getElementById("datepick2").disabled = false;
			$("#datepick2").datepicker( "option", "showOn", "button" );
		}else{
			document.getElementById("datepick1").disabled = true;
			$("#datepick1").datepicker( "option", "showOn", "focus" );
			document.getElementById("datepick2").disabled = true;
			$("#datepick2").datepicker( "option", "showOn", "focus" );
		}
	}
	</script>
</div>
	<jsp:include page="footer.jsp"></jsp:include>

</body>

</html>
