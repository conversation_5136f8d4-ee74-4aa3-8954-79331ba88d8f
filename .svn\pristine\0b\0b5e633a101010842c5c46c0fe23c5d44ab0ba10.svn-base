<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="scriptlet" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="30" bottomMargin="30" whenNoDataType="NoDataSection" whenResourceMissingType="Empty" >
	<property name="com.jasperassistant.designer.Grid" value="false"/>
	<property name="com.jasperassistant.designer.SnapToGrid" value="false"/>
	<property name="com.jasperassistant.designer.GridWidth" value="12"/>
	<property name="com.jasperassistant.designer.GridHeight" value="12"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="net.sf.jasperreports.awt.ignore.missing.font" value="true"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	
	<field name="errorCodeId" class="java.lang.String">
		<fieldDescription><![CDATA[errorCodeId]]></fieldDescription>
	</field>
	<field name="unitManagerName" class="java.lang.String">
		<fieldDescription><![CDATA[unitManagerName]]></fieldDescription>
	</field>
	
	<field name="editCode" class="java.lang.String">
		<fieldDescription><![CDATA[editCode]]></fieldDescription>
	</field>
	<field name="associateName" class="java.lang.String">
		<fieldDescription><![CDATA[associateName]]></fieldDescription>
	</field>
	<field name="dcn" class="java.lang.String">
		<fieldDescription><![CDATA[dcn]]></fieldDescription>
	</field>
	<field name="processedDate" class="java.lang.String">
		<fieldDescription><![CDATA[processedDate]]></fieldDescription>
	</field>
	<field name="errorReason" class="java.lang.String">
		<fieldDescription><![CDATA[errorReason]]></fieldDescription>
	</field>
	
	
	<group name="dummy"> 		
	<groupExpression><![CDATA["dummy"]]></groupExpression> 
		<groupHeader>
			
		</groupHeader>
	</group>
	
	
	<pageHeader>
		<band height="81" splitType="Stretch">
			<rectangle>
				<reportElement x="0" y="75" width="555" height="2" forecolor="#FFFFFF" backcolor="#000080"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
				<rectangle>
				<reportElement x="0" y="27" width="555" height="3" forecolor="#FFFFFF" backcolor="#C0C0C0"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="1" y="54" width="555" height="2" forecolor="#FFFFFF" backcolor="#000080"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
			<textField>
				<reportElement x="8" y="0" width="201" height="30" forecolor="#000080"  />
				<textElement>
					<font fontName="Times New Roman" size="20" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Edit codes for Error :"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="191" y="0" width="201" height="30" forecolor="#000080"  />
				<textElement>
					<font fontName="Times New Roman" size="20" isBold="false" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{errorCodeId}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="7" y="33" width="63" height="18" forecolor="#000080"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Times New Roman" size="14" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Manager :]]></text>
			</staticText>
			<textField>
				<reportElement x="72" y="34" width="370" height="20"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="Arial" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{unitManagerName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="14" y="58" width="50" height="18" forecolor="#000080"  />
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Edit Code]]></text>
			</staticText>
			<staticText>
				<reportElement x="84" y="58" width="72" height="18" forecolor="#000080"  />
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Associate]]></text>
			</staticText>
			<staticText>
				<reportElement x="235" y="58" width="72" height="18" forecolor="#000080"  >
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[DCN]]></text>
			</staticText>
			<staticText>
				<reportElement x="313" y="58" width="81" height="18" forecolor="#000080"  >
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Processed Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="405" y="59" width="130" height="18" forecolor="#000080"  >
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Reason For Error]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band/>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="15" y="1" width="60" height="10"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{editCode}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="84" y="1" width="146" height="10"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{associateName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="233" y="1" width="80" height="10"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dcn}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="319" y="1" width="60" height="10"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{processedDate}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="405" y="1" width="149" height="10"  >
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{errorReason}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="43" splitType="Stretch">
			<textField>
				<reportElement x="381" y="7" width="100" height="30" forecolor="#000080"  />
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="481" y="7" width="100" height="30" forecolor="#000080"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="EEEE, MMMMM dd, yyyy">
				<reportElement x="4" y="7" width="133" height="17" forecolor="#000080"  />
				<textElement>
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="140" y="7" width="260" height="16" forecolor="#000080"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Quality Assurance Productivity Management]]></text>
			</staticText>
			<rectangle>
				<reportElement x="0" y="3" width="555" height="3" forecolor="#FFFFFF" backcolor="#C0C0C0"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
		</band>
	</pageFooter>
	<lastPageFooter>
		<band height="41" splitType="Stretch">
			<textField>
				<reportElement x="381" y="7" width="100" height="30" forecolor="#000080"  />
				<textElement textAlignment="Right">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="481" y="7" width="100" height="30" forecolor="#000080"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="EEEE, MMMMM dd, yyyy">
				<reportElement x="4" y="7" width="133" height="17" forecolor="#000080"  />
				<textElement>
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="140" y="7" width="260" height="16" forecolor="#000080"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Quality Assurance Productivity Management]]></text>
			</staticText>
			<rectangle>
				<reportElement x="0" y="3" width="555" height="3" forecolor="#FFFFFF" backcolor="#C0C0C0"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="local_mesure_unitwidth" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
		</band>
	</lastPageFooter>
	
	<noData>
		<band height="55" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="180" y="0" width="316" height="47" forecolor="#000050">
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="22" isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[No records found!!]]></text>
			</staticText>
		</band>
	</noData>
	
</jasperReport>
