package com.carefirst.audit.model;

import java.util.List;

public class AuditDetails {

	private String dcn;
	private String lob;
	private String memberId;
	private String processDate;
	private String paidDate;

	private String totalCharge;
	private String paid;
	private String monetaryError;
	private String amountp;
	private String theoreticalPaid;
	private String piChk;
	private String pi;
	private String juridiction;

	private String mock;
	private String oos;
	private String e2e;
	private String riskAccount;
	private String adjustment;
	private String platform;
	private String fyi;
	private String fyicheck;

	private String claimType;
	private String claimTypeId;
	private String penaltyInterestType;
	private String piTypeId;
	private String auditType;
	private String audTypeId;
	private String priPGA;
	private String pPgaId;
	private String secPGA;
	private String sPgaId;
	private String processtype;
	private String processTypId;

	private String auditId;
	
	private String subscriberCK;
	private String AccountID;
	private String AccountName;
	private String Jurisdiction;
	private String Group;
	private String claimCurrentStatus;
	private String productID;
	private String adjustmentReasoncode;
	private List<Products> products;
	private List<String> BSBSCode;
	private List<String> productLine;
	private List<String> productDescription;
	private List<String> groupID;
	
	//High Dollar
	private String claimAdjFlag;
	private String serviceAdjFlag;
	private String examinerName;
	private String QAName;
	private String patientName;
	private String serviceDates;
	private String typeOfService;
	private String diagnosis;
	private String surgeryDOS;
	private String surgery;
	private String fileReferenced;
	private String interestPaid;/*Enhancement*/
	private String providerName;
	private String providerNumber;
	private String payee;
	private String notes;
	private String audSignDate;
	private String vpSignDate;
	private String forwrdToDate;
	private String rcvedFrmDate;
	private String releasedByDate;
	
	//Auditor List
	private String auditorName;
	
	public String getInterestPaid() {
		return interestPaid;
	}

	public void setInterestPaid(String interestPaid) {
		this.interestPaid = interestPaid;
	}

	public String getAdjustmentReasoncode() {
		return adjustmentReasoncode;
	}

	public void setAdjustmentReasoncode(String adjustmentReasoncode) {
		this.adjustmentReasoncode = adjustmentReasoncode;
	}

	public String getAuditorName() {
		return auditorName;
	}

	public void setAuditorName(String auditorName) {
		this.auditorName = auditorName;
	}

	public String getAudSignDate() {
		return audSignDate;
	}

public String getPlatform() { 
return platform;
}

public void setPlatform(String platform) {  // Was previously: public void setAdjustment(String adjustment)
this.platform = platform;
}

	public void setAudSignDate(String audSignDate) {
		this.audSignDate = audSignDate;
	}

	public String getVpSignDate() {
		return vpSignDate;
	}

	public void setVpSignDate(String vpSignDate) {
		this.vpSignDate = vpSignDate;
	}

	public String getForwrdToDate() {
		return forwrdToDate;
	}

	public void setForwrdToDate(String forwrdToDate) {
		this.forwrdToDate = forwrdToDate;
	}

	public String getRcvedFrmDate() {
		return rcvedFrmDate;
	}

	public void setRcvedFrmDate(String rcvedFrmDate) {
		this.rcvedFrmDate = rcvedFrmDate;
	}

	public String getReleasedByDate() {
		return releasedByDate;
	}

	public void setReleasedByDate(String releasedByDate) {
		this.releasedByDate = releasedByDate;
	}

	public String getExaminerName() {
		return examinerName;
	}

	public void setExaminerName(String examinerName) {
		this.examinerName = examinerName;
	}

	public String getPatientName() {
		return patientName;
	}

	public void setPatientName(String patientName) {
		this.patientName = patientName;
	}

	public String getServiceDates() {
		return serviceDates;
	}

	public void setServiceDates(String serviceDates) {
		this.serviceDates = serviceDates;
	}

	public String getTypeOfService() {
		return typeOfService;
	}

	public void setTypeOfService(String typeOfService) {
		this.typeOfService = typeOfService;
	}

	public String getDiagnosis() {
		return diagnosis;
	}

	public void setDiagnosis(String diagnosis) {
		this.diagnosis = diagnosis;
	}

	public String getSurgeryDOS() {
		return surgeryDOS;
	}

	public void setSurgeryDOS(String surgeryDOS) {
		this.surgeryDOS = surgeryDOS;
	}

	public String getSurgery() {
		return surgery;
	}

	public void setSurgery(String surgery) {
		this.surgery = surgery;
	}

	public String getFileReferenced() {
		return fileReferenced;
	}

	public void setFileReferenced(String fileReferenced) {
		this.fileReferenced = fileReferenced;
	}

	public String getProviderName() {
		return providerName;
	}

	public void setProviderName(String providerName) {
		this.providerName = providerName;
	}

	public String getProviderNumber() {
		return providerNumber;
	}

	public void setProviderNumber(String providerNumber) {
		this.providerNumber = providerNumber;
	}

	public String getPayee() {
		return payee;
	}

	public void setPayee(String payee) {
		this.payee = payee;
	}

	public String getNotes() {
		return notes;
	}

	public void setNotes(String notes) {
		this.notes = notes;
	}

	public String getClaimAdjFlag() {
		return claimAdjFlag;
	}

	public void setClaimAdjFlag(String claimAdjFlag) {
		this.claimAdjFlag = claimAdjFlag;
	}

	public String getServiceAdjFlag() {
		return serviceAdjFlag;
	}

	public void setServiceAdjFlag(String serviceAdjFlag) {
		this.serviceAdjFlag = serviceAdjFlag;
	}

	public String getQAName() {
		return QAName;
	}

	public void setQAName(String qAName) {
		QAName = qAName;
	}

	public List<Products> getProducts() {
		return products;
	}

	public void setProducts(List<Products> products) {
		this.products = products;
	}

	public String getSubscriberCK() {
		return subscriberCK;
	}

	public void setSubscriberCK(String subscriberCK) {
		this.subscriberCK = subscriberCK;
	}

	public String getAccountID() {
		return AccountID;
	}

	public void setAccountID(String accountID) {
		AccountID = accountID;
	}

	public String getAccountName() {
		return AccountName;
	}

	public void setAccountName(String accountName) {
		AccountName = accountName;
	}

	public String getJurisdiction() {
		return Jurisdiction;
	}

	public void setJurisdiction(String jurisdiction) {
		Jurisdiction = jurisdiction;
	}

	public String getGroup() {
		return Group;
	}

	public void setGroup(String group) {
		Group = group;
	}

	public String getClaimCurrentStatus() {
		return claimCurrentStatus;
	}

	public void setClaimCurrentStatus(String claimCurrentStatus) {
		this.claimCurrentStatus = claimCurrentStatus;
	}

	public String getProductID() {
		return productID;
	}

	public void setProductID(String productID) {
		this.productID = productID;
	}

	public List<String> getBSBSCode() {
		return BSBSCode;
	}

	public void setBSBSCode(List<String> bSBSCode) {
		BSBSCode = bSBSCode;
	}

	public List<String> getProductLine() {
		return productLine;
	}

	public void setProductLine(List<String> productLine) {
		this.productLine = productLine;
	}

	public List<String> getProductDescription() {
		return productDescription;
	}

	public void setProductDescription(List<String> productDescription) {
		this.productDescription = productDescription;
	}

	public List<String> getGroupID() {
		return groupID;
	}

	public void setGroupID(List<String> groupID) {
		this.groupID = groupID;
	}

	public String getFyicheck() {
		return fyicheck;
	}

	public void setFyicheck(String fyicheck) {
		this.fyicheck = fyicheck;
	}

	public String getFyi() {
		return fyi;
	}

	public void setFyi(String fyi) {
		this.fyi = fyi;
	}

	public String getProcessDate() {
		return processDate;
	}

	public void setProcessDate(String processDate) {
		this.processDate = processDate;
	}

	public String getPaidDate() {
		return paidDate;
	}

	public void setPaidDate(String paidDate) {
		this.paidDate = paidDate;
	}

	public String getJuridiction() {
		return juridiction;
	}

	public void setJuridiction(String juridiction) {
		this.juridiction = juridiction;
	}

	public String getAuditId() {
		return auditId;
	}

	public void setAuditId(String auditId) {
		this.auditId = auditId;
	}

	public String getProcessTypId() {
		return processTypId;
	}

	public void setProcessTypId(String processTypId) {
		this.processTypId = processTypId;
	}

	public String getClaimTypeId() {
		return claimTypeId;
	}

	public void setClaimTypeId(String claimTypeId) {
		this.claimTypeId = claimTypeId;
	}

	public String getPiTypeId() {
		return piTypeId;
	}

	public void setPiTypeId(String piTypeId) {
		this.piTypeId = piTypeId;
	}

	public String getAudTypeId() {
		return audTypeId;
	}

	public void setAudTypeId(String audTypeId) {
		this.audTypeId = audTypeId;
	}

	public String getpPgaId() {
		return pPgaId;
	}

	public void setpPgaId(String pPgaId) {
		this.pPgaId = pPgaId;
	}

	public String getsPgaId() {
		return sPgaId;
	}

	public void setsPgaId(String sPgaId) {
		this.sPgaId = sPgaId;
	}

	public String getClaimType() {
		return claimType;
	}

	public void setClaimType(String claimType) {
		this.claimType = claimType;
	}

	public String getAuditType() {
		return auditType;
	}

	public void setAuditType(String auditType) {
		this.auditType = auditType;
	}

	public String getPriPGA() {
		return priPGA;
	}

	public void setPriPGA(String priPGA) {
		this.priPGA = priPGA;
	}

	public String getSecPGA() {
		return secPGA;
	}

	public void setSecPGA(String secPGA) {
		this.secPGA = secPGA;
	}

	public String getProcesstype() {
		return processtype;
	}

	public void setProcesstype(String processtype) {
		this.processtype = processtype;
	}

	public String getPenaltyInterestType() {
		return penaltyInterestType;
	}

	public void setPenaltyInterestType(String penaltyInterestType) {
		this.penaltyInterestType = penaltyInterestType;
	}

	public String getDcn() {
		return dcn;
	}

	public void setDcn(String dcn) {
		this.dcn = dcn;
	}

	public String getLob() {
		return lob;
	}

	public void setLob(String lob) {
		this.lob = lob;
	}

	public String getMemberId() {
		return memberId;
	}

	public void setMemberId(String memberId) {
		this.memberId = memberId;
	}

	public String getTotalCharge() {
		return totalCharge;
	}

	public void setTotalCharge(String totalCharge) {
		this.totalCharge = totalCharge;
	}

	public String getPaid() {
		return paid;
	}

	public void setPaid(String paid) {
		this.paid = paid;
	}

	public String getMonetaryError() {
		return monetaryError;
	}

	public void setMonetaryError(String monetaryError) {
		this.monetaryError = monetaryError;
	}

	public String getTheoreticalPaid() {
		return theoreticalPaid;
	}

	public void setTheoreticalPaid(String theoreticalPaid) {
		this.theoreticalPaid = theoreticalPaid;
	}

	public String getMock() {
		return mock;
	}

	public void setMock(String mock) {
		this.mock = mock;
	}

	public String getOos() {
		return oos;
	}

	public void setOos(String oos) {
		this.oos = oos;
	}

	public String getE2e() {
		return e2e;
	}

	public void setE2e(String e2e) {
		this.e2e = e2e;
	}

	public String getRiskAccount() {
		return riskAccount;
	}

	public void setRiskAccount(String riskAccount) {
		this.riskAccount = riskAccount;
	}

	public String getAdjustment() {
		return adjustment;
	}

	public void setAdjustment(String adjustment) {
		this.adjustment = adjustment;
	}

	public String getAmountp() {
		return amountp;
	}

	public void setAmountp(String amountp) {
		this.amountp = amountp;
	}

	public String getPiChk() {
		return piChk;
	}

	public void setPiChk(String piChk) {
		this.piChk = piChk;
	}

	public String getPi() {
		return pi;
	}

	public void setPi(String pi) {
		this.pi = pi;
	}

}
