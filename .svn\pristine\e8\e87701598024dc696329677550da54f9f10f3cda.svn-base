<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<head>
<link href="webResources/css/qadb.css" rel="stylesheet">

<script type="text/javascript" src="webResources/js/metro.min.js"></script>
<!-- Local JavaScript -->

<link href="webResources/css/iconFont.css" rel="stylesheet">
<link href="webResources/css/docs.css" rel="stylesheet">
<link href="webResources/js/prettify/prettify.css" rel="stylesheet">

<!-- Load JavaScript Libraries -->
<script src="webResources/js/jquery/jquery.min.js"></script>
<script src="webResources/js/jquery/jquery.widget.min.js"></script>
<script src="webResources/js/jquery/jquery.dataTables.js"></script>
<style>
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
	margin-right: auto;
	height: 210%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}
.hidden
        {
            display: none;
        }
</style>



<title><spring:message code="admin.mapping.title" /></title>
</head>
<body>
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container2">

		<!-- Sidebar Start-->
		<div id="left-sidebar">
			<h2 style="color: white;padding-top:32px"><spring:message code="admin.mapping.edit.leftNav.heading1" /> </h2>
			<h3 style="color: white;padding-top:10px"><spring:message code="admin.mapping.edit.leftNav.heading2" /> </h3>

		
		</div>
		<!-- Sidebar End-->

		<div id="content-container" style="padding-left: 40px">

			<form:form id="mappingSearchForm" name="mappingSearchForm" method="GET" commandName="mappingSearchForm" action="editMappingResult" style="width:700px">


				<div style="padding: 10px 0px 0px 0px;">
					<span class="bread1"><spring:message
							code="admin.associate.bread1" /> </span><span class="bread2"><spring:message
							code="admin.mapping.edit.leftNav.heading1" />
					</span>
				</div>
				<br>
				<span style="color: #0070c0; font-size: 16px"><spring:message code="admin.mapping.edit.leftNav.heading1" /></span>
				<div class="line-separator"></div>
				<br>

				<table class="tableForm">
					<tr>
									<td style="padding-left: 7px;"><spring:message
									code="admin.mapping.auditor" /> </td>
									<td><div class="input-control select" >
											<select style="width: 200px; font-size: 14px; border-color: #919191" name="auditorName" id="auditorName">
												<option value="" selected="">Select Auditor</option>
								 		     <c:forEach items="${auditorsList}" var="auditorsList">
													<option value="${auditorsList.auditorName}"
														 ${auditorsList.auditorName == auditorNames ? 'selected="selected"' : ''}>${auditorsList.auditorName}</option>					 	
											 </c:forEach>
											</select>
										</div></td>
					</tr>
					<tr>
									<td style="padding-left: 7px;"><spring:message
									code="admin.mapping.month" /> </td>
									<td><div class="input-control select" >
											<select style="width: 200px; font-size: 14px; border-color: #919191" name="month" id="month">
												<!-- <option value="0" selected="">Select Month</option> -->
												<c:forEach items="${months}" var="months">
													<option value="${months}"
														${months == monthEO ? 'selected="selected"' : ''}>${months}</option>					 	
											 </c:forEach>
											</select>
										</div></td>
								</tr>
								<tr>
									<td style="padding-left: 7px;"><spring:message
									code="admin.mapping.year" /> </td>
									<td><div class="input-control select" id="jobIdc">
											<select style="width: 200px; font-size: 14px; border-color: #919191" name="year" >
												<!-- <option value="0">Select Year</option> -->
												<c:forEach items="${years}" var="years">
													<option value="${years}"
														${years == yearEO ? 'selected="selected"' : ''}>${years}</option>					 	
											 </c:forEach>
											</select>
										</div></td>
								</tr>

				</table>
				

				<div style="width: 750px">
					<div style="float: right">
						<button type="reset" title="Reset" class="button inverse"><spring:message code="qadb.reset" /></button>
						<button type="submit" title="Search" style="background-color: #298fd8"
							class="button default"> <spring:message code="qadb.search" /></button>
					</div>
				</div>
			</form:form>

			

		<c:if test="${null!=mappingRO }">
		<br> <br> <span style="color: #0070c0; font-size: 16px"> <spring:message code="qadb.searchResults" />
				</span> <br>
			<br>
			<div class="line-separator"></div>
			<table class="table striped hovered dataTable" id="searchDatatable"
				width="700px">
				<thead>
					<tr style="height: 30px">

						<td class="text-left" font-size="25px" style="width: 120px"><spring:message code="admin.mapping.auditor" /></td>
						<td class="text-left" font-size="25px" style="width: 240px"><spring:message code="admin.mapping.associates" /></td>
						<td class="text-left" font-size="25px" style="width: 250px"><spring:message code="admin.mapping.month" /></td>
						<td class="text-left" font-size="25px" style="width: 90px"><spring:message code="admin.mapping.year" /></td>

					</tr>
				</thead>

				<tbody>
						
					<c:forEach items="${mappingRO}" var="rs">
						<tr style="height: 30px">
							<td>${rs.auditorName}</td>
							<td>${rs.associateName}</td>
							<td>${rs.month}</td>
							<td>${rs.year}</td>
						</tr>
					</c:forEach> 
						
				</tbody>


			</table>

			</c:if>
			 <script>
			 	
				$(function() {
					$('#searchDatatable').dataTable({
										
					"columns" : [
					
					{
					"render": function(data, type, full, meta) {
					/* return '<a href="'+full[0]+'"><u>'+data+'<u></a>'; */
					return '<a href="getMapping?id='+full[0]+'"><u>'+data+'<u></a>';
                   
            }
            }, {}, {},{}]
									});
				});
			</script> 
		</div>
	</div>

	<jsp:include page="footer.jsp"></jsp:include>

</body>

</html>
