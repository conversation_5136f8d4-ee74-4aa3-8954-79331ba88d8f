package com.carefirst.qadb.service;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.servlet.ModelAndView;

public interface AuthorizationService {

	public String getRole(String userId);
	
	public ModelAndView AuthorizeUsersAudit(String jspView,HttpServletRequest request);
	
	public ModelAndView AuthorizeUsersReports(String jspView,HttpServletRequest request);
	
	public ModelAndView AuthorizeUsersAdmin(String jspView,HttpServletRequest request);

	public String getUserGroup(HttpServletRequest request);
}
