package com.carefirst.qadb.dao;

import java.sql.SQLException;
import java.util.List;

import net.sf.jasperreports.engine.JRDataSource;

import com.carefirst.audit.model.Associate;
import com.carefirst.audit.model.PerformanceGroupErrorSheet;
import com.carefirst.audit.model.ScoresAndTrends;

public interface ScoresReportsDAO {

	JRDataSource getScoresReport(ScoresAndTrends scores) throws SQLException;
	
	JRDataSource getTrendsReport(ScoresAndTrends scores) throws SQLException;
	
	List<Associate> getClaimProcessors(String jobTitleName, String userGrp) throws SQLException;
	
	String getTimeframe (String period,String year,String timeframeIp);
	
	String getType (String type,String subType);
	
	String getTitle (String subject);
	
	JRDataSource getMonetoryReport(ScoresAndTrends scores)throws SQLException ;

	List<Associate> getFacetsIds() throws SQLException;
	
}
