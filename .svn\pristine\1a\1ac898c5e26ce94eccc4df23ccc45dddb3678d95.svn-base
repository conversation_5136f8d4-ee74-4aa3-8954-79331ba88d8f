<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>

<head>
<link href="webResources/css/qadb.css" rel="stylesheet">

<script type="text/javascript" src="webResources/js/metro.min.js"></script>
<!-- Local JavaScript -->

<link href="webResources/css/iconFont.css" rel="stylesheet">
<link href="webResources/css/docs.css" rel="stylesheet">
<link href="webResources/js/prettify/prettify.css" rel="stylesheet">
<link href="webResources/css/jquery-ui.css" rel="stylesheet">

<!-- Load JavaScript Libraries -->
<script src="webResources/js/jquery/jquery.widget.min.js"></script>
<script type="text/javascript" src="webResources/js/jquery.min.js"></script>
<script type="text/javascript" src="webResources/js/jquery-ui.js"></script>
<script type="text/javascript" src="webResources/js/qadb.js"></script>
<script type="text/javascript" src="webResources/js/datep.js"></script>
<style>
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
	margin-right: auto;
	height: 210%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}

.ui-widget {
	font-family: Verdana, Arial, sans-serif;
	font-size: 1.1em;
}

.ui-widget .ui-widget {
	font-size: 1em;
}

.ui-widget input, .ui-widget select, .ui-widget textarea, .ui-widget button
	{
	font-family: Verdana, Arial, sans-serif;
	font-size: 1em;
}

.ui-widget-content {
	border: 1px solid #aaaaaa;
	background: #ffffff url() 50% 50% repeat-x;
	color: #222222;
}

.ui-widget-content a {
	color: #222222;
}

/* .ui-widget-header {
	border: 1px solid #aaaaaa;
	background: #2573ab url(images/ui-bg_highlight-soft_75_cccccc_1x100.png)
		50% 50% repeat-x;
	color: #e6f5fc;
	font-weight: bold;
} */

.ui-widget-header a {
	color: #222222;
}

.ui-datepicker-trigger {
	background-image: url("webResources/images/Forms/Icn_Date_Picker.png");
	height: 36px;
	width: 36px;
	background-color: white;
}

.ui-icon-circle-triangle-e {
	background-image: url("webResources//Navbar/Icn_Right_Arrow.png");
}

.ui-icon-circle-triangle-w {
	background-image: url("webResources/images/skip_backward.png");
}
</style>



<title>Reports-User Reports</title>
</head>
<body>
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container2">

		<!-- Sidebar Start-->
		<div id="left-sidebar">
			<h2 style="color: white;padding-top: 32px;">
				<spring:message code="reports.user.leftNav.heading1"></spring:message>
			</h2>
			<h3 style="color: white;">
				<spring:message code="reports.user.leftNav.heading2"></spring:message>
			</h3>


		</div>
		<!-- Sidebar End-->

		<div id="content-container" style="padding: 10px 0px 0px 40px;">
			<span class="bread1">Reports & Trends > </span><span class="bread2">User
				Reports</span>
			<form:form id="user_report" name="user_report" method="GET" commandName="adjReqForm" action="AdjustmentsRequiredReport"
				style="width:700px">

				<br>
				<span style="color: #0070c0;font-size:16px"><spring:message
						code="reports.user.adjustmentReport"></spring:message></span>
				<div class="line-separator"></div>
				
				<div style="padding-top: 10px;padding-left: 7px;background-color: #fafafa;width:750px">
				<table class="tableForm1" style="width:98%">
					<tr>
						<td width="30%" style="padding:5px 0px 0px 15px"><spring:message
								code="reports.user.adjustmentReport.name"></spring:message></td>
						<td width="70%">
							<div class="input-control select">
								<select name="auditorName">
									<c:forEach items="${auditorsList}" var="auditorsList">
												<option value="${auditorsList.auditorName}">${auditorsList.auditorName}</option>
											</c:forEach>
								</select>
							</div>
						</td>
					</tr>
					<tr>
						<td><span style="color: #0070c0; font-size: 16px;padding-left:5px;"><spring:message code="audit.search.results.audit.date" /></span></td>
					</tr>
					<tr>
						<td width="30%" style="padding:5px 0px 0px 15px"><spring:message code="audit.search.processed.date.from" /></td>
						<td><div class="input-control text" >
								<input id="datepick1" size="50" name="auditFromDate" required/>
			                </div>
						</td>
					</tr>
							<tr>
								<td width="30%" style="padding:5px 0px 0px 15px"><spring:message code="audit.search.processed.date.to" /></td>
								<td><div class="input-control text" >
										<input id="datepick2" size="50" name="auditToDate" required/>	
			                         </div>
								</td>
							</tr>
				</table>
				</div>
					<div style="width:750px">
						<div style="float:right">
					<button type="reset" id="reset" class="button inverse">Reset</button>
					<button type="submit"  style="background-color:#298fd8"
						class="button default">Generate</button>
					</div></div>
			</form:form>
			<br> <span style="color: #0070c0;font-size:16px"><spring:message
					code="reports.user.otherReport"></spring:message></span>
			<div class="line-separator"></div>


			<form:form id="otherReportForm" name="otherReportForm" method="GET"
				commandName="associateForm" action="ListOfErrorCodes"
				style="width:700px">
				<div style="padding-top: 10px;padding-left: 7px;background-color: #fafafa;width:750px">
				<table class="tableForm1" style="width:98%">
					<tr>
						<td width="30%"><spring:message
								code="reports.user.otherReport.reportType"></spring:message></td>
						<td>
							<div class="input-control radio default-style">
								<label> <input type="radio" id="errorCodes"
									name="reportType" value="E" checked="checked"> <span
									class="check"></span>
								</label>

							</div>
							<spring:message
								code="reports.user.otherReport.reportType.errorCodes"></spring:message>
							<div class="input-control radio default-style">
								<label> <input type="radio" id="CurrentProcessors"
									name="reportType" value="C"> <span class="check"></span>
								</label>

							</div>
							<spring:message
								code="reports.user.otherReport.reportType.currentProcessors"></spring:message>
							<div class="input-control radio default-style">
								<label> <input type="radio" id="Supervisors"
									name="reportType" value="S"> <span class="check"></span>
								</label>

							</div>
							<spring:message
								code="reports.user.otherReport.reportType.supervisors"></spring:message>
						</td>
					</tr>
					<tr>
						<td width="30%"><spring:message
								code="reports.user.otherReport.status"></spring:message></td>
						<td width="70%">
							<div class="input-control select">
								<select name="status">
									<option value="Enabled" selected>Enabled</option>
									<option value="Disabled">Disabled</option>
									<option value="All">All</option>
								</select>
							</div>
						</td>
					</tr>
				</table>
				</div>
					<div style="width:750px">
						<div style="float:right">
					<button type="reset" id="reset" class="button inverse">Reset</button>
					<button type="submit" id="generate" style="background-color:#298fd8"
						class="button default">Generate</button>
					</div></div>






			</form:form>




			<script type="text/javascript">
				$("#datepick1").datepicker(
						{
							showOn : "button",
							onSelect : function(selected) {
								$("#datepick2").datepicker("option", "minDate",
										selected)
							},
						});
				$("#datepick2").datepicker(
						{
							showOn : "button",
							onSelect : function(selected) {
								$("#datepick1").datepicker("option", "maxDate",
										selected)
							},
						});

				jQuery(function($) {
					$("#datepick1").mask("99/99/9999", {
						placeholder : "MM/DD/YYYY"
					});
					$("#datepick2").mask("99/99/9999", {
						placeholder : "MM/DD/YYYY"
					});
				});
			</script>
			<!--change form action depending upon radio selection action  -->
			<script type="text/javascript">
				$("#generate").click(
						function() {
							$("#otherReportForm").removeAttr("action");

							if ($("input[name=reportType]:checked",
									"#otherReportForm").val() == "E") {
								console.log("E");
								$("#otherReportForm").attr("action",
										"ListOfErrorCodes");
							}
							if ($("input[name=reportType]:checked",
									"#otherReportForm").val() == "C") {
								console.log("C");
								$("#otherReportForm").attr("action",
										"ListOfCurrentProcessors");
							}
							if ($("input[name=reportType]:checked",
									"#otherReportForm").val() == "S") {
								console.log("S");
								$("#otherReportForm").attr("action",
										"ListOfSupervisors");
							}
						});
			</script>


		</div>
	</div>

	<jsp:include page="footer.jsp"></jsp:include>

</body>

</html>
