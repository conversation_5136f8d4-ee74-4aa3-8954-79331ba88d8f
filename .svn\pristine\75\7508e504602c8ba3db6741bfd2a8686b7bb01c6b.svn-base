<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>

<head>
<link href="webResources/css/qadb.css" rel="stylesheet">

<script type="text/javascript" src="webResources/js/metro.min.js"></script>
                <!-- Local JavaScript -->
			
			 <link href="webResources/css/iconFont.css" rel="stylesheet">
    <link href="webResources/css/docs.css" rel="stylesheet">
    <link href="webResources/js/prettify/prettify.css" rel="stylesheet">
    <link href="webResources/css/jquery-ui.css" rel="stylesheet">

    <!-- Load JavaScript Libraries -->
    <script src="webResources/js/jquery/jquery.min.js"></script>
    <script src="webResources/js/jquery/jquery.widget.min.js"></script>
    <script type="text/javascript" src="webResources/js/jquery-ui.js"></script>
    <script type="text/javascript" src="webResources/js/qadb.js"></script>
 	<script type="text/javascript" src="webResources/js/datep.js"></script>
<style>
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
	margin-right: auto;
	height: 210%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}

.ui-widget {
	font-family: Verdana, Arial, sans-serif;
	font-size: 1.1em;
}

.ui-widget .ui-widget {
	font-size: 1em;
}

.ui-widget input, .ui-widget select, .ui-widget textarea, .ui-widget button
	{
	font-family: Verdana, Arial, sans-serif;
	font-size: 1em;
}

.ui-widget-content {
	border: 1px solid #aaaaaa;
	background: #ffffff url() 50% 50% repeat-x;
	color: #222222;
}

.ui-widget-content a {
	color: #222222;
}

/* .ui-widget-header {
	border: 1px solid #aaaaaa;
	background: #2573ab url(images/ui-bg_highlight-soft_75_cccccc_1x100.png)
		50% 50% repeat-x;
	color: #e6f5fc;
	font-weight: bold;
} */

.ui-widget-header a {
	color: #222222;
}

.ui-datepicker-trigger {
	background-image: url("webResources/images/Forms/Icn_Date_Picker.png");
	height: 36px;
	width: 36px;
	background-color: white;
}

.ui-icon-circle-triangle-e {
	background-image: url("webResources//Navbar/Icn_Right_Arrow.png");
}

.ui-icon-circle-triangle-w {
	background-image: url("webResources/images/skip_backward.png");
}
</style>



<title><spring:message code="reports.editCode.title"></spring:message></title>
</head>
<body>
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container2">

		<!-- Sidebar Start-->
		<div id="left-sidebar">
			<h2 style="color: white;padding-top: 32px;"><spring:message code="reports.editCode.leftNav.heading1"></spring:message></h2>
			<h3 style="color: white;"><spring:message code="reports.editCode.leftNav.heading2"></spring:message></h3>

			
		</div>
		<!-- Sidebar End-->

		<div id="content-container"  style="padding: 10px 0px 0px 40px;">
		<span class="bread1"><spring:message code="reports.bread1"></spring:message></span><span class="bread2"><spring:message code="reports.editCode.leftNav.heading1"></spring:message></span>
		<form:form id="edit_code_report" name="edit_code_report" method="GET" commandName="editCodeForm" action="EDIT_Code_Report" style="width:700px">

		<br>
		<span style="color: #0070c0;font-size:16px"><spring:message code="reports.editCode.timeFrame"></spring:message></span>
		<div class="line-separator"></div>
		  <div style="padding-top: 10px;padding-left: 7px;background-color: #fafafa;width:750px">
		  <table class="tableForm1" style="width:98%">
		  <tr>
		  		<td width="30%"><spring:message code="reports.editCode.from"></spring:message></td>
		  		<td>
		  			<div class="input-control text" >
                                 <input id="datepick1" size="50" name="fromDate" required/>
								
                                </div>
		  		</td>
		  </tr>
		   <tr>
		  		<td width="30%"><spring:message code="reports.editCode.to"></spring:message></td>
		  		<td>
		  			<div class="input-control text" >
                                 <input id="datepick2" size="50" name="toDate"  required/>
								
                                </div>
		  		</td>
		  </tr>
		 </table>
		 </div>
		 	
		<div style="padding-top: 10px;padding-left: 7px;background-color: #fafafa;width:750px">
		   <table class="tableForm1" style="width:98%">
			<br>
			<span style="color: #0070c0;font-size:16px"><spring:message code="reports.editCode.reportParameters"></spring:message></span>
			<div class="line-separator"></div>
		 
		  <tr>
		  		<td width="30%"><spring:message code="reports.editCode.errorCode"></spring:message></td>
		  		<td width="70%">
		  			<div class="input-control select" >
							<select name="errorCodeId">
								<optgroup label="Code      --- Name  ">	
									<c:forEach items="${errorCodes}" var="errorCodes">
											<option value="${errorCodes.errorCodeId}">${errorCodes.errorCodeId}.${errorCodes.errorCode}</option>
									</c:forEach>
								</optgroup>
							</select>
						</div>
		  		</td>
		  </tr>
		  <tr>
		  		<td width="30%"><spring:message code="reports.editCode.unitManager"></spring:message></td>
		  		<td width="70%">
		  			<div class="input-control select" >
							<select name="unitManagerName">
								<c:forEach items="${managers}" var="managers">
										<option value="${managers.managerId}">${managers.manager}</option>
								</c:forEach>
							</select>
						</div>
		  		</td>
		  		
		  </tr>
		 </table>
		  </div>
		  <div style="width:750px">
			    <div style="float:right">
    	<button type="reset" class="button inverse">Reset</button> <button type="submit" style="background-color:#298fd8" class="button default">Generate</button> 
   			</div>
			</div>
		  </form:form>
		 
		
		
			
		 
		
		  
			

	
	
	
	
	<script type="text/javascript">
				$("#datepick1").datepicker(
						{
							showOn : "button",
							onSelect : function(selected) {
								$("#datepick2").datepicker("option", "minDate",
										selected)
							},
						});
				$("#datepick2").datepicker(
						{
							showOn : "button",
							onSelect : function(selected) {
								$("#datepick1").datepicker("option", "maxDate",
										selected)
							},
						});

				jQuery(function($) {
					$("#datepick1").mask("99/99/9999", {
						placeholder : "MM/DD/YYYY"
					});
					$("#datepick2").mask("99/99/9999", {
						placeholder : "MM/DD/YYYY"
					});
				});
			</script>			
        
       
           
           
		</div>
	</div>

	<jsp:include page="footer.jsp"></jsp:include>

</body>

</html>
