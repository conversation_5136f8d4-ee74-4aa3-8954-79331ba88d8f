package com.carefirst.audit.model;

import java.util.List;

public class PerformanceGroup {
	
	private String grpid;
	private String groupId;
	private String groupName;
	private String grpSammd;
	private String grpCd;
	private String userGrp;
	private String type;
	private List<String> secPGAMappingsRO;
	private List<String> secPGAMappings;
	private String disable;
	private String disableFrom;
	private String disableTo;
	private String effectiveFrom;
	private String effectiveTo;
	private String proceduralAccuracyExp;
	private String proceduralMonthlyPenalty;
	private String dollarFrequencyExp;
	private String dollarFreqMonthlyPenalty;
	private String dollarAccuracyExp;
	private String dollarAccuracyMonthlyPenalty;
	private String userid;
	private String userActyp;
	private String successCode;
	private String successMsg;
	private String status;
	
	public List<String> getSecPGAMappingsRO() {
		return secPGAMappingsRO;
	}
	public void setSecPGAMappingsRO(List<String> secPGAMappingsRO) {
		this.secPGAMappingsRO = secPGAMappingsRO;
	}
	public List<String> getSecPGAMappings() {
		return secPGAMappings;
	}
	public void setSecPGAMappings(List<String> secPGAMappings) {
		this.secPGAMappings = secPGAMappings;
	}
	public String getUserGrp() {
		return userGrp;
	}
	public void setUserGrp(String userGrp) {
		this.userGrp = userGrp;
	}
	public String getGrpSammd() {
		return grpSammd;
	}
	public void setGrpSammd(String grpSammd) {
		this.grpSammd = grpSammd;
	}
	public String getGrpCd() {
		return grpCd;
	}
	public void setGrpCd(String grpCd) {
		this.grpCd = grpCd;
	}
	public String getStatus() {
		return status;
	}
	public void setStatus(String status) {
		this.status = status;
	}
	public String getSuccessCode() {
		return successCode;
	}
	public void setSuccessCode(String successCode) {
		this.successCode = successCode;
	}
	public String getSuccessMsg() {
		return successMsg;
	}
	public void setSuccessMsg(String successMsg) {
		this.successMsg = successMsg;
	}
	public String getGrpid() {
		return grpid;
	}
	public void setGrpid(String grpid) {
		this.grpid = grpid;
	}
	public String getUserid() {
		return userid;
	}
	public void setUserid(String userid) {
		this.userid = userid;
	}
	public String getUserActyp() {
		return userActyp;
	}
	public void setUserActyp(String userActyp) {
		this.userActyp = userActyp;
	}
	public String getGroupId() {
		return groupId;
	}
	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getDisable() {
		return disable;
	}
	public void setDisable(String disable) {
		this.disable = disable;
	}
	public String getDisableFrom() {
		return disableFrom;
	}
	public void setDisableFrom(String disableFrom) {
		this.disableFrom = disableFrom;
	}
	public String getDisableTo() {
		return disableTo;
	}
	public void setDisableTo(String disableTo) {
		this.disableTo = disableTo;
	}
	public String getEffectiveFrom() {
		return effectiveFrom;
	}
	public void setEffectiveFrom(String effectiveFrom) {
		this.effectiveFrom = effectiveFrom;
	}
	public String getEffectiveTo() {
		return effectiveTo;
	}
	public void setEffectiveTo(String effectiveTo) {
		this.effectiveTo = effectiveTo;
	}
	public String getProceduralAccuracyExp() {
		return proceduralAccuracyExp;
	}
	public void setProceduralAccuracyExp(String proceduralAccuracyExp) {
		this.proceduralAccuracyExp = proceduralAccuracyExp;
	}
	public String getProceduralMonthlyPenalty() {
		return proceduralMonthlyPenalty;
	}
	public void setProceduralMonthlyPenalty(String proceduralMonthlyPenalty) {
		this.proceduralMonthlyPenalty = proceduralMonthlyPenalty;
	}
	public String getDollarFrequencyExp() {
		return dollarFrequencyExp;
	}
	public void setDollarFrequencyExp(String dollarFrequencyExp) {
		this.dollarFrequencyExp = dollarFrequencyExp;
	}
	public String getDollarFreqMonthlyPenalty() {
		return dollarFreqMonthlyPenalty;
	}
	public void setDollarFreqMonthlyPenalty(String dollarFreqMonthlyPenalty) {
		this.dollarFreqMonthlyPenalty = dollarFreqMonthlyPenalty;
	}
	public String getDollarAccuracyExp() {
		return dollarAccuracyExp;
	}
	public void setDollarAccuracyExp(String dollarAccuracyExp) {
		this.dollarAccuracyExp = dollarAccuracyExp;
	}
	public String getDollarAccuracyMonthlyPenalty() {
		return dollarAccuracyMonthlyPenalty;
	}
	public void setDollarAccuracyMonthlyPenalty(String dollarAccuracyMonthlyPenalty) {
		this.dollarAccuracyMonthlyPenalty = dollarAccuracyMonthlyPenalty;
	}
	
	
	
	
}
