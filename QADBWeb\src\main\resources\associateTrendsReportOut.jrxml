<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="tree-template" pageWidth="595" pageHeight="842"  whenNoDataType="NoDataSection" columnWidth="535" >
	<property name="com.jasperassistant.designer.Grid" value="false"/>
	<property name="com.jasperassistant.designer.SnapToGrid" value="false"/>
	<property name="com.jasperassistant.designer.GridWidth" value="12"/>
	<property name="com.jasperassistant.designer.GridHeight" value="12"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	
	<field name="title" class="java.lang.String">
		<fieldDescription><![CDATA[title]]></fieldDescription>
	</field>
	<field name="timeframe" class="java.lang.String">
		<fieldDescription><![CDATA[timeframe]]></fieldDescription>
	</field>
	<field name="e2e" class="java.lang.String">
		<fieldDescription><![CDATA[e2e]]></fieldDescription>
	</field>
	<field name="associateName" class="java.lang.String">
		<fieldDescription><![CDATA[associateName]]></fieldDescription>
	</field>
	<field name="auditType" class="java.lang.String">
		<fieldDescription><![CDATA[auditType]]></fieldDescription>
	</field>
	<field name="subType" class="java.lang.String">
		<fieldDescription><![CDATA[subType]]></fieldDescription>
	</field>
	
	<field name="totalClaims" class="java.lang.String">
		<fieldDescription><![CDATA[totalClaims]]></fieldDescription>
	</field>
	<field name="totalErrClaims" class="java.lang.String">
		<fieldDescription><![CDATA[totalErrClaims]]></fieldDescription>
	</field>
	<field name="isTotalClaims" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalClaims]]></fieldDescription>
	</field>
	<field name="isTotalErrClaims" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalErrClaims]]></fieldDescription>
	</field>
	<field name="oosTotalClaims" class="java.lang.String">
		<fieldDescription><![CDATA[oosTotalClaims]]></fieldDescription>
	</field>
	<field name="oosTotalErrClaims" class="java.lang.String">
		<fieldDescription><![CDATA[oosTotalErrClaims]]></fieldDescription>
	</field>
	<field name="isErrorId" class="java.lang.String">
		<fieldDescription><![CDATA[isErrorId]]></fieldDescription>
	</field>
	<field name="isErrorName" class="java.lang.String">
		<fieldDescription><![CDATA[isErrorName]]></fieldDescription>
	</field>
	<field name="isErrorMonetary" class="java.lang.String">
		<fieldDescription><![CDATA[isErrorMonetary]]></fieldDescription>
	</field>
	<field name="isErrorProcedural" class="java.lang.String">
		<fieldDescription><![CDATA[isErrorProcedural]]></fieldDescription>
	</field>
	<field name="isTotalMonetary" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalMonetary]]></fieldDescription>
	</field>
	<field name="isTotalProcedural" class="java.lang.String">
		<fieldDescription><![CDATA[isTotalProcedural]]></fieldDescription>
	</field>
	<field name="oosTotalErrors" class="java.lang.String">
		<fieldDescription><![CDATA[oosTotalErrors]]></fieldDescription>
	</field>
	
	<title>
		<band height="35" splitType="Stretch">
			<line>
				<reportElement positionType="Float" x="1" y="0" width="555" height="2"  >
				</reportElement>
				<graphicElement>
					<pen lineStyle="Solid"/>
				</graphicElement>
			</line>
			<line>
				<reportElement positionType="Float" x="1" y="33" width="555" height="2"  >
				</reportElement>
				<graphicElement>
					<pen lineStyle="Solid"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement x="0" y="2" width="555" height="30"  >
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="20"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{title}+"'s Error Stroke Sheet for "+$F{timeframe}+" "+"("+$F{e2e}+")"]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<pageHeader>
		<band height="147" splitType="Stretch">
			<rectangle>
				<reportElement x="200" y="30" width="80" height="16" backcolor="#99CCFF"  >
				</reportElement>
			</rectangle>
			<textField>
				<reportElement x="81" y="5" width="299" height="21"  />
				<textElement>
					<font fontName="Haettenschweiler" size="16" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{associateName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Transparent" x="4" y="1" width="76" height="30"  />
				<box topPadding="4" leftPadding="8">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement>
					<font fontName="Haettenschweiler" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{title}+" : "]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="120" y="46" width="80" height="16" backcolor="#99CCFF"  >
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="120" y="62" width="80" height="16" backcolor="#99CCFF"  >
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="4" y="46" width="116" height="16" backcolor="#99CCFF"  >
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="4" y="62" width="116" height="16" backcolor="#99CCFF"  >
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="120" y="30" width="80" height="16" backcolor="#99CCFF"  >
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="290" y="30" width="50" height="16"  >
				</reportElement>
				<textElement>
					<font fontName="Times New Roman" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[In-Sample]]></text>
			</staticText>
			<rectangle>
				<reportElement x="200" y="62" width="80" height="16" backcolor="#99CCFF"  >
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="200" y="46" width="80" height="16" backcolor="#99CCFF"  >
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="278" y="30" width="80" height="16" backcolor="#99CCFF"  >
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="278" y="62" width="80" height="16" backcolor="#99CCFF"  >
				</reportElement>
			</rectangle>
			<rectangle>
				<reportElement x="278" y="46" width="80" height="16" backcolor="#99CCFF"  >
				</reportElement>
			</rectangle>
			<staticText>
				<reportElement x="4" y="87" width="76" height="30"  />
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="Haettenschweiler" size="12"/>
				</textElement>
				<text><![CDATA[ Error]]></text>
			</staticText>
			<staticText>
				<reportElement x="81" y="87" width="299" height="30"  />
				<box padding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement>
					<font fontName="Haettenschweiler" size="12"/>
				</textElement>
				<text><![CDATA[ Name]]></text>
			</staticText>
			<staticText>
				<reportElement x="380" y="87" width="175" height="15"  >
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12"/>
				</textElement>
				<text><![CDATA[Number of Times Error Occured]]></text>
			</staticText>
			<staticText>
				<reportElement x="380" y="102" width="80" height="15"  >
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12"/>
				</textElement>
				<text><![CDATA[Monetary]]></text>
			</staticText>
			<staticText>
				<reportElement x="460" y="102" width="95" height="15"  >
				</reportElement>
				<box>
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12"/>
				</textElement>
				<text><![CDATA[Procedural]]></text>
			</staticText>
			<line>
				<reportElement positionType="Float" x="1" y="26" width="555" height="1"  >
				</reportElement>
				<graphicElement>
					<pen lineStyle="Solid"/>
				</graphicElement>
			</line>
			<rectangle>
				<reportElement x="4" y="122" width="175" height="18" forecolor="#0A0909" backcolor="#0A0A0A"  />
			</rectangle>
			<staticText>
				<reportElement x="0" y="123" width="179" height="24" forecolor="#FFFFFF" backcolor="#2DE339"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[ OUT-OF-SAMPLE ERRORS]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="288" y="65" width="54" height="14" forecolor="#050505" backcolor="#FFFFFF"  >
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{totalErrClaims}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="288" y="49" width="54" height="14" forecolor="#050505" backcolor="#FFFFFF"  >
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{totalClaims}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="290" y="31" width="50" height="16"  >
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Times New Roman" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Total]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="210" y="66" width="54" height="14" forecolor="#050505" backcolor="#FFFFFF"  >
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosTotalErrClaims}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="210" y="50" width="54" height="14" forecolor="#050505" backcolor="#FFFFFF"  >
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosTotalClaims}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="206" y="31" width="70" height="16"  >
				</reportElement>
				<textElement>
					<font fontName="Times New Roman" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Out-Of-Sample]]></text>
			</staticText>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="130" y="65" width="54" height="14" forecolor="#050505" backcolor="#FFFFFF"  >
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isTotalErrClaims}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="130" y="49" width="54" height="14" forecolor="#050505" backcolor="#FFFFFF"  >
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0"/>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isTotalClaims}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="135" y="31" width="50" height="16"  >
				</reportElement>
				<textElement>
					<font fontName="Times New Roman" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[In-Sample]]></text>
			</staticText>
			<staticText>
				<reportElement x="7" y="63" width="113" height="16"  >
				</reportElement>
				<textElement>
					<font fontName="Times New Roman" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Total Claims with Errors]]></text>
			</staticText>
			<staticText>
				<reportElement x="7" y="48" width="98" height="16"  >
				</reportElement>
				<textElement>
					<font fontName="Times New Roman" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Total Claims Audited]]></text>
			</staticText>
			<staticText>
				<reportElement x="383" y="32" width="75" height="14" forecolor="#000080"  />
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Audit Type :]]></text>
			</staticText>
			<textField>
				<reportElement x="442" y="33" width="118" height="13"  />
				<textElement>
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{auditType}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="383" y="49" width="75" height="14" forecolor="#000080"  />
				<textElement>
					<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
				</textElement>
				<text><![CDATA[Sub Type :]]></text>
			</staticText>
			<textField>
				<reportElement x="435" y="50" width="120" height="13"  />
				<textElement>
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{subType}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="15" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="3" y="1" width="77" height="14" forecolor="#030303" backcolor="#FFFFFF"  >
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[" "+$F{isErrorId}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="80" y="1" width="300" height="14" forecolor="#030303" backcolor="#FFFFFF"  >
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[" "+$F{isErrorName}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="380" y="1" width="80" height="14" forecolor="#030303" backcolor="#FFFFFF"  >
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isErrorMonetary}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Transparent" x="460" y="1" width="95" height="14" forecolor="#030303" backcolor="#FFFFFF"  >
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isErrorProcedural}]]></textFieldExpression>
			</textField>
		</band>
		
	</detail>
	<columnFooter>
		
	</columnFooter>
	<pageFooter>
		
	</pageFooter>
		
	<summary>
		<band height="15" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement mode="Opaque" x="3" y="0" width="377" height="14" forecolor="#030303" backcolor="#C0C0C0"  >
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Haettenschweiler" size="12" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[" TOTAL OUT-OF-SAMPLE ERRORS = "+$F{oosTotalErrors}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Opaque" x="380" y="0" width="80" height="14" forecolor="#030303" backcolor="#C0C0C0"  >
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isTotalMonetary}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement mode="Opaque" x="460" y="0" width="95" height="14" forecolor="#030303" backcolor="#C0C0C0"  >
				</reportElement>
				<box padding="0" topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#030303"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{isTotalProcedural}]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
