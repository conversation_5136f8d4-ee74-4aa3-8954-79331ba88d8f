package com.carefirst.qadb.constant;

public class QADBConstants {

    public static final String FACETS_SERVICE_ENDPOINT = "http://dvetib2:12721/ClaimsQASampling/BusinessProcesses/Common/ClaimsQASampling.serviceagent/QASamplingPortEndpoint1";
    
    //User authorization constants
    public static final String READ_ONLY_ROLE = "RO"; 
    public static final String AUDIT_ROLE = "AUD";
    public static final String ADMIN_ROLE = "ADM";
    public static final String SUPER_ADMIN_ROLE = "SADM";
    
    //Admin Error
    public static final String USER_ACTION_NEW = "NEW";
    public static final String DEFAULT_FLAG_YES = "Y";
    public static final String SUCCESS_MESSAGE_ATTRIBUTE = "success";
    public static final String USER_ACTION_SEARCH = "SEARCH";
    public static final String USER_ACTION_BASIC = "BASIC";
    public static final String USER_ACTION_EDIT = "EDIT";
    public static final String EDIT_ASSOCIATE="EDIT_ASSOCIATE";
    public static final String USERNAME="username";
    public static final String PARAM_ASSOCIATE_ID="paramAssociateId";
    public static final String ASSOCIATE_RES="associateRO";
    public static final String UPDATE_SUCCESS="upSucess";
    public static final String DELETE_SUCCESS="delSucess";
    public static final String EDIT_OP_UNIT="EDIT_OP_UNIT";
    public static final String OP_PAGE_TYPE="ASSCT";
    public static final String OP_GNRL="GNRL";
    public static final String ERROR_CODES_RES="errorCodesRO";
    public static final String EDIT_ERR_CODE="EDIT_ERR_CD";
    public static final String EDIT_PERF_GRP="EDIT_PERF_GRP";
    public static final String ROOT_CAUSE_RES="rootCauseRO";
    public static final String EDIT_RT_CAUSE="EDIT_RT_CAUSE";
    public static final String EDIT_JOB_TITLE="EDIT_JOB_TITLE";
    public static final String EDIT_SPECIALTY="EDIT_SPECIALTY";
    public static final String MAPPING_RES="mappingRO";
    public static final String QADBWEBAPP_LOGGER ="QADBWebAppLogger";
    public static final String SUCCESS_CODE ="successCode";
    public static final String SUCCESS_MESSAGE ="successMessage";
    
    //Admin Queries
    public static final String ADMIN_ASSOCIATE_QUERIES_JOB_TITLES_LIST ="SELECT JOB_TITLE_ID,JOB_TITLE_NAME FROM QADB_PRD.TBL_JOB_TITLE WHERE JOB_TITLE_ACTIVE_FLG = 'Y' ORDER BY JOB_TITLE_ID ";
    public static final String ADMIN_ASSOCIATE_QUERIES_WORK_UNITS_LIST ="SELECT  TOU.UNIT_ID, TOU.UNIT_NAME, TA.ASSOCIATE_LAST_NAME|| ', ' || TA.ASSOCIATE_FIRST_NAME  AS UNIT_SUPERVISOR FROM QADB_PRD.TBL_OPERATION_UNIT TOU INNER JOIN QADB_PRD.TBL_ASSOCIATES TA ON TOU.UNIT_SUPERVISOR = TA.ASSOCIATE_ID WHERE UNIT_ACTIVE_FLG = 'Y' ORDER BY UNIT_ID ";
    public static final String ADMIN_ASSOCIATE_QUERIES_LOCATIONS_LIST ="SELECT LOCATION_ID,LOCATION_DESC from QADB_PRD.TBL_LKUP_LOCATION WHERE LOCATION_ACTIVE_INDC = 'Y' AND LOCATION_DESC IS NOT NULL ORDER BY LOCATION_DESC ";
    public static final String ADMIN_ASSOCIATE_DELETE = " UPDATE  QADB_PRD.TBL_ASSOCIATES SET  ASSOCIATE_ACTIVE_FLG  = 'N' WHERE ASSOCIATE_ID =  " ;
    public static final String ADMIN_OP_LIST ="SELECT OPERATION_ID,OPERATION_DESC FROM QADB_PRD.TBL_LKUP_OPERATION WHERE OPERATION_ACTIVE_INDC = 'Y' AND OPERATION_DESC IS NOT NULL ORDER BY OPERATION_DESC ";
    public static final String ADMIN_OP_DIVISION_LIST ="SELECT DIVISION_ID,DIVISION_DESC FROM QADB_PRD.TBL_LKUP_DIVISION WHERE DIVISION_ACTIVE_INDC = 'Y' AND DIVISION_DESC IS NOT NULL ORDER BY DIVISION_DESC";
    public static final String ADMIN_OP_SUPERVISORS_LIST ="SELECT ASSOCIATE_ID,ASSOCIATE_FIRST_NAME,ASSOCIATE_LAST_NAME FROM QADB_PRD.TBL_ASSOCIATES WHERE JOB_TITLE_ID = 2 AND ASSOCIATE_ACTIVE_FLG = 'Y' AND (SYSDATE BETWEEN ASSOCIATE_WORK_START_DT AND ASSOCIATE_WORK_END_DT) AND NVL(ASSOCIATE_DISABLE_DT,TO_DATE('31-DEC-99','DD-MON-YY')) >= SYSDATE ORDER BY ASSOCIATE_LAST_NAME ";
    public static final String ADMIN_OP_MANAGERS_LIST ="SELECT ASSOCIATE_ID,ASSOCIATE_FIRST_NAME,ASSOCIATE_LAST_NAME FROM QADB_PRD.TBL_ASSOCIATES WHERE JOB_TITLE_ID = 3 AND ASSOCIATE_ACTIVE_FLG = 'Y' AND (SYSDATE BETWEEN ASSOCIATE_WORK_START_DT AND ASSOCIATE_WORK_END_DT) AND NVL(ASSOCIATE_DISABLE_DT,TO_DATE('31-DEC-99','DD-MON-YY')) >= SYSDATE ORDER BY ASSOCIATE_LAST_NAME ";
    public static final String ADMIN_OP_DIRECTORS_LIST ="SELECT ASSOCIATE_ID,ASSOCIATE_FIRST_NAME,ASSOCIATE_LAST_NAME FROM QADB_PRD.TBL_ASSOCIATES WHERE JOB_TITLE_ID = 4 AND ASSOCIATE_ACTIVE_FLG = 'Y' AND (SYSDATE BETWEEN ASSOCIATE_WORK_START_DT AND ASSOCIATE_WORK_END_DT) AND NVL(ASSOCIATE_DISABLE_DT,TO_DATE('31-DEC-99','DD-MON-YY')) >= SYSDATE ORDER BY ASSOCIATE_LAST_NAME ";
    public static final String ADMIN_OP_DELETE="UPDATE  QADB_PRD.TBL_OPERATION_UNIT SET  UNIT_ACTIVE_FLG  = 'N' WHERE UNIT_ID = ";
    
    //SP
    public static final String ADMIN_ASSOCIATE_SAVE_SP ="{call QADB_PRD.PKG_ADMIN.PROC_NEW_ASSCT(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    public static final String ADMIN_ASSOCIATE_SEARCH_SP="{call QADB_PRD.PKG_ADMIN.PROC_SRCH_ASSCT(?,?,?,?,?,?,?,?)}";
    public static final String ADMIN_ERROR_SAVE_SP="{call QADB_PRD.PKG_ADMIN.PROC_NEW_ERR_CD(?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    public static final String ADMIN_ERROR_SEARCH_SP="{call QADB_PRD.PKG_ADMIN.PROC_SRCH_ERR_CD(?,?,?,?,?,?,?,?,?,?)}";
    public static final String ADMIN_JOB_TITLE_SAVE_SP="{call QADB_PRD.PKG_ADMIN.PROC_MANAGE_JOB_TITLES(?,?,?,?,?,?,?,?)}";
    public static final String ADMIN_JOB_TITLE_SEARCH_SP="{call QADB_PRD.PKG_ADMIN.PROC_JOB_TITLES(?,?,?,?,?,?)}";
    public static final String ADMIN_OP_SAVE_SP="{call QADB_PRD.PKG_ADMIN.PROC_NEW_OP_UNIT(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    public static final String ADMIN_OP_SEARCH_SP="{call QADB_PRD.PKG_ADMIN.PROC_SRCH_OP_UNIT(?,?,?,?,?,?,?,?,?,?)}";
    public static final String ADMIN_PERF_GROUP_SAVE_SP="{call QADB_PRD.PKG_ADMIN.PROC_NEW_PERF_GRP(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    public static final String ADMIN_PERF_GROUP_SEARCH_SP="{call QADB_PRD.PKG_ADMIN.PROC_SRCH_PERF_GRP(?,?,?,?,?,?,?,?,?,?,?,?)}";
    public static final String ADMIN_ROOT_CAUSE_SAVE_SP="{call QADB_PRD.PKG_ADMIN.PROC_NEW_RT_CAUSE(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    public static final String ADMIN_ROOT_CAUSE_SEARCH_SP="{call QADB_PRD.PKG_ADMIN.PROC_SRCH_RT_CAUSE(?,?,?,?,?,?,?,?,?,?)}";
    public static final String ADMIN_SPECIALTY_SEARCH_SP="{call QADB_PRD.PKG_ADMIN.PROC_SPCLTY(?,?,?,?,?,?,?)}";
    public static final String ADMIN_SPECIALTY_SAVE_SP="{call QADB_PRD.PKG_ADMIN.PROC_MANAGE_SPCLTY(?,?,?,?,?,?,?,?,?)}";
    public static final String ADMIN_MAPPING_SP="{call QADB_PRD.PKG_ADMIN.PROC_AUDTR_ASSCT_MAPPING(?,?,?,?,?,?,?,?,?,?,?)}";

    //Audit
    public static final String AUDIT_AUDITORS_LIST1 ="SELECT DISTINCT INITCAP(TAS.USER_ID) AS USER_ID FROM QADB_PRD.TBL_AUDIT_DETAILS TAD "
                  + " INNER JOIN QADB_PRD.TBL_AUDIT_STATISTICS TAS ON  TAD.AUDIT_ID = TAS.AUDIT_ID "
                  + " INNER JOIN QADB_PRD.TBL_AUDIT_CLAIM_DETAILS TACD ON  TAS.AUDIT_ID = TACD.AUDIT_ID "
                  + " WHERE TAD.AUDIT_DELETE_FLG = 'N' AND (UPPER(TACD.GROUP_CODE) IN ";
    public static final String AUDIT_AUDITORS_LIST2 =" OR TACD.GROUP_CODE IS NULL) ORDER BY INITCAP(TAS.USER_ID) ";
    public static final String AUDIT_CLAIM_TYPES_LIST ="SELECT CLAIM_TYPE_ID,CLAIM_TYPE_DESC FROM QADB_PRD.TBL_LKUP_CLAIM_TYPE ORDER BY CLAIM_TYPE_ID ";
    public static final String AUDIT_PENALTY_INTEREST_TYPES_LIST ="SELECT PI_TYPE_ID,PI_TYPE_DESC FROM QADB_PRD.TBL_LKUP_PI_TYPE ";
    public static final String AUDIT_AUDIT_TYPES_LIST ="SELECT AUDIT_TYPE_ID,AUDIT_TYPE_DESC FROM QADB_PRD.TBL_LKUP_AUDIT_TYPE ";
    public static final String AUDIT_PRIMARY_PGA_LIST1 ="SELECT PERF_GROUP_ID,PERF_GROUP_NAME,PERF_GROUP_USER_GROUP "
                  + " FROM QADB_PRD.TBL_PERF_GROUP WHERE (SYSDATE BETWEEN PERF_GROUP_EFFECTIVE_START_DT AND PERF_GROUP_EFFECTIVE_END_DT) "
                  + " AND (SYSDATE NOT BETWEEN NVL(PERF_GROUP_DISABLE_START_DT,'01-JAN-01') AND NVL(PERF_GROUP_DISABLE_END_DT,'01-JAN-01')) "
                  + " AND PERF_GROUP_TYP = 'P'AND (UPPER(PERF_GROUP_USER_GROUP) IN ";
    public static final String AUDIT_PRIMARY_PGA_LIST2 =" OR PERF_GROUP_USER_GROUP IS NULL) ORDER BY PERF_GROUP_NAME ";
    
    public static final String AUDIT_SEC_PGA_LIST1 ="SELECT PERF_GROUP_ID,PERF_GROUP_NAME,PERF_GROUP_USER_GROUP "
                  + " FROM QADB_PRD.TBL_PERF_GROUP WHERE PERF_GROUP_MAPPING = ";
    public static final String AUDIT_SEC_PGA_LIST2 = " AND PERF_GROUP_ACTIVE_FLG = 'Y' AND (SYSDATE BETWEEN PERF_GROUP_EFFECTIVE_START_DT AND PERF_GROUP_EFFECTIVE_END_DT) "
                  + " AND (SYSDATE NOT BETWEEN NVL(PERF_GROUP_DISABLE_START_DT,'01-JAN-01') AND NVL(PERF_GROUP_DISABLE_END_DT,'01-JAN-01')) "
                  + " AND PERF_GROUP_TYP = 'S'AND (UPPER(PERF_GROUP_USER_GROUP) IN ";
    public static final String AUDIT_SEC_PGA_LIST3 =" OR PERF_GROUP_USER_GROUP IS NULL) ORDER BY PERF_GROUP_ID ";
    
    public static final String REPORTS_SEC_PGA_LIST1 ="SELECT PERF_GROUP_ID,PERF_GROUP_NAME,PERF_GROUP_USER_GROUP "
                  + " FROM QADB_PRD.TBL_PERF_GROUP WHERE (SYSDATE BETWEEN PERF_GROUP_EFFECTIVE_START_DT AND PERF_GROUP_EFFECTIVE_END_DT) "
                  + " AND (SYSDATE NOT BETWEEN NVL(PERF_GROUP_DISABLE_START_DT,'01-JAN-01') AND NVL(PERF_GROUP_DISABLE_END_DT,'01-JAN-01')) "
                  + " AND PERF_GROUP_TYP = 'S'AND (UPPER(PERF_GROUP_USER_GROUP) IN ";
    public static final String REPORTS_SEC_PGA_LIST2 =" OR PERF_GROUP_USER_GROUP IS NULL) ORDER BY PERF_GROUP_NAME ";
    
    public static final String AUDIT_PROCESS_TYPE_LIST ="SELECT PROCESS_TYPE_ID,PROCESS_TYPE_DESC FROM QADB_PRD.TBL_LKUP_PROCESS_TYPE WHERE PROCESS_TYPE_ACTIVE_INDC = 'Y'";
    public static final String AUDIT_ERROR_CODES_LIST1="SELECT ERROR_ID,ERROR_DESC,ERROR_USER_GROUP,ERROR_ACTIVE_FLG FROM QADB_PRD.TBL_LKUP_ERRORS "
                  + " WHERE (SYSDATE BETWEEN ERROR_EFFECTIVE_START_DT AND ERROR_EFFECTIVE_END_DT) "
                  + " AND ERROR_ACTIVE_FLG = 'Y' AND (ERROR_USER_GROUP IN ";
    public static final String AUDIT_ERROR_CODES_LIST2=" OR ERROR_USER_GROUP IS NULL) ORDER BY ERROR_ID";
    
    public static final String AUDIT_EDIT_ERROR_CODES_LIST1="SELECT ERROR_ID,ERROR_DESC,ERROR_USER_GROUP,ERROR_ACTIVE_FLG FROM QADB_PRD.TBL_LKUP_ERRORS "
                  + " WHERE  (ERROR_USER_GROUP IN ";
    public static final String AUDIT_EDIT_ERROR_CODES_LIST2=" OR ERROR_USER_GROUP IS NULL) ORDER BY ERROR_ID";
    
    public static final String AUDIT_SPECIALTY_LIST1="SELECT SPECIALTY_ID,SPECIALTY_NAME,SPECIALTY_USER_GROUP "
                  + " FROM QADB_PRD.TBL_SPECIALTY WHERE SPECIALTY_ACTIVE_FLG = 'Y' AND (SPECIALTY_USER_GROUP IN ";
    public static final String AUDIT_SPECIALTY_LIST2=" OR SPECIALTY_USER_GROUP IS NULL) ORDER BY SPECIALTY_NAME";
    
    public static final String AUDIT_ROOT_CAUSE_LIST1="SELECT ROOT_CAUSE_ID,ROOT_CAUSE_NAME,ROOT_CAUSE_USER_GROUP FROM QADB_PRD.TBL_ROOT_CAUSE "
                  + " WHERE ROOT_CAUSE_ACTIVE_FLG = 'Y' "
                  + " AND (SYSDATE BETWEEN ROOT_CAUSE_EFFECTIVE_START_DT AND ROOT_CAUSE_EFFECTIVE_END_DT) "
                  + " AND (SYSDATE NOT BETWEEN NVL(ROOT_CAUSE_DISABLE_START_DT,'01-JAN-01') AND NVL(ROOT_CAUSE_DISABLE_END_DT,'01-JAN-01')) "
                  + " AND (ROOT_CAUSE_USER_GROUP IN ";
    public static final String AUDIT_ROOT_CAUSE_LIST2= " OR ROOT_CAUSE_USER_GROUP IS NULL) ORDER BY ROOT_CAUSE_NAME ";
    
    public static final String AUDIT_ALL_ERROR_TYPES_LIST="SELECT ERROR_TYPE_DESC FROM QADB_PRD.TBL_LKUP_ERROR_TYPE WHERE  ERROR_TYPE_ACTIVE_INDC = 'Y' AND ERROR_TYPE_ID != 17 ORDER BY ERROR_TYPE_DESC ";   
    public static final String AUDIT_ERROR_TYPES_LIST1 ="SELECT RTC.ROOT_CAUSE_ID, RTC.ROOT_CAUSE_NAME, ERR.ERROR_TYPE_ID, ERR.ERROR_TYPE_DESC FROM QADB_PRD.TBL_LKUP_ERROR_TYPE ERR INNER JOIN QADB_PRD.TBL_ROOT_CAUSE RTC ON UPPER(ERR.ERROR_TYPE_DESC) = UPPER(RTC.ROOT_CAUSE_RESPONSIBLE) WHERE RTC.ROOT_CAUSE_ACTIVE_FLG   = 'Y' AND   ERR.ERROR_TYPE_ACTIVE_INDC  = 'Y' AND   RTC.ROOT_CAUSE_ID           =  ";
    public static final String AUDIT_ERROR_INFO_LIST="SELECT ERROR_ID,MONETARY_FLG,PROCEDURAL_FLG,ROOT_CAUSE_ID,SPECIALTY_ID,EDIT_CODE FROM QADB_PRD.TBL_AUDIT_ERRORS where AUDIT_ID =  ";
    public static final String AUD_DEL_QUERY="UPDATE  QADB_PRD.TBL_AUDIT_DETAILS ";
    public static final String IN_SAMPLE_COUNT_ONE="SELECT COUNT(AUDIT_ID) FROM QADB_PRD.TBL_AUDIT_DETAILS WHERE ASSOCIATE_ID = ";
    public static final String IN_SAMPLE_COUNT_TWO=" AND OOS_FLG = 'N' AND TO_CHAR(USER_INSRT_TMSTP,'MON') = TO_CHAR(SYSDATE,'MON')";
    
    //SP
    public static final String AUDIT_SAVE_SP ="{call QADB_PRD.PKG_AUDIT.PROC_NEW_AUDIT(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    public static final String AUDIT_ASSOCIATE_DETAILS_SP="{call QADB_PRD.PROC_ASSOCIATE_DETAILS(?,?,?,?,?,?)}";
    public static final String AUDIT_RECENT_SP="{call QADB_PRD.PROC_RECENT_AUDITS(?,?,?,?,?)}";
    public static final String AUDIT_SEARCH_SP="{call QADB_PRD.PKG_AUDIT.PROC_SRCH_AUDIT(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    public static final String AUDIT_COUNT_SP="{call QADB_PRD.PROC_AUDIT_COUNTS(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    public static final String AUDIT_ASSESMENT_SP="{call QADB_PRD.PKG_REPORTS.PROC_AUDIT_REPORTS(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    
    public static final String[] SPCLCHARLATINARR = {"?","?","?","%26nbsp"};
           
 

  //PROD
    public static final String QA_CLAIMS_WSDL="http://prod.gwservices.carefirst.com/facets/claimsqasampling/v1/?wsdl";//This URL is API GAteway URL for EAPM Migration.
    public static final String QA_CLAIMS_END_POINT="http://prod.gwservices.carefirst.com/facets/claimsqasampling/v1/";//This URL is API GAteway URL for EAPM Migration.
    public static final String VAULT_QUERY ="appid:QADB,safe:A_QADB_PROD_ISAMLDAP,username:DZLFD2EBLK";//vault query for EAPM Migration 
      
    //Reports
    
    //Score & Trends
    //SP
    public static final String REPORTS_SCORES_SP="{call QADB_PRD.PKG_REPORTS.PROC_SCORES_REPORTS(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    public static final String REPORTS_TRENDS_SP="{call QADB_PRD.PKG_REPORTS.PROC_TRENDS_REPORTS(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    public static final String REPORTS_MONETORY_ERROR_SHEET_SP = "{call QADB_PRD.PKG_REPORTS.PROC_MONETARY_POSTPAY_REPORTS(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    //Queries
    public static final String REPORTS_SCORES_TRENDS_CLAIM_PROCESSORS_LIST1 =" SELECT ASSOCIATE_ID,ASSOCIATE_FIRST_NAME,ASSOCIATE_LAST_NAME,ASSOCIATE_FACETS_ID FROM QADB_PRD.TBL_ASSOCIATES WHERE JOB_TITLE_ID = ";
    public static final String REPORTS_SCORES_TRENDS_CLAIM_PROCESSORS_LIST2 =" AND ASSOCIATE_ACTIVE_FLG = 'Y' AND (SYSDATE BETWEEN ASSOCIATE_WORK_START_DT AND ASSOCIATE_WORK_END_DT) AND NVL(ASSOCIATE_DISABLE_DT,TO_DATE('31-DEC-99','DD-MON-YY')) >= SYSDATE ORDER BY UPPER(ASSOCIATE_LAST_NAME)  ";
    public static final String REPORTS_FACETS_IDS_LIST =" SELECT ASSOCIATE_FACETS_ID FROM QADB_PRD.TBL_ASSOCIATES WHERE JOB_TITLE_ID = 1 "
                  + " AND ASSOCIATE_ACTIVE_FLG = 'Y' AND (SYSDATE BETWEEN ASSOCIATE_WORK_START_DT AND ASSOCIATE_WORK_END_DT) AND NVL(ASSOCIATE_DISABLE_DT,TO_DATE('31-DEC-99','DD-MON-YY')) >= SYSDATE ORDER BY UPPER(ASSOCIATE_FACETS_ID) ";
    public static final String REPORTS_SCORES_TRENDS_DIVISION =" SELECT DIVISION_ID,DIVISION_DESC FROM QADB_PRD.TBL_LKUP_DIVISION WHERE DIVISION_ACTIVE_INDC = 'Y' AND DIVISION_DESC IS NOT NULL ORDER BY DIVISION_DESC ";
    
    //User Reports
    //SP
    public static final String REPORTS_OTHER_REPORT_SP="{call QADB_PRD.PKG_REPORTS.PROC_USER_REPORTS(?,?,?,?,?,?)}";
    public static final String REPORTS_ADJUSTMENTS_REQUIRED_REPORT_SP="{call QADB_PRD.PKG_REPORTS.PROC_ADJUSTMNT_REQD_REPORT(?,?,?,?,?,?,?,?,?)}";
    
    //PG Error Sheet
    public static final String REPORTS_PG_ERR_SHEET_SP="{call QADB_PRD.PKG_REPORTS.PROC_PG_ERR_SHEET(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";
    
    //Claims Audit Assessment Report
    public static final String CLAIMS_AUDIT_ASSESMENT_REPORT_SP="{call QADB_PRD.PKG_REPORTS.PROC_AUDIT_ASSESSMENT_REPORT(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)}";

    //Edit code Report
    public static final String REPORTS_EDIT_CODE_REPORT_SP="{call QADB_PRD.PKG_REPORTS.PROC_EDIT_CODE_REPORT(?,?,?,?,?,?,?,?,?,?)}";
    
    //In Sample Out Of Sample Report
    public static final String REPORTS_IS_OOS_REPORT_SP="{call QADB_PRD.PKG_REPORTS.PROC_INOUT_SAMPLE_REPORT(?,?,?,?,?,?,?,?,?,?,?,?)}";

}
