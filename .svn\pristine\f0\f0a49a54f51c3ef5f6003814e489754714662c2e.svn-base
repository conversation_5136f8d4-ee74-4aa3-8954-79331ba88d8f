<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="scriptlet" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802"  whenResourceMissingType="Empty" isSummaryWithPageHeaderAndFooter="true" isSummaryNewPage="true">
	<property name="com.jasperassistant.designer.Grid" value="false"/>
	<property name="com.jasperassistant.designer.SnapToGrid" value="false"/>
	<property name="com.jasperassistant.designer.GridWidth" value="12"/>
	<property name="com.jasperassistant.designer.GridHeight" value="12"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="net.sf.jasperreports.awt.ignore.missing.font" value="true"/>
	<property name="net.sf.jasperreports.export.pdf.force.linebreak.policy" value="true" />
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="month" class="java.lang.String">
		<fieldDescription><![CDATA[month]]></fieldDescription>
	</field>
	<field name="associateName" class="java.lang.String">
		<fieldDescription><![CDATA[associateName]]></fieldDescription>
	</field>
	<field name="empNO" class="java.lang.String">
		<fieldDescription><![CDATA[empNO]]></fieldDescription>
	</field>
	<field name="supervisor" class="java.lang.String">
		<fieldDescription><![CDATA[supervisor]]></fieldDescription>
	</field>
	
	<field name="oosSno" class="java.lang.String">
		<fieldDescription><![CDATA[oosSno]]></fieldDescription>
	</field>
	<field name="oosDcn" class="java.lang.String">
		<fieldDescription><![CDATA[oosDcn]]></fieldDescription>
	</field>
	<field name="oosMemId" class="java.lang.String">
		<fieldDescription><![CDATA[oosMemId]]></fieldDescription>
	</field>
	<field name="oosPaid" class="java.lang.String">
		<fieldDescription><![CDATA[oosPaid]]></fieldDescription>
	</field>
	<field name="oosUnderPaid" class="java.lang.String">
		<fieldDescription><![CDATA[oosUnderPaid]]></fieldDescription>
	</field>
	<field name="oosOverPaid" class="java.lang.String">
		<fieldDescription><![CDATA[oosOverPaid]]></fieldDescription>
	</field>
	<field name="oosHighDoller" class="java.lang.String">
		<fieldDescription><![CDATA[oosHighDoller]]></fieldDescription>
	</field>
	<field name="oosAuditType" class="java.lang.String">
		<fieldDescription><![CDATA[oosAuditType]]></fieldDescription>
	</field>
	<field name="oosMockAudit" class="java.lang.String">
		<fieldDescription><![CDATA[oosMockAudit]]></fieldDescription>
	</field>
	<field name="reportType" class="java.lang.String">
		<fieldDescription><![CDATA[reportType]]></fieldDescription>
	</field>
	<field name="oosAdjRequired" class="java.lang.String">
		<fieldDescription><![CDATA[oosAdjRequired]]></fieldDescription>
	</field>
	<field name="oosProcessOn" class="java.lang.String">
		<fieldDescription><![CDATA[oosProcessOn]]></fieldDescription>
	</field>
	<field name="oosErrorCode" class="java.lang.String">
		<fieldDescription><![CDATA[oosErrorCode]]></fieldDescription>
	</field>
	<field name="oosMonetary" class="java.lang.String">
		<fieldDescription><![CDATA[oosMonetary]]></fieldDescription>
	</field>
	<field name="oosProcedural" class="java.lang.String">
		<fieldDescription><![CDATA[oosProcedural]]></fieldDescription>
	</field>
	<field name="oosErrorType" class="java.lang.String">
		<fieldDescription><![CDATA[oosErrorType]]></fieldDescription>
	</field>
	<field name="oosExplanation" class="java.lang.String">
		<fieldDescription><![CDATA[oosExplanation]]></fieldDescription>
	</field>
	<field name="oosFyi" class="java.lang.String">
		<fieldDescription><![CDATA[oosFyi]]></fieldDescription>
	</field>
	<field name="oosSop" class="java.lang.String">
		<fieldDescription><![CDATA[oosSop]]></fieldDescription>
	</field>
	<field name="oosPriPerfGroup" class="java.lang.String">
		<fieldDescription><![CDATA[oosPriPerfGroup]]></fieldDescription>
	</field>
	<field name="oosSecPerfGroup" class="java.lang.String">
		<fieldDescription><![CDATA[oosSecPerfGroup]]></fieldDescription>
	</field>
	<field name="oosAuditor" class="java.lang.String">
		<fieldDescription><![CDATA[oosAuditor]]></fieldDescription>
	</field>
	<field name="oosAuditDate" class="java.lang.String">
		<fieldDescription><![CDATA[oosAuditDate]]></fieldDescription>
	</field>
	
	<field name="oosTotalClaims" class="java.lang.String">
		<fieldDescription><![CDATA[oosTotalClaims]]></fieldDescription>
	</field>
	<field name="oosProcClaims" class="java.lang.String">
		<fieldDescription><![CDATA[oosProcClaims]]></fieldDescription>
	</field>
	<field name="oosMonClaims" class="java.lang.String">
		<fieldDescription><![CDATA[oosMonClaims]]></fieldDescription>
	</field>
	<field name="oosTotalPaid" class="java.lang.String">
		<fieldDescription><![CDATA[oosTotalPaid]]></fieldDescription>
	</field>
	<field name="oosTotalOvPaid" class="java.lang.String">
		<fieldDescription><![CDATA[oosTotalOvPaid]]></fieldDescription>
	</field>
	<field name="oosTotalUnPaid" class="java.lang.String">
		<fieldDescription><![CDATA[oosTotalUnPaid]]></fieldDescription>
	</field>
	
	<group name="dummy"> 		
	<groupExpression><![CDATA["dummy"]]></groupExpression> 
		<groupHeader>
			
		</groupHeader>
	</group>
	
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band height="154" splitType="Stretch">
			<rectangle>
				<reportElement x="4" y="37" width="326" height="18" forecolor="#0A0909" backcolor="#0A0A0A"  />
			</rectangle>
			<staticText>
				<reportElement x="41" y="37" width="220" height="24" forecolor="#FFFFFF" backcolor="#2DE339"  />
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[OUT-OF-SAMPLE AUDIT]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="4" y="63" width="88" height="30"  />
				<box topPadding="4" leftPadding="8">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement>
					<font fontName="SansSerif" size="14"/>
				</textElement>
				<text><![CDATA[Associate : ]]></text>
			</staticText>
			<staticText>
				<reportElement x="311" y="80" width="75" height="14"  />
				<textElement>
					<font fontName="Haettenschweiler" size="12" />
				</textElement>
				<text><![CDATA[Supervisor   :]]></text>
			</staticText>
			<rectangle>
				<reportElement x="1" y="100" width="800" height="4" backcolor="#080707"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="117" y="118" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[High Dollar]]></text>
			</staticText>
			<staticText>
				<reportElement x="-12" y="118" width="131" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Membership #]]></text>
			</staticText>
			<staticText>
				<reportElement x="169" y="134" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Out-Of-Sample]]></text>
			</staticText>
			<staticText>
				<reportElement x="342" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Monetary]]></text>
			</staticText>
			<staticText>
				<reportElement x="392" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Procedural]]></text>
			</staticText>
			<staticText>
				<reportElement x="228" y="134" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Processed On]]></text>
			</staticText>
			<staticText>
				<reportElement x="67" y="134" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Under Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="66" y="118" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Over Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="169" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Audit Type]]></text>
			</staticText>
			<staticText>
				<reportElement x="169" y="118" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Mock Audit]]></text>
			</staticText>
			<staticText>
				<reportElement x="279" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Error]]></text>
			</staticText>
			<staticText>
				<reportElement x="-18" y="103" width="131" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[DCN]]></text>
			</staticText>
			<staticText>
				<reportElement x="713" y="103" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Auditor]]></text>
			</staticText>
			<staticText>
				<reportElement x="630" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[SOP Reference]]></text>
			</staticText>
			<staticText>
				<reportElement x="228" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Adj. Required]]></text>
			</staticText>
			<staticText>
				<reportElement x="622" y="134" width="116" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Performance Group]]></text>
			</staticText>
			<staticText>
				<reportElement x="68" y="103" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="516" y="103" width="114" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Explanation]]></text>
			</staticText>
			<staticText>
				<reportElement x="713" y="134" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Audit Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="431" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Error]]></text>
			</staticText>
			<rectangle>
				<reportElement x="1" y="150" width="800" height="4" backcolor="#080707"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement x="92" y="68" width="206" height="21"  />
				<textElement>
					<font size="14" fontName="Arial" isUnderline="true" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{associateName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="4" y="2" width="797" height="30"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="28"/>
				</textElement>
				<textFieldExpression><![CDATA["Claims Audit Assessment Report"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="367" y="65" width="100" height="13"  />
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empNO}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="367" y="83" width="100" height="13"  />
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{supervisor}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="311" y="63" width="75" height="14"  />
				<textElement>
					<font fontName="Haettenschweiler" size="12" />
				</textElement>
				<text><![CDATA[Employee # :]]></text>
			</staticText>
			<staticText>
				<reportElement x="302" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Codes]]></text>
			</staticText>
			<staticText>
				<reportElement x="451" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Type]]></text>
			</staticText>
			<staticText>
				<reportElement x="516" y="135" width="114" height="16"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[FYI]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band/>
	</columnHeader>
	<detail>
		<band height="60" splitType="Stretch">
				  <printWhenExpression><![CDATA[!($F{oosSno}).isEmpty()]]></printWhenExpression>
			<image>
				<reportElement x="154" y="13" width="16" height="15"  >
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<imageExpression><![CDATA[$F{oosHighDoller}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="202" y="13" width="16" height="15"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<imageExpression><![CDATA[$F{oosMockAudit}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="202" y="28" width="16" height="15"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<imageExpression><![CDATA[$F{reportType}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="265" y="2" width="16" height="15"  />
				<imageExpression><![CDATA[$F{oosAdjRequired}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="377" y="2" width="16" height="15"  />
				<imageExpression><![CDATA[$F{oosMonetary}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<image>
				<reportElement x="429" y="2" width="16" height="15"  />
				<imageExpression><![CDATA[$F{oosProcedural}.equals("Y")?"check2.png":"check1.png"]]></imageExpression>
			</image>
			<line>
				<reportElement positionType="Float" x="0" y="1" width="800" height="1"  />
				<graphicElement>
					<pen lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement x="1" y="2" width="20" height="22"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosSno}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="11" y="2" width="74" height="18"  />
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosDcn}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="11" y="15" width="74" height="18"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosMemId}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="82" y="2" width="63" height="12"  />
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{oosPaid} != null && $F{oosPaid}.length() > 0 ? Double.valueOf($F{oosPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="82" y="15" width="63" height="12"  />
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{oosOverPaid} != null && $F{oosOverPaid}.length() > 0 ? Double.valueOf($F{oosOverPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="82" y="29" width="63" height="12"  />
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{oosUnderPaid} != null && $F{oosUnderPaid}.length() > 0 ? Double.valueOf($F{oosUnderPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="188" y="2" width="50" height="12"  />
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosAuditType}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="248" y="29" width="51" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosProcessOn}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="461" y="2" width="51" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosErrorType}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement stretchType="RelativeToTallestObject"  x="520" y="2" width="110" height="40"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosExplanation}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="520" y="43" width="110" height="17"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosFyi}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="640" y="29" width="92" height="14" positionType="Float" >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosPriPerfGroup}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="640" y="44" width="92" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosSecPerfGroup}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="721" y="2" width="78" height="12"  />
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosAuditor}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="640" y="2" width="81" height="26"  />
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosSop}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="316" y="2" width="31" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="local_mesure_unitx" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosErrorCode}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="732" y="29" width="51" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosAuditDate}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="34" splitType="Stretch">
			<textField>
				<reportElement x="644" y="1" width="100" height="30"  />
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="744" y="1" width="100" height="30"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="EEEE, MMMMM dd, yyyy">
				<reportElement x="4" y="1" width="146" height="17"  />
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="180" y="2" width="420" height="16"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[Quality Assurance Productivity Management]]></text>
			</staticText>
		</band>
	</pageFooter>
	<lastPageFooter>
		<band height="33" splitType="Stretch">
			<textField>
				<reportElement x="645" y="2" width="100" height="30"  />
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="745" y="2" width="100" height="30"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="EEEE, MMMMM dd, yyyy">
				<reportElement x="4" y="0" width="146" height="17"  />
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="180" y="2" width="420" height="16"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[Quality Assurance Productivity Management]]></text>
			</staticText>
		</band>
	</lastPageFooter>
	<summary>
		<band height="249" splitType="Stretch">
			<textField>
				<reportElement x="6" y="5" width="415" height="12"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Summary for 'Associate'= "+$F{associateName}+" ("+$F{oosTotalClaims}+" claims)"]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="6" y="20" width="158" height="48"  />
			</rectangle>
			<rectangle>
				<reportElement x="112" y="20" width="52" height="16" backcolor="#CCCCCC"  />
			</rectangle>
			<staticText>
				<reportElement x="9" y="21" width="74" height="15"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Claims]]></text>
			</staticText>
			<staticText>
				<reportElement x="9" y="37" width="133" height="15"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Procedural Error Claims]]></text>
			</staticText>
			<staticText>
				<reportElement x="9" y="53" width="133" height="15"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Monetary Error Claims]]></text>
			</staticText>
			<rectangle>
				<reportElement x="176" y="20" width="153" height="48"  />
			</rectangle>
			<staticText>
				<reportElement x="178" y="37" width="91" height="15"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Over Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="178" y="21" width="74" height="15"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="178" y="53" width="91" height="15"  />
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Under Paid]]></text>
			</staticText>
			<rectangle>
				<reportElement x="6" y="78" width="785" height="23"  />
			</rectangle>
			<rectangle>
				<reportElement x="6" y="101" width="785" height="23"  />
			</rectangle>
			<rectangle>
				<reportElement x="6" y="124" width="785" height="23"  />
			</rectangle>
			<staticText>
				<reportElement x="42" y="83" width="750" height="15"  />
				<textElement>
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[I certify that I have completed this audit in accordance with the standards and criteria appointed by the Quality Assurance Department.]]></text>
			</staticText>
			<staticText>
				<reportElement x="7" y="155" width="748" height="24"  />
				<textElement>
					<font fontName="Haettenschweiler" size="12"/>
				</textElement>
				<text><![CDATA[Please forward any dispute to this audit within 48 hours of the date listed on the bottom of the sheet. 
]]></text>
			</staticText>
			<rectangle>
				<reportElement x="112" y="36" width="52" height="16" backcolor="#CCCCCC"  />
			</rectangle>
			<rectangle>
				<reportElement x="112" y="52" width="52" height="16" backcolor="#CCCCCC"  />
			</rectangle>
			<rectangle>
				<reportElement x="254" y="20" width="76" height="16" backcolor="#CCCCCC"  />
			</rectangle>
			<rectangle>
				<reportElement x="254" y="36" width="76" height="16" backcolor="#CCCCCC"  />
			</rectangle>
			<rectangle>
				<reportElement x="254" y="52" width="76" height="16" backcolor="#CCCCCC"  />
			</rectangle>
			<staticText>
				<reportElement x="420" y="172" width="224" height="30"  />
				<textElement>
					<font fontName="Haettenschweiler" size="12"/>
				</textElement>
				<text><![CDATA[ ADJUSTED ?: ?YES     ?NO    ]]></text>
			</staticText>
			<rectangle>
				<reportElement x="125" y="186" width="157" height="1" forecolor="#FFFFFF" backcolor="#0A0A0A"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.25" lineStyle="Solid" lineColor="#0A0A0A"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="322" y="186" width="96" height="1" forecolor="#FFFFFF" backcolor="#0A0A0A"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.25" lineStyle="Solid" lineColor="#0A0A0A"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="7" y="172" width="748" height="18"  />
				<textElement>
					<font fontName="Haettenschweiler" size="12"/>
				</textElement>
				<text><![CDATA[Processor Signature :                                                                                                   Date :                                              ]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="113" y="23" width="51" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosTotalClaims}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="113" y="40" width="51" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosProcClaims}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="113" y="55" width="51" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{oosMonClaims}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="254" y="23" width="76" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{oosTotalPaid} != null && $F{oosTotalPaid}.length() > 0 ? Double.valueOf($F{oosTotalPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="254" y="40" width="76" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{oosTotalOvPaid} != null && $F{oosTotalOvPaid}.length() > 0 ? Double.valueOf($F{oosTotalOvPaid}) : 0))]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="253" y="55" width="76" height="14"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.text.DecimalFormat("$#,##0.00").format(Double.valueOf($F{oosTotalUnPaid} != null && $F{oosTotalUnPaid}.length() > 0 ? Double.valueOf($F{oosTotalUnPaid}) : 0))]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
