<!DOCTYPE HTML><%@page language="java"
	contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
	<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
	<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
	<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<html>
<head>
<title><spring:message code="audit.new.leftNav.title" /></title>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<script type="text/javascript" src="webResources/js/qadb.js"></script>

</head>
<body>
	
 

<c:choose>
    <c:when test="${edit != null}">
    
    <h2 style="color: white ;padding-top: 32px;">${eDCN} </h2>
	<h3 style="color: white">
		
		<table>
		<%-- <tr>
			<td>
			<img alt="errors" src="webResources/images/Sidebar/Icn_Last_Saved_User.png">
			
			</td> 
			<td>Last Saved<br>
				${savedBy} <br>
				${savedOn}
			</td>
		</tr>
		<tr>
			<td style="padding-right:10px;">
			
			
			</td>
			<td>Last Saved
				 
				
			</td>
		</tr> --%>
                <tr>
                        <td style="padding-right:5px;"><img alt="errors" src="webResources/images/Sidebar/Icn_Last_Saved_User.png"></td>
						<td><spring:message code="audit.edit.leftNav.text" /></td>
                </tr>
                <tr>
                        <td></td>
                         <td>${savedBy}<br>
							${savedOn}</td>
                       <%--  <td>${savedBy}${statsLast.savedBy} <br>
							${savedOn}${fn:substring(statsLast.savedTime, 0, 5)} ${statsLast.savedDate}</td> --%> <!-- for new stastics -->
						<%-- <td>${statsLast.savedBy} <br>
							${statsLast.savedTime} ${statsLast.savedDate}</td> --%>
                </tr>
		
		
		</table>
		
		
		
		</h3> 
	<div class="grid fluid">
		<div class="row">
			<div class="span4">

				<nav class="sidebar light">
					<ul>
    					
    				<li id="auditLeftLi" class="">
						<a id="auditLeft" onclick="clickGeneral();"><img alt="general" src="webResources/images/Tabs/Audit/Icn_Audit_General.png">&nbsp;&nbsp;&nbsp;&nbsp;
								<spring:message code="audit.new.leftNav.heading1" /></a></li>

						
    
       					<%-- <li class="${pageContext.request.requestURI eq '/QADBWeb/WEB-INF/jsp/audit.jsp' ? ' active' : ''}">
						<a href="editAuditGeneral?id=${eAudId}&dcnNo=${eDCN}"><img alt="general"
								src="webResources/images/Tabs/Audit/Icn_Audit_General.png">&nbsp;&nbsp;&nbsp;&nbsp;
								<spring:message code="audit.new.leftNav.heading1" /></a></li> --%>
						
						<li id="errorleftLi" class="">
						<a  id="editAuditError" onclick="updateLink();"><img alt="errors"
								src="webResources/images/Tabs/Audit/Icn_Audit_Errors.png">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<spring:message code="audit.new.leftNav.heading2" /></a></li> 
					
 						<li id ="statsLi" class="">
							<%-- <a href="editAuditStats"><img alt="Statastics"
								src="webResources/images/Tabs/Audit/Icn_Audit_Statistics.png">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<spring:message code="audit.edit.leftNav.heading1" /></a> --%>
							<a id="stats" ><img alt="Statastics"
								src="webResources/images/Tabs/Audit/Icn_Audit_Statistics.png">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<spring:message code="audit.edit.leftNav.heading1" /></a>
						
						</li>
						
					</ul>
				</nav>
				
			</div>


		</div>
	</div>		
								
    </c:when>    
    <c:otherwise>
      
      	<h2 style="color: white;padding-top: 32px;" ><spring:message code="audit.new.leftNav.heading" /></h2>
		<h3 style="color: white">
			<spring:message code="audit.new.leftNav.text" /></h3>
		<div class="grid fluid">
		<div class="row">
			<div class="span4">

				<nav class="sidebar light">
					<ul>
    	
       	<li id="auditLeftLi" class="">
						<a id="auditLeft" onclick="clickGeneral();"><img alt="general" src="webResources/images/Tabs/Audit/Icn_Audit_General.png">&nbsp;&nbsp;&nbsp;&nbsp;
								<spring:message code="audit.new.leftNav.heading1" /></a></li>

						<li id="errorleftLi" class="">
						<a id="errorleft"  onclick="validateFormNav();"><img alt="errors"
								src="webResources/images/Tabs/Audit/Icn_Audit_Errors.png">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<spring:message code="audit.new.leftNav.heading2" /></a></li>
					</ul>
				</nav>
				
			</div>


		</div>
	</div>
 						
    </c:otherwise>
</c:choose>
						

</body>

</html>