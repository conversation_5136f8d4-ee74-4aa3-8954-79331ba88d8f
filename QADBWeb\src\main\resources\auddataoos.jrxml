<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="test" pageWidth="842" pageHeight="595" columnWidth="802" isSummaryWithPageHeaderAndFooter="true">
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<parameter name="htmlCode" class="java.lang.String"/>
	<parameter name="htmlOosValue" class="java.lang.String"/>
	<parameter name="PREVIOUS_PAGE_NUMBER" class="java.lang.Integer"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	
	<field name="associateName" class="java.lang.String">
		<fieldDescription><![CDATA[associateName]]></fieldDescription>
	</field>
	<field name="empNO" class="java.lang.String">
		<fieldDescription><![CDATA[empNO]]></fieldDescription>
	</field>
	<field name="supervisor" class="java.lang.String">
		<fieldDescription><![CDATA[supervisor]]></fieldDescription>
	</field>
	
	
	<field name="htmlOosRow" class="java.lang.String">
		<fieldDescription><![CDATA[htmlOosRow]]></fieldDescription>
	</field>
	<pageHeader>
		<band height="154" splitType="Stretch">
			<rectangle>
				<reportElement x="4" y="37" width="280" height="18" forecolor="#0A0909" backcolor="#0A0A0A"  />
			</rectangle>
			<staticText>
				<reportElement x="41" y="37" width="220" height="24" forecolor="#FFFFFF" backcolor="#2DE339"  />
				<textElement textAlignment="Center">
					<font fontName="SansSerif" size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[OUT-OF-SAMPLE AUDITS]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="4" y="63" width="88" height="30"  />
				<box topPadding="4" leftPadding="8">
					<topPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
					<rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#FFFFFF"/>
				</box>
				<textElement>
					<font fontName="SansSerif" size="14"/>
				</textElement>
				<text><![CDATA[Associate : ]]></text>
			</staticText>
			<staticText>
				<reportElement x="311" y="80" width="75" height="14"  />
				<textElement>
					<font fontName="Haettenschweiler" size="12" />
				</textElement>
				<text><![CDATA[Supervisor   :]]></text>
			</staticText>
			<rectangle>
				<reportElement x="1" y="100" width="800" height="4" backcolor="#080707"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="128" y="118" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[High Dollar]]></text>
			</staticText>
			<staticText>
				<reportElement x="-12" y="118" width="131" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Membership #]]></text>
			</staticText>
			<staticText>
				<reportElement x="-15" y="134" width="131" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Platform]]></text>
			</staticText>
			<staticText>
				<reportElement x="195" y="134" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Out-Of-Sample]]></text>
			</staticText>
			<staticText>
				<reportElement x="365" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Monetary]]></text>
			</staticText>
			<staticText>
				<reportElement x="420" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Procedural]]></text>
			</staticText>
			<staticText>
				<reportElement x="261" y="134" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Processed On]]></text>
			</staticText>
			<staticText>
				<reportElement x="75" y="134" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Under Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="76" y="118" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Over Paid]]></text>
			</staticText>
			<staticText>
				<reportElement x="195" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Audit Type]]></text>
			</staticText>
			<staticText>
				<reportElement x="195" y="118" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Mock Audit]]></text>
			</staticText>
			<staticText>
				<reportElement x="299" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Error]]></text>
			</staticText>
			<staticText>
				<reportElement x="-18" y="103" width="131" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[DCN]]></text>
			</staticText>
			<staticText>
				<reportElement x="700" y="103" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Auditor]]></text>
			</staticText>
			<staticText>
				<reportElement x="595" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[SOP Reference]]></text>
			</staticText>
			<staticText>
				<reportElement x="261" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Adj. Required]]></text>
			</staticText>
			<staticText>
				<reportElement x="585" y="134" width="116" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Performance Group]]></text>
			</staticText>
			<staticText>
				<reportElement x="73" y="103" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Paid]]></text>
			</staticText>
			<!--<staticText>
				<reportElement x="516" y="103" width="114" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Explanation]]></text>
			</staticText>-->
			<staticText>
				<reportElement x="705" y="134" width="91" height="20"  >
					<property name="local_mesure_unity" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Audit Date]]></text>
			</staticText>
			<staticText>
				<reportElement x="475" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Root]]></text>
			</staticText>
			<rectangle>
				<reportElement x="1" y="150" width="800" height="4" backcolor="#080707"  >
					<property name="local_mesure_unitheight" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.2"/>
				</graphicElement>
			</rectangle>
			
			<!-- change 08/26/2020-->
			<textField isBlankWhenNull="true">
				<reportElement x="92" y="68" width="206" height="21"  />
				<textElement>
					<font size="14" fontName="Arial" isUnderline="true" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{associateName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="4" y="2" width="797" height="30"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="28"/>
				</textElement>
				<textFieldExpression><![CDATA["Claims Audit Assessment Report"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="367" y="65" width="100" height="13"  />
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{empNO}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="367" y="83" width="100" height="13"  />
				<textElement>
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{supervisor}]]></textFieldExpression>
			</textField>
			
			
			<staticText>
				<reportElement x="311" y="63" width="75" height="14"  />
				<textElement>
					<font fontName="Haettenschweiler" size="12" />
				</textElement>
				<text><![CDATA[Employee # :]]></text>
			</staticText>
			<staticText>
				<reportElement x="322" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Codes]]></text>
			</staticText>
			<staticText>
				<reportElement x="495" y="103" width="91" height="20"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[Cause]]></text>
			</staticText>
			<!--<staticText>
				<reportElement x="516" y="135" width="114" height="16"  />
				<textElement textAlignment="Center">
					<font fontName="Haettenschweiler" size="12" isBold="false"/>
				</textElement>
				<text><![CDATA[FYI]]></text>
			</staticText>-->
		</band>
	</pageHeader>
	<detail>

		<band height="160" splitType="Stretch">
		
			
			<genericElement>
                <reportElement x="0" y="0" width="790" height="160"/>
                <genericElementType namespace="http://jasperreports.sourceforge.net/jasperreports/html" name="htmlelement"/>
                <genericElementParameter name="htmlContent">
                    <valueExpression><![CDATA[$F{htmlOosRow}]]></valueExpression>
                </genericElementParameter>
                <genericElementParameter name="scaleType">
                    <valueExpression><![CDATA["RetainShape"]]></valueExpression>
                </genericElementParameter>
                <genericElementParameter name="verticalAlign">
                    <valueExpression><![CDATA["Top"]]></valueExpression>
                </genericElementParameter>
                <genericElementParameter name="horizontalAlign">
                    <valueExpression><![CDATA["Left"]]></valueExpression>
                </genericElementParameter>
            </genericElement>
            

		</band>

	</detail>
	<!--Displaying for OOS-->
	

	<pageFooter>
		<band height="57" splitType="Stretch">
			<textField>
				<reportElement x="645" y="22" width="100" height="30"  />
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["${CURRENT_PAGE_NUMBER}"]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="745" y="22" width="100" height="30"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["${TOTAL_PAGE_NUMBER}"]]></textFieldExpression>
			</textField>
			<textField pattern="EEEE, MMMMM dd, yyyy">
				<reportElement x="4" y="20" width="146" height="17"  />
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="180" y="22" width="420" height="16"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[Quality Assurance Productivity Management]]></text>
			</staticText>
		</band>
	</pageFooter>
	<lastPageFooter>
		<band height="57" splitType="Stretch">
			<textField>
				<reportElement x="645" y="22" width="100" height="30"  />
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["${CURRENT_PAGE_NUMBER}"]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="745" y="22" width="100" height="30"  />
				<textElement textAlignment="Left">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA["${TOTAL_PAGE_NUMBER}"]]></textFieldExpression>
			</textField>
			<textField pattern="EEEE, MMMMM dd, yyyy">
				<reportElement x="4" y="20" width="146" height="17"  />
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression class="java.util.Date"><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="180" y="22" width="420" height="16"  />
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<text><![CDATA[Quality Assurance Productivity Management]]></text>
			</staticText>
		</band>
	</lastPageFooter>
	<summary>
		<band height="315" splitType="Stretch">

			
			
			<genericElement>
                <reportElement x="0" y="0" width="870" height="315"/>
                <genericElementType namespace="http://jasperreports.sourceforge.net/jasperreports/html" name="htmlelement"/>
                <genericElementParameter name="htmlContent">
                    <valueExpression><![CDATA[$P{htmlOosValue}]]></valueExpression>
                </genericElementParameter>
                <genericElementParameter name="scaleType">
                    <valueExpression><![CDATA["RetainShape"]]></valueExpression>
                </genericElementParameter>
                <genericElementParameter name="verticalAlign">
                    <valueExpression><![CDATA["Top"]]></valueExpression>
                </genericElementParameter>
                <genericElementParameter name="horizontalAlign">
                    <valueExpression><![CDATA["Left"]]></valueExpression>
                </genericElementParameter>
            </genericElement>

		</band>
	</summary>
	
</jasperReport>