<!DOCTYPE HTML><%@page language="java"
	contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<html>
<head>
<!-- Load JavaScript Libraries -->
<script type="text/javascript" src="webResources/js/datep.js"></script>
<!-- Local JavaScript -->
<script type="text/javascript" src="webResources/js/qadb.js"></script>
<link href="webResources/css/iconFont.css" rel="stylesheet">
<link href="webResources/css/docs.css" rel="stylesheet">
<link href="webResources/js/prettify/prettify.css" rel="stylesheet">

<!-- Load JavaScript Libraries -->
<script src="webResources/js/jquery/jquery.min.js"></script>

<script type="text/javascript" src="webResources/js/jquery-ui.js"></script>
<script type="text/javascript" src="webResources/js/qadb.js"></script>
<link href="webResources/css/qadb.css" rel="stylesheet">
<link href="webResources/css/jquery-ui.css" rel="stylesheet">

</head>

<style>

/* Collapse -Expand */
#errorMessageDiv {
	padding: 5px 5px 5px 5px;
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	width: 350px;
	font-size: 11pt;
	background-color: #CE352C;
	color: white;
}

.headerA {
	background: url('webResources/images/Forms/Icn_Accordion_Collapse.png')
		no-repeat;
	background-position: right 0px;
	cursor: pointer;
}

.collapsed .headerA {
	background-image:
		url('webResources/images/Forms/Icn_Accordion_Expand.png');
}

.contentA {
	height: auto;
	min-height: 100px;
	overflow: hidden;
	transition: all 0.3s linear;
	-webkit-transition: all 0.3s linear;
	-moz-transition: all 0.3s linear;
	-ms-transition: all 0.3s linear;
	-o-transition: all 0.3s linear;
	background-color: #fafafa;
}

.collapsed .contentA {
	min-height: 0px;
	height: 0px;
}

#popupClaimRecordsD {
	width: 100%;
	height: 100%;
	opacity: 0.5;
	top: 0;
	left: 0;
	display: none;
	position: fixed;
	background-color: #F8F8F8;
	overflow: auto
}

div#popupClaimsD {
	position: fixed;
	border: 1px solid #383838;
	background-color: #FFFFFF;
	left: 55%;
	top: 40%;
	margin-left: -202px;
	display: none;
	font-family: 'Raleway', sans-serif
}

/*Aud count popUp  */
#popupAuditCountD {
	width: 100%;
	height: 100%;
	opacity: 0.5;
	top: 0;
	left: 0;
	display: none;
	position: fixed;
	background-color: #F8F8F8;
	overflow: auto
}

div#popupAuditCount {
	position: fixed;
	border: 1px solid #383838;
	background-color: #FFFFFF;
	left: 55%;
	top: 25%;
	margin-left: -262px;
	display: none;
	font-family: 'Raleway', sans-serif
}

.ui-widget {
	font-family: Verdana, Arial, sans-serif;
	font-size: 1.1em;
}

.ui-widget .ui-widget {
	font-size: 1em;
}

.ui-widget input, .ui-widget select, .ui-widget textarea, .ui-widget button
	{
	font-family: Verdana, Arial, sans-serif;
	font-size: 1em;
}

.ui-widget-content {
	border: 1px solid #aaaaaa;
	background: #ffffff url() 50% 50% repeat-x;
	color: #222222;
}

.ui-widget-content a {
	color: #222222;
}

/* .ui-widget-header {
	border: 1px solid #aaaaaa;
	background: #2573ab url(images/ui-bg_highlight-soft_75_cccccc_1x100.png)
		50% 50% repeat-x;
	color: #e6f5fc;
	font-weight: bold;
} */

.ui-widget-header a {
	color: #222222;
}

.ui-datepicker-trigger {
	background-image: url("webResources/images/Forms/Icn_Date_Picker.png");
	height: 36px;
	width: 36px;
	background-color: white;
}

.ui-icon-circle-triangle-e {
	background-image: url("webResources/images/skip_forward.png");
}

.ui-icon-circle-triangle-w {
	background-image: url("webResources/images/skip_backward.png");
}
</style>
<body>
	<div id="content-container" style="padding-left: 40px">
		<div style="padding: 10px 0px 0px 0px;">
			<c:choose>
				<c:when test="${edit != null}">
					<span class="bread1"><spring:message code="audit.new.bread1" />
						> </span>
					<span class="bread1"> <spring:message
							code="audit.edit.bread1" /> >
					</span>
					<span class="bread2">${eDCN} </span>
				</c:when>
				<c:otherwise>
					<span class="bread1"><spring:message code="audit.new.bread1" />
						> </span>
					<span class="bread2"> <spring:message
							code="audit.new.leftNav.heading" /></span>
				</c:otherwise>
			</c:choose>
		</div>

		<%
			String formAction = "";
		%>

		<c:if test="${edit != null}">
			<%
				formAction = "High_Dollar_Report_";
			%><!--Audit edit update  -->
		</c:if>
		<c:if test="${edit == null}">
			<%
				formAction = "High_Dollar_Report";
			%><!--Audit save  -->
		</c:if>

		<form id="retainForm" name="retainForm" method="POST">
			<input type="hidden" name="dcnNumber" id="dcnNumber"
				value="${dcnNumber}" /> <input type="hidden" name="lob" id="lob"
				value="${lob}" /> <input type="hidden" name="claimType"
				id="claimType" value="${claimType}" /> <input type="hidden"
				name="memberId" id="memberId" value="${memberId}" /> <input
				type="hidden" name="processDate" id="processDate"
				value="${processDate}" /> <input type="hidden" name="paidDate"
				id="paidDate" value="${paidDate}" /> <input type="hidden"
				name="totalCharge" id="totalCharge" value="${totalCharge}" /> <input
				type="hidden" name="paid" id="paid" value="${paid}" /> <input
				type="hidden" name="monetaryError" id="monetaryError"
				value="${monetaryError}" /> <input type="hidden" name="amountp"
				id="amountp" value="${amountp}" /> <input type="hidden"
				name="theoreticalPaid" id="theoreticalPaid"
				value="${theoreticalPaid}" /> <input type="hidden" name="isChk"
				id="isChk" value="${isChk}" /> <input type="hidden"
				name="penalityInterest" id="penalityInterest"
				value="${penalityInterest}" /> <input type="hidden"
				name="piCalculated" id="piCalculated" value="${piCalculated}" /> <input
				type="hidden" name="juridiction" id="juridiction"
				value="${juridiction}" /> <input type="hidden" name="auditType"
				id="auditType" value="${auditType}" /> <input type="hidden"
				name="priPGA" id="priPGA" value="${priPGA}" /> <input type="hidden"
				name="secPGA" id="secPGA" value="${secPGA}" /> <input type="hidden"
				name="mock" id="mock" value="${mock}" /> <input type="hidden"
				name="oos" id="oos" value="${oos}" /> <input type="hidden"
				name="e2e" id="e2e" value="${e2e}" /> <input type="hidden"
				name="processtype" id="processtype" value="${processtype}" /> <input
				type="hidden" name="riskAccount" id="riskAccount"
				value="${riskAccount}" /> <input type="hidden" name="adjustment"
				id="adjustment" value="${adjustment}" /> <input type="hidden"
				name="fyicheck" id="fyicheck" value="${fyicheck}" /> <input
				type="hidden" name="fyi" id="fyi" value="${fyi}" /> <input
				type="hidden" name="subscriberCK" id="subscriberCK"
				value="${subscriberCK}" /> <input type="hidden" name="AccountID"
				id="AccountID" value="${AccountID}" /> <input type="hidden"
				name="Group" id="Group" value="${Group}" /> <input type="hidden"
				name="claimCurrentStatus" id="claimCurrentStatus"
				value="${claimCurrentStatus}" /> <input type="hidden"
				name="AccountName" id="AccountName" value="${AccountName}" /> <input
				type="hidden" name="productID" id="productID" value="${productID}" />
				<input type="hidden" name="adjustmentReasoncode" id="adjustmentReasoncode" value="${adjustmentReasoncode}" />
			<c:forEach items="${productsList}" var="products"
				varStatus="rowCount">
				<input type="hidden" name="BSBSCode_${rowCount.index }"
					id="BSBSCode_${rowCount.index }" value="${products.BSBSCode }" />
				<input type="hidden" name="groupID_${rowCount.index }"
					id="groupID_${rowCount.index }" value="${products.groupID }" />
				<input type="hidden" name="productLine_${rowCount.index }"
					id="productLine_${rowCount.index }"
					value="${products.productLine }" />
				<input type="hidden" name="productDescription_${rowCount.index }"
					id="productDescription_${rowCount.index }"
					value="${products.productDescription }" />
			</c:forEach>
			<input type="hidden" name="prodSize" id="prodSize"
				value="${fn:length(productsList)}">
			<!-- High dollar info -->
			<input type="hidden" name="claimAdjFlag" id="claimAdjFlag"
				value="${claimAdjFlag}" /> <input type="hidden"
				name="serviceAdjFlag" id="serviceAdjFlag" value="${serviceAdjFlag}" />
			<input type="hidden" name="examinerName" id="examinerName"
				value="${examinerName}" /> <input type="hidden" name="QAName"
				id="QAName" value="${QAName}" /> <input type="hidden"
				name="patientName" id="patientName" value="${patientName}" /> <input
				type="hidden" name="serviceDates" id="serviceDates"
				value="${serviceDates}" /> <input type="hidden"
				name="typeOfService" id="typeOfService" value="${typeOfService}" />
			<input type="hidden" name="diagnosis" id="diagnosis"
				value="${diagnosis}" /> <input type="hidden" name="surgeryDOS"
				id="surgeryDOS" value="${surgeryDOS}" /> <input type="hidden"
				name="surgery" id="surgery" value="${surgery}" /> <input
				type="hidden" name="fileRef" id="fileRef" value="${fileRef}" />
				<input
				type="hidden" name="interestPaid" id="interestPaid"
				value="${interestPaid}" />
				 <input
				type="hidden" name="providerName" id="providerName"
				value="${providerName}" /> <input type="hidden"
				name="providerNumber" id="providerNumber" value="${providerNumber}" />
			<input type="hidden" name="payee" id="payee" value="${payee}" /> <input
				type="hidden" name="notes" id="notes" value="${notes}" /> <input
				type="hidden" name="audSignDate" id="audSignDate"
				value="${audSignDate}" /> <input type="hidden" name="vpSignDate"
				id="vpSignDate" value="${vpSignDate}" /> <input type="hidden"
				name="forwardToDate" id="forwardToDate" value="${forwardToDate}" />
			<input type="hidden" name="rcvedFrmDate" id="rcvedFrmDate"
				value="${rcvedFrmDate}" /> <input type="hidden"
				name="releasedByDate" id="releasedByDate" value="${releasedByDate}" />


			<c:if test="${edit != null}">
				<input type="hidden" name="editPage" id="editPage" value="Y" />
				<input type="hidden" name="auditId" id="auditId" value="${auditId}" />
				<input type="hidden" name="efacetsId" id="efacetsId"
					value="${efacetsId}" />
			</c:if>

		</form>

		<form:form id="errorForm" name="errorForm" method="GET"
			onsubmit="return validateErrorFields();" style="width:750px"
			commandName="errorForm" action="<%=formAction%>"
			onkeypress="stopEnterSubmitting(window.event)">
			<input type="hidden" name="monetaryErrorV" id="monetaryErrorV"
				value="${monetaryError}" />
			<table width="100%" style="padding: 10px 10px 10px 0px">
				<tr style="height: 20px;border-bottom-color: gray;">
					<c:choose>
						<c:when test="${edit != null}">
							<td width="40%" style="padding-left: 0px;"><span
								style="font-size: 20px"><spring:message
										code="audit.edit.header" /> ${eDCN} </span></td>
							<td width="60%" style="text-align: right;"><c:set
									var="userRole">
									<%=request.getHeader("iv-groups")%>
								</c:set> <%-- <c:set var="userRole" > "Contractor","PeopleSoft_ELM_User","onestop_users","null" </c:set> --%>
								<c:if
									test="${!((fn:contains(userRole, 'qadb-samd-readonly_user'))||(fn:contains(userRole, 'qadb-cd-readonly_user'))||(fn:contains(userRole, 'null')))}">
								<a onclick="clearForm();">
									<button title="Cancel" type="button"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img alt="Clear error details" src="webResources/images/Actions/Icn_Cancel.png">
									</button>
								</a>
									<a onclick="divDelA_show();">
										<button title="Delete" type="button"
											style="background-color: transparent; border-color: transparent; size: 10px;">
											<img alt="Delete Audit"
												src="webResources/images/Actions/Icn_Delete.png">
										</button>
									</a>
									<button title="Save" type="submit"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png">
									</button>
								</c:if> <a onclick="divCount_show();">
									<button title="Click to view total Audit count" type="button"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img alt="Audit Count"
											src="webResources/images/Actions/Icn_Snapshot.png">
									</button>
							</a></td>
						</c:when>
						<c:otherwise>
							<td width="30%" style="padding-left: 0px;"><span
								style="font-size: 20px"><spring:message
										code="audit.new.leftNav.heading"></spring:message> </span></td>
							<td width="70%" style="text-align: right;"><c:if
									test="${audCountRS.processDate!=null}">
									<a onclick="divCount_show();">
										<button title="Click to view total Audit count" type="button"
											style="background-color: transparent; border-color: transparent; size: 10px;">
											<img alt="Audit Count"
												src="webResources/images/Actions/Icn_Snapshot.png">
										</button>
									</a>
								</c:if>
								<button type="reset"
									style="background-color: transparent; border-color: transparent; size: 10px;">
									<img src="webResources/images/Actions/Icn_Cancel.png">
								</button>
								<button type="submit"
									style="background-color: transparent; border-color: transparent; size: 10px;">
									<img src="webResources/images/Actions/Icn_Save.png">
								</button></td>
						</c:otherwise>
					</c:choose>
				</tr>
			</table>
			<div style="color: red;">
				<c:if test="${claimDetails.statusBlock.statusCode=='02' }">
					<c:forEach items="${claimDetails.statusBlock.message }"
						var="messagedesc">
						<c:out value="${messagedesc.value }"></c:out>
					</c:forEach>
				</c:if>
			</div>
			<div id="errorMessageDiv"></div>
			<div style="padding-left:7px">
			<hr style="color: gray; size: 2px solid;">

					<table width="100%"
						style="border-bottom: 1 px solid gray; font-size: 14px">
						<tr style="height: 40px;">
							<td width="20%"><spring:message code="audit.new.associate" /></td>
							<td width="20%"><spring:message
									code="audit.new.associate.id" /></td>
							<td width="20%"><spring:message
									code="audit.new.associate.supervisor" /></td>
							<td width="20%"><spring:message
									code="audit.new.associate.manager" /></td>
							<td width="20%"><spring:message
									code="audit.new.associate.director" /></td>
						</tr>
						<tr style="height: 40px">
							<c:choose>
								<c:when test="${edit == null}">
									<td>${assoDetails.associateName}</td>
									<td>${assoDetails.associateId}</td>
									<td>${assoDetails.superVisor}</td>
									<td>${assoDetails.manager}</td>
									<td>${assoDetails.director}</td>
								</c:when>
								<c:when test="${edit != null}">
									<td>${assocEditDetails.associateName}</td>
									<td>${assocEditDetails.facetsId}</td>
									<td>${assocEditDetails.superVisor}</td>
									<td>${assocEditDetails.manager}</td>
									<td>${assocEditDetails.director}</td>
								</c:when>
								<c:otherwise>
									<td>----------</td>
									<td>----------</td>
									<td>----------</td>
									<td>----------</td>
									<td>----------</td>
								</c:otherwise>
							</c:choose>
						</tr>
					</table>
			</div>
			<div class="panelContainer">
				<div class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA"
						style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
						<span style="color: #0070c0; font-size: 18px"><spring:message
								code="audit.new.error.information" /></span>
					</div>
					<div class="contentA" style="padding-top: 8px;padding-left: 7px;">

						<table class="table" id="ARtable" width="100%"">
							<tbody id="ARtable">
								<tr>
									<td style="padding:8px;width:20%"><spring:message
											code="audit.new.error.code" /></td>
									<td style="padding:8px;width:3%"><spring:message
											code="audit.new.error.monetary" /><span class="manditory"
										style="color:red">*</span></td>
									<td style="padding:8px;width:3%"><spring:message
											code="audit.new.error.procedural" /><span class="manditory"
										style="color:red">*</span></td>
									<td style="padding:8px;width:31%"><spring:message
											code="audit.new.error.specialty" /><span class="manditory"
										style="color:red">*</span></td>
									<td style="padding:8px;width:31%"><spring:message
											code="audit.new.error.root.cause" /><span class="manditory"
										style="color:red">*</span></td>
									<td style="padding:8px;width:7%"><spring:message
											code="audit.new.error.edit.code" /></td>
									<td style="padding:8px;width:5%"></td>
								</tr>
								<!--error edit  -->
								<c:choose>
									<c:when test="${edit != null && (not empty errorsRowsRecv) }">
										<c:forEach items="${errorsRowsRecv}" var="rs"
											varStatus="rowCount">
											<tr style="height: 30px" id="error_row_${rowCount.index }">
												<td style="padding:8px;"><select name="errorCodesId"
													style="width: 80px; font-size: 14px; border-color: #919191">
														<option value=""><spring:message
																code="audit.new.error.select" /></option>
														<optgroup label="Code      --- Name  ">
															<c:forEach items="${errorCodes}" var="errorCodes">
																<option value="${errorCodes.errorCodeId}"
																	${errorCodes.errorCodeId == rs.errorCodeId ? 'selected="selected"' : ''}
																	${errorCodes.errorActive == 'N' ? 'disabled="disabled"' : ''}
																	>${errorCodes.errorCodeId}.${errorCodes.errorCode}</option>
															</c:forEach>
														</optgroup>
												</select> <form:hidden path="errorCategory"
														id="errorCategory${rowCount.index }"
														value="${rs.monetary }" /></td>
												<td style="padding:8px;">
													<div class="radio">
														<c:choose>
															<c:when test="${rs.monetary=='Y' }">
																<label><input type="radio"
																	name="errorCategory${rowCount.index }" value="M"
																	checked="checked" onclick="setVal(this.value, this)" />
																	<span class="check"></span> </label>
															</c:when>
															<c:otherwise>
																<label><input type="radio"
																	name="errorCategory${rowCount.index }" value="M"
																	onclick="setVal(this.value, this)" /> <span
																	class="check"></span> </label>
															</c:otherwise>
														</c:choose>
													</div>
												</td>
												<td style="padding:8px;">
													<div class="radio default-style">
														<c:choose>
															<c:when test="${rs.procedural=='Y' }">
																<label><input type="radio"
																	name="errorCategory${rowCount.index }" value="P"
																	checked="checked" onclick="setVal(this.value, this)" />
																	<span class="check"></span> </label>
															</c:when>
															<c:otherwise>
																<label><input type="radio"
																	name="errorCategory${rowCount.index }" value="P"
																	onclick="setVal(this.value, this)" /> <span
																	class="check"></span> </label>
															</c:otherwise>
														</c:choose>
													</div>
												</td>
												<td style="padding:8px;"><select
													id="specialitysId${rowCount.index }" name="specialitysId"
													style="width: 150px; font-size: 14px; border-color: #919191">
														<option value=""><spring:message
																code="audit.new.error.select" /></option>
														<c:forEach items="${speciality}" var="speciality">
															<option value="${speciality.spcialityId}"
																${speciality.spcialityId == rs.spcialityId ? 'selected="selected"' : ''}>${speciality.spciality}</option>
														</c:forEach>
												</select></td>
												<td style="padding:8px;"><select
													id="rootCausesId${rowCount.index }" name="rootCausesId"
													onchange="getErrorTypeDropdown();"
													style="width: 150px; font-size: 14px; border-color: #919191 ;">
														<option value=""><spring:message
																code="audit.new.error.select" /></option>
														<c:forEach items="${rootCause}" var="rootCause">
															<option value="${rootCause.rootCauseId}"
																${rootCause.rootCauseId == rs.rootCauseId ? 'selected="selected"' : ''}>${rootCause.rootCause}</option>
														</c:forEach>
												</select></td>
												<td style="padding:8px;"><input type="text"
													id="editCodesId${rowCount.index }" name="editCodesId"
													style="width: 80px; font-size: 14px; border-color: #919191"
													value="${fn:trim(rs.editCodeId)}"></td>
												<td style="padding:8px;"><c:choose>
														<c:when test="${rowCount.index==0 }"></c:when>
														<c:otherwise>
															<a href="#" id="remRow"><img title="Delete Row"
																alt="Delete row" id=""
																src="webResources/images/Data Grid/Icn_Delete.png"></a>
														</c:otherwise>
													</c:choose></td>
											</tr>
										</c:forEach>
									</c:when>
									<c:otherwise>
										<tr id="error_row_0">
											<td style="padding:8px;"><select name="errorCodesId"
												style="width: 80px; font-size: 14px; border-color: #919191">
													<option value=""><spring:message
															code="audit.new.error.select" /></option>
													<optgroup label="Code      --- Name  ">
														<c:forEach items="${errorCodes}" var="errorCodes">
															<option value="${errorCodes.errorCodeId}">${errorCodes.errorCodeId}.
																${errorCodes.errorCode}</option>
														</c:forEach>
													</optgroup>
											</select> <form:hidden path="errorCategory" id="errorCategory"
													value="" /></td>
											<td style="padding:8px;">
												<div class="radio">
													<label><input type="radio" name="errorCategory"
														value="M" onclick="setVal(this.value, this)" /> <span
														class="check"></span> </label>
												</div>
											</td>
											<td style="padding:8px;"><div
													class="radio default-style">
													<label><input type="radio" name="errorCategory"
														value="P" onclick="setVal(this.value, this)" /> <span
														class="check"></span> </label>
												</div></td>
											<td style="padding:8px;"><select id="specialitysId"
												name="specialitysId"
												style="width: 150px; font-size: 14px; border-color: #919191">
													<option value=""><spring:message
															code="audit.new.error.select" /></option>
													<c:forEach items="${speciality}" var="speciality">
														<option value="${speciality.spcialityId}">${speciality.spciality}</option>
													</c:forEach>
											</select></td>
											<td style="padding:8px;"><select id="rootCausesId"
												name="rootCausesId"
												style="width: 150px; font-size: 14px; border-color: #919191"
												onchange="getErrorTypeDropdown();">
													<option value=""><spring:message
															code="audit.new.error.select" /></option>
													<c:forEach items="${rootCause}" var="rootCause">
														<option value="${rootCause.rootCauseId}">${rootCause.rootCause}</option>
													</c:forEach>
											</select></td>
											<td style="padding:8px;"><input type="text"
												id="editCodesId" name="editCodesId" value=""
												style="width: 80px; font-size: 14px; border-color: #919191">
											</td>
										</tr>
									</c:otherwise>
								</c:choose>
							</tbody>
						</table>
						<div>
							<a href="#" id="addRow"
								style="font-size: 14px; text-decoration: none;color:black">
								<img title="Add Row" alt="Add row" id="addRow"
								src="webResources/images/Data Grid/Icn_New_Row.png">&nbsp;
								<spring:message code="audit.new.error.add.row" />
							</a>
						</div>
					</div>
				</div>
			</div>

			<div class="panelContainer">
				<div class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA"
						style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
						<span style="color: #0070c0; font-size: 18px"><spring:message
								code="audit.new.claim.details" /></span>
					</div>

					<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
						<table class="table">
							<tr>
								<td style="width: 250px;"><spring:message code="audit.new.claim.error.type" /></td>
								<td>
									<div class="input-control select" id="errorType">
										<jsp:include
											page="../jsp/audit_error_errorTypeDropDown_Div.jsp"></jsp:include>
										<%-- <select id="errorType" name="errorType"
											style="width: 200px; font-size: 14px; border-color: #919191">
										<option value=""><spring:message code="audit.new.error.select" /></option>
											<c:forEach items="${errorTypes}" var="errorTypes">
												<option value="${errorTypes.errorTypeId}"
													${errorTypes.errorTypeId == eErrTypeId ? 'selected="selected"' : ''}>${errorTypes.errorTypes}</option>
											</c:forEach>
										</select> --%>
									</div>
								</td>
							</tr>
							<tr>
								<td><spring:message code="audit.new.claim.sop" /></td>
								<td><div class="input-control text">
										<input type="text" name="sop"
											placeholder="SOP/NewsFlash Reference" maxlength="50" style="width: 403px;"
											value="${eSop}">
									</div></td>
							</tr>
							<tr>
								<td><spring:message code="audit.new.claim.reason" /> <span
									class="manditory" style="color:red">*</span>
									<p>
										<spring:message code="audit.new.claim.reason.text" />
									</p></td>
								<td>
									<div id="reasonc" class="input-control textarea">
										<textarea id="reason" name="reason" maxlength="1000"
											style="width: 242px; height: 106px;">${eReason}</textarea>
									</div>
								</td>
							</tr>
							<tr height="20px">
								<td></td>
							</tr>
							<tr>
								<td><spring:message code="audit.new.claim.comments" /><!-- <span
									class="manditory" style="color:red">*</span> --> <spring:message
										code="audit.new.claim.comments.text" /></td>
								<td>
									<div id="commentsc" class="input-control textarea">
										<textarea id="comments" name="comments" maxlength="1000"
											style="width: 242px; height: 106px;">${eComments}</textarea>
									</div>
								</td>
							</tr>
						</table>
					</div>
				</div>
			</div>

			<div class="panelContainer">
				<div class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA"
						style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
						<span style="color: #0070c0; font-size: 18px"><spring:message
								code="audit.new.adjustments" /></span>
					</div>
					<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
						<table width="100%">
							<tr>
								<td width="35%"><spring:message
										code="audit.new.adjustments.required" /></td>
								<td><div class="input-control checkbox ">
										<c:choose>
											<c:when test="${eRequired != null}">
												<label> <input type="checkbox" name="required"
													value="Y" checked="checked"> <span class="check"></span>
												</label>
											</c:when>
											<c:otherwise>
												<label> <input type="checkbox" name="required"
													value="Y"> <span class="check"></span>
												</label>
											</c:otherwise>
										</c:choose>
									</div></td>
							</tr>
							<tr>
								<td><spring:message code="audit.new.adjustments.completed" />
								</td>
								<td><div class="input-control checkbox ">
										<c:choose>
											<c:when test="${eComplted != null}">
												<label> <input type="checkbox" name="completed"
													value="Y" checked="checked"> <span class="check"></span>
												</label>
											</c:when>
											<c:otherwise>
												<label> <input type="checkbox" name="completed"
													value="Y"> <span class="check"></span>
												</label>
											</c:otherwise>
										</c:choose>
									</div></td>
							</tr>
							<tr>
								<td><spring:message
										code="audit.new.adjustments.date.adjusted" /></td>
								<td>
									<div class="input-control text">
										<input id="datepick2" size="50" name="dateAdj"
											value="${eDateAdj}" />

									</div>
								</td>
								<td></td>
							</tr>
							<tr>
								<td><spring:message code="audit.new.adjustments.appeal" />
								</td>
								<td><div class="input-control checkbox ">
										<c:choose>
											<c:when test="${eAppeal != null}">
												<label> <input type="checkbox" name="appeal"
													value="Y" checked="checked"> <span class="check"></span>
												</label>
											</c:when>
											<c:otherwise>
												<label> <input type="checkbox" name="appeal"
													value="Y"> <span class="check"></span>
												</label>
											</c:otherwise>
										</c:choose>
									</div></td>
							</tr>
						</table>
					</div>
				</div>
				<div>
					<table width="100%" style="padding: 10px 10px 10px 50px">
						<tr style="height: 20px;border-bottom-color: gray;">
							<c:choose>
								<c:when test="${edit != null}">
									<td width="30%">
										<div style="float: left;padding-left: 0px;">
											<a style="float: left;" href="#top">Back To Top</a>
										</div>
									</td>
									<td width="70%" style="text-align: right;"><c:set
											var="userRole">
											<%=request.getHeader("iv-groups")%>
										</c:set> <%-- <c:set var="userRole" > "Contractor","PeopleSoft_ELM_User","onestop_users","null" </c:set> --%>
										<c:if
											test="${!((fn:contains(userRole, 'qadb-samd-readonly_user'))||(fn:contains(userRole, 'qadb-cd-readonly_user'))||(fn:contains(userRole, 'null')))}">
											<button title="Cancel" type="reset"
												style="background-color: transparent; border-color: transparent; size: 10px;">
												<img src="webResources/images/Actions/Icn_Cancel.png">
											</button>
											<a onclick="divDelA_show();">
												<button title="Delete" type="button"
													style="background-color: transparent; border-color: transparent; size: 10px;">
													<img alt="Delete Audit"
														src="webResources/images/Actions/Icn_Delete.png">
												</button>
											</a>
											<button title="Save" type="submit"
												style="background-color: transparent; border-color: transparent; size: 10px;">
												<img src="webResources/images/Actions/Icn_Save.png">
											</button>
										</c:if> <a onclick="divCount_show();">
											<button title="Click to view total Audit count" type="button"
												style="background-color: transparent; border-color: transparent; size: 10px;">
												<img alt="Audit Count"
													src="webResources/images/Actions/Icn_Snapshot.png">
											</button>
									</a></td>
								</c:when>
								<c:otherwise>
									<td width="30%">
										<div style="float: left;padding-left: 0px;">
											<a style="float: left;" href="#top">Back To Top</a>
										</div>
									</td>
									<td width="70%" style="text-align: right;">
									
										<!-- <button type="reset" onclick="clearRadio();" -->
										<button type="reset" onclick="clearForm();"
											style="background-color: transparent; border-color: transparent; size: 10px;">
											<img src="webResources/images/Actions/Icn_Cancel.png">
										</button>
										<button type="submit"
											style="background-color: transparent; border-color: transparent; size: 10px;">
											<img src="webResources/images/Actions/Icn_Save.png">
										</button>
									</td>
								</c:otherwise>
							</c:choose>
						</tr>
					</table>
				</div>
			</div>
		</form:form>
	</div>
	<!-- delete popup -->
	<jsp:include page="../jsp/deleteConfirm.jsp"></jsp:include>
	<!-- Audit counts popup -->
	<jsp:include page="../jsp/auditCounts.jsp"></jsp:include>
	<script type="text/javascript">
		var twoMonthBackDate = new Date();
		twoMonthBackDate.setMonth(twoMonthBackDate.getMonth()-2);

		$("#datepick2").datepicker({
			showOn : "button",
			maxDate : new Date(),
			/* minDate : twoMonthBackDate */
		});

		/* Script for collapse */
		$(function() {
			$('.headerA').click(function() {
				$(this).closest('.containerA').toggleClass('collapsed');
			});

		});

		/* $("#datepick2").keypress(function(e) {
			e.preventDefault();
		}); */

		function clearRadio() {
			$('[id^="errorCategory"]').each(function() {
				document.getElementById(this).value = "";
			});
		}
	</script>

	<script type="text/javascript">
		var scntDiv = $('#ARtable');

		var i = $('#ARtable tr').size() + 1;
		$('#addRow')
				.click(
						function() {
							var x = document.getElementById("ARtable");
							var new_row = x.rows[1].cloneNode(true);
							var len = x.rows.length;
							var inp0 = new_row.cells[0]
									.getElementsByTagName("select")[0];
							inp0.id += len;
							inp0.value = '';

							var sel1 = new_row.cells[3]
									.getElementsByTagName("select")[0];
							sel1.id += len;
							sel1.value = '';

							var sel2 = new_row.cells[4]
									.getElementsByTagName("select")[0];
							sel2.id += len;
							sel2.value = '';

							var rad2 = new_row.cells[1]
									.getElementsByTagName("input")[0];
							rad2.name += len;
							rad2.id += len;

							var rad3 = new_row.cells[2]
									.getElementsByTagName("input")[0];
							rad3.name += len;
							rad3.id += len;

							var mainrad = new_row.cells[0]
									.getElementsByTagName("input")[0];
							mainrad.id += len;

							var inp1 = new_row.cells[5]
									.getElementsByTagName("input")[0];
							inp1.id += len;
							inp1.value = "";

							var trial = new_row.cells[6]

							console.log("cell = " + trial);

							var remove = new_row.insertCell(6);
							remove.innerHTML = '<td style="padding-left:8px"><a href="#" id="remRow"><img title="Delete Row" alt="Delete row" id="" src="webResources/images/Data Grid/Icn_Delete.png"></a></td>';

							x.appendChild(new_row);

							i++;
							return false;
						});

		//Remove button
		$(document).on('click', '#remRow', function() {
			if (i > 2) {
				$(this).closest('tr').remove();
				i--;
			}
			getErrorTypeDropdown();
			return false;
		});
	</script>

	<script type="text/javascript">
		var isErrorCodeSel;
		$("input[type='checkbox']").on("change", function() {
			if ($(this).is(":checked"))
				$(this).val("Y");
			else
				$(this).val("N");

		});

		//Manditory div show/hide

		$('.manditory').hide();

		$("select[name='errorCodesId']").change(function() {
			if ((this.value) != null) {
				$('.manditory').show();
			}
			if ((this.value) === "") {
				$('.manditory').hide();
			}

		});

		$("#2").change(function() {

			if ((this.value) != null) {
				$('.manditory').show();
			}
			if ((this.value) === '-') {
				$('.manditory').hide();
			}

		});

		function setVal(val, element) {
			document.getElementById(element.name).value = val;
			console.log(document.getElementById(element.name).value);
		}
	</script>
	<script type="text/javascript">
		function stopEnterSubmitting(e) {
			if (e.keyCode == 13) {
				var src = e.srcElement || e.target;
				if (src.tagName.toLowerCase() != "textarea") {
					if (e.preventDefault) {
						e.preventDefault();
					} else {
						e.returnValue = false;
					}
				}
			}
		}
	</script>
	<!-- Script for dependent Dropdown for root cause -->
	<script type="text/javascript">
		/* $(document).ready(
						function() {
							$("#rootCausesId").change(
											function() {
												alert("Selected value is : "+ document.getElementById("rootCausesId").value);
												$("#errorType option").hide().removeAttr("selected").parent().find(
													"option[value^=" + $(this).val()
															+ "]").show().first().prop(
													"selected", true);
											});
						}); */

		function getErrorTypeDropdown(val) {
			if (val == null) {
				var subject;
				//var subject = document.getElementById("rootCausesId").value;
				if ((document.getElementById("rootCausesId")) === null) {
					subject = document.getElementById("rootCausesId0").value;//for edit audit error
					var type = "edit";
				} else {
					subject = document.getElementById("rootCausesId").value;
				}
			}
			console.log("inpt1--->" + subject);
			var url = "errorTypeDropDown";
			$.ajax({
				type : "GET",
				url : url,
				dataType : "html",
				data : {
					'subject' : subject
				},
				success : function(response) {
					$("#errorType").html(response);
				},
				error : function() {

				}

			});

			//if(($('[id^=error_row_0]').length) >1){
			if (((document.getElementsByName('rootCausesId')).length) > 1) {
				console.log("calling");

				if (type == "edit") {
					setTimeout(multipleErrorEdit, 500);
				} else {
					setTimeout(multipleError, 500);
				}

			}
		}
	</script>
	<script type="text/javascript">
		function multipleError() {

			var rc = document.getElementById("rootCausesId").value;
			//var length = $('[id^=error_row_0]').length;		
			var elements = document.getElementsByName('rootCausesId');
			for (var i = 0; i < elements.length; i++) {
				if (null != (elements[i].getAttribute('id'))) {
					var nextRc = document.getElementById(elements[i]
							.getAttribute('id')).value;
					if (rc != nextRc) {
						console.log("No match");
						$("#errorTypes").empty();
						$("#errorTypes").append(
								new Option("Multiple Errors", "17"));
					}
				}

			}
		};

		function multipleErrorEdit() {

			var rc = document.getElementById("rootCausesId0").value;
			var elements = document.getElementsByName('rootCausesId');
			for (var i = 1; i < elements.length; i++) {
				if (null != (elements[i].getAttribute('id'))) {
					var nextRc = document.getElementById(elements[i]
							.getAttribute('id')).value;
					if (rc != nextRc) {
						console.log("No match");
						$("#errorTypes").empty();
						$("#errorTypes").append(
								new Option("Multiple Errors", "17"));
					}
				}

			}

		};

		$(function() {
			getErrorTypeDropdown();
		});
		
		function clearForm() {
  			$(':input',"#errorForm")
 			.not(':button, :submit, :reset')
 			.val('')
 			.removeAttr('checked')
 			.removeAttr('selected');
		}
	</script>
</body>
</html>