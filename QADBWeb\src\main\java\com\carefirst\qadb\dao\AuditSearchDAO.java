package com.carefirst.qadb.dao;


import java.sql.SQLException;
import java.util.List;

import net.sf.jasperreports.engine.JRDataSource;

import com.carefirst.audit.model.AuditCounts;
import com.carefirst.audit.model.AuditSave;
import com.carefirst.audit.model.AuditSearch;
import com.carefirst.audit.model.Statistics;

public interface AuditSearchDAO {

	public List<AuditSearch> getSearchResults(AuditSearch searchTO) throws SQLException;

	public AuditSave getEditAudit(AuditSearch auditSearchTO);
	
	public List<Statistics> getStatastics(AuditSearch auditSearchTO) throws SQLException;

	public AuditCounts getAuditCounts(AuditCounts auditCountsTO) throws SQLException;

	public JRDataSource getHighDollarReport(String auditId) throws SQLException;
	
}
