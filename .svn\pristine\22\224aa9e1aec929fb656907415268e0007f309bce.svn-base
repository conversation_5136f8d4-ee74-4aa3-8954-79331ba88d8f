<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<head>
<link href="webResources/css/qadb.css" rel="stylesheet">

<script type="text/javascript" src="webResources/js/metro.min.js"></script>
<!-- Local JavaScript -->

<link href="webResources/css/iconFont.css" rel="stylesheet">
<link href="webResources/css/docs.css" rel="stylesheet">
<link href="webResources/js/prettify/prettify.css" rel="stylesheet">

<!-- Load JavaScript Libraries -->
<script type="text/javascript" src="webResources/js/datep.js"></script>
<script src="webResources/js/jquery/jquery.min.js"></script>
<script src="webResources/js/jquery/jquery.widget.min.js"></script>

<script type="text/javascript" src="webResources/js/jquery.min.js"></script>
<script type="text/javascript" src="webResources/js/jquery-ui.js"></script> 
<script type="text/javascript" src="webResources/js/qadb.js"></script>
<link href="webResources/css/qadb.css" rel="stylesheet">
<link href="webResources/css/jquery-ui.css" rel="stylesheet">

<style>
/* Collapse -Expand */
.headerA {
	background: url('webResources/images/Forms/Icn_Accordion_Collapse.png')
		no-repeat;
	background-position: right 0px;
	cursor: pointer;
}

.collapsed .headerA {
	background-image:
		url('webResources/images/Forms/Icn_Accordion_Expand.png');
}

.contentA {
	height: auto;
	min-height: 100px;
	overflow: hidden;
	transition: all 0.3s linear;
	-webkit-transition: all 0.3s linear;
	-moz-transition: all 0.3s linear;
	-ms-transition: all 0.3s linear;
	-o-transition: all 0.3s linear;
	background-color:#fafafa;
}

.collapsed .contentA {
	min-height: 0px;
	height: 0px;
}

.correct {
	display: none;
}
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
	margin-right: auto;
	height: 130%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}

.hidden {
	display: none;
}
div.success {
	padding-left: 5px;
	padding-top: 5px;
	height: 30px; 
	background: #73AD21; 
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	color : white;
}
#error {
    padding: 5px 5px 5px 5px ;
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	width:350px;
	font-size:11pt;
	background-color:#CE352C;
	color:white;
}
.ui-widget {
	font-family: Verdana,Arial,sans-serif;
	font-size: 1.1em;
}
.ui-widget .ui-widget {
	font-size: 1em;
}
.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
	font-family: Verdana,Arial,sans-serif;
	font-size: 1em;
}
.ui-widget-content {
	border: 1px solid #aaaaaa;
	background: #ffffff url() 50% 50% repeat-x;
	color: #222222;
}
.ui-widget-content a {
	color: #222222;
}
/* .ui-widget-header {
	border: 1px solid #aaaaaa;
	background:#2573ab  url(images/ui-bg_highlight-soft_75_cccccc_1x100.png) 50% 50% repeat-x;
	color: #e6f5fc;
	font-weight: bold;
} */
.ui-widget-header a {
	color: #222222;
}
.ui-datepicker-trigger{
background-image: url("webResources/images/Forms/Icn_Date_Picker.png");
height: 36px;
width: 36px;
background-color:white;
}
.ui-icon-circle-triangle-e{
background-image: url("webResources//Navbar/Icn_Right_Arrow.png");
}
.ui-icon-circle-triangle-w{
background-image: url("webResources/images/skip_backward.png");
}

</style>

<script type="text/javascript">
	var a = 1;
	$(document).ready(function() {
	if (a == 1) {
		$('#success').delay(800).fadeIn(400);
			
	}
	})

</script>

<title><spring:message code="admin.mapping.title" /></title>
</head>
<body>
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container2">

		<!-- Sidebar Start-->
		<c:choose>
   			 <c:when test="${edit != null}">
   			 	
   			 	<div id="left-sidebar">
					<h2 align="center" style="color: white ; padding-top:32px ">
					<spring:message code="admin.mapping.edit.leftNav.heading1"></spring:message> 
					</h2>
					<h3 style="color: white; padding:10px 0px 0px 20px">
						<spring:message code="admin.mapping.update.leftNav.heading2" />
					</h3>
				</div>
   			 
    		 </c:when>
    		 <c:otherwise>
    		 		<div id="left-sidebar">
					<h2 style="color: white ; padding-top:32px">
						<spring:message code="admin.mapping.leftNav.heading1" />
					</h2>
					<h3 style="color: white; padding-top:10px">
						<spring:message code="admin.mapping.leftNav.heading2" />
						</h3>
				</div>
    		 
    		 </c:otherwise>
    	</c:choose>
		<!-- Sidebar End-->

		<div id="content-container" style="padding-left: 40px">
			<form:form id="mappingForm" name="mappingForm" method="GET" commandName="mappingForm" action="saveMapping" style="width:700px">

				<div style="padding: 10px 0px 0px 0px;">
					<span class="bread1"><spring:message code="admin.associate.bread1" /> </span>
					<c:if test="${edit == null}">
						<input id="userActTyp" name="userActTyp" type="hidden" value="NEW"/> <!-- Action type for new mapping  -->
					<span class="bread2"><spring:message code="admin.mapping.leftNav.heading1" /></span>
					</c:if>
					<c:if test="${edit != null}">
						<input id="userActTyp" name="userActTyp" type="hidden" value="EDIT"/> <!-- Action type for update mapping  -->
					<span class="bread2"><spring:message code="admin.mapping.update.bread2" /></span>
					</c:if>
				</div>
				<table width="98%" style="padding: 10px 10px 10px 50px">
					<tr style="height: 20px; border-bottom-color: gray;">
						<c:choose>
							<c:when test="${edit != null}">
								<td width="40%"><span style="font-size: 20px"><spring:message
											code="admin.mapping.edit.leftNav.heading1"></spring:message> </span></td>
								<td width="60%" style="text-align: right;">

									<button title="Reset" type="reset" onclick="hideErrorDiv();"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Cancel.png" alt="Cancel">
									</button> 
									<button title="Save" type="submit"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png" alt="Save">
									</button></td>
									<tr>
									</tr>
							</c:when>
							<c:otherwise>
								<td width="40%"><span style="font-size: 20px"><spring:message
											code="admin.mapping.leftNav.heading1"></spring:message> </span></td>
								<td width="60%" style="text-align: right;">
									<button type="reset" title="Reset" onclick="hideErrorDiv();"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Cancel.png">
									</button>
									<button type="submit" title="Save"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png">
									</button></td>
							</c:otherwise>
						</c:choose>
					</tr>
				</table>
				
<div >
				<br>
				<div id="error"></div><div>
					<c:if test="${successCode != '000'}"> 
						<div id="divDuplicate" style="width: 680px;color: red;font-weight: bold;">${successMessage}</div>
					</c:if>
					<c:if test="${success == 'SUCCESS'}"> 
						<div id="success" class="success" style="width: 240px;">Mapping added successfully!</div>
					</c:if>
					<c:if test="${upSucess=='EDIT_SUCCESS'}"> 
						<div id="success" class="success" style="width: 280px;">Mapping details updated successfully!</div>
					</c:if>	
				</div>
				<br>
				<div class="line-separator"></div>
				<div class="panelContainer">
					<div class="containerA" style="padding-left:7px;padding-top: 7px;">
						<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 16px"><spring:message
									code="admin.mapping.details" />
							</span>
						</div>
						
						<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
							<table class="tableForm">
								<tbody><tr>
									<td><spring:message
									code="admin.mapping.auditor" /> </td>
									<td><div class="input-control select" >
											<select style="width: 200px; font-size: 14px; border-color: #919191" name="auditorName" id="auditorName">
												 <c:forEach items="${auditorsList}" var="auditorsList">
														<option value="${auditorsList.auditorName}"
														 ${auditorsList.auditorName == auditorNames ? 'selected="selected"' : ''}>${auditorsList.auditorName}</option>					 	
												</c:forEach>
											</select>
										</div></td>
								</tr> 
								<tr>
									<td><spring:message
									code="admin.mapping.associates" /> </td>
									<td><div class="input-control select" id="jobIdc">
											<select style="width: 200px; font-size: 14px; border-color: #919191" multiple="" name="associatesIds" required="true">
  												<c:if test="${edit != null}">
													<c:forEach items="${associatesList}" var="associatesList">
														<c:set var="match" value=""></c:set>
  															 <c:forEach items="${mappingRO.associatesIds}" var="id">
																<c:if test="${associatesList.associateId == id}">
																	<c:set var="match" value="true"></c:set>
																		 <option value="${associatesList.associateId}"
																		${associatesList.associateId == id ? 'selected="selected"' : ''}>${associatesList.associateName}</option>
																</c:if>
  															</c:forEach> 
  														<c:if test="${match != 'true' }">
  														 	<option value="${associatesList.associateId}">${associatesList.associateName}</option>
  														</c:if>
  													</c:forEach> 
  												</c:if>
  												<c:if test="${edit == null}">
  													<c:forEach items="${associatesList}" var="associatesList">
														<option value="${associatesList.associateId}"
															>${associatesList.associateName}</option>
   												 	</c:forEach>
  												</c:if>
											</select>
										</div></td>
								</tr>
							</tbody></table>
						</div>
					</div>
				</div>
				
				<div class="panelContainer">
					<div class="containerA" style="padding-left:7px;padding-top: 7px;">
						<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 16px"><spring:message
									code="admin.mapping.timeframe" />
							</span>
						</div>
						<div class="contentA" style="padding-top: 10px;padding-left: 7px;">
							<table class="tableForm" style="padding-left: 50px;">
								
								<tbody><tr>
									<td><spring:message
									code="admin.mapping.month" /> </td>
									<td><div class="input-control select" >
											<select style="width: 200px; font-size: 14px; border-color: #919191" name="month" id="month">
												<c:forEach items="${months}" var="months">
													<option value="${months}"
														${months == monthEO ? 'selected="selected"' : ''}>${months}</option>					 	
											 </c:forEach>
											</select>
										</div></td>
								</tr>
								<tr>
									<td><spring:message
									code="admin.mapping.year" /> </td>
									<td><div class="input-control select" id="jobIdc">
											<select style="width: 200px; font-size: 14px; border-color: #919191" name="year" >
												<c:forEach items="${years}" var="years">
													<option value="${years}"
														${years == yearEO ? 'selected="selected"' : ''}>${years}</option>					 	
											 </c:forEach>
											</select>
										</div></td>
								</tr>
							</tbody>
							</table>
						</div>
					</div>
				</div>
							
	</div>
	
			</form:form>


		</div>
<script type="text/javascript">
	
   $(function() {
		$('.headerA').click(function() {
			$(this).closest('.containerA').toggleClass('collapsed');
		});

	});
	</script>
		
</div>

	<jsp:include page="footer.jsp"></jsp:include>

</body>

</html>
