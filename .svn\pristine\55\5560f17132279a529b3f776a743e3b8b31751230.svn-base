package com.carefirst.qadb.utils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;


import java.util.LinkedHashMap;
import java.util.Set;

import org.apache.log4j.Logger;

import com.carefirst.qadb.constant.QADBConstants;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.xml.DomDriver;

public class ServiceUtil {
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);
		public static String objectToXML(Object obj) {
		
		XStream xstream = new XStream(new DomDriver());
		return xstream.toXML(obj);
		}
		
		public static String formatDate(String date) throws ParseException{
			String start_dt = date.substring(0 , 11);
			SimpleDateFormat sf=new SimpleDateFormat("MM-dd-yy");
		    String lDate=sf.format(new SimpleDateFormat("yyyy-MM-dd").parse(start_dt));
			return lDate;
		}
		
		public static String formattedDate(String date) throws ParseException{
		    SimpleDateFormat sf=new SimpleDateFormat("dd MMMM yyyy");
		    String lDate=sf.format(new SimpleDateFormat("yyyy-MM-dd").parse(date));
			return lDate;
		}
		
		public static void main(String[] args) throws ParseException{
			String date = "2014-03-05";
		    SimpleDateFormat sf=new SimpleDateFormat("MM-dd-yy");
		    String lDate=sf.format(new SimpleDateFormat("yyyy-MM-dd").parse(date));
		    String start_dt = "2014-03-05";
			SimpleDateFormat sf1=new SimpleDateFormat("dd MMMM yyyy");
		    String lDate1=sf1.format(new SimpleDateFormat("yyyy-MM-dd").parse(start_dt));
			System.out.println("-->"+lDate1);
		}
		
		public static LinkedHashMap<String, String> createMapFromString(String serializedForm){
			logger.info("Entry ServiceUtil createObjectFromMap ----->> ");
			LinkedHashMap<String, String> createdMap = new LinkedHashMap<String, String>();
			
				String[] firstPair = serializedForm.split("&");
				for(String pair : firstPair){
					String[] secondPair = pair.split("=");
					//log.debug("secondPair.length -- "+secondPair.length);
					if(secondPair.length>1){
						createdMap.put(formatTheString(secondPair[0]), formatTheString(secondPair[1]));
					}else{
						createdMap.put(formatTheString(secondPair[0]), "");
					}
				
			}
			//log.info("Exit ServiceUtil createObjectFromMap --->>>"+objectToXML(createdMap));
			//log.info(createdMap.toString());
			Set<String> keys = createdMap.keySet();
			for(String key:keys){
				createdMap.put(key, textEditorUtilsDecode(createdMap.get(key)));
			}
			return createdMap;
		}
		
		public static String formatTheString(String value){
			value = value.replace("%2F", "/");
			value = value.replace("+", " ");
			value = value.replace("%5B", "[");
			value = value.replace("%5D", "]");
			value = value.replace("%3C", "<");
			value = value.replace("%3E", ">");
			value = value.replace("%2C", ",");
			value = value.replace("%3A", ":");
			value = value.replaceAll("%0A", "\n");
			value = value.replace("%23", "#");
			value = value.replace("%24", "$");
			return value;
		}
		
		public static String textEditorUtilsDecode(String value){
			String arr[]={"!","#","$","&","'","(",")","*","+",",","/",":",";","=","?","@","[","]","\n"," ","\"","%","-",".","<",">","\\","^","_","`","{","|","}","~"};
			String arrVal[]={"PeRcEnTaGe21","PeRcEnTaGe23","PeRcEnTaGe24","PeRcEnTaGe26","PeRcEnTaGe27","PeRcEnTaGe28","PeRcEnTaGe29","PeRcEnTaGe2A","PeRcEnTaGe2B","PeRcEnTaGe2C","PeRcEnTaGe2F","PeRcEnTaGe3A","PeRcEnTaGe3B","PeRcEnTaGe3D","PeRcEnTaGe3F","PeRcEnTaGe40","PeRcEnTaGe5B","PeRcEnTaGe5D","PeRcEnTaGe0DPeRcEnTaGe0A","PeRcEnTaGe20","PeRcEnTaGe22","PeRcEnTaGe25","PeRcEnTaGe2D","PeRcEnTaGe2E","PeRcEnTaGe3C","PeRcEnTaGe3E","PeRcEnTaGe5C","PeRcEnTaGe5E","PeRcEnTaGe5F","PeRcEnTaGe60","PeRcEnTaGe7B","PeRcEnTaGe7C","PeRcEnTaGe7D","PeRcEnTaGe7E"};
			//log.info("arr.length"+arr.length+"arrObj.leng"+arrVal.length);
			for(int i=0;i<arr.length;i++){
				value = value.replaceAll("\\"+arr[i], arrVal[i]);
			}
			
			try{
				value=java.net.URLDecoder.decode(value);
			}catch(Error e){
				logger.error("Error decoding :"+value);
			}
			
			for(int i=0;i<arr.length;i++){
				value = value.replaceAll(arrVal[i],"\\"+arr[i]);
			}
			
			//log.info("Before :"+value);
			
			Set<String> latinKeys = TextEditorUtil.latinCharMap.keySet();
			for(String key:latinKeys){
				if(value.contains(key)){
					value =value.replaceAll(key,TextEditorUtil.latinCharMap.get(key));
				}
			}
			
			for(String val:QADBConstants.SPCLCHARLATINARR){
				if(value.contains(val)){
					value =value.replaceAll(val,"");
				}
			}
			
			//log.info("After :"+value);
			return value;
		}
}
