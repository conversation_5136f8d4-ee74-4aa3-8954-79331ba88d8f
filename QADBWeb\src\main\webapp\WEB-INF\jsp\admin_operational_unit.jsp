<!DOCTYPE html>
<html style="background-color: #dddfe1;">
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>
<head>
<link href="webResources/css/qadb.css" rel="stylesheet">

<script type="text/javascript" src="webResources/js/metro.min.js"></script>
<!-- Local JavaScript -->
<script type="text/javascript" src="webResources/js/qadb.js"></script>
<link href="webResources/css/iconFont.css" rel="stylesheet">
<link href="webResources/css/docs.css" rel="stylesheet">
<link href="webResources/js/prettify/prettify.css" rel="stylesheet">
<link href="webResources/css/jquery-ui.css" rel="stylesheet"> <!-- for datepick -->

<!-- Load JavaScript Libraries -->
<script type="text/javascript" src="webResources/js/qadb.js"></script>
<script type="text/javascript" src="webResources/js/datep.js"></script>
<script src="webResources/js/jquery/jquery.min.js"></script>
<script src="webResources/js/jquery/jquery.widget.min.js"></script>
<script src="webResources/js/jquery/jquery.dataTables.js"></script>
<script type="text/javascript" src="webResources/js/jquery-ui.js"></script>  <!-- for datepick -->
<style>
/* Col */
/* Collapse -Expand */
.headerA {
	background: url('webResources/images/Forms/Icn_Accordion_Collapse.png')
		no-repeat;
	background-position: right 0px;
	cursor: pointer;
}

.collapsed .headerA {
	background-image:
		url('webResources/images/Forms/Icn_Accordion_Expand.png');
}

.contentA {
	height: auto;
	min-height: 100px;
	overflow: hidden;
	transition: all 0.3s linear;
	-webkit-transition: all 0.3s linear;
	-moz-transition: all 0.3s linear;
	-ms-transition: all 0.3s linear;
	-o-transition: all 0.3s linear;
	background-color:#fafafa;
}

.collapsed .contentA {
	min-height: 0px;
	height: 0px;
}

.correct {
	display: none;
}
.container {
	width: 1040px;
	background-color: white;
	padding: 0.4px 15px 0px 15px;
}

.container2 {
	margin-left: auto;
	margin-right: auto;
	height: 150%;
	width: 1040px;
	background-color: white;
	padding: 0px 15px 0px 0px;
}

div.success {
	padding-left: 5px;
	padding-top: 5px;
	height: 30px; 
	background: #73AD21; 
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	color : white;
}

div.errorInfo {
    padding: 5px 5px 5px 5px ;
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	width:350px;
	font-size:11pt;
	background-color:#CE352C;
	color:white;
}

#error {
    padding: 5px 5px 5px 5px ;
	display: none;
	box-shadow: 5px 5px 5px #888888;
	border-radius: 3px;
	width:350px;
	font-size:11pt;
	background-color:#CE352C;
	color:white;
}
.hidden {
	display: none;
}

/* To From Datepick */

.ui-widget {
	font-family: Verdana,Arial,sans-serif;
	font-size: 1.1em;
}
.ui-widget .ui-widget {
	font-size: 1em;
}
.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
	font-family: Verdana,Arial,sans-serif;
	font-size: 1em;
}
.ui-widget-content {
	border: 1px solid #aaaaaa;
	background: #ffffff url(images/ui-bg_flat_75_ffffff_40x100.png) 50% 50% repeat-x;
	color: #222222;
}
.ui-widget-content a {
	color: #222222;
}
/* .ui-widget-header {
	border: 1px solid #aaaaaa;
	background:#2573ab  url(images/ui-bg_highlight-soft_75_cccccc_1x100.png) 50% 50% repeat-x;
	color: #e6f5fc;
	font-weight: bold;
} */
.ui-widget-header a {
	color: #222222;
}
.ui-datepicker-trigger{
background-image: url("webResources/images/Forms/Icn_Date_Picker.png");
height: 36px;
width: 36px;
background-color:white;
}
.ui-icon-circle-triangle-e{
background-image: url("webResources//Navbar/Icn_Right_Arrow.png");
}
.ui-icon-circle-triangle-w{
background-image: url("webResources/images/skip_backward.png");
}
#popupD {
	width: 100%;
	height: 100%;
	opacity: 0.5;
	top: 0;
	left: 0;
	display: none;
	position: fixed;
	background-color: #F8F8F8;
	overflow: auto
}

div#popupOperationalUnitD {
	position: fixed;
	border: 1px solid #383838;
	background-color: #FFFFFF;
	left: 55%;
	top: 40%;
	margin-left: -202px;
	display: none;
	font-family: 'Raleway', sans-serif
}

</style>
<script type="text/javascript">
	var a = 1;
		$(document).ready(function() {
		if (a == 1) {
			$('#success').delay(800).fadeIn(400);
				
		}
	})
</script>


<title><spring:message code="admin.opUnit.title" /></title>
</head>
<body>
	<jsp:include page="../jsp/header.jsp"></jsp:include>
	<div class="container2">

		<!-- Sidebar Start-->
		<c:choose>
   			 <c:when test="${edit != null}">
   			 	<div id="left-sidebar">
					<h2 align="center" style="color: white ; padding-top:32px ">
					${opUnitRO.unitName} 
					</h2>
					<h3 style="color: white; padding:10px 0px 0px 30px">
						<table>
						<tr>
                        <td style="padding-right:5px;"><img alt="errors" src="webResources/images/Sidebar/Icn_Unit_ID.png"></td>
						<td>Unit ID </td>
                		</tr>
                		<tr>
                		<td></td>
                		<td>${opUnitRO.unitId}</td>
                		</tr>
				</table>
						</h3>
						
				<div class="grid fluid">
				<div class="row">
					<div class="span4">

						<nav class="sidebar light">
							<ul>

								<li class="notActive" id="one"><a style="cursor: pointer;" title="General" id="preview" onclick="switchVisible();"><img
										
										src="webResources/images/Tabs/Operations Units/Icn_Operations_Unit_General.png">&nbsp;&nbsp;&nbsp;&nbsp;General</a>
								</li>

								<li class="notActive"><a style="cursor: pointer;" title="Associates" id="preview2" onclick="switchVisibles();"><img 
										src="webResources/images/Tabs/Operations Units/Icn_Operations_Unit_Associates.png">&nbsp;&nbsp;&nbsp;&nbsp;
										Associates</a>
								</li>

							</ul>
						</nav>

					</div>

				</div>
			</div>
				</div>
   			 	
    		 </c:when>
    		 <c:otherwise>
    		 		<div id="left-sidebar">
					<h2 style="color: white ; padding-top:32px">
						<spring:message code="admin.opUnit.leftNav.heading1" />
					</h2>
					<h3 style="color: white; padding-top:10px">
						<spring:message code="admin.opUnit.leftNav.heading2" />
						</h3>
				</div>
    		 
    		 </c:otherwise>
    		 
    		 
    	</c:choose>
		
	
		<!-- Sidebar End-->

		<div id="content-container" style="padding-left: 40px">

<!--change form action depending upon add /update action  -->

	<%String formAction=""; %>

 	<c:if test="${edit != null}"> 
  		<% formAction="updateOperationalUnit" ;%>
    </c:if>
	<c:if test="${edit == null}"> 
  		<% formAction="saveOpUnit" ;%>
    </c:if>

			<form:form id="opUnitForm" name="opUnitForm" method="GET" onsubmit="return ValidatesOU()" commandName="opUnitForm" action="<%=formAction%>" style="width:700px">
			<input type="hidden" id="opEndDt" value="${opUnitRO.effectiveDateEnd}"/>
			<input type="hidden" id="opStartDt" value="${opUnitRO.effectiveDateStart}"/>

				<div style="padding: 10px 0px 0px 0px;">
					<span class="bread1"><spring:message code="admin.associate.bread1" /> </span>
					<c:if test="${edit == null}">
					<span class="bread2"><spring:message code="admin.opUnit.leftNav.heading1" /></span>
					</c:if>
					<c:if test="${edit != null}">
					<span class="bread2"><spring:message code="admin.opUnit.edit.bread2" /></span>
					</c:if>
				</div>
				<table width="98%" style="padding: 10px 10px 10px 50px">
					<tr style="height: 20px; border-bottom-color: gray;">


						<c:choose>
							<c:when test="${edit != null}">
								<td width="40%"><span style="font-size: 20px">${opUnitRO.unitName}
										 </span></td>
								<td width="60%" style="text-align: right;">

									<button title="Cancel" type="reset" onclick="hideErrorDiv();"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Cancel.png" alt="Cancel">
									</button> 
									<a href="#" onclick="divdel_show();">
										<button title="Delete" type="button"
											style="background-color: transparent; border-color: transparent; size: 10px;">
											<img 
												src="webResources/images/Actions/Icn_Delete.png" >
										</button>
									</a>
									<button title="Save" type="submit"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png" alt="Save">
									</button></td>
									
									
									<tr>
									
									
									
									</tr>
									
							</c:when>

							<c:otherwise>
								<td width="40%"><span style="font-size: 20px"><spring:message
											code="admin.opUnit.leftNav.heading1"></spring:message> </span></td>
								<td width="60%" style="text-align: right;">
									<button type="reset" title="Cancel" onclick="hideErrorDiv();"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Cancel.png">
									</button>
									<button type="submit" title="Save"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png">
									</button></td>
							</c:otherwise>
						</c:choose>
					</tr>
				</table>
				
				
				<!--Success Msgs  -->
				<div id="error" ></div><div>
					<c:if test="${(successCode != '000') && (successCode != '201') && (successCode != null)}"> 
						<div id="divDuplicate" style="width: 240px;color: red;font-weight: bold;">${successMessage}</div>
					</c:if>
					<c:if test="${success == 'SUCCESS'}"> 
						<div id="success" class="success" style="width: 240px;">Operational Unit added successfully!</div>
					</c:if>
					<c:if test="${upSucess=='SUCCESS'}"> 
						<div id="success" class="success" style="width: 280px;">Operational Unit updated successfully!</div>
					</c:if>	
					<c:if test="${delSucess=='SUCCESS'}"> 
						<div id="success" class="success" style="width: 280px;">Operational Unit deleted successfully!</div>
					</c:if>
					<%-- <c:if test="${(fn:contains(success, 'System')) || (fn:contains(upSucess, 'System')) || (fn:contains(delSucess, 'System'))}">
						<div id="success" class="errorInfo" style="width: 300px;font-size:10pt">System Error, please try again after some time !</div>
					</c:if> --%>
					<c:if test="${successCode == '201'}">
						<div id="success" class="errorInfo" style="width: 300px;">System Error, please try again after some time !</div>
					</c:if>		
				</div>
				<br>
				<div id="indicate1" class="line-separator"></div><br>
				<div id="indicate" style="color:red">* indicates required</div><br>
		<div id="general">
			<div class="panelContainer">
				<div class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">	
						<span style="color: #0070c0; font-size: 16px"><spring:message code="admin.opUnit.generalInformation" />
						</span>
					</div>	<!-- <div class="line-separator"></div> -->
				<div class="contentA" style="padding-top: 10px;padding-left: 7px">
				<table class="tableForm" style="padding-left: 50px;">
					<tr>
						<td><spring:message code="admin.opUnit.generalInformation.name" /><span style="color:red">*</span></td>
						<td><div id="unitNamec" class="input-control text">
								<input type="text" id="unitName" name="unitName" autofocus="autofocus" placeholder="Unit Name" value="${opUnitRO.unitName}" />
								&nbsp;<span id="errmsguname" style="color:red">
							</div>
						</td>
					</tr>
					<tr style="display: none;">
						<c:if test="${edit != null}">
						<td style="width:33.2%"><spring:message code="admin.opUnit.generalInformation.enabled" /></td>
						<td><div class="input-control checkbox">
							<c:choose>
								<c:when test="${opUnitRO.status == 'Disabled'}">
									<label> <input type="checkbox" name="status" value="N"/> <span class="check"></span>
									 </label>
								</c:when>
								
								<c:otherwise>
									<label> <input type="checkbox" name="status" checked="true" value="Y"/> <span class="check"></span>
									 </label>
								
								</c:otherwise>
							</c:choose>
						
								
							</div></td>
						</c:if>
					</tr>
					<tr>
						<td><span style="color: #0070c0; font-size: 14px"><spring:message code="admin.opUnit.effectiveDate" /></span></td>
						
					</tr>
					<tr>
						<td><spring:message code="admin.opUnit.effectiveDate.start" /><span style="color:red">*</span></td>
						
						<td><div id="datepickop2c" class="input-control text">
							<input id="datepickop2" size="50" style="width: 200px" class="form-control" name="effectiveDateStart" value="${opUnitRO.effectiveDateStart}"/>
							&nbsp;<span id="errmsgdatepickop2" style="color:red">
						</div>
						</td>
					</tr>
					
					<tr>
						<td><spring:message code="admin.opUnit.effectiveDate.end" /><span style="color:red">*</span></td>
						
						<td><div id="datepickop3c" class="input-control text">
							<input id="datepickop3" size="50" style="width: 200px" class="form-control" name="effectiveDateEnd" value="${opUnitRO.effectiveDateEnd}"/>
							&nbsp;<span id="errmsgdatepickop3" style="color:red">
						</div>
						</td>
					</tr>
					

				</table>
			</div>
			</div>
			</div>	
			
			<div class="panelContainer">
				<div class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
						<span style="color: #0070c0; font-size: 16px"><spring:message code="admin.opUnit.organization" /></span>
						<!-- <div class="line-separator"></div> -->
					</div>
					<div class="contentA" style="padding-top: 10px;padding-left: 7px">
						<table class="tableForm">
							<tr>
								<td><spring:message code="admin.opUnit.organization.operation" /></td>
								<td><div class="input-control select">
								<c:choose>
									<c:when test="${edit != null}">
										<select name="operationId"
											style="width: 200px; font-size: 14px; border-color: #919191">
											<c:forEach items="${operations}" var="operations">
												<option value="${operations.operationId}"
													${operations.operationId == opUnitRO.operationId ? 'selected="selected"' : ''}>${operations.operation}</option>
											</c:forEach>
										</select>
									</c:when>
									<c:otherwise>
										<select name="operationId"
											style="width: 200px; font-size: 14px; border-color: #919191">
											<c:forEach items="${operations}" var="operations">
												<option value="${operations.operationId}"
													${operations.operationId == 1 ? 'selected="selected"' : ''}>${operations.operation}</option>
											</c:forEach>
										</select>
									</c:otherwise>
								</c:choose>
									</div>
								</td>
							</tr>
							<tr>
								<td><spring:message code="admin.opUnit.organization.division" /></td>
								<td><div class="input-control select">
								
									<c:choose>
									<c:when test="${edit != null}">
										<select name="divisionId"
											style="width: 200px; font-size: 14px; border-color: #919191">
											<c:forEach items="${divisions}" var="divisions">
												<option value="${divisions.divisionId}"
													${divisions.divisionId== opUnitRO.divisionId ? 'selected="selected"' : ''}>${divisions.division}</option>
											</c:forEach>
										</select>
									</c:when>
									<c:otherwise>
										<select name="divisionId"
											style="width: 200px; font-size: 14px; border-color: #919191">
											<c:forEach items="${divisions}" var="divisions">
												<option value="${divisions.divisionId}"
													${divisions.divisionId== 7 ? 'selected="selected"' : ''}>${divisions.division}</option>
											</c:forEach>
										</select>
									</c:otherwise>
									</c:choose>
										
									</div>
								</td>
							</tr>
							<tr>
								<td><spring:message code="admin.opUnit.organization.supervisor" /></td>
								<td><div class="input-control select">
										<select name="supervisorId"
											style="width: 200px; font-size: 14px; border-color: #919191">
											<c:forEach items="${supervisors}" var="supervisors">
												<option value="${supervisors.supervisorId}"
													${supervisors.supervisorId== opUnitRO.supervisorId ? 'selected="selected"' : ''}>${supervisors.supervisor}</option>
													<!--  -->
											</c:forEach>
										</select>
									</div>
								</td>
							</tr>
							<tr>
								<td><spring:message code="admin.opUnit.organization.manager" /></td>
								<td><div class="input-control select">
										<select name="managerId"
											style="width: 200px; font-size: 14px; border-color: #919191">
											<c:forEach items="${managers}" var="managers">
												<option value="${managers.managerId}"
													${managers.managerId== opUnitRO.managerId ? 'selected="selected"' : ''}>${managers.manager}</option>
													<!--  -->
											</c:forEach>
										</select>
									</div>
								</td>
							</tr>
							<tr>
								<td><spring:message code="admin.opUnit.organization.director" /></td>
								<td><div class="input-control select">
										<select name="directorId"
											style="width: 200px; font-size: 14px; border-color: #919191">
											<c:forEach items="${directors}" var="directors">
												<option value="${directors.directorId}"
												${directors.directorId== opUnitRO.directorId ? 'selected="selected"' : ''}>${directors.director}</option>
													<!--  -->
											</c:forEach>
										</select>
									</div>
								</td>
							</tr>
						</table>
					</div>
				</div>
				</div>
				
				<div class="panelContainer">
					<div class="containerA" style="padding-left:7px;padding-top: 7px;">
						<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
							<span style="color: #0070c0; font-size: 16px"><spring:message code="admin.opUnit.office" /></span>
							<!-- <div class="line-separator"></div> -->
						</div>
						<div class="contentA" style="padding-top: 10px;padding-left: 7px">
							<table class="tableForm" >
								<tr>
									<td style="width:33.2%"><spring:message code="admin.opUnit.location" /></td>
									<td><div class="input-control select">
											<select name="locationId"
												style="width: 200px; font-size: 14px; border-color: #919191">
												<c:forEach items="${locations}" var="locations">
													<option value="${locations.locationId}"
														${locations.locationId== opUnitRO.locationId ? 'selected="selected"' : ''}>${locations.locations}</option>
												</c:forEach>
											</select>
										</div>
									</td>
								</tr>
			
			
							</table>
						</div>
					</div>
				</div>
				
				
					<div class="line-separator"></div>
				<table width="98%" style="padding: 10px 10px 10px 50px">
					<tr style="height: 20px; border-bottom-color: gray;">

								<td width="30%">
									<div style="float: left">
						<a href="#top" style="float: left;"><spring:message code="qadb.backToTop" /></a>
					</div>
								</td>
								<td width="70%" style="text-align: right;">
									<button title="Reset" type="reset" onclick="hideErrorDiv();"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Cancel.png">
									</button>
									<c:if test="${edit != null}">
									<a href="#" onclick="divdel_show();">
										<button title="Delete" type="button"
											style="background-color: transparent; border-color: transparent; size: 10px;">
											<img 
												src="webResources/images/Actions/Icn_Delete.png" >
										</button>
									</a>
									</c:if>
									<button title="Save" type="submit"
										style="background-color: transparent; border-color: transparent; size: 10px;">
										<img src="webResources/images/Actions/Icn_Save.png">
									</button></td>
					</tr>
				</table>
	</div>
				<div class="line-separator"></div>
				<div id = "associates" style="width: 750px">
				<div class="panelContainer">
				<div class="containerA" style="padding-left:7px;padding-top: 7px;">
					<div class="headerA" style="padding: 0px 10px 10px 0px;border-bottom: 1px solid #EAEAEA">
						<span style="color: #0070c0; font-size: 16px"><spring:message
								code="admin.opUnit.update.leftNav.tag2" />
						</span>
					</div>
					<div class="contentA" style="padding-top: 10px">				
				<table class="table striped hovered dataTable" id="searchDatatable"
				width="700px">
				<thead>
					<tr style="height: 30px">

						<td class="text-left" font-size="25px"><spring:message code="admin.opUnit.generalInformation.name" /></td>
						<td class="text-left" font-size="25px">Associate Id</td>
						<td class="text-left" font-size="25px">Start Date</td>
						<td class="text-left" font-size="25px">End Date</td>
						


					</tr>
				</thead>

				<tbody>
					<c:forEach items="${associatesRS}" var="rs">
						<tr style="height: 30px">
							<td>${rs.assoName}</td>
							<td>${rs.assoId}</td>
							<td>${rs.assoStartDate}</td>
							<td>${rs.assoEndDate}</td>
						</tr>
					</c:forEach> 

				</tbody>


			</table>
			</div>
			</div>
			</div>
			<!-- </div> -->
			 <script>
				$(function() {
					$('#searchDatatable').dataTable({
										
										
					"columns" : [
					
					
					{
					
            }, {}, {}, {} ]
									});
				});
			</script> 

			
		
		
		
	
		
		</div>
				

			</form:form>

	<div id="popupD">
		<!-- popup claim records Starts Here -->


	</div>
	<div id="popupOperationalUnitD"
		style="text-align: center; padding: 25px 25px 25px 25px;border-radius: 5px;
    box-shadow: 5px 5px 5px #888888;">
		<img id="close" src="webResources/images/Misc/Icn_Close2.png"
			onclick="divdel_hide()"> <span style="font-size: 15px;"><b>
				Are you sure to delete this operational unit?</b> </span>
		<br>
		<br>
		<div align="center" style="width: 450">
		<table>	
			<tr>
					
				<a  onclick="divdel_hide();">	<button type="reset" class="button inverse">No</button> </a>
				<a  href="deleteOpUnit">	<button style="background-color: #298fd8"
							class="button default">Yes</button> </a>
				</tr>
			
		</table>	
			
		</div>
	</div>

		</div>


		</div>
		
		
		
		
		
	<script type="text/javascript">
	/*Start end date  */
		$("#datepickop2").datepicker({
			showOn:"button",
	    	onSelect: function(selected) {
	     	 $("#datepickop3").datepicker("option","minDate", selected)
	   		 },
		});
		$("#datepickop3").datepicker({
			showOn:"button",
			onSelect : function(selected) {
			$("#datepickop2").datepicker("option","maxDate", selected)
			}
		}); 
		/* Associates tabs */
		
	$(function(){
    $("#associates").hide();
    $("#preview").on("click", function switchVisible(){
        $("#general").show();
        $("#associates").hide();
        $("#indicate").show();
        $("#indicate1").show();
    });
     $("#preview2").on("click", function switchVisibles(){
        $("#associates").show();
        $("#general").hide();
        $("#indicate").hide();
        $("#indicate1").hide();
        
    });
	});
		
	/* Active tab */
	$(function(){
    $("#one").addClass("active");
    $(".notActive").click(function (e) {
	$(this).addClass("active").siblings().removeClass("active");
	});
    });
		
   
   $("#unitNamec").keypress(function (e) {
		if ((e.which == 0) || e.which == 8) {}
		else{
			var check = maxLimitForName(e,"errmsguname","unitName");
			if(check==false){
			return false;
		}
		}
	});
  	$("#unitNamec").keydown(function (e) {
		if(e.ctrlKey && e.keyCode==86){
		   if ((e.which == 0) || e.which == 8) {}
			else{
				var check = maxLimitForName(e,"errmsguname","unitName");
				if(check==false){
				return false;
			}
		}
		}
	});
  $("#unitNamec").keyup(function (e) {
		if($("#unitName").val().length>49){
		var name = $("#unitName").val().substring(0,50);
		document.getElementById("unitName").value=name;
		$("#errmsguname").html("Maximum limit of 50 char.").show().fadeOut("slow");
		}
	});
	
   jQuery(function($){
  
  	 $("#datepickop2").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("opStartDt").value);
  	 $("#datepickop3").mask("99/99/9999",{placeholder:"MM/DD/YYYY"}).val(document.getElementById("opEndDt").value);
   
	});
   
		$(document).ready(function() {
			if(document.getElementById("opEndDt").value==""){
				$("#datepickop3").datepicker( "setDate" , "12/31/9999" );
			}
			
		})
   
   $(function() {
		$('.headerA').click(function() {
			$(this).closest('.containerA').toggleClass('collapsed');
		});

	});
	</script>
		
	</div>

	<jsp:include page="footer.jsp"></jsp:include>

</body>

</html>
