package com.carefirst.audit.model;

public class Associate{

	private String userid;
	private String userActyp;
	private String searchType;
	
	private String associateDBId;
	private String associateId;
	private String firstName;
	private String middleName;
	private String lastName;
	private String associateName;

	private String jobTitleId;
	private String jobTitles;
	private String dateOfHire;
	
	private String locationId;
	private String locations;
	private String workPhone;
	private String workUnitId;
	private String workUnitStartDate;
	private String workUnitEndDate;
	private String facetsId;
	private String auditingStatus;
	private String status;
	private String disableDate;
	private String comments;
	
	private String superVisor ;
	private String manager ;
	private String director;
	
	private String count;
	private String successMsg;
	private String sucessCode;
	
	private String dcnCheck;
	private String userGroup;
	
	public String getUserGroup() {
		return userGroup;
	}

	public void setUserGroup(String userGroup) {
		this.userGroup = userGroup;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getAssociateDBId() {
		return associateDBId;
	}

	public void setAssociateDBId(String associateDBId) {
		this.associateDBId = associateDBId;
	}

	public String getCount() {
		return count;
	}

	public void setCount(String count) {
		this.count = count;
	}

	public String getDcnCheck() {
		return dcnCheck;
	}

	public void setDcnCheck(String dcnCheck) {
		this.dcnCheck = dcnCheck;
	}

	public String getSearchType() {
		return searchType;
	}

	public void setSearchType(String searchType) {
		this.searchType = searchType;
	}

	public String getFacetsId() {
		return facetsId;
	}

	public void setFacetsId(String facetsId) {
		this.facetsId = facetsId;
	}

	public String getWorkUnitId() {
		return workUnitId;
	}

	public void setWorkUnitId(String workUnitId) {
		this.workUnitId = workUnitId;
	}

	public String getWorkUnitStartDate() {
		return workUnitStartDate;
	}

	public void setWorkUnitStartDate(String workUnitStartDate) {
		this.workUnitStartDate = workUnitStartDate;
	}

	public String getWorkUnitEndDate() {
		return workUnitEndDate;
	}

	public void setWorkUnitEndDate(String workUnitEndDate) {
		this.workUnitEndDate = workUnitEndDate;
	}

	public String getDisableDate() {
		return disableDate;
	}

	public void setDisableDate(String disableDate) {
		this.disableDate = disableDate;
	}


	
	
	public String getUserActyp() {
		return userActyp;
	}

	public void setUserActyp(String userActyp) {
		this.userActyp = userActyp;
	}

	public String getSuccessMsg() {
		return successMsg;
	}

	public void setSuccessMsg(String successMsg) {
		this.successMsg = successMsg;
	}

	public String getSucessCode() {
		return sucessCode;
	}

	public void setSucessCode(String sucessCode) {
		this.sucessCode = sucessCode;
	}

	public String getUserid() {
		return userid;
	}

	public void setUserid(String userid) {
		this.userid = userid;
	}

	public String getAssociateId() {
		return associateId;
	}

	public void setAssociateId(String associateId) {
		this.associateId = associateId;
	}

	public String getJobTitleId() {
		return jobTitleId;
	}

	public void setJobTitleId(String jobTitleId) {
		this.jobTitleId = jobTitleId;
	}

	public String getJobTitles() {
		return jobTitles;
	}

	public void setJobTitles(String jobTitles) {
		this.jobTitles = jobTitles;
	}

	public String getDateOfHire() {
		return dateOfHire;
	}

	public void setDateOfHire(String dateOfHire) {
		this.dateOfHire = dateOfHire;
	}

	public String getLocationId() {
		return locationId;
	}

	public void setLocationId(String locationId) {
		this.locationId = locationId;
	}

	public String getLocations() {
		return locations;
	}

	public void setLocations(String locations) {
		this.locations = locations;
	}

	public String getWorkPhone() {
		return workPhone;
	}

	public void setWorkPhone(String workPhone) {
		this.workPhone = workPhone;
	}

	public String getAuditingStatus() {
		return auditingStatus;
	}

	public void setAuditingStatus(String auditingStatus) {
		this.auditingStatus = auditingStatus;
	}

	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}


	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getMiddleName() {
		return middleName;
	}

	public void setMiddleName(String middleName) {
		this.middleName = middleName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getSuperVisor() {
		return superVisor;
	}

	public void setSuperVisor(String superVisor) {
		this.superVisor = superVisor;
	}

	public String getManager() {
		return manager;
	}

	public void setManager(String manager) {
		this.manager = manager;
	}

	public String getDirector() {
		return director;
	}

	public void setDirector(String director) {
		this.director = director;
	}
	
	public String getAssociateName() {
		return associateName;
	}

	public void setAssociateName(String associateName) {
		this.associateName = associateName;
	}
	
	
	public void getDetails(String byName) {
		// TODO Auto-generated method stub
		
	}
}
