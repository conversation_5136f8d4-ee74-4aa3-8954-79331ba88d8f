package com.carefirst.qadb.dao;

import java.sql.SQLException;
import java.util.List;

import com.carefirst.audit.model.ErrorDetails;

public interface ErrorDetailsDAO {
	
	public List<ErrorDetails> getErrorCodes(String pageType, String userGrp) throws SQLException;
	
	public List<ErrorDetails> getSpeciality(String userGrp) throws SQLException;
	
	public List<ErrorDetails> getRootCause(String userGrp) throws SQLException;
	
	public List<ErrorDetails> getErrorTypes(String rootCauseId) throws SQLException;
	
	public List<ErrorDetails> getAllErrorTypes() throws SQLException;

	public List<ErrorDetails> getErrorInfo(String auditId) throws SQLException;
	
}
