<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:util="http://www.springframework.org/schema/util"
	xmlns:p="http://www.springframework.org/schema/p" xmlns="http://www.springframework.org/schema/beans"
	xmlns:aop="http://www.springframework.org/schema/aop" xmlns:context="http://www.springframework.org/schema/context"
	xmlns:jee="http://www.springframework.org/schema/jee" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
       http://www.springframework.org/schema/aop
       http://www.springframework.org/schema/aop/spring-aop-3.1.xsd       
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context-3.1.xsd
       http://www.springframework.org/schema/jee
       http://www.springframework.org/schema/jee/spring-jee-3.1.xsd
       http://www.springframework.org/schema/tx
       http://www.springframework.org/schema/tx/spring-tx-3.1.xsd
       http://www.springframework.org/schema/oxm 
       http://www.springframework.org/schema/oxm/spring-oxm-3.1.xsd
       http://www.springframework.org/schema/util 
	   http://www.springframework.org/schema/util/spring-util-2.0.xsd">

	<context:component-scan base-package="com.carefirst.*" />
	<context:annotation-config/>

	<!-- Internalization use tag <support:message> -->

	<bean id="messageSource"
		class="org.springframework.context.support.ResourceBundleMessageSource">
		<property name="basenames">
			<list>
				<value>qadb</value>
			</list>
		</property>
	</bean>	
	


	
	<bean id="jdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">  
		<property name="dataSource" ref="dataSource"></property>  
	</bean> 
	 <!--
	<bean id="jobTitlesDAOImpl" class="com.carefirst.qadb.dao.JobTitlesDAOImpl">  
		<property name="jdbcTemplate" ref="jdbcTemplate"></property>  
	</bean>
	-->
	
	<!-- telling container to take care of annotations stuff -->
	<context:annotation-config />



	<!-- adding view resolver to show jsp's on browser -->


	<!-- declare beans -->
  	<!-- <bean id="claimService" class="com.carefirst.qadb.service" /> -->
  	<!-- Audit module -->
  	<bean id="auditDetailsDao" class="com.carefirst.qadb.dao.AuditDetailsDAOImpl" />
  	<bean id="auditSearchDao" class="com.carefirst.qadb.dao.AuditSearchDAOImpl" />
  	<bean id="errorDetailsDao" class="com.carefirst.qadb.dao.ErrorDetailsDAOImpl" />
  	
  	<!-- Report module -->
  	<bean id="AuthorizationService" class="com.carefirst.qadb.service.AuthorizationServiceImpl" />
	<bean id="scoresReportsDAO" class="com.carefirst.qadb.dao.ScoresReportsDAOImpl" />
	<bean id="UserReportsDAO" class="com.carefirst.qadb.dao.UserReportsDAOImpl" />
	<bean id="performanceGroupErrorSheetDAO" class="com.carefirst.qadb.dao.PerformanceGroupErrorSheetDAOImpl" />
  	<bean id="ClaimsAuditAssessmentReportDAO" class="com.carefirst.qadb.dao.ClaimsAuditAssessmentReportDAOImpl" />
  	<bean id="EditCodeReportsDAO" class="com.carefirst.qadb.dao.EditCodeReportsDAOImpl" />
  	<bean id="InSampleOutOfSampleReportsDAO" class="com.carefirst.qadb.dao.InSampleOutOfSampleReportsDAOImpl" />
  	
  	<!-- Admin module -->
  	<bean id="associateDao" class="com.carefirst.qadb.dao.AssociateDAOImpl" />
  	<bean id="operationalUnitDao" class="com.carefirst.qadb.dao.OperationalUnitDAOImpl" />
  	<bean id="errorCodesDAO" class="com.carefirst.qadb.dao.ErrorCodesDAOImpl" />
  	<bean id="performanceGroupDAO" class="com.carefirst.qadb.dao.PerformanceGroupDAOImpl" />
  	<bean id="jobTitlesDao" class="com.carefirst.qadb.dao.JobTitlesDAOImpl" />
  	<bean id="specialityDao" class="com.carefirst.qadb.dao.SpecialityDAOImpl" />
  	<bean id="rootCauseDAO" class="com.carefirst.qadb.dao.RootCauseDAOImpl" />
  	<bean id="mappingDAO" class="com.carefirst.qadb.dao.MappingDAOImpl" />
  	
	<!-- declare datasource bean -->
	 <bean id="dataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
	    <property name="jndiName" value="java:comp/env/jdbc/QADBApp"/>
	</bean>
	
</beans>
