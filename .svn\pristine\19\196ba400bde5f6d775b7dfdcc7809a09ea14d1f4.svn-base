package com.carefirst.qadb.controller;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.log4j.Logger;
import org.apache.log4j.PropertyConfigurator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.SessionAttributes;
import org.springframework.web.servlet.ModelAndView;

import com.carefirst.audit.model.RecentAudits;
import com.carefirst.audit.model.UserInfo;
import com.carefirst.qadb.constant.QADBConstants;
import com.carefirst.qadb.dao.AuditDetailsDAO;
import com.carefirst.qadb.logger.LogContext;
import com.carefirst.qadb.logger.LogContextManager;
import com.carefirst.qadb.service.AuthorizationServiceImpl;

@Controller
@SessionAttributes("UserInfo")
public class HomeController {
	public static boolean islogloaded = false;
	static {
        loadProperties();
    }
    
	private static void loadProperties() {
    	if (! (islogloaded)){

    	    //String sdlc_stage= System.getProperty("SDLC_STAGE");
    		String sdlc_stage= "local".toLowerCase();
    		Properties logProperties = new Properties();
    		String prefix = null;		

    		try{
    			if ("dev".equalsIgnoreCase(sdlc_stage)) {                 
    				prefix =(new StringBuffer("dev").append("Qadb")).toString();
    			}else if ("sit".equalsIgnoreCase(sdlc_stage)) {
    				prefix = (new StringBuffer("sit").append("Qadb")).toString();
    			}else {
    				prefix = (new StringBuffer("local").append("Qadb")).toString();
    			}
    			logProperties.load(HomeController.class.getClassLoader().getResourceAsStream(prefix+"log4j.properties"));			
    			PropertyConfigurator.configure(logProperties);	
    			islogloaded = true;

    		}catch (Exception e) {
    			//e.printStackTrace();	
    		}
    	}
    }
	
	
	static Logger logger = Logger.getLogger(QADBConstants.QADBWEBAPP_LOGGER);
	
	@Autowired  
	AuditDetailsDAO auditDetailsDao;
	
	@RequestMapping(value = "/login")
	public ModelAndView login() {
		UserInfo user = new UserInfo(); // we want to store user data into this
										// object, returning back to jsp
		ModelAndView mv = new ModelAndView("login", "usermodel", user);

		return mv;
	}
	@RequestMapping(value = "/", method = RequestMethod.GET)
	public ModelAndView home(@ModelAttribute UserInfo user, Model model,
			HttpSession session,HttpServletRequest request) {

		LogContext logContext = new LogContext();
		logContext.setSessionId(session.getId());
		logContext.setUserId(user.getUserId());
		LogContextManager.getThreadlocalObj();
		LogContextManager.set(logContext);
		
		
		logger.debug("Inside Home Controller. Logs are configured.");
		// System.out.println("UserId : " + user.getUserId());
		String name=request.getHeader("iv-user");
		logger.debug("name--->"+request.getHeader("iv-user"));
		session.setAttribute("username", name);
        logger.debug("*** Entry recentsHome method ***");
		
        ModelAndView mv = new ModelAndView("home");
        
        logger.debug("Grp "+request.getHeader("iv-groups"));
		String b[] = {request.getHeader("iv-groups")};
		//String b[] = {"Contractor","PeopleSoft_ELM_User","onestop_users","qadb_users","qadb-superadmin_users"};
		logger.debug("b[]: "+b);
		logger.debug("ArrayTostring: "+Arrays.toString(b));
		logger.debug("ArrayAs list: "+Arrays.asList(b));

		if((!(((Arrays.toString(b)).replace("[", "")).replace("]", "")).equals("null"))||(b.length != 0)){
			logger.debug("b[] "+b);
			logger.debug("ArrayTostring: "+Arrays.toString(b));
			logger.debug("ArrayAs list: "+Arrays.asList(b));
			if(((Arrays.toString(b)).contains("qadb-superadmin_users"))    ||
					((Arrays.toString(b)).contains("qadb-samd-admin_users"))    || ((Arrays.toString(b)).contains("qadb-cd-admin_users"))	||
					((Arrays.toString(b)).contains("qadb-samd-auditor_users"))  || ((Arrays.toString(b)).contains("qadb-cd-auditor_users"))	||
					((Arrays.toString(b)).contains("qadb-samd-readonly_users")) || ((Arrays.toString(b)).contains("qadb-cd-readonly_users")) ){
				logger.debug("*** Authorization successful for Home page ***");
				mv = new ModelAndView("home");
			}
			else{
				logger.debug("*** Authorization denied for Home page ***");
				mv = new ModelAndView("unauthorized");
			}
		}
		else{
			logger.debug("*** Authorization denied for Home page ***");
			mv = new ModelAndView("unauthorized");
		}
    	RecentAudits rcTO = new RecentAudits();
    	
		rcTO.setRecords("2");
		rcTO.setOrder("DESC");
    	
		List<RecentAudits> rs  = null;
    	try {
    		rs = auditDetailsDao.getRecentAudits(rcTO);
    	} catch (Exception e) {
		logger.debug("Exception generated"+e.getMessage());
    	}
    	mv.addObject("recent",rs);
    	logger.debug("*** Exit recentsHome method ***");
		
		LogContextManager.unset();
		return mv;
	}
	
	/***
	 * 
	 * @param user
	 * @param model
	 * @param session
	 * @return
	 * @throws SQLException 
	 */
	
	@RequestMapping(value = "/authorize", method = RequestMethod.POST)
	public ModelAndView homeDummy(@ModelAttribute UserInfo user, Model model,
			HttpSession session) throws SQLException {

		LogContext logContext = new LogContext();
		logContext.setSessionId(session.getId());
		logContext.setUserId(user.getUserId());
		LogContextManager.getThreadlocalObj();
		LogContextManager.set(logContext);

		logger.debug("Inside Home Controller. Logs are configured.");
		// System.out.println("UserId : " + user.getUserId());

		session.setAttribute("username", user.getUserId());
		String username = (String) session.getAttribute("username");
		// System.out.println("username: " + username);

		AuthorizationServiceImpl auth = new AuthorizationServiceImpl();
		String role = auth.getRole(user.getUserId());

		ModelAndView mv = new ModelAndView("home");

		if (role.equalsIgnoreCase(QADBConstants.READ_ONLY_ROLE)) {
			mv.setViewName("readOnlyUser/RO_home");
		}
		if (role.equalsIgnoreCase(QADBConstants.AUDIT_ROLE)) {
			mv.setViewName("auditUsers/AUD_home");
		}
		
		RecentAudits rcTO = new RecentAudits();
		
		
		rcTO.setRecords("2");
		rcTO.setOrder("DESC");
		
		List<RecentAudits> rs = auditDetailsDao.getRecentAudits(rcTO);
		
		for (RecentAudits recentAudits : rs) {
			logger.debug("dcn "+recentAudits.getDcnNo());
			logger.debug("time "+recentAudits.getTime());
			logger.debug("date "+recentAudits.getDate());
		}
		
		mv.addObject("recent",rs);
		
		LogContextManager.unset();
		return mv;
	}

	@RequestMapping(value = "/logout", method = RequestMethod.GET)
	public ModelAndView logout(HttpSession session) {
		logger.debug("inside logout method");
		session.removeAttribute("username");
		session.invalidate();
		logger.debug("exit logout method");
		return new ModelAndView("logout");
	
	}
	
}
