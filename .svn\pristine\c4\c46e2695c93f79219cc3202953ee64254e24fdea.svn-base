<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="scriptlet" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="30" bottomMargin="30" whenResourceMissingType="Empty" >
	<property name="com.jasperassistant.designer.Grid" value="false"/>
	<property name="com.jasperassistant.designer.SnapToGrid" value="false"/>
	<property name="com.jasperassistant.designer.GridWidth" value="12"/>
	<property name="com.jasperassistant.designer.GridHeight" value="12"/>
	<property name="ireport.zoom" value="1.0"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<property name="net.sf.jasperreports.awt.ignore.missing.font" value="true"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="errorId" class="java.lang.String">
		<fieldDescription><![CDATA[errorId]]></fieldDescription>
	</field>
	<field name="name" class="java.lang.String">
		<fieldDescription><![CDATA[name]]></fieldDescription>
	</field>
	<field name="deleted" class="java.lang.String">
		<fieldDescription><![CDATA[deleted]]></fieldDescription>
	</field>
	<field name="description" class="java.lang.String">
		<fieldDescription><![CDATA[description]]></fieldDescription>
	</field>
	<group name="dummy"> 		
	<groupExpression><![CDATA["dummy"]]></groupExpression> 
		<groupHeader>
			<band height="20">
				<staticText>
					<reportElement x="0" y="1" width="86" height="15" forecolor="#000080" />
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
					</textElement>
					<text><![CDATA[Error ID]]></text>
				</staticText>
				<staticText>
					<reportElement x="86" y="0" width="200" height="15" forecolor="#000080" />
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
					</textElement>
					<text><![CDATA[Name]]></text>
				</staticText>
				<staticText>
					<reportElement x="286" y="0" width="128" height="15" forecolor="#000080" />
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="true"/>
					</textElement>
					<text><![CDATA[Deleted]]></text>
				</staticText>
				<staticText>
					<reportElement x="414" y="0" width="121" height="15" forecolor="#000080" />
					<textElement textAlignment="Left" verticalAlignment="Middle">
						<font fontName="Times New Roman" size="11" isBold="true" isItalic="true" isUnderline="false"/>
					</textElement>
					<text><![CDATA[Description]]></text>
				</staticText>
			</band>
			<band/>
			<band height="3">
				<line>
					<reportElement x="0" y="2" width="515" height="1" forecolor="#000080" />
					<graphicElement>
						<pen lineWidth="3.0"/>
					</graphicElement>
				</line>
			</band>
		</groupHeader>
	</group>
	<title>
		<band height="40">
			<staticText>
				<reportElement mode="Opaque" x="0" y="0" width="216" height="34" forecolor="#000050" />
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="20" isBold="true" isItalic="true" isUnderline="false"/>
				</textElement>
				<text><![CDATA[Errors]]></text>
			</staticText>
		</band>
	</title>
	<columnHeader>
		<band/>
	</columnHeader>
	<detail>
		<band height="17">
			<textField>
				<reportElement x="0" y="0" width="86" height="15" />
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{errorId}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="86" y="0" width="210" height="15" />
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="286" y="0" width="128" height="15" />
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font fontName="Times New Roman" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{deleted}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="414" y="0" width="121" height="15" />
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Times New Roman" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{description}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band height="38">
			<line>
				<reportElement x="0" y="14" width="515" height="1" forecolor="#999999" />
				<graphicElement>
					<pen lineWidth="3.0"/>
				</graphicElement>
			</line>
			
			<textField>
				<reportElement x="323" y="12" width="80" height="20" forecolor="#000080" />
				<textElement textAlignment="Right">
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA["Page "+$V{PAGE_NUMBER}+" of"]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="403" y="12" width="112" height="20" forecolor="#000080" />
				<textElement>
					<font fontName="Times New Roman" size="14"/>
				</textElement>
				<textFieldExpression><![CDATA[" " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
