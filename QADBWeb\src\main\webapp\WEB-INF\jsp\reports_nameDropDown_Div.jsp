<!DOCTYPE html><%@page language="java" contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="form" uri="http://www.springframework.org/tags/form"%>

<html>	

                               <select id="name" name="name">
									<c:forEach items="${claimProcessors}" var="claimProcessors">
													<option value="${claimProcessors.associateId}"
														 ${claimProcessors.associateId== associateRO.associateId ? 'selected="selected"' : ''}>${claimProcessors.associateName}</option>
									</c:forEach>
								</select>
								
</html>	