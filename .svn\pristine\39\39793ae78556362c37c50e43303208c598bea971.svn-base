package com.carefirst.qadb.dao;

import java.sql.SQLException;
import java.util.List;

import net.sf.jasperreports.engine.JRDataSource;

import com.carefirst.audit.model.AdjustmentsReport;
import com.carefirst.audit.model.AuditAssessment;
import com.carefirst.audit.model.AuditSearch;
import com.carefirst.audit.model.OtherReports;

public interface ClaimsAuditAssessmentReportDAO {

	JRDataSource getClaimsAuditAssesmentReport(AuditSearch auditAssessmentForm) throws SQLException;
	List<AuditAssessment> getClaimsAuditAssessmentList(AuditSearch auditAssessmentForm) throws SQLException;

}
