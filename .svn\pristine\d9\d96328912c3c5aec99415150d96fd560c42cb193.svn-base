package com.carefirst.audit.model;

import java.util.List;
import java.util.ArrayList;


import org.apache.commons.collections.FactoryUtils;
import org.apache.commons.collections.list.LazyList;

public class ErrorDetails {
    
	//variables to set values received from DB
	private String errorCode;
	private String errorCodeId;
    private String spciality ;
    private String spcialityId;
    private String rootCause ;
    private String rootCauseId;
    private String editCode ;
    private String editCodeId;
    private String monetary;
    private String procedural;
    
    private String errorActive;
    public String getErrorActive() {
		return errorActive;
	}
	public void setErrorActive(String errorActive) {
		this.errorActive = errorActive;
	}
	public String getMonetary() {
		return monetary;
	}
	public void setMonetary(String monetary) {
		this.monetary = monetary;
	}
	public String getProcedural() {
		return procedural;
	}
	public void setProcedural(String procedural) {
		this.procedural = procedural;
	}

	//  private int errorCodesId[];
    private List<String> errorCodesId;
   	private List<String> errorCodes;
	
	private List<String> monetarys;
	private List<String> procedurals;
	
	private List<String> specialitysId;
	private String specialitys;
	
	private List<String> rootCausesId;
	private String rootCauses;
	
	private List<String> editCodesId;
	private String editCodes;
	
    
	
	
	
	
	//private List<ErrorInformation> errorInfo = LazyList.decorate(new ArrayList<ErrorInformation>(),FactoryUtils.instantiateFactory(ErrorInformation.class));
    
	//private List<ErrorInformation> errorInfo;
	/*private boolean monetary;
    private boolean procedural;
    private ClaimDetails claimDetails;
    private Adjustments adjustments;*/
    
	private List<String>  errorCategory;


	public List<String> getErrorCategory() {
		return errorCategory;
	}
	public void setErrorCategory(List<String> errorCategory) {
		this.errorCategory = errorCategory;
	}
	public List<String> getErrorCodesId() {
		return errorCodesId;
	}
	public String getEditCodeId() {
		return editCodeId;
	}
	public void setEditCodeId(String editCodeId) {
		this.editCodeId = editCodeId;
	}
	public List<String> getEditCodesId() {
		return editCodesId;
	}
	public void setEditCodesId(List<String> editCodesId) {
		this.editCodesId = editCodesId;
	}
	public String getEditCodes() {
		return editCodes;
	}
	public void setEditCodes(String editCodes) {
		this.editCodes = editCodes;
	}
	public void setErrorCodesId(List<String> errorCodesId) {
		this.errorCodesId = errorCodesId;
	}
	public List<String> getErrorCodes() {
		return errorCodes;
	}
	public void setErrorCodes(List<String> errorCodes) {
		this.errorCodes = errorCodes;
	}
	public List<String> getMonetarys() {
		return monetarys;
	}
	public void setMonetarys(List<String> monetarys) {
		this.monetarys = monetarys;
	}
	public List<String> getProcedurals() {
		return procedurals;
	}
	public void setProcedurals(List<String> procedurals) {
		this.procedurals = procedurals;
	}
	public List<String> getSpecialitysId() {
		return specialitysId;
	}
	public void setSpecialitysId(List<String> specialitysId) {
		this.specialitysId = specialitysId;
	}
	public String getSpecialitys() {
		return specialitys;
	}
	public void setSpecialitys(String specialitys) {
		this.specialitys = specialitys;
	}
	public List<String> getRootCausesId() {
		return rootCausesId;
	}
	public void setRootCausesId(List<String> rootCausesId) {
		this.rootCausesId = rootCausesId;
	}
	public String getRootCauses() {
		return rootCauses;
	}
	public void setRootCauses(String rootCauses) {
		this.rootCauses = rootCauses;
	}
	
	private String errorType ;
	private String errorTypeId ;
	private String errorTypes ;
    private String sop;
    private String reason;
    private String comments;
    private String required;
    private String completed;
    private String dateAdj;
    private String appeal;
    
    
    public String getErrorTypeId() {
		return errorTypeId;
	}
	public void setErrorTypeId(String errorTypeId) {
		this.errorTypeId = errorTypeId;
	}
	public String getErrorTypes() {
		return errorTypes;
	}
	public void setErrorTypes(String errorTypes) {
		this.errorTypes = errorTypes;
	}
	public String getErrorCodeId() {
		return errorCodeId;
	}
	public void setErrorCodeId(String errorCodeId) {
		this.errorCodeId = errorCodeId;
	}
	public String getSpcialityId() {
		return spcialityId;
	}
	public void setSpcialityId(String spcialityId) {
		this.spcialityId = spcialityId;
	}
	public String getRootCauseId() {
		return rootCauseId;
	}
	public void setRootCauseId(String rootCauseId) {
		this.rootCauseId = rootCauseId;
	}
	public String getSop() {
		return sop;
	}
	public void setSop(String sop) {
		this.sop = sop;
	}
	public String getReason() {
		return reason;
	}
	public void setReason(String reason) {
		this.reason = reason;
	}
	public String getComments() {
		return comments;
	}
	public void setComments(String comments) {
		this.comments = comments;
	}
	public String getRequired() {
		return required;
	}
	public void setRequired(String required) {
		this.required = required;
	}
	public String getCompleted() {
		return completed;
	}
	public void setCompleted(String completed) {
		this.completed = completed;
	}
	public String getDateAdj() {
		return dateAdj;
	}
	public void setDateAdj(String dateAdj) {
		this.dateAdj = dateAdj;
	}
	public String getAppeal() {
		return appeal;
	}
	public void setAppeal(String appeal) {
		this.appeal = appeal;
	}
	public String getErrorType() {
		return errorType;
	}
	public void setErrorType(String errorType) {
		this.errorType = errorType;
	}
	public String getSpciality() {
		return spciality;
	}
	public void setSpciality(String spciality) {
		this.spciality = spciality;
	}
	public String getRootCause() {
		return rootCause;
	}
	public void setRootCause(String rootCause) {
		this.rootCause = rootCause;
	}
	public String getEditCode() {
		return editCode;
	}
	public void setEditCode(String editCode) {
		this.editCode = editCode;
	}
	public String getErrorCode() {
		return errorCode;
	}
	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}
	/*public List<ErrorInformation> getErrorInfo() {
		return errorInfo;
	}
	public void setErrorInfo(List<ErrorInformation> errorInfo) {
		this.errorInfo = errorInfo;
	}
    */
    
    
    
    
}
